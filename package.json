{
  "name": "divinci-server",
  "version": "1.0.0",
  "private": true,
  "workspaces": {
    "packages": [
      "workspace/clients/*",
      "workspace/resources/*",
      "workspace/servers/*"
    ],
    "nohoist": [
      "workspace/workers/*",
      "**/dist/**"
    ]
  },
  "description": "Root of Divinci-Server git submodules.",
  "main": "index.js",
  "scripts": {
    "test": "node ./scripts/run-all-tests.js --skip-workers --skip-migration --no-fail",
    "test:all": "node ./scripts/run-all-tests.js --skip-migration",
    "test:coverage": "node ./scripts/run-all-tests.js --coverage --skip-workers --skip-migration --no-fail",
    "test:watch": "node ./scripts/run-all-tests.js --watch --skip-workers --skip-migration --no-fail",
    "test:parallel": "node ./scripts/run-all-tests.js --parallel --skip-workers --skip-migration --no-fail",
    "test:filter": "node ./scripts/run-all-tests.js --filter",
    "test:pnpm": "pnpm -r run test",
    "uninstall:all": "./workspace/uninstall.js --all",
    "uninstall:clients": "bash workspace/uninstall.sh --type clients",
    "install:all": "./workspace/install-with-pnpm.sh",
    "install:all:verbose": "./workspace/install-with-pnpm.sh --verbose",
    "install:no-frozen-lockfile": "./workspace/install-with-pnpm.sh --no-frozen-lockfile",
    "prepare": "husky",
    "allow-all-builds": "pnpm config set dangerously-allow-all-builds true"
  },
  "repository": {
    "type": "git",
    "url": "git+https://github.com/Divinci-AI/server.git"
  },
  "author": "",
  "license": "ISC",
  "bugs": {
    "url": "https://github.com/Divinci-AI/server/issues"
  },
  "homepage": "https://github.com/Divinci-AI/server#readme",
  "resolutions": {
    "@types/react": "^18.3.1"
  },
  "devDependencies": {
    "@stylistic/eslint-plugin-ts": "^0.1.2",
    "@types/jest": "^29.5.12",
    "@types/node": "^22.5.2",
    "@types/react": "^18.3.1",
    "@types/trusted-types": "^2.0.7",
    "@typescript-eslint/eslint-plugin": "^6.21.0",
    "@typescript-eslint/parser": "^6.21.0",
    "@vitejs/plugin-react-swc": "^3.7.0",
    "dotenv": "^16.4.5",
    "eslint": "^8.57.0",
    "eslint-plugin-jest": "^27.9.0",
    "eslint-plugin-node": "^11.1.0",
    "eslint-plugin-react": "^7.34.2",
<<<<<<< HEAD
=======
    "fast-check": "^4.1.1",
>>>>>>> WA-170_MCP
    "glob": "^10.4.1",
    "husky": "^9.1.6",
    "jest": "^29.7.0",
    "ts-jest": "^29.2.5",
    "typescript": "^5.8.3",
    "vitest": "^3.1.1"
  },
  "husky": {
    "hooks": {
      "pre-push": ".husky/pre-push",
      "pre-commit": ".husky/pre-commit"
    }
  },
  "pnpm": {
    "neverBuiltDependencies": [],
    "peerDependencyRules": {
      "ignoreMissing": [
        "@aws-sdk/client-sso-oidc"
      ],
      "allowAny": [
        "undici"
      ]
    }
  },
  "engines": {
    "node": ">=22.11.0",
    "pnpm": ">=9"
  },
  "volta": {
    "node": "22.11.0",
<<<<<<< HEAD
    "pnpm": "10.11.0"
  },
  "packageManager": "pnpm@10.11.0",
=======
    "pnpm": "10.11.5"
  },
  "packageManager": "pnpm@10.12.1",
>>>>>>> WA-170_MCP
  "publishConfig": {
    "registry": "https://npm.pkg.github.com",
    "access": "restricted"
  }
}
