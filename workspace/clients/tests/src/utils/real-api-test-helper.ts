/**
 * Real API Test Helper
 *
 * This module provides utilities for real API testing that integrate with both
 * the existing resource pool architecture and the new real API cleanup mechanisms.
 */

import { test as base, expect } from '@playwright/test';
import { ApiClient } from '../api/api-client';
import { getAuthFileForRole } from '../auth/auth-utils';
import { RealApiCleanupManager, getCleanupManager } from './real-api-cleanup';
import { config } from '../config/config';

/**
 * Extended test fixtures for real API testing
 */
export interface RealApiTestFixtures {
  adminClient: ApiClient;
  userClient: ApiClient;
  ownerClient: ApiClient;
  cleanupManager: RealApiCleanupManager;
}

/**
 * Test fixture that provides authenticated API clients and cleanup management
 */
export const test = base.extend<RealApiTestFixtures>({
  adminClient: async ({}, use) => {
    const client = new ApiClient(config.apiBaseUrl, getAuthFileForRole('admin'));
    await use(client);
  },

  userClient: async ({}, use) => {
    const client = new ApiClient(config.apiBaseUrl, getAuthFileForRole('user'));
    await use(client);
  },

  ownerClient: async ({}, use) => {
    const client = new ApiClient(config.apiBaseUrl, getAuthFileForRole('owner'));
    await use(client);
  },

  cleanupManager: async ({ adminClient }, use) => {
    const manager = getCleanupManager(adminClient);

    await use(manager);

    // Cleanup after test
    await manager.cleanupAll();
  }
});

/**
 * Helper class for real API testing with automatic resource tracking
 */
export class RealApiTestHelper {
  private cleanupManager: RealApiCleanupManager;
  private apiClient: ApiClient;

  constructor(apiClient: ApiClient, cleanupManager: RealApiCleanupManager) {
    this.apiClient = apiClient;
    this.cleanupManager = cleanupManager;
  }

  /**
   * Create a chat and automatically track it for cleanup
   */
  async createChat(data: { title: string; releases?: string[] }): Promise<any> {
    const chatData = {
      title: data.title,
      releases: data.releases || []
    };

    const response = await this.apiClient.post('ai-chat', chatData);

    // Track the chat for cleanup
    if (response.chat && response.chat._id) {
      this.cleanupManager.trackChat(response.chat._id, { title: data.title });
    }

    return response;
  }

  /**
   * Add a message to a chat and automatically track it for cleanup
   */
  async addMessage(chatId: string, data: { content: string; role?: string }): Promise<any> {
    const messageData = {
      content: data.content,
      role: data.role || 'user'
    };

    const response = await this.apiClient.post(`ai-chat/${chatId}/message`, messageData);

    // Track the message for cleanup (if the API returns message ID)
    if (response.messageId) {
      this.cleanupManager.trackMessage(response.messageId, chatId, { content: data.content });
    }

    return response;
  }

  /**
   * Get a chat by ID
   */
  async getChat(chatId: string): Promise<any> {
    return await this.apiClient.get(`ai-chat/${chatId}`);
  }

  /**
   * List all chats
   */
  async listChats(): Promise<any> {
    return await this.apiClient.get('ai-chat');
  }

  /**
   * Update a chat (rename)
   */
  async updateChat(chatId: string, data: { title?: string }): Promise<any> {
    if (data.title) {
      // Use the rename endpoint for title updates
      return await this.apiClient.post(`ai-chat/${chatId}/rename`, { title: data.title });
    }
    throw new Error('Only title updates are supported via the rename endpoint');
  }

  /**
   * Delete a chat (and remove from cleanup tracking)
   */
  async deleteChat(chatId: string): Promise<any> {
    const response = await this.apiClient.delete(`ai-chat/${chatId}`);

    // Remove from cleanup tracking since we manually deleted it
    const manager = this.cleanupManager as any;
    if (manager.resources) {
      manager.resources = manager.resources.filter((r: any) =>
        !(r.type === 'chat' && r.id === chatId)
      );
    }

    return response;
  }

  /**
   * Get cleanup statistics
   */
  getCleanupStats() {
    return this.cleanupManager.getCleanupStats();
  }

  /**
   * Manually trigger cleanup
   */
  async cleanup(): Promise<void> {
    await this.cleanupManager.cleanupAll();
  }
}

/**
 * Create a test helper instance
 */
export function createRealApiTestHelper(apiClient: ApiClient, cleanupManager: RealApiCleanupManager): RealApiTestHelper {
  return new RealApiTestHelper(apiClient, cleanupManager);
}

/**
 * Assertion helpers for real API responses
 */
export const realApiExpect = {
  /**
   * Expect a chat response to have the correct structure
   */
  chatResponse: (response: any, expectedTitle?: string) => {
    expect(response).toBeDefined();
    expect(response.chat).toBeDefined();
    expect(response.chat._id).toBeDefined();
    expect(response.chat.title).toBeDefined();
    expect(response.chat.ownerUser).toBeDefined();
    expect(response.chat.releases).toBeInstanceOf(Array);
    expect(response.transcript).toBeDefined();
    expect(response.transcript._id).toBeDefined();
    expect(response.transcript.messages).toBeInstanceOf(Array);

    if (expectedTitle) {
      expect(response.chat.title).toBe(expectedTitle);
    }
  },

  /**
   * Expect a chat list response to have the correct structure
   */
  chatListResponse: (response: any) => {
    expect(response).toBeInstanceOf(Array);
    response.forEach((chat: any) => {
      expect(chat._id).toBeDefined();
      expect(chat.title).toBeDefined();
      expect(chat.ownerUser).toBeDefined();
      expect(chat.releases).toBeInstanceOf(Array);
      expect(chat.createdAt).toBeDefined();
      expect(chat.updatedAt).toBeDefined();
    });
  },

  /**
   * Expect a chat detail response to have the correct structure
   */
  chatDetailResponse: (response: any, expectedId?: string) => {
    expect(response).toBeDefined();
    expect(response.chat).toBeDefined();
    expect(response.chat._id).toBeDefined();
    expect(response.transcript).toBeDefined();
    expect(response.releases).toBeInstanceOf(Array);

    if (expectedId) {
      expect(response.chat._id).toBe(expectedId);
    }
  },

  /**
   * Expect an error response to have the correct structure
   */
  errorResponse: (error: any, expectedStatus: number, expectedMessageContains?: string) => {
    expect(error.status).toBe(expectedStatus);
    expect(error.body).toBeDefined();
    expect(error.body.status).toBe('error');
    expect(error.body.message).toBeDefined();

    if (expectedMessageContains) {
      expect(error.body.message).toContain(expectedMessageContains);
    }
  }
};

export { expect };
