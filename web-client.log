
> @divinci-ai/web-client@0.3.0 start /workspaces/server/workspace/clients/web
> node server.js

🐞LOG_DEBUG MODE:  1
🐞LOG_DEBUG MODE:  1
🔎 CURRENT ENVIRONMENT: dev | NODE_ENV: development | FORCE_R2_STORAGE: undefined
🔎 CORS FULL ORIGINS (raw): api.slack.com,dev-46tiys6hnb6vbg17-cd-hljzhimsn7o28fvq.edge.tenants.us.auth0.com,dev-46tiys6hnb6vbg17.us.auth0.com,local-chunks-vectorized,127.0.0.1:,obscure-chainsaw-jj4x9rq9gx4qcqw45-8080.app.github.dev,obscure-chainsaw-jj4x9rq9gx4qcqw45-8081.app.github.dev,obscure-chainsaw-jj4x9rq9gx4qcqw45-8082.app.github.dev,obscure-chainsaw-jj4x9rq9gx4qcqw45-8083.app.github.dev,obscure-chainsaw-jj4x9rq9gx4qcqw45-9080.app.github.dev
🔎 CORS FULL ORIGINS (cleaned): [
  'api.slack.com',
  'dev-46tiys6hnb6vbg17-cd-hljzhimsn7o28fvq.edge.tenants.us.auth0.com',
  'dev-46tiys6hnb6vbg17.us.auth0.com',
  'local-chunks-vectorized',
  '127.0.0.1:',
  'obscure-chainsaw-jj4x9rq9gx4qcqw45-8080.app.github.dev',
  'obscure-chainsaw-jj4x9rq9gx4qcqw45-8081.app.github.dev',
  'obscure-chainsaw-jj4x9rq9gx4qcqw45-8082.app.github.dev',
  'obscure-chainsaw-jj4x9rq9gx4qcqw45-8083.app.github.dev',
  'obscure-chainsaw-jj4x9rq9gx4qcqw45-9080.app.github.dev'
]
🔑 [DROPBOX-ENV] Loading Dropbox environment variables...
🔑 [DROPBOX-ENV] Dropbox environment variables loaded successfully {
  clientId: 'snqjeycw...',
  clientSecret: '[PRESENT]',
  redirectUri: 'http://localhost:9080/auth/dropbox/callback'
}
🌐 Web CORS_PORTS:  [
  8080, 8081, 8082,
  8083, 8084, 9080,
  9081, 8787, 8788,
  8789, 8790, 9000
]
node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8080
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at Function.listen (/workspaces/server/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/workspaces/server/workspace/clients/web/server.js:163:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1975:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8080
}

Node.js v22.16.0
 ELIFECYCLE  Command failed with exit code 1.
