/**
 * Dropbox Sync Router
 */

import { Router } from "express";

export const router = Router({ mergeParams: true });

// Import route handlers
import { createDropboxSyncJob } from "./create-sync-job";
import { getDropboxSyncStatus } from "./sync-status";
import { cancelDropboxSync } from "./cancel-sync";
import { retryDropboxSync } from "./retry-sync";
import { getDropboxSyncStats } from "./sync-stats";
import { updateDropboxFileStatus } from "./update-file-status";
import { updateDropboxSyncJob } from "./update-job";
import { updateDropboxFileMapping } from "./update-file-mapping";

// Sync management routes
router.post("/create", createDropboxSyncJob);
router.get("/status", getDropboxSyncStatus);
router.post("/cancel", cancelDropboxSync);
router.post("/retry", retryDropboxSync);
router.get("/stats", getDropboxSyncStats);

// Worker callback routes (for internal use)
router.post("/update-file-status", updateDropboxFileStatus);
router.post("/update-job", updateDropboxSyncJob);
router.post("/update-file-mapping", updateDropboxFileMapping);
