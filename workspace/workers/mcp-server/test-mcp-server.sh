#!/bin/bash

# Test script for Divinci MCP Server
# This script tests all the basic functionality of the MCP server

set -e

echo "🧪 Testing Divinci MCP Server"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Server URL
SERVER_URL="http://localhost:8793"

# Function to test HTTP endpoints
test_endpoint() {
    local endpoint=$1
    local expected_status=$2
    local description=$3
    
    echo -n "Testing $description... "
    
    response=$(curl -s -w "%{http_code}" -o /tmp/response.json "$SERVER_URL$endpoint")
    status_code="${response: -3}"
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✓ PASS${NC} (Status: $status_code)"
        if [ -f /tmp/response.json ]; then
            echo "   Response: $(cat /tmp/response.json | jq -c . 2>/dev/null || cat /tmp/response.json)"
        fi
    else
        echo -e "${RED}✗ FAIL${NC} (Expected: $expected_status, Got: $status_code)"
        if [ -f /tmp/response.json ]; then
            echo "   Response: $(cat /tmp/response.json)"
        fi
        return 1
    fi
}

# Function to check if server is running
check_server() {
    echo "🔍 Checking if MCP server is running..."
    if curl -s "$SERVER_URL/health" > /dev/null; then
        echo -e "${GREEN}✓ Server is running${NC}"
        return 0
    else
        echo -e "${RED}✗ Server is not running${NC}"
        echo "Please start the server with: npm run dev"
        return 1
    fi
}

# Function to test MCP SSE endpoint
test_mcp_sse() {
    echo "🔌 Testing MCP SSE endpoint..."
    
    # Test SSE connection (this will timeout after 5 seconds, which is expected)
    timeout 5s curl -s -H "Accept: text/event-stream" "$SERVER_URL/sse" > /tmp/sse_response.txt || true
    
    if [ -s /tmp/sse_response.txt ]; then
        echo -e "${GREEN}✓ SSE endpoint is responding${NC}"
        echo "   First few lines of SSE response:"
        head -3 /tmp/sse_response.txt | sed 's/^/   /'
    else
        echo -e "${YELLOW}⚠ SSE endpoint test inconclusive${NC}"
        echo "   This is normal - SSE connections require MCP client protocol"
    fi
}

# Main test execution
main() {
    echo "Starting tests at $(date)"
    echo ""
    
    # Check if server is running
    if ! check_server; then
        exit 1
    fi
    
    echo ""
    echo "📋 Testing HTTP Endpoints"
    echo "-------------------------"
    
    # Test health endpoint
    test_endpoint "/health" "200" "Health endpoint"
    
    # Test root endpoint
    test_endpoint "/" "200" "Root endpoint"
    
    # Test 404 endpoint
    test_endpoint "/nonexistent" "404" "404 handling"
    
    # Test CORS preflight
    echo -n "Testing CORS preflight... "
    cors_response=$(curl -s -w "%{http_code}" -X OPTIONS -H "Origin: http://localhost:5173" "$SERVER_URL/sse")
    cors_status="${cors_response: -3}"
    if [ "$cors_status" = "200" ]; then
        echo -e "${GREEN}✓ PASS${NC} (Status: $cors_status)"
    else
        echo -e "${RED}✗ FAIL${NC} (Status: $cors_status)"
    fi
    
    echo ""
    echo "🔌 Testing MCP Protocol"
    echo "----------------------"
    
    # Test SSE endpoint
    test_mcp_sse
    
    echo ""
    echo "📊 Test Summary"
    echo "==============="
    echo -e "${GREEN}✓ Basic HTTP endpoints working${NC}"
    echo -e "${GREEN}✓ CORS configuration working${NC}"
    echo -e "${GREEN}✓ MCP SSE endpoint accessible${NC}"
    echo ""
    echo "🎯 Next Steps for Manual Testing:"
    echo "1. Open MCP Inspector: http://localhost:6274"
    echo "2. Connect to: http://localhost:8793/sse"
    echo "3. Test available tools: create_chat, send_message, list_chats, get_user_profile"
    echo ""
    echo "🚀 For Claude Desktop integration:"
    echo "Add this to your Claude Desktop config:"
    echo '{'
    echo '  "mcpServers": {'
    echo '    "divinci": {'
    echo '      "command": "npx",'
    echo '      "args": ["mcp-remote", "http://localhost:8793/sse"]'
    echo '    }'
    echo '  }'
    echo '}'
}

# Run tests
main "$@"
