/**
 * Types for Dropbox Sync Workflow Steps
 */

// WorkflowStep type (would be imported from cloudflare:workers in real deployment)
type WorkflowStep = any;
import { 
  Env, 
  DropboxSyncFile, 
  DropboxSyncProcessingConfig,
  FileDownloadResult,
  AudioProcessingResult,
  ChunksWorkflowResult,
  WorkflowMetadata
} from "../types";

// Common step context
export interface StepContext {
  env: Env;
  jobId: string;
  whitelabelId: string;
  auth0Token: string;
  accessToken: string; // Dropbox access token
}

// Step result types
export interface InitializeWorkflowResult {
  metadata: WorkflowMetadata;
  validatedFiles: DropboxSyncFile[];
  processingConfig: DropboxSyncProcessingConfig;
}

export interface DownloadFilesResult {
  downloadedFiles: (FileDownloadResult & { 
    dropboxFileId: string;
    originalFile: DropboxSyncFile;
  })[];
  totalBytes: number;
  failedDownloads: {
    dropboxFileId: string;
    fileName: string;
    error: string;
  }[];
}

export interface ProcessAudioResult {
  processedFiles: (AudioProcessingResult & {
    dropboxFileId: string;
    fileName: string;
    downloadResult: FileDownloadResult;
  })[];
  failedProcessing: {
    dropboxFileId: string;
    fileName: string;
    error: string;
  }[];
}

export interface TriggerChunksWorkflowResult {
  triggeredWorkflows: (ChunksWorkflowResult & {
    dropboxFileId: string;
    fileName: string;
  })[];
  failedTriggers: {
    dropboxFileId: string;
    fileName: string;
    error: string;
  }[];
}

export interface UpdateDatabaseResult {
  updatedSyncJob: boolean;
  updatedFileMappings: number;
  failedUpdates: string[];
}

// API client interfaces
export interface DropboxAPIClient {
  downloadFile(path: string): Promise<{
    metadata: any;
    content: ArrayBuffer;
  }>;
  getFileMetadata(path: string): Promise<any>;
}

export interface AudioProcessingClient {
  transcribeAudio(audioUrl: string, options?: any): Promise<{
    transcriptionId: string;
    text: string;
    metadata: any;
  }>;
  
  diarizeAudio(audioUrl: string, options?: any): Promise<{
    diarizationId: string;
    segments: any[];
    metadata: any;
  }>;
}

export interface ChunksWorkflowClient {
  triggerWorkflow(payload: {
    files: {
      fileId: string;
      target: string;
      fileName: string;
      bucket: string;
      objectKey: string;
      processor: string;
      processorConfig: any;
    }[];
    vectorizeConfig: {
      whitelabelId: string;
      ragId?: string;
      auth0Token: string;
    };
  }): Promise<{
    success: boolean;
    workflowId: string;
  }>;
}

export interface DatabaseAPIClient {
  updateSyncJob(jobId: string, updates: any): Promise<{ success: boolean }>;
  updateFileMapping(dropboxFileId: string, updates: any): Promise<{ success: boolean }>;
  createFileRecord(payload: any): Promise<{ success: boolean; data: { _id: string } }>;
}
