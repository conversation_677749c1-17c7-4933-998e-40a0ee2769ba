<<<<<<< HEAD
import path from 'path';
import fs from 'fs';
=======
import * as path from 'path';
import * as fs from 'fs';
>>>>>>> WA-170_MCP

/**
 * Helper function to get a proper screenshot path for E2E tests.
 * This ensures all screenshots are saved in a 'screenshots' directory
 * next to the test file, keeping the repository organized.
 * 
 * @param testFilePath - The __dirname of the test file (use __dirname)
 * @param filename - The screenshot filename (e.g., 'login-page.png')
 * @returns The full path where the screenshot should be saved
 */
export function getScreenshotPath(testFilePath: string, filename: string): string {
  const screenshotsDir = path.join(testFilePath, 'screenshots');
  
  // Ensure screenshots directory exists
  if (!fs.existsSync(screenshotsDir)) {
    fs.mkdirSync(screenshotsDir, { recursive: true });
  }
  
  return path.join(screenshotsDir, filename);
}

/**
 * Helper function to create a screenshot path factory for a specific test file.
 * This is useful when you want to avoid passing __dirname repeatedly.
 * 
 * @param testFilePath - The __dirname of the test file (use __dirname)
 * @returns A function that takes only the filename and returns the full screenshot path
 */
export function createScreenshotPathFactory(testFilePath: string) {
  return (filename: string) => getScreenshotPath(testFilePath, filename);
}
