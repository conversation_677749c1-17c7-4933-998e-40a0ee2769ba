/**
 * Disconnect Dropbox account
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { dropboxOAuth } from "@divinci-ai/server-globals";
import { DropboxConnectionModel } from "@divinci-ai/server-models";
import { ensureWhitelabel } from "../util/ensure-whitelabel";

export const disconnectDropbox: RequestHandler = async function(req, res, next){
  try {
    const { whitelabel, userId } = await ensureWhitelabel(req);

    // Find active connection for this user
    if(!userId) {
      return res.status(400).json({
        success: false,
        error: "user_id_missing",
        message: "User ID is required"
      });
    }

    const connection = await DropboxConnectionModel.findActiveConnection(
      whitelabel._id?.toString() || "",
      userId
    );

    if(!connection) {
      return res.status(404).json({
        success: false,
        error: "connection_not_found",
        message: "No active Dropbox connection found"
      });
    }

    // Revoke token with Dropbox (best effort - don't fail if this fails)
    try {
      await dropboxOAuth.revokeAccess(connection.accessToken);
    }catch(revokeError) {
      console.warn("Failed to revoke Dropbox token:", revokeError);
    }

    // Mark connection as revoked in our database
    await connection.revoke("User requested disconnection");

    res.status(200).json({
      success: true,
      data: {
        connectionId: connection._id.toString(),
        disconnectedAt: new Date(),
        message: "Dropbox account disconnected successfully"
      }
    });

  }catch(error) {
    next(error);
  }
};
