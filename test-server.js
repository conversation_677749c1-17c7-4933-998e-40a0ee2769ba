const https = require("https");
const fs = require("fs");
const path = require("path");

const port = 8080;

// Try to use the SSL certificates if they exist
const certPath = "/workspaces/server/private-keys/local-fast/certs/server.crt";
const keyPath = "/workspaces/server/private-keys/local-fast/certs/server.key";

let server;

if (fs.existsSync(certPath) && fs.existsSync(keyPath)) {
  console.log("🔒 Starting HTTPS server with SSL certificates...");
  const options = {
    cert: fs.readFileSync(certPath),
    key: fs.readFileSync(keyPath),
  };

  server = https.createServer(options, (req, res) => {
    res.writeHead(200, { "Content-Type": "text/html" });
    res.end(`
      <html>
        <head><title>Test Server</title></head>
        <body>
          <h1>🎉 Test Server is Working!</h1>
          <p>This is a test HTTPS server running on port ${port}</p>
          <p>Environment: ${
            process.env.CODESPACES ? "GitHub Codespaces" : "Local"
          }</p>
          <p>Time: ${new Date().toISOString()}</p>
        </body>
      </html>
    `);
  });
} else {
  console.log("⚠️ SSL certificates not found, starting HTTP server...");
  const http = require("http");

  server = http.createServer((req, res) => {
    res.writeHead(200, { "Content-Type": "text/html" });
    res.end(`
      <html>
        <head><title>Test Server</title></head>
        <body>
          <h1>🎉 Test Server is Working!</h1>
          <p>This is a test HTTP server running on port ${port}</p>
          <p>Environment: ${
            process.env.CODESPACES ? "GitHub Codespaces" : "Local"
          }</p>
          <p>Time: ${new Date().toISOString()}</p>
        </body>
      </html>
    `);
  });
}

server.listen(port, "0.0.0.0", () => {
  console.log(`🚀 Test server running on port ${port}`);
  console.log(
    `🌐 Access at: https://sturdy-space-broccoli-g4xpjgv6376q-${port}.app.github.dev`
  );
});
