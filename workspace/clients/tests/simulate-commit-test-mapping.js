#!/usr/bin/env node

/**
 * Simulate Commit Test Mapping
 * 
 * This script simulates a commit with changed files and verifies that the
 * correct test suites are triggered based on the test mapping configuration.
 * It then runs those test suites to ensure they work correctly.
 */

const { execSync } = require('child_process');
const path = require('path');
const { getApiTestSuitesForChangedFolders } = require('./src/api-test-mapping.ts');

// Simulate different commit scenarios
const commitScenarios = [
  {
    name: 'AI Chat API Changes',
    description: 'Developer modifies AI Chat API routes and handlers',
    changedFiles: [
      'workspace/servers/public-api/src/routes/ai-chat/router/chat.ts',
      'workspace/servers/public-api/src/routes/ai-chat/handlers/create.ts',
      'workspace/servers/public-api/src/routes/ai-chat/handlers/update.ts',
      'workspace/servers/public-api/src/routes/ai-chat/middleware/auth.ts'
    ],
    expectedTestSuites: ['AI Chats'],
    shouldRunRealAPI: true
  },
  {
    name: 'White Label API Changes',
    description: 'Developer modifies White Label API functionality',
    changedFiles: [
      'workspace/servers/public-api/src/routes/whitelabel/router/index.ts',
      'workspace/servers/public-api/src/routes/whitelabel/handlers/create.ts'
    ],
    expectedTestSuites: ['White Label'],
    shouldRunRealAPI: false // Mock tests only for now
  },
  {
    name: 'Multiple API Changes',
    description: 'Developer modifies multiple API areas in one commit',
    changedFiles: [
      'workspace/servers/public-api/src/routes/ai-chat/router/chat.ts',
      'workspace/servers/public-api/src/routes/whitelabel/handlers/create.ts',
      'workspace/servers/public-api/src/routes/rag/vector-search.ts',
      'workspace/servers/public-api/src/routes/finetune/training.ts'
    ],
    expectedTestSuites: ['AI Chats', 'White Label', 'RAG', 'Fine Tune'],
    shouldRunRealAPI: true // Run real API for AI Chats
  },
  {
    name: 'Test Infrastructure Changes',
    description: 'Developer modifies test infrastructure',
    changedFiles: [
      'workspace/clients/tests/src/auth/auth-utils.ts',
      'workspace/clients/tests/src/api/api-client.ts',
      'workspace/clients/tests/src/config/config.ts'
    ],
    expectedTestSuites: ['AI Chats', 'White Label', 'Fine Tune', 'Prompt Moderation', 'Thread Prefix', 'Message Prefix', 'RAG', 'Workspace Release', 'Audio Transcript'],
    shouldRunRealAPI: true // Run all tests including real API
  },
  {
    name: 'Frontend Only Changes',
    description: 'Developer modifies only frontend code',
    changedFiles: [
      'workspace/clients/web/src/components/Chat/ChatInput.tsx',
      'workspace/clients/web/src/pages/Dashboard/Dashboard.tsx',
      'workspace/clients/web/src/styles/global.css'
    ],
    expectedTestSuites: [],
    shouldRunRealAPI: false
  }
];

/**
 * Extract folders from changed files
 */
function extractFoldersFromFiles(changedFiles) {
  const folders = new Set();
  
  changedFiles.forEach(file => {
    const parts = file.split('/');
    
    // Build folder paths from most specific to least specific
    for (let i = parts.length - 1; i >= 1; i--) {
      const folder = parts.slice(0, i).join('/');
      folders.add(folder);
    }
  });
  
  return Array.from(folders);
}

/**
 * Run test suites
 */
function runTestSuites(testSuites, useRealAPI = false) {
  console.log(`\n🧪 Running test suites: ${testSuites.join(', ')}`);
  
  if (testSuites.length === 0) {
    console.log('   No test suites to run');
    return { success: true, output: 'No tests needed' };
  }
  
  try {
    // Build the test command
    let command = 'npx playwright test --project=api-tests';
    
    if (useRealAPI) {
      command = `USE_REAL_AUTH0=true ${command}`;
    }
    
    // Add test suite filters
    const testFilters = testSuites.map(suite => `-g "${suite}"`).join(' ');
    command += ` ${testFilters} --reporter=line`;
    
    console.log(`   Command: ${command}`);
    
    const output = execSync(command, { 
      encoding: 'utf8',
      cwd: process.cwd(),
      timeout: 120000 // 2 minutes timeout
    });
    
    console.log('   ✅ Tests passed');
    return { success: true, output };
  } catch (error) {
    console.log('   ❌ Tests failed');
    console.log(`   Error: ${error.message}`);
    return { success: false, output: error.message };
  }
}

/**
 * Simulate a commit scenario
 */
function simulateCommitScenario(scenario) {
  console.log(`\n${'='.repeat(80)}`);
  console.log(`📝 Scenario: ${scenario.name}`);
  console.log(`📄 Description: ${scenario.description}`);
  console.log(`📁 Changed files (${scenario.changedFiles.length}):`);
  scenario.changedFiles.forEach(file => console.log(`   - ${file}`));
  
  // Extract folders from changed files
  const allFolders = extractFoldersFromFiles(scenario.changedFiles);
  console.log(`\n📂 Extracted folders (${allFolders.length}):`);
  allFolders.forEach(folder => console.log(`   - ${folder}`));
  
  // Get test suites using the mapping function
  const testSuites = getApiTestSuitesForChangedFolders(allFolders);
  console.log(`\n🎯 Expected test suites: ${scenario.expectedTestSuites.join(', ') || 'None'}`);
  console.log(`🎯 Actual test suites: ${testSuites.join(', ') || 'None'}`);
  
  // Verify mapping is correct
  const mappingCorrect = JSON.stringify(testSuites.sort()) === JSON.stringify(scenario.expectedTestSuites.sort());
  if (mappingCorrect) {
    console.log('✅ Test mapping is correct');
  } else {
    console.log('❌ Test mapping is incorrect');
    return { success: false, reason: 'Test mapping mismatch' };
  }
  
  // Run the tests
  const testResult = runTestSuites(testSuites, scenario.shouldRunRealAPI);
  
  return {
    success: mappingCorrect && testResult.success,
    testSuites,
    testResult
  };
}

/**
 * Main execution
 */
function main() {
  console.log('🚀 Simulating Commit Test Mapping Scenarios');
  console.log('=' .repeat(80));
  
  const results = [];
  
  // Run each scenario
  for (const scenario of commitScenarios) {
    const result = simulateCommitScenario(scenario);
    results.push({
      scenario: scenario.name,
      ...result
    });
  }
  
  // Summary
  console.log(`\n${'='.repeat(80)}`);
  console.log('📊 SUMMARY');
  console.log('=' .repeat(80));
  
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  console.log(`\n✅ Successful scenarios: ${successCount}/${totalCount}`);
  console.log(`❌ Failed scenarios: ${totalCount - successCount}/${totalCount}`);
  
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    console.log(`   ${status} ${result.scenario}`);
    if (!result.success && result.reason) {
      console.log(`      Reason: ${result.reason}`);
    }
  });
  
  // Overall result
  if (successCount === totalCount) {
    console.log('\n🎉 All commit scenarios passed! Test mapping is working correctly.');
    process.exit(0);
  } else {
    console.log('\n💥 Some commit scenarios failed. Please check the test mapping configuration.');
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  simulateCommitScenario,
  extractFoldersFromFiles,
  runTestSuites
};
