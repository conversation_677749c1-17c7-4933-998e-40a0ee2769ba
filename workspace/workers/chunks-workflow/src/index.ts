import type { Context } from "hono";
import { Hono } from "hono";
import { cors } from "hono/cors";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
// Import the workflow
import { ChunksVectorizedWorkflow } from "./workflows/chunks-vectorized";
import { VideoToAudioWorkflow } from "./workflows/video-to-audio";
import { Env } from "./types";
import { createStorageClient } from "./utils/storage-client";

export function createCloudflareExecutionContext(honoCtx: Context["executionCtx"]): ExecutionContext{
  return {
    waitUntil: (promise: Promise<any>)=>honoCtx.waitUntil(promise),
    passThroughOnException: ()=>honoCtx.passThroughOnException(),
    props: {}, // Required by Cloudflare"s ExecutionContext
  };
}

// Create the Hono app with the correct environment type
type AppEnv = {
  Bindings: Env & {
    [key: string]: unknown, // Add index signature
  },
};

const app = new Hono<AppEnv>();

// Create a CORS middleware handler function
const createCorsMiddleware = (c: Context)=>{
  return cors({
    origin: (origin)=>{
      const allowedOrigins = c.env.ALLOWED_ORIGINS.split(",");
      console.log("🔍 CORS middleware - Checking origin:", origin);
      console.log("🔍 CORS middleware - Allowed origins:", allowedOrigins);

      if(!origin) {
        console.log("🔍 CORS middleware - No origin, returning *");
        return "*";
      }

      // First check for exact matches
      if(allowedOrigins.includes(origin)) {
        console.log(`🔍 CORS middleware - Exact match found for origin: ${origin}`);
        return origin;
      }

      // Then check for wildcard patterns
      const matchesPattern = allowedOrigins.some(allowed=>{
        if(allowed.includes("*")) {
          // Convert the wildcard pattern to a proper regex
          // e.g., "https://*.stage.divinci.app" becomes "^https:\/\/[^\/]*\.stage\.divinci\.app$"
          const regexPattern = allowed
            .replace(/\./g, "\\.")
            .replace(/\*/g, "[^\\/]*");
          const pattern = new RegExp(`^${regexPattern}$`);
          const matches = pattern.test(origin);
          console.log(`🔍 CORS middleware - Testing pattern ${allowed} against ${origin}: ${matches}`);
          return matches;
        }
        return false;
      });

      if(matchesPattern) {
        console.log(`🔍 CORS middleware - Pattern match found for origin: ${origin}`);
        return origin;
      }

      // If we get here, the origin is not allowed
      console.log(`🔍 CORS middleware - No match found for origin: ${origin}`);
      return "";
    },
    allowMethods: ["POST", "GET", "OPTIONS", "HEAD"],
    allowHeaders: [
      "Content-Type",
      "Authorization",
      "x-file-name",
      "x-file-id",
      "x-target",
      "x-processor",
      "x-processor-config",
      "x-vectorize-config",
      "x-debug-client",
      "cloudflare-worker-x-dev-auth",
      "x-worker-local-dev",
      "divinci-organization",
    ],
    exposeHeaders: [
      "x-file-name",
      "x-file-id",
      "x-target",
      "x-processor",
      "x-processor-config",
      "x-vectorize-config",
      "divinci-organization",
    ],
    maxAge: 600,
    credentials: true,
  });
};

// Use the middleware with context
app.use("*", async (c, next)=>{
  const corsMiddleware = createCorsMiddleware(c);
  return corsMiddleware(c, next);
});

// Add a test endpoint for storage operations
app.get("/storage-test", async (c)=>{
  console.log("🔄 [TEST] Running storage test endpoint...");

  try {
    // Create the storage client
    const storage = createStorageClient(c.env);

    // Create a test file with timestamp
    const testKey = `test/storage-test-${Date.now()}.txt`;
    const testContent = `Storage test file created at ${new Date().toISOString()}`;

    console.log(`🔄 [TEST] Creating test file: ${testKey}`);
    const putResult = await storage.put(testKey, testContent, {
      httpMetadata: { contentType: "text/plain" },
      customMetadata: { test: "true", timestamp: Date.now().toString() }
    });

    console.log(`🔄 [TEST] Test file created, fetching it back...`);
    const getResult = await storage.get(testKey);

    let content = "File not found";
    if(getResult) {
      content = await getResult.text();
      console.log(`🔄 [TEST] Retrieved file content: ${content}`);
    }

    // Delete the test file
    console.log(`🔄 [TEST] Deleting test file...`);
    await storage.delete(testKey);

    // List all test files
    console.log(`🔄 [TEST] Listing test files...`);
    const listResult = await storage.list({ prefix: "test/" });

    return c.json({
      status: "success",
      message: "Storage test completed successfully",
      created: putResult ? true : false,
      retrieved: getResult ? true : false,
      content: content,
      files_in_test_dir: listResult.objects.length,
      base_url: c.env.R2_BUCKET_URL || "using default"
    });
  }catch(error) {
    console.error(`❌ [TEST] Storage test error: ${error.message}`);
    return c.json({
      status: "error",
      message: `Storage test failed: ${error.message}`,
      stack: error.stack
    }, 500);
  }
});

// Add explicit OPTIONS handler for preflight requests
app.options("*", (c)=>{
  const origin = c.req.header("Origin");
  console.log("🔍 OPTIONS request from origin:", origin);

  // Get allowed origins from environment
  const allowedOrigins = c.env.ALLOWED_ORIGINS.split(",");
  console.log("🔍 Allowed origins:", allowedOrigins);

  // Check if origin is allowed
  const isAllowed = !origin || allowedOrigins.includes(origin) || allowedOrigins.includes("*");
  console.log("🔍 Is origin allowed:", isAllowed);

  return new Response(null, {
    status: 204,
    headers: {
      "Access-Control-Allow-Origin": origin || "*",
      "Access-Control-Allow-Methods": "GET, POST, OPTIONS, HEAD",
      "Access-Control-Allow-Credentials": "true",
      "Access-Control-Max-Age": "600",
      "Access-Control-Allow-Headers": c.req.header("Access-Control-Request-Headers") ||
        "Content-Type, Authorization, x-file-name, x-file-id, x-target, x-processor, x-processor-config, x-vectorize-config, x-debug-client, cloudflare-worker-x-dev-auth, x-worker-local-dev, divinci-organization"
    }
  });
});

// Add a middleware to ensure proper Content-Type
app.use("*", async (c, next)=>{
  if(c.req.method === "POST") {
    c.header("Content-Type", "application/json");
  }
  await next();
});

// Define the validation schema for single file processing
const processDocumentSchema = z.object({
  files: z.array(z.object({
    fileId: z.string(),
    target: z.string(),
    fileName: z.string(),
    bucket: z.string(),
    objectKey: z.string(),
    processor: z.enum(["unstructured", "openparse"])
      .default("unstructured")
      .transform((val)=>{
        console.log("🔄 Processor selected:", val);
        return val;
      }),
    processorConfig: z.object({
      semantic_chunking: z.boolean().optional(),
      embeddings_provider: z.enum(["cloudflare", "ollama", "none"]).optional(),
      minTokens: z.number().optional(),
      maxTokens: z.number().optional(),
      relevanceThreshold: z.number().optional().default(0.3),
      skipDeJunk: z.boolean().optional(),
      disableStreaming: z.boolean().optional().default(false),
      unstructured: z.object({
        chunkingStrategy: z.enum(["by_title", "by_similarity", "by_page", "by_character"]).optional(),
        maxCharacters: z.number().optional(),
        similarityThreshold: z.number().optional(),
        includeOriginalElements: z.boolean().optional(),
        multipageSections: z.boolean().optional()
      }).optional()
    }).optional(),
    fileData: z.object({
      base64: z.string(),
      type: z.string(),
      size: z.number()
    }).optional(),
    title: z.string().optional(),
    description: z.string().optional()
  })).min(1),
  vectorizeConfig: z.object({
    accountId: z.string(),
    apiToken: z.string(),
    ragName: z.string(),
    ragVectors: z.array(z.string()),
    whitelabelId: z.string(),
    auth0Token: z.string()
  }),
  isBatch: z.boolean().optional()
}).strict();

// Define the batch processing schema
const batchProcessSchema = z.object({
  files: z.array(z.object({
    fileId: z.string(),
    target: z.string(),
    fileName: z.string(),
    bucket: z.string(),
    objectKey: z.string(),
    processor: z.enum(["unstructured", "openparse"]).default("unstructured"),
    processorConfig: z.object({
      semantic_chunking: z.boolean().optional(),
      embeddings_provider: z.enum(["cloudflare", "ollama", "none"]).optional(),
      minTokens: z.number().optional(),
      maxTokens: z.number().optional(),
      relevanceThreshold: z.number().optional().default(0.3),
      skipDeJunk: z.boolean().optional(),
      unstructured: z.object({
        chunkingStrategy: z.enum(["by_title", "by_similarity", "by_page", "by_character"]).optional(),
        maxCharacters: z.number().optional(),
        similarityThreshold: z.number().optional(),
        includeOriginalElements: z.boolean().optional(),
        multipageSections: z.boolean().optional()
      }).optional()
    }).optional(),
    file: z.any().optional(),
    title: z.string().optional(),
    description: z.string().optional()
  })),
  vectorizeConfig: z.object({
    accountId: z.string(),
    apiToken: z.string(),
    ragName: z.string(),
    ragVectors: z.array(z.string()),
    whitelabelId: z.string(),
    auth0Token: z.string()
  }),
  isBatch: z.boolean().optional()
}).strict();

// Define the routes
app.post("/api/process", zValidator("json", processDocumentSchema), async (c)=>{
  const payload = c.req.valid("json");
  const file = payload.files[0];

  try {
    // Create a unique instance ID with proper formatting for Cloudflare Workflows
    // Workflow instance IDs must be alphanumeric with hyphens/underscores only, max 64 chars
    // Pattern: ^[a-zA-Z0-9_][a-zA-Z0-9-_]*$

    // Use a more compact format to stay within 64 character limit
    const shortUUID = crypto.randomUUID().replace(/-/g, "").substring(0, 12);
    const shortTarget = file.target.replace(/[^a-zA-Z0-9]/g, "").substring(0, 8);
    const shortFileId = file.fileId.replace(/[^a-zA-Z0-9]/g, "").substring(0, 8);
    const instanceId = `wf_${shortTarget}_${shortFileId}_${shortUUID}`;

    console.log(`🔍 [API] Generated workflow instance ID: ${instanceId} (length: ${instanceId.length})`);

    // Keep the original fileId - don't generate fake ObjectIds
    // The workflow will handle file record creation and get the real ObjectId
    let fileId = file.fileId;
    const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(fileId);

    if(!isValidObjectId) {
      console.log(`🔄 [API] FileId is not a valid ObjectId, will use original: ${file.fileId}`);
      // Don't generate fake ObjectIds - use the original and let the workflow handle it
    }

    // Ensure fileId is a valid MongoDB ObjectId format (24 hex chars)
    let fileId = file.fileId;
    const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(fileId);

    // If not valid, generate a valid ObjectId
    if (!isValidObjectId) {
      fileId = crypto.randomUUID().replace(/-/g, "").substring(0, 24);
      console.log(`🔄 [API] Converting invalid ObjectId format to valid one: ${file.fileId} -> ${fileId}`);
    }

    const workflow = await c.env.CHUNKS_VECTORIZED.create({
      id: instanceId, // Add the id field here
      params: {
        files: [{
          fileId: fileId, // Use valid MongoDB ObjectId format
          originalFileId: file.fileId, // Keep original for debugging
          target: file.target,
          fileName: file.fileName,
          bucket: file.bucket,
          objectKey: file.objectKey,
          processor: file.processor || "unstructured",
          processorConfig: file.processorConfig,
          title: file.title,
          description: file.description
        }],
        vectorizeConfig: {
          accountId: payload.vectorizeConfig.accountId,
          apiToken: payload.vectorizeConfig.apiToken,
          ragName: payload.vectorizeConfig.ragName,
          ragVectors: payload.vectorizeConfig.ragVectors,
          whitelabelId: payload.vectorizeConfig.whitelabelId,
          auth0Token: payload.vectorizeConfig.auth0Token
        },
        timestamp: new Date(), // Create a new Date object for the current timestamp
        instanceId
      }
    });

    return c.json({ success: true, workflowId: instanceId });
  }catch(error) {
    console.error("Workflow error:", error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    }, 500);
  }
});

app.post("/api/process/batch", zValidator("json", batchProcessSchema), async (c)=>{
  const payload = c.req.valid("json");

  try {
    // Create a unique instance ID with proper formatting for Cloudflare Workflows
    // Workflow instance IDs must be alphanumeric with hyphens/underscores only, max 64 chars
    const shortUUID = crypto.randomUUID().replace(/-/g, "").substring(0, 12);
    const instanceId = `wf_batch_${shortUUID}`;

    console.log(`🔍 [BATCH] Generated workflow instance ID: ${instanceId} (length: ${instanceId.length})`);

    // Process files - keep original fileIds and let workflow handle file record creation
    const processedFiles = payload.files.map(file=>{
      // Check if fileId is valid MongoDB ObjectId (24 hex chars)
      const isValidObjectId = typeof file.fileId === "string" && /^[0-9a-fA-F]{24}$/.test(file.fileId);

      // Keep the original fileId - don't generate fake ObjectIds
      const validFileId = file.fileId;

      if(!isValidObjectId) {
        console.log(`🔄 [BATCH] FileId is not a valid ObjectId, will use original: ${file.fileId}`);
      }

      return {
        fileId: validFileId, // Use valid MongoDB ObjectId format
        originalFileId: file.fileId, // Keep original for debugging
        target: file.target,
        fileName: file.fileName,
        bucket: file.bucket,
        objectKey: file.objectKey,
        processor: file.processor || "unstructured",
        processorConfig: file.processorConfig,
        title: file.title,
        description: file.description
      };
    });

    // Process files to ensure all have valid MongoDB ObjectId format
    const processedFiles = payload.files.map(file => {
      // Check if fileId is valid MongoDB ObjectId (24 hex chars)
      const isValidObjectId = typeof file.fileId === 'string' && /^[0-9a-fA-F]{24}$/.test(file.fileId);

      // Generate a valid ObjectId if needed
      const validFileId = isValidObjectId
        ? file.fileId
        : crypto.randomUUID().replace(/-/g, "").substring(0, 24);

      if (!isValidObjectId) {
        console.log(`🔄 [BATCH] Converting invalid ObjectId format to valid one: ${file.fileId} -> ${validFileId}`);
      }

      return {
        fileId: validFileId, // Use valid MongoDB ObjectId format
        originalFileId: file.fileId, // Keep original for debugging
        target: file.target,
        fileName: file.fileName,
        bucket: file.bucket,
        objectKey: file.objectKey,
        processor: file.processor || "unstructured",
        processorConfig: file.processorConfig,
        title: file.title,
        description: file.description
      };
    });

    const workflow = await c.env.CHUNKS_VECTORIZED.create({
      id: instanceId, // Add the id field here
      params: {
        files: processedFiles,
        vectorizeConfig: {
          accountId: payload.vectorizeConfig.accountId,
          apiToken: payload.vectorizeConfig.apiToken,
          ragName: payload.vectorizeConfig.ragName,
          ragVectors: payload.vectorizeConfig.ragVectors,
          whitelabelId: payload.vectorizeConfig.whitelabelId,
          auth0Token: payload.vectorizeConfig.auth0Token
        },
        timestamp: new Date(), // Create a new Date object for the current timestamp
        instanceId
      }
    });

    return c.json({
      success: true,
      workflowId: instanceId
    });
  }catch(error) {
    console.error("Batch workflow error:", error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    }, 500);
  }
});

app.post("/api/process/stream", async (c)=>{
  // Get metadata from headers
  const fileName = c.req.header("x-file-name");
  const fileId = c.req.header("x-file-id");
  const target = c.req.header("x-target");
  const processor = c.req.header("x-processor");
  const processorConfigStr = c.req.header("x-processor-config");
  const vectorizeConfigStr = c.req.header("x-vectorize-config");
  const authHeader = c.req.header("authorization");
  const origin = c.req.header("origin");

  // Add debug logging
  console.log("📝 Processing stream request:", {
    fileName,
    fileId,
    target,
    processor: processor || "default",
    processorConfig: processorConfigStr ? JSON.parse(processorConfigStr) : null,
    hasAuth: !!authHeader,
    origin
  });

  // Log CORS information
  console.log("🔍 CORS information:", {
    origin,
    allowedOrigins: c.env.ALLOWED_ORIGINS.split(","),
    isAllowed: !origin || c.env.ALLOWED_ORIGINS.split(",").includes(origin)
  });

  // Parse configs with error handling
  let processorConfig;
  try {
    processorConfig = processorConfigStr ? JSON.parse(processorConfigStr) : {};
    console.log("Parsed processorConfig:", processorConfig);  // Debug log
  }catch(error) {
    console.error("Failed to parse processor config:", error);
    return c.json({ error: "Invalid processor config" }, 400);
  }

  // Create a cleaner, more predictable instance ID
  // Ensure fileId exists with a fallback, and convert to 24-character hex for MongoDB ObjectId
  const cleanFileId = fileId ? fileId.replace(/[^a-zA-Z0-9-]/g, "") : "unknown";

  // Check if cleanFileId is already a valid MongoDB ObjectId (24 hex chars)
  const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(cleanFileId);

  // Keep the original fileId - don't generate fake ObjectIds
  const finalFileId = cleanFileId;

  console.log(`🔍 [STREAM] Original fileId: ${fileId}, cleaned: ${cleanFileId}, final: ${finalFileId}`);

  // Create a unique instance ID with proper formatting for Cloudflare Workflows
  // Workflow instance IDs must be alphanumeric with hyphens/underscores only, max 64 chars
  const shortTarget = (target || "unknown").replace(/[^a-zA-Z0-9]/g, "").substring(0, 8);
  const shortUUID = crypto.randomUUID().replace(/-/g, "").substring(0, 12);
  const instanceId = `wf_${shortTarget}_${shortUUID}`;

  console.log("🔍 Creating workflow instance:", {
    instanceId,
    instanceIdLength: instanceId.length,
    originalFileId: fileId,
    cleanFileId,
    finalFileId,
    target,
    processor: processor || "default"
  });

  // Report which headers are missing
  const missingHeaders = [];
  if(!fileName) missingHeaders.push("file-name");
  if(!fileId) missingHeaders.push("file-id");
  if(!target) missingHeaders.push("target");
  if(!processor) missingHeaders.push("processor");
  if(!vectorizeConfigStr) missingHeaders.push("vectorize-config");

  if(missingHeaders.length > 0) {
    return c.json({
      error: "Missing required headers",
      missingHeaders,
      providedHeaders: {
        fileName: !!fileName,
        fileId: !!fileId,
        target: !!target,
        processor: !!processor,
        vectorizeConfig: !!vectorizeConfigStr
      }
    }, 400);
  }

  try {
    let fileData;

    // Check if the content type is multipart/form-data
    const contentType = c.req.header("content-type") || "";
    if(contentType.includes("multipart/form-data")) {
      // Handle FormData
      const formData = await c.req.formData();
      const file = formData.get("file");

      if(!file || !(file instanceof File)) {
        return c.json({ error: "Missing file in form data" }, 400);
      }

      fileData = await file.arrayBuffer();
    } else {
      // Handle JSON body (original implementation)
      const [bodyStream1, bodyStream2] = c.req.raw.body?.tee() || [];

      // Parse the JSON metadata from the first stream
      const bodyText = await new Response(bodyStream1).text();
      let bodyData;
      try {
        bodyData = JSON.parse(bodyText);
        console.log("🔍 API - Body data:", {
          processorConfig: bodyData?.processorConfig,
          size: bodyText.length,
          keys: Object.keys(bodyData || {}),
          fileData: bodyData?.fileData ? {
            type: bodyData.fileData.type,
            size: bodyData.fileData.size,
            base64Length: bodyData.fileData.base64?.length || 0
          } : undefined
        });

        // Get file data from base64
        if(bodyData?.fileData?.base64) {
          const base64Data = bodyData.fileData.base64;
          const binaryData = Buffer.from(base64Data, "base64");
          fileData = binaryData.buffer;
        } else {
          // Get the file data from the second stream
          fileData = await new Response(bodyStream2).arrayBuffer();
        }

        processorConfig = bodyData?.processorConfig;
      }catch(e) {
        console.log("🔍 Not a JSON body, treating as raw file");
        fileData = await new Response(bodyStream2).arrayBuffer();
      }
    }

    const vectorizeConfig = JSON.parse(vectorizeConfigStr);
    const objectKey = `${target}/${fileId}/${Date.now()}-${fileName}`;

    // Log only essential information
    console.log("🚀 Creating workflow: ", {
      instanceId,
      fileName,
      processor,
      objectKey
    });

    // Create a storage client using our DNS-based approach
    const storageClient = createStorageClient(c.env);

    // Upload to storage (R2 or MinIO depending on environment)
    console.log(`🔄 Uploading file to storage: ${objectKey}`);
    await storageClient.put(objectKey, fileData, {
      httpMetadata: {
        contentType: c.req.header("content-type") || "application/octet-stream",
      },
      customMetadata: {
        fileName,
        uploadedAt: new Date().toISOString()
      }
    });
    console.log(`🔄 File uploaded to storage: ${objectKey}`);

    // Create workflow with validated instance ID - IMPORTANT: Don"t pass fileData to workflow
    const workflow = await c.env.CHUNKS_VECTORIZED.create({
      id: instanceId,
      params: {
        files: [{
          fileId: finalFileId, // Use 24-character hex format for MongoDB ObjectId
          originalFileId: fileId, // Keep the original file ID for debugging
          target,
          fileName,
          bucket: "default",
          objectKey,
          processor: (processor === "unstructured" || processor === "openparse")
            ? processor
            : "unstructured", // Default to unstructured if invalid value
          processorConfig: processorConfig ? {
            semantic_chunking: processorConfig.semantic_chunking,
            embeddings_provider: processorConfig.embeddings_provider,
            minTokens: processorConfig.minTokens,
            maxTokens: processorConfig.maxTokens,
            relevanceThreshold: processorConfig.relevanceThreshold,
            skipDeJunk: processorConfig.skipDeJunk,
          } : undefined,
          // Don't include fileData here to avoid RPC size limit
        }],
        vectorizeConfig,
        timestamp: new Date(),
        instanceId
      }
    });

    return c.json({
      id: workflow.id,
      success: true,
      objectKey
    });

  }catch(error) {
    console.error("Stream processing error:", {
      error: error.message,
      instanceId,
      originalFileId: fileId,
      fileId: finalFileId,
      target
    });
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    }, 500);
  }
});

// Endpoint to receive logs from tail worker
app.post("/api/logs/chunks-workflow", async (c)=>{
  try {
    const events = await c.req.json();

    // Process the workflow events
    for(const event of events) {
      const {
        scriptName,
        outcome,
        eventTimestamp,
        logs,
        exceptions
      } = event;

      // Option 1: Store in dedicated LOGS_DB
      await c.env.LOGS_DB.prepare(`
        INSERT INTO workflow_logs (
          script_name,
          outcome,
          timestamp,
          logs,
          exceptions
        ) VALUES (?, ?, ?, ?, ?)
      `).bind(
        scriptName,
        outcome,
        new Date(eventTimestamp).toISOString(),
        JSON.stringify(logs),
        JSON.stringify(exceptions)
      ).run();
    }

    return c.json({ success: true });
  }catch(error) {
    console.error("Failed to process workflow logs:", error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    }, 500);
  }
});

app.post("/api/video-to-audio", async (c)=>{
  try {
    const payload = await c.req.json();
    const { videoFile, instanceId, whitelabelId, auth0Token } = payload;

    if(!videoFile || !instanceId || !whitelabelId || !auth0Token) {
      return c.json({
        success: false,
        error: "Missing required fields in payload"
      }, 400);
    }

    console.log("🎬 Starting video-to-audio workflow:", {
      instanceId,
      fileId: videoFile.fileId,
      fileName: videoFile.fileName
    });

    // Create the workflow with the provided instance ID
    const workflow = await c.env.VIDEO_TO_AUDIO.create({
      id: instanceId,
      params: {
        videoFile,
        instanceId,
        whitelabelId,
        auth0Token
      }
    });

    return c.json({
      success: true,
      workflowId: instanceId
    });
  }catch(error) {
    console.error("Video-to-audio workflow error:", error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    }, 500);
  }
});

export { ChunksVectorizedWorkflow, VideoToAudioWorkflow };

export default app;

console.log("🚀 Chunks workflow worker up!");
