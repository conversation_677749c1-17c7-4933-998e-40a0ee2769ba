import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getWhitelabelTarget } from "../data-source/audio-transcript/util/whitelabel";
import { HTTP_ERRORS_WITH_CONTEXT, jsonBody } from "@divinci-ai/server-utils";
import { castShallowObject } from "@divinci-ai/utils";
import { DataSourceAudioTranscriptModel } from "@divinci-ai/server-models";
import { createVoiceprint as createVoiceprintAPI } from "@divinci-ai/server-tools";

/**
 * Get webhook URL based on environment
 */
function getWebhookUrl(audioDocId: string): string | undefined {
  // Check current environment
  const environment = process.env.ENVIRONMENT || process.env.NODE_ENV || 'local';
  const isLocalMode = environment === 'local' || environment === 'development';

  // Get webhook base URL from environment
  let webhookBaseUrl = process.env.WEBHOOK_BASE_URL;

  // If no explicit webhook URL is set, determine based on environment
  if (!webhookBaseUrl) {
    if (isLocalMode) {
      // Local development - use webhook.site for testing (temporary)
      // TODO: Replace with your actual webhook URL for testing
      const testWebhookUrl = process.env.TEST_WEBHOOK_URL || 'https://webhook.site/your-unique-id';
      console.log(`🔄 [WEBHOOK] Local development mode detected (${environment}) - using test webhook: ${testWebhookUrl}`);
      webhookBaseUrl = testWebhookUrl;
    } else if (environment === 'develop' || environment === 'development') {
      // Development environment - use development API host
      const apiHost = process.env.API_HOST || 'api.dev.divinci.app';
      const isSecure = process.env.API_IS_SECURE === '1';
      const protocol = isSecure ? 'https' : 'http';
      webhookBaseUrl = `${protocol}://${apiHost}`;
      console.log(`🔄 [WEBHOOK] Development environment detected - using: ${webhookBaseUrl}`);
    } else if (environment === 'staging') {
      // Staging environment
      const apiHost = process.env.API_HOST || 'api.staging.divinci.app';
      webhookBaseUrl = `https://${apiHost}`;
      console.log(`🔄 [WEBHOOK] Staging environment detected - using: ${webhookBaseUrl}`);
    } else {
      // Production environment
      const apiHost = process.env.API_HOST || 'api.divinci.app';
      webhookBaseUrl = `https://${apiHost}`;
      console.log(`🔄 [WEBHOOK] Production environment detected - using: ${webhookBaseUrl}`);
    }
  } else {
    console.log(`🔄 [WEBHOOK] Using explicit WEBHOOK_BASE_URL: ${webhookBaseUrl}`);
  }

  // Check if webhook URL is localhost (should be disabled)
  if (webhookBaseUrl && (webhookBaseUrl.includes('localhost') || webhookBaseUrl.includes('127.0.0.1'))) {
    console.log(`🔄 [WEBHOOK] Disabling webhook for localhost URL: ${webhookBaseUrl}`);
    return undefined;
  }

  // Return full webhook URL
  return webhookBaseUrl ? `${webhookBaseUrl}/pyannote/voiceprint/${audioDocId}` : undefined;
}

/**
 * Create Voiceprint Endpoint
 *
 * Handles creating voiceprints from either:
 * 1. Audio transcript segments (existing workflow)
 * 2. Direct audio file URLs (new workflow)
 */

export const createVoiceprint: RequestHandler = async function(req, res, next) {
  try {
    console.log("🎤 [VOICEPRINT-CREATE] Starting voiceprint creation...");

    const { target, whitelabel } = await getWhitelabelTarget(req);

    // Check if voiceprints are supported by checking the diarizer tools used
    const diarizerCheck = await DataSourceAudioTranscriptModel.findOne({
      target,
      "tools.diarizer": { $exists: true }
    }).select("tools.diarizer");

    const isDivinciPyannote = diarizerCheck?.tools?.diarizer === '@divinci-ai/pyannote-segmentation';

    if (isDivinciPyannote) {
      console.log("🚫 [VOICEPRINT-CREATE] Voiceprints not supported with Divinci Pyannote");
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
        "Voiceprints are not available when using Divinci Pyannote. Please use Official Pyannote API for voiceprint features."
      );
    }

    const body = await jsonBody(req);

    // Cast and validate the request body
    const config = castShallowObject(body, {
      // Option 1: From audio transcript segment
      audioTranscriptId: "string?",
      segmentIndex: "number?",
      startTime: "number?",
      endTime: "number?",

      // Option 2: From direct audio file URL
      audioFileUrl: "string?",

      // Common fields
      speakerLabel: "string?",
      name: "string?",
    });

    console.log("📋 [VOICEPRINT-CREATE] Request config:", config);

    // Validate that we have either transcript info or direct URL
    const hasTranscriptInfo = config.audioTranscriptId &&
      (config.segmentIndex !== undefined || (config.startTime !== undefined && config.endTime !== undefined));
    const hasDirectUrl = config.audioFileUrl;

    if (!hasTranscriptInfo && !hasDirectUrl) {
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
        "Must provide either audioTranscriptId with segment info OR audioFileUrl"
      );
    }

    if (hasTranscriptInfo && hasDirectUrl) {
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
        "Cannot provide both audioTranscriptId and audioFileUrl - choose one method"
      );
    }

    let audioUrl: string;
    let audioDoc: any = null;

    if (hasDirectUrl) {
      // Method 1: Direct audio file URL
      console.log("🔗 [VOICEPRINT-CREATE] Using direct audio URL:", config.audioFileUrl);

      // Check if the URL is a video file that needs audio extraction
      const originalUrl = config.audioFileUrl!;
      const isVideoFile = originalUrl.includes('.mp4') || originalUrl.includes('.mov') ||
                         originalUrl.includes('.avi') || originalUrl.includes('.mkv') ||
                         originalUrl.includes('.webm');

      if (isVideoFile) {
        console.log("🎬 [VOICEPRINT-CREATE] Video file detected - this should have been processed during upload");
        console.error("❌ [VOICEPRINT-CREATE] Received video URL instead of processed audio URL");
        console.error("🔍 [VOICEPRINT-CREATE] Video URL:", originalUrl);

        throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
          "Video files must be processed during upload. The upload endpoint should extract audio and return an audio URL. " +
          "Please ensure the frontend uses the processed audio URL returned by the upload endpoint."
        );
      } else {
        // Direct audio file - use the signed URL as-is (Cloudflare R2 URLs may require double slash before the key)
        audioUrl = originalUrl;

        console.log("🎵 [VOICEPRINT-CREATE] Using signed URL for private storage access");
      }

    } else {
      // Method 2: Extract from audio transcript
      console.log("📄 [VOICEPRINT-CREATE] Extracting from audio transcript:", config.audioTranscriptId);

      // Validate ObjectId format before querying
      const mongoose = require('mongoose');
      if (!mongoose.Types.ObjectId.isValid(config.audioTranscriptId)) {
        throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
          `Invalid audioTranscriptId format. Expected MongoDB ObjectId (24 hex characters), got: ${config.audioTranscriptId}`
        );
      }

      // Find the audio document
      audioDoc = await DataSourceAudioTranscriptModel.findById(config.audioTranscriptId);
      if (!audioDoc) {
        throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("Audio transcript not found");
      }

      // Get the public audio URL
      if (!audioDoc.publicAudioUrl) {
        throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Audio transcript does not have a public URL");
      }

      // For segment-based extraction, we'll use the full audio URL
      // The Pyannote API doesn't support time-based extraction, so we'll need to
      // implement audio slicing if needed in the future
      audioUrl = audioDoc.publicAudioUrl;

      console.log("🎵 [VOICEPRINT-CREATE] Using audio URL from transcript:", audioUrl);
    }

    // For direct uploads, create tracking document BEFORE webhook URL construction
    // This ensures we always have a valid ObjectId for the webhook
    if (!audioDoc && hasDirectUrl) {
      console.log("📝 [VOICEPRINT-CREATE] Creating tracking document for direct upload BEFORE webhook construction");

      // Extract filename from URL for tracking
      const urlParts = audioUrl.split('/');
      const filename = urlParts[urlParts.length - 1].split('?')[0]; // Remove query params

      audioDoc = new DataSourceAudioTranscriptModel({
        target: target,
        userInfo: {
          title: `Voiceprint: ${config.name || config.speakerLabel || 'Unknown Speaker'}`,
          description: `Direct upload voiceprint for ${config.speakerLabel || 'unknown speaker'}`
        },
        sourceOrigin: {
          sourceType: 'file',
          mediaType: 'audio',
          sourceId: 'temp-placeholder', // Will be updated with actual jobId later
          info: {
            originalUrl: config.audioFileUrl,
            method: 'direct-upload'
          }
        },
        tools: {
          diarizer: '@pyannote/pyannote-segmentation',
          transcriber: '@cf/openai/whisper-large-v3-turbo' // Required field, but not used for voiceprint-only
        },
        audio: {
          Bucket: 'private-temporary-uploads',
          Key: filename,
          publicUrl: audioUrl,
          filename: filename
        },
        speakerNames: {},
        processStatus: 'diarization',
        ignoredSamples: [],
        failedSamples: [],
        samples: [],
        voiceprints: [] // Will be populated with the job
      });

      // Save the tracking document to get a valid _id
      await audioDoc.save();
      console.log("💾 [VOICEPRINT-CREATE] Created tracking document with ID:", audioDoc._id);
    }

    // Create webhook URL for status updates based on environment
    const webhookUrl = getWebhookUrl(audioDoc!._id);

    if (webhookUrl) {
      console.log("🔗 [VOICEPRINT-CREATE] Using webhook URL:", webhookUrl);
    } else {
      console.log("🔄 [VOICEPRINT-CREATE] Webhook disabled for local development (Pyannote API doesn't accept localhost URLs)");
    }

    // Call Pyannote API to create voiceprint
    console.log("📤 [VOICEPRINT-CREATE] Calling Pyannote API...");

    // Build config object - only include webhook if it's defined
    const voiceprintConfig: any = {
      metadata: {
        speakerLabel: config.speakerLabel,
        name: config.name || `Voiceprint created ${new Date().toISOString()}`,
      }
    };

    // Only add webhook if it's defined (don't send undefined)
    if (webhookUrl) {
      voiceprintConfig.webhook = webhookUrl;
      console.log("🔗 [VOICEPRINT-CREATE] Including webhook in request:", webhookUrl);
    } else {
      console.log("🔄 [VOICEPRINT-CREATE] No webhook included (local development)");
    }

    const voiceprintResult = await createVoiceprintAPI({
      audioUrl,
      config: voiceprintConfig
    });

    console.log("✅ [VOICEPRINT-CREATE] Pyannote API response:", voiceprintResult);

    // Store voiceprint job information
    const voiceprintJob = {
      jobId: voiceprintResult.jobId,
      status: voiceprintResult.status || 'created',
      voiceprintData: undefined, // Will be populated when job completes
      createdFrom: hasDirectUrl ? {
        speaker: config.speakerLabel || 'Unknown Speaker',
        start: 0, // Direct uploads don't have specific time segments
        end: 0,   // Direct uploads don't have specific time segments
        audioUrl: audioUrl,
        // Additional metadata for tracking
        method: 'direct-upload',
        originalUrl: config.audioFileUrl
      } : {
        speaker: config.speakerLabel || 'Unknown Speaker',
        start: config.startTime || 0,
        end: config.endTime || 0,
        audioUrl: audioUrl,
        // Additional metadata for tracking
        method: 'transcript-segment',
        audioTranscriptId: config.audioTranscriptId,
        segmentIndex: config.segmentIndex
      },
      timestamp: Date.now(),
      name: config.name || `Voiceprint created ${new Date().toISOString()}`,
      speakerLabel: config.speakerLabel,
    };

    // Update the tracking document with the actual job ID
    if (hasDirectUrl) {
      // Update the sourceId with the actual job ID (was placeholder before)
      audioDoc.sourceOrigin.sourceId = voiceprintResult.jobId;
    }

    // Add voiceprint job to audio document (always exists now)
    audioDoc.voiceprints = audioDoc.voiceprints || [];
    audioDoc.voiceprints.push(voiceprintJob);
    await audioDoc.save();
    console.log("💾 [VOICEPRINT-CREATE] Added voiceprint job to audio document");

    // Return the job information
    res.status(202).json({
      success: true,
      jobId: voiceprintResult.jobId,
      status: voiceprintResult.status,
      message: "Voiceprint creation job started",
      voiceprint: voiceprintJob
    });

  } catch (error) {
    console.error("❌ [VOICEPRINT-CREATE] Creation failed:", error);
    next(error);
  }
};
