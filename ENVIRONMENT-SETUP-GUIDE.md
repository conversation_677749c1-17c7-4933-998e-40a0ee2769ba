# Environment Setup Guide for E2E Cloud Testing

This guide helps you configure the necessary credentials and environment variables for E2E testing against cloud environments.

## 🔑 Required Credentials

You'll need to obtain and configure the following credentials in your `private-keys/[environment]/test.env` files:

### 1. Auth0 Configuration

#### Where to Find Auth0 Credentials:
1. Go to [Auth0 Dashboard](https://manage.auth0.com/)
2. Navigate to **Applications** > Your Application
3. Copy the following values:

```bash
# Replace these placeholders in your test.env files:
AUTH0_BASE_URL=https://your-tenant.us.auth0.com  # Your Auth0 domain
AUTH0_CLIENT_ID=your_client_id_here              # Application Client ID
AUTH0_AUDIENCE=https://api.dev.divinci.app       # API Identifier
AUTH0_S2S_CLIENT_ID=your_s2s_client_id_here     # Machine-to-Machine Client ID
AUTH0_S2S_CLIENT_SECRET=your_s2s_secret_here     # Machine-to-Machine Secret
```

#### Test User Credentials:
Create test users in Auth0 or use existing ones:

```bash
AUTH0_TEST_USER_EMAIL=<EMAIL>           # Regular test user
AUTH0_TEST_USER_PASSWORD=your_secure_password    # Test user password
AUTH0_ADMIN_USER_EMAIL=<EMAIL>         # Admin test user
AUTH0_ADMIN_USER_PASSWORD=your_admin_password    # Admin password
```

### 2. Cloudflare Access Configuration

#### Where to Find Cloudflare Access Credentials:
1. Go to [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. Navigate to **Access** > **Service Auth** > **Service Tokens**
3. Create a new service token or use existing one:

```bash
# Replace these placeholders in your test.env files:
CF_ACCESS_CLIENT_ID=your_cf_client_id_here       # Service Token Client ID
CF_ACCESS_CLIENT_SECRET=your_cf_secret_here      # Service Token Secret
```

#### Creating a Cloudflare Service Token:
1. Click **"Create Service Token"**
2. Name: `E2E Testing Token`
3. Copy the **Client ID** and **Client Secret**
4. Create an Access Policy that allows this token to access your API endpoints

### 3. Environment-Specific URLs

Verify these URLs match your actual deployments:

#### Development Environment:
```bash
API_URL=https://api.dev.divinci.app
WEB_URL=https://chat.dev.divinci.app
```

#### Staging Environment:
```bash
API_URL=https://api.stage.divinci.app
WEB_URL=https://chat.stage.divinci.app
```

#### Production Environment:
```bash
API_URL=https://api.divinci.app
WEB_URL=https://chat.divinci.app
```

## 🛠️ Configuration Steps

### Step 1: Update Development Environment
```bash
# Edit the develop test environment file
nano private-keys/develop/test.env

# Replace all placeholders with actual values:
# - AUTH0_CLIENT_ID=your_actual_develop_client_id
# - CF_ACCESS_CLIENT_ID=your_actual_develop_cf_client_id
# - AUTH0_TEST_USER_EMAIL=your_actual_test_user_email
# etc.
```

### Step 2: Update Staging Environment
```bash
# Edit the staging test environment file
nano private-keys/staging/test.env

# Replace all placeholders with actual staging values
```

### Step 3: Update Production Environment (if needed)
```bash
# Edit the production test environment file
nano private-keys/production/test.env

# Add production-specific credentials (use with caution!)
```

## 🧪 Testing Your Configuration

### Test 1: Verify Environment Variables Load
```bash
# Source the environment file and check variables
source private-keys/develop/test.env
echo "Auth0 Client ID: $AUTH0_CLIENT_ID"
echo "CF Access Client ID: $CF_ACCESS_CLIENT_ID"
echo "API URL: $API_URL"
```

### Test 2: Test Cloudflare Access
```bash
# Test CF Access manually
source private-keys/develop/test.env
curl -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" \
     -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" \
     "$API_URL/health"
```

### Test 3: Test Auth0 Connection
```bash
# Test Auth0 token endpoint
source private-keys/develop/test.env
curl -X POST "$AUTH0_BASE_URL/oauth/token" \
  -H "Content-Type: application/json" \
  -d '{
    "client_id":"'$AUTH0_S2S_CLIENT_ID'",
    "client_secret":"'$AUTH0_S2S_CLIENT_SECRET'",
    "audience":"'$AUTH0_AUDIENCE'",
    "grant_type":"client_credentials"
  }'
```

## 🔒 Security Best Practices

### 1. Credential Management
- **Never commit real credentials** to version control
- Use **environment-specific credentials** (don't reuse across environments)
- **Rotate credentials regularly**, especially for production
- Use **least-privilege access** for service tokens

### 2. Test User Management
- Create **dedicated test users** that don't interfere with real users
- Use **non-production email addresses** for test accounts
- **Clean up test data** after test runs (especially in staging)

### 3. Access Control
- **Limit Cloudflare service token access** to only necessary endpoints
- **Monitor service token usage** in Cloudflare dashboard
- **Set up alerts** for unusual access patterns

## 🚨 Troubleshooting

### Common Issues:

#### 1. "CF Access 403 Forbidden"
```bash
# Check if your service token has proper access policies
# Verify the token hasn't expired
# Ensure the token is configured for the correct domain
```

#### 2. "Auth0 Invalid Client"
```bash
# Verify AUTH0_CLIENT_ID is correct
# Check that the application is enabled in Auth0
# Ensure the audience matches your API identifier
```

#### 3. "Service Not Found"
```bash
# Verify the URLs are correct for your environment
# Check that the services are actually deployed
# Ensure DNS is properly configured
```

### Debug Commands:
```bash
# Check service availability
curl -I https://api.dev.divinci.app/health

# Test DNS resolution
nslookup api.dev.divinci.app

# Check Cloudflare settings
curl -I https://chat.dev.divinci.app
```

## 📋 Checklist

Before running E2E tests, ensure:

- [ ] Auth0 credentials are configured for each environment
- [ ] Cloudflare Access service tokens are created and configured
- [ ] Test users exist and have proper permissions
- [ ] Environment URLs are correct and accessible
- [ ] Service tokens have access policies configured
- [ ] Test environment files are properly formatted
- [ ] No placeholder values remain in configuration files

## 🎯 Next Steps

Once configuration is complete:

1. **Test connectivity**: `./scripts/wait-for-services.sh develop`
2. **Run health checks**: Execute the cloud health check test
3. **Validate full workflow**: Use act to simulate deployment with E2E testing

Remember to keep your credentials secure and never commit them to version control!
