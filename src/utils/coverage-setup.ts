/**
 * Coverage Setup
 * 
 * This file sets up code coverage for API and E2E tests.
 * It uses Istanbul to track code coverage and generate reports.
 */

import { writeFileSync } from 'fs';
import { join } from 'path';
import { createCoverageMap } from 'istanbul-lib-coverage';

// Global variable to store coverage data
declare global {
  var __coverage__: any;
}

/**
 * Initialize coverage tracking
 */
export function initCoverage(): void {
  global.__coverage__ = global.__coverage__ || {};
  console.log('🔍 Coverage tracking initialized');
}

/**
 * Write coverage data to a file
 */
export function writeCoverage(): void {
  if (global.__coverage__) {
    const coverageMap = createCoverageMap(global.__coverage__);
    const coverageDir = join(process.cwd(), '.nyc_output');
    const coverageFile = join(coverageDir, 'coverage.json');
    
    try {
      writeFileSync(coverageFile, JSON.stringify(coverageMap));
      console.log(`✅ Coverage data written to ${coverageFile}`);
    } catch (error) {
      console.error('❌ Error writing coverage data:', error);
    }
  } else {
    console.warn('⚠️ No coverage data available');
  }
}

/**
 * Merge coverage data from multiple sources
 * @param coverageData Array of coverage data objects
 */
export function mergeCoverage(coverageData: any[]): void {
  const coverageMap = createCoverageMap({});
  
  for (const coverage of coverageData) {
    if (coverage) {
      coverageMap.merge(coverage);
    }
  }
  
  global.__coverage__ = coverageMap.toJSON();
}
