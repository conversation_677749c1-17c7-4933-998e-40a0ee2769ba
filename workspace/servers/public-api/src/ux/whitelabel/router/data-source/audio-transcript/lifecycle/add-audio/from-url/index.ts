import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getWhitelabelTarget } from "../../../util/whitelabel";
import { HTTP_ERRORS_WITH_CONTEXT, jsonBody } from "@divinci-ai/server-utils";

import { castShallowObject } from "@divinci-ai/utils";

import { handleDirectUrl } from "./url-to-s3";
import { ensureValidMediaName } from "../resuable/validate-media-name";
import { transcribeMedia } from "../resuable/transcribe-media";
import { processDropboxFolder, isDropboxFolderUrl } from "./dropbox-folder-processor";

/**
 * Extract filename from URL, handling Dropbox URLs specially
 */
function extractFilenameFromUrl(url: URL): string {
  // Check if it's a Dropbox URL
  if (url.hostname === 'www.dropbox.com' || url.hostname === 'dropbox.com') {
    const pathParts = url.pathname.split('/');

    // For Dropbox file URLs like /scl/fi/xyz/filename.mp4 or /s/abc123/filename.mp3
    // Find the last part that looks like a filename (contains a dot)
    for (let i = pathParts.length - 1; i >= 0; i--) {
      const part = pathParts[i];
      if (part && part.includes('.')) {
        return part;
      }
    }

    // If no filename found, it might be a folder URL or malformed URL
    // For folder URLs, we'll let the download attempt proceed and fail gracefully
    // Generate a generic filename based on the URL structure
    if (url.pathname.includes('/scl/fo/')) {
      // This is a Dropbox folder URL - we'll handle it specially
      return "dropbox-folder.zip"; // Placeholder filename for folder processing
    }

    // If we can't extract a filename, throw an error
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Could not extract filename from Dropbox URL. Please ensure the URL points to a specific file with a valid extension.");
  }

  // For non-Dropbox URLs, use the pathname directly
  return url.pathname;
}

/**
 * Process a Dropbox folder URL by extracting all audio/video files and creating transcription jobs for each
 */
async function processDropboxFolderBatch(
  req: any, res: any, next: any,
  whitelabel: any, target: any,
  url: URL,
  diarizerTool: string,
  transcriberTool: string,
  requestStartTime: number
) {
  try {
    console.log(`📁 Starting Dropbox folder processing...`);

    // Process the Dropbox folder and extract audio/video files
    const folderResult = await processDropboxFolder(whitelabel._id.toString(), url);

    console.log(`✅ Extracted ${folderResult.extractedFiles.length} files from Dropbox folder`);

    if (folderResult.extractedFiles.length === 0) {
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("No audio or video files found in the Dropbox folder");
    }

    // Create transcription jobs for each extracted file
    const transcriptionJobs = [];

    for (const file of folderResult.extractedFiles) {
      console.log(`🎵 Creating transcription job for: ${file.filename}`);

      const { doc, work } = await transcribeMedia(
        target, whitelabel._id.toString(),
        { diarizer: diarizerTool, transcriber: transcriberTool },
        { Bucket: file.Bucket, Key: file.Key },
        {
          filename: file.filename,
          sourceType: "url",
          rawValue: url.href,
        },
        { Bucket: file.Bucket, Key: file.Key }
      );

      // Handle the background work promise to prevent unhandled rejections
      work.catch((error: any) => {
        console.error("❌ Background transcription work failed for batch file:", file.filename, error);
        // The error is logged but doesn't affect the HTTP response
      });

      transcriptionJobs.push({
        id: doc._id,
        filename: file.filename,
        status: doc.processStatus,
        statusUrl: `/white-label/${whitelabel._id}/data-source/audio-transcript/${doc._id}/status`
      });
    }

    const totalDuration = Date.now() - requestStartTime;
    console.log(`🎉 Created ${transcriptionJobs.length} transcription jobs in ${totalDuration}ms`);

    // Return batch processing results
    res.statusCode = 202; // Accepted
    res.json({
      message: `Dropbox folder processed successfully. Created ${transcriptionJobs.length} transcription jobs.`,
      totalFiles: folderResult.totalFiles,
      processedFiles: folderResult.extractedFiles.length,
      skippedFiles: folderResult.skippedFiles,
      jobs: transcriptionJobs
    });

    console.log(`📤 Batch response sent with ${transcriptionJobs.length} transcription jobs`);

  } catch (error) {
    const errorDuration = Date.now() - requestStartTime;
    console.log(`❌ Dropbox folder processing failed after ${errorDuration}ms`);
    console.log(`❌ Error details:`, (error as any)?.message || error);
    next(error);
  }
}
export const createAudioTranscriptFromURL: RequestHandler = async function(req, res, next){
  const requestStartTime = Date.now();
  console.log(`🎬 Audio transcript from URL request started at: ${new Date(requestStartTime).toISOString()}`);
  console.log(`🔍 Request method: ${req.method}, URL: ${req.url}`);
  console.log(`🔍 Request headers:`, JSON.stringify(req.headers, null, 2));
  console.log(`🔍💛 Request body:`, JSON.stringify(req.body, null, 2));

  try {
    console.log(`📥 Parsing request body and getting whitelabel target...`);

    console.log(`🔍 Getting whitelabel target...`);
    const whitelabelResult = await getWhitelabelTarget(req);
    console.log(`✅ Whitelabel target retrieved: ${whitelabelResult.whitelabel._id}`);

    console.log(`🔍 Parsing JSON body...`);
    let body;
    try {
      console.log(`🔍 About to call jsonBody function...`);
      body = await jsonBody(req);
      console.log(`✅ JSON body parsed successfully:`, JSON.stringify(body, null, 2));
    } catch (error) {
      console.error(`❌ JSON body parsing failed:`, error);
      throw error;
    }

    const { target, whitelabel } = whitelabelResult;

    const { url: urlRaw, diarizerTool, transcriberTool } = castShallowObject(
      body, {
        url: "string", diarizerTool: "string", transcriberTool: "string"
      }
    );

    console.log(`🔗 Processing URL: ${urlRaw}`);
    console.log(`🔧 Tools - Diarizer: ${diarizerTool}, Transcriber: ${transcriberTool}`);

    if(!URL.canParse(urlRaw)){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Invalid url");
    }
    const url = new URL(urlRaw);

    console.log(`✅ URL parsed successfully: ${url.href}`);

    // Check if this is a Dropbox folder URL and handle it specially
    if (isDropboxFolderUrl(url)) {
      console.log(`📁 Detected Dropbox folder URL, processing as batch...`);
      return await processDropboxFolderBatch(req, res, next, whitelabel, target, url, diarizerTool, transcriberTool, requestStartTime);
    }

    // Handle Dropbox URLs specially
    const filename = extractFilenameFromUrl(url);
    console.log(`📄 Extracted filename: ${filename}`);

    ensureValidMediaName(
      filename, HTTP_ERRORS_WITH_CONTEXT.BAD_FORM
    );

    console.log(`✅ Media name validation passed`);
    console.log(`📥 Starting URL download and upload to R2...`);

    const urlProcessStartTime = Date.now();
    const audio = await handleDirectUrl(whitelabel._id.toString(), url);
    const urlProcessDuration = Date.now() - urlProcessStartTime;

    console.log(`✅ URL processing completed in ${urlProcessDuration}ms (${(urlProcessDuration / 1000).toFixed(2)}s)`);
    console.log(`📁 File uploaded to: ${audio.r2Pointer.Bucket}/${audio.r2Pointer.Key}`);

    console.log(`🎵 Starting transcription process...`);
    const transcribeStartTime = Date.now();

    const { doc, work } = await transcribeMedia(
      target, whitelabel._id.toString(),
      { diarizer: diarizerTool, transcriber: transcriberTool },
      audio.r2Pointer,
      {
        filename: audio.filename,
        sourceType: "url",
        rawValue: urlRaw,
      },
      audio.r2Pointer
    );

<<<<<<< HEAD
=======
    // Handle the background work promise to prevent unhandled rejections
    work.catch((error: any) => {
      console.error("❌ Background transcription work failed:", error);
      // The error is logged but doesn't affect the HTTP response
    });

    const transcribeDuration = Date.now() - transcribeStartTime;
    console.log(`✅ Transcription job created in ${transcribeDuration}ms (${(transcribeDuration / 1000).toFixed(2)}s)`);

    const totalDuration = Date.now() - requestStartTime;
    console.log(`🎉 Total request processing time: ${totalDuration}ms (${(totalDuration / 1000).toFixed(2)}s)`);

>>>>>>> WA-170_MCP
    // Return immediately with the document ID and status URL
    res.statusCode = 202; // Accepted
    res.json({
      id: doc._id,
      status: doc.processStatus,
      statusUrl: `/white-label/${whitelabel._id}/data-source/audio-transcript/${doc._id}/status`,
      message: "Audio transcription job started. Poll the statusUrl to check progress."
    });
<<<<<<< HEAD
  }catch(e){
=======

    console.log(`📤 Response sent with audio transcript ID: ${doc._id}`);
  }catch(e: any){
    const errorDuration = Date.now() - requestStartTime;
    console.log(`❌ Request failed after ${errorDuration}ms (${(errorDuration / 1000).toFixed(2)}s)`);
    console.log(`❌ Error details:`, e?.message || e);
>>>>>>> WA-170_MCP
    next(e);
  }
};
