/**
 * Vector Tests
<<<<<<< HEAD
 *
=======
 * 
>>>>>>> WA-170_MCP
 * This file contains tests for RAG vectors using the hybrid StorageState + Resource Pooling architecture.
 */

import { test, expect } from '@playwright/test';
import { getWhitelabel, releaseWhitelabel } from '../resources/whitelabel-pool';
import { getVector, releaseVector } from '../resources/vector-pool';
import { updateVector, getVectors } from '../api/vector-api';
import { randomName } from '../utils/test-utils';

// Define a test suite for vector management
test.describe('Vector Management', () => {
<<<<<<< HEAD
  // Edge case tests
  test.describe('Edge Cases', () => {
    test('should handle very long vector names', async () => {
      // Get a whitelabel from the pool
      const whitelabel = await getWhitelabel('admin');

      try {
        // Create a very long name (500 characters) - should trigger validation error
        const longName = 'Very long vector name '.repeat(25);

        // Try to create a vector with a very long name (should fail)
        let error: Error | null = null;
        try {
          await getVector('admin', whitelabel._id, longName);
          // If we get here, the validation didn't work
          expect(true).toBe(false);
        } catch (e) {
          error = e as Error;
        }

        // Verify that a validation error was thrown
        expect(error).not.toBeNull();
        expect(error?.message).toContain('cannot exceed 100 characters');

        // Log the expected validation error for debugging
        console.log('Expected validation error:', error?.message);
      } finally {
        // Release the whitelabel back to the pool
        releaseWhitelabel('admin');
      }
    });

    test('should handle special characters in vector names', async () => {
      // Get a whitelabel from the pool
      const whitelabel = await getWhitelabel('admin');

      try {
        // Create a name with special characters
        const specialName = 'Special Characters: !@#$%^&*()_+{}[]|\\:;"\'<>,.?/~`';

        // Create a vector with special characters in the name
        const vector = await getVector('admin', whitelabel._id, specialName);

        try {
          // Verify that the special characters were preserved or sanitized appropriately
          expect(vector).toBeDefined();
          expect(vector.name).toBeDefined();

          // The API might sanitize some special characters, so we check if it contains the base text
          expect(vector.name).toContain('Special Characters');
        } finally {
          // Release the vector back to the pool
          releaseVector('admin', whitelabel._id, specialName);
        }
      } finally {
        // Release the whitelabel back to the pool
        releaseWhitelabel('admin');
      }
    });

    test('should handle emoji characters in vector names', async () => {
      // Get a whitelabel from the pool
      const whitelabel = await getWhitelabel('admin');

      try {
        // Create a name with emoji characters
        const emojiName = '😀 Vector with Emojis 🚀';

        // Create a vector with emoji in the name
        const vector = await getVector('admin', whitelabel._id, emojiName);

        try {
          // Verify that the emoji were preserved
          expect(vector).toBeDefined();
          expect(vector.name).toBeDefined();

          // The API might handle emoji differently, so we check if it contains the base text
          expect(vector.name).toContain('Vector with Emojis');
        } finally {
          // Release the vector back to the pool
          releaseVector('admin', whitelabel._id, emojiName);
        }
      } finally {
        // Release the whitelabel back to the pool
        releaseWhitelabel('admin');
      }
    });

    test('should handle very long descriptions when updating a vector', async () => {
      // Get a vector from the pool with a specific name
      const vector = await getVector('admin', undefined, 'long-desc-test-vector');

      try {
        // Create a very long description (1000 characters)
        const longDescription = 'Very long vector description. '.repeat(40);

        // Update the vector with a very long description
        const updatedVector = await updateVector('admin', vector._id, {
          description: longDescription
        });

        // Verify that the description was preserved or truncated appropriately
        expect(updatedVector).toBeDefined();
        expect(updatedVector.description).toBeDefined();

        // Some APIs might truncate long descriptions, so we check if it's either the full description or a truncated version
        if (updatedVector.description.length < longDescription.length) {
          expect(updatedVector.description).toContain('Very long vector description');
          expect(updatedVector.description.length).toBeLessThan(longDescription.length);
        } else {
          expect(updatedVector.description).toBe(longDescription);
        }
      } finally {
        // Release the vector back to the pool
        releaseVector('admin', undefined, 'long-desc-test-vector');
      }
    });

    test('should handle boundary case of minimum length name', async () => {
      // Get a whitelabel from the pool
      const whitelabel = await getWhitelabel('admin');

      try {
        // Create a vector with a very short name
        const vector = await getVector('admin', whitelabel._id, 'A'); // Single character name

        try {
          // Verify that the short name was accepted
          expect(vector).toBeDefined();
          expect(vector.name).toBe('A');
        } finally {
          // Release the vector back to the pool
          releaseVector('admin', whitelabel._id, 'A');
        }
      } finally {
        // Release the whitelabel back to the pool
        releaseWhitelabel('admin');
      }
    });
  });

  // Error condition tests
  test.describe('Error Conditions', () => {
    test('should handle invalid input when creating a vector', async () => {
      // Get a whitelabel from the pool
      const whitelabel = await getWhitelabel('admin');

      try {
        // Try to create a vector with missing name
        let error: Error | null = null;
        try {
          // @ts-ignore - intentionally passing invalid data
          await getVector('admin', whitelabel._id, '');
        } catch (e) {
          error = e as Error;
        }

        // Verify that an error was thrown
        expect(error).not.toBeNull();
        expect(error?.message).toContain('name');
      } finally {
        // Release the whitelabel back to the pool
        releaseWhitelabel('admin');
      }
    });

    test('should handle not found error when getting a non-existent vector', async () => {
      // Try to get a non-existent vector
      let error: Error | null = null;
      try {
        // Force an error by using a special key that will trigger a not found error
        await getVector('admin', 'non-existent-whitelabel-id', 'non-existent-vector');
      } catch (e) {
        error = e as Error;
      }

      // Verify that an error was thrown
      expect(error).not.toBeNull();
      expect(error?.message).toContain('not found');
    });

    test('should handle unauthorized access when accessing with wrong role', async () => {
      // Get a whitelabel from the pool
      const whitelabel = await getWhitelabel('admin');

      try {
        // Create a vector as admin
        const vector = await getVector('admin', whitelabel._id, 'auth-test-vector');

        try {
          // Try to access it with a different role (user trying to access admin's vector)
          let error: Error | null = null;
          try {
            // Try to update the vector with user role (should fail authorization)
            await updateVector('user', vector._id, {
              name: 'Unauthorized Update'
            });
          } catch (e) {
            error = e as Error;
          }

          // Verify that an error was thrown
          expect(error).not.toBeNull();
          expect(error?.message.toLowerCase()).toContain('unauthorized');
        } finally {
          // Release the vector back to the pool
          releaseVector('admin', whitelabel._id, 'auth-test-vector');
        }
      } finally {
        // Release the whitelabel back to the pool
        releaseWhitelabel('admin');
      }
    });

    test('should handle invalid input when updating a vector', async () => {
      // Get a vector from the pool with a specific name
      const vector = await getVector('admin', undefined, 'update-error-test-vector');

      try {
        // Try to update with invalid data
        let error: Error | null = null;
        try {
          // Force an empty name error
          await updateVector('admin', vector._id, {
            name: '' // Empty name should be invalid
          });
        } catch (e) {
          error = e as Error;
        }

        // Verify that an error was thrown
        expect(error).not.toBeNull();
        expect(error?.message).toContain('name');
      } finally {
        // Release the vector back to the pool
        releaseVector('admin', undefined, 'update-error-test-vector');
      }
    });

    test('should handle not found error when updating a non-existent vector', async () => {
      // Try to update a non-existent vector
      let error: Error | null = null;
      try {
        await updateVector('admin', 'non-existent-id', {
          name: 'Updated Name'
        });
      } catch (e) {
        error = e as Error;
      }

      // Verify that an error was thrown
      expect(error).not.toBeNull();
      expect(error?.message).toContain('not found');
    });
  });
  // Test creating a vector with admin role
  test('should create a vector with admin role', async () => {
    // Get a vector from the pool with a specific name
    const vector = await getVector('admin', undefined, 'admin-test-vector');

=======
  // Test creating a vector with admin role
  test('should create a vector with admin role', async () => {
    // Get a vector from the pool
    const vector = await getVector('admin');
    
>>>>>>> WA-170_MCP
    try {
      // Verify the vector properties
      expect(vector._id).toBeDefined();
      expect(vector.name).toBeDefined();
      expect(vector.whitelabelId).toBeDefined();
      expect(vector.ownerUser).toBeDefined();
      expect(vector.createdAt).toBeDefined();
      expect(vector.updatedAt).toBeDefined();
      expect(vector.status).toBeDefined();
      expect(vector.fileCount).toBeDefined();
<<<<<<< HEAD

=======
      
>>>>>>> WA-170_MCP
      // Log the vector for debugging
      console.log('Vector:', vector);
    } finally {
      // Release the vector back to the pool
<<<<<<< HEAD
      releaseVector('admin', undefined, 'admin-test-vector');
    }
  });

=======
      releaseVector('admin');
    }
  });
  
>>>>>>> WA-170_MCP
  // Test creating a vector with a specific whitelabel
  test('should create a vector with a specific whitelabel', async () => {
    // Get a whitelabel from the pool
    const whitelabel = await getWhitelabel('admin', undefined, 'vector-test-whitelabel');
<<<<<<< HEAD

    try {
      // Get a vector from the pool with the specific whitelabel
      const vector = await getVector('admin', whitelabel._id, 'test-vector');

=======
    
    try {
      // Get a vector from the pool with the specific whitelabel
      const vector = await getVector('admin', whitelabel._id, 'test-vector');
      
>>>>>>> WA-170_MCP
      try {
        // Verify the vector properties
        expect(vector._id).toBeDefined();
        expect(vector.name).toBe('test-vector');
        expect(vector.whitelabelId).toBe(whitelabel._id);
<<<<<<< HEAD

=======
        
>>>>>>> WA-170_MCP
        // Log the vector for debugging
        console.log('Vector:', vector);
      } finally {
        // Release the vector back to the pool
        releaseVector('admin', whitelabel._id, 'test-vector');
      }
    } finally {
      // Release the whitelabel back to the pool
      releaseWhitelabel('admin', undefined, 'vector-test-whitelabel');
    }
  });
<<<<<<< HEAD

  // Test updating a vector
  test('should update a vector', async () => {
    // Get a vector from the pool with a specific name
    const vector = await getVector('admin', undefined, 'update-test-vector');

=======
  
  // Test updating a vector
  test('should update a vector', async () => {
    // Get a vector from the pool
    const vector = await getVector('admin');
    
>>>>>>> WA-170_MCP
    try {
      // Generate a new name for the vector
      const newName = randomName('updated-vector');
      const newDescription = 'Updated description for testing purposes';
<<<<<<< HEAD

=======
      
>>>>>>> WA-170_MCP
      // Update the vector
      const updatedVector = await updateVector('admin', vector._id, {
        name: newName,
        description: newDescription
      });
<<<<<<< HEAD

=======
      
>>>>>>> WA-170_MCP
      // Verify that the vector was updated
      expect(updatedVector.name).toBe(newName);
      expect(updatedVector.description).toBe(newDescription);
      expect(updatedVector._id).toBe(vector._id);
      expect(updatedVector.whitelabelId).toBe(vector.whitelabelId);
<<<<<<< HEAD

=======
      
>>>>>>> WA-170_MCP
      // Log the updated vector for debugging
      console.log('Updated Vector:', updatedVector);
    } finally {
      // Release the vector back to the pool
<<<<<<< HEAD
      releaseVector('admin', undefined, 'update-test-vector');
    }
  });

=======
      releaseVector('admin');
    }
  });
  
>>>>>>> WA-170_MCP
  // Test creating multiple vectors in the same whitelabel
  test('should create multiple vectors in the same whitelabel', async () => {
    // Get a whitelabel from the pool
    const whitelabel = await getWhitelabel('admin', undefined, 'multi-vector-whitelabel');
<<<<<<< HEAD

=======
    
>>>>>>> WA-170_MCP
    try {
      // Get two vectors from the pool with the same whitelabel
      const vector1 = await getVector('admin', whitelabel._id, 'vector1');
      const vector2 = await getVector('admin', whitelabel._id, 'vector2');
<<<<<<< HEAD

=======
      
>>>>>>> WA-170_MCP
      try {
        // Verify that the vectors are different
        expect(vector1._id).not.toBe(vector2._id);
        expect(vector1.name).toBe('vector1');
        expect(vector2.name).toBe('vector2');
        expect(vector1.whitelabelId).toBe(whitelabel._id);
        expect(vector2.whitelabelId).toBe(whitelabel._id);
<<<<<<< HEAD

        // Get all vectors for the whitelabel
        const vectors = await getVectors('admin', whitelabel._id);

=======
        
        // Get all vectors for the whitelabel
        const vectors = await getVectors('admin', whitelabel._id);
        
>>>>>>> WA-170_MCP
        // Verify that both vectors are in the list
        expect(vectors.length).toBeGreaterThanOrEqual(2);
        expect(vectors.some(v => v._id === vector1._id)).toBe(true);
        expect(vectors.some(v => v._id === vector2._id)).toBe(true);
<<<<<<< HEAD

=======
        
>>>>>>> WA-170_MCP
        // Log the vectors for debugging
        console.log('Vector 1:', vector1);
        console.log('Vector 2:', vector2);
        console.log('All Vectors:', vectors);
      } finally {
        // Release the vectors back to the pool
        releaseVector('admin', whitelabel._id, 'vector1');
        releaseVector('admin', whitelabel._id, 'vector2');
      }
    } finally {
      // Release the whitelabel back to the pool
      releaseWhitelabel('admin', undefined, 'multi-vector-whitelabel');
    }
  });
});
