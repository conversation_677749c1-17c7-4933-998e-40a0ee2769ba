import { WorkflowStep } from "cloudflare:workers";
<<<<<<< HEAD
import { StepContext, VectorizeConfig, ChunkData, InitialSetup } from "./types";

/**
 * Vectorizes chunks
=======
import { StepContext, ChunkData, VectorizeConfig, InitialSetup } from "../types";

/**
 * Vectorizes chunks and stores them in Cloudflare Vectorize
>>>>>>> WA-170_MCP
 */
export async function vectorizeChunks(
  step: WorkflowStep,
  context: StepContext,
  filteredChunks: ChunkData[],
  vectorizeConfig: VectorizeConfig,
  initialSetup: InitialSetup
<<<<<<< HEAD
): Promise<void>{
  await step.do("vectorizeChunks", async ()=>{

    // Only vectorize if we have a ragId
    if(!vectorizeConfig.ragId) {
      console.log("⚠️ No ragId provided, skipping vectorization");
      return;
    }

    const vectorIndex = `vector-index-${vectorizeConfig.ragId}`;

    // Ensure vector index exists first
    console.log(`🔄 Ensuring vector index exists: ${vectorIndex}`);
    try {
      // Use the Cloudflare API directly
      // Import the VectorizeAPI class from utils
      const { VectorizeAPI } = await import("../../utils/vectorize-api");

      // Create a new instance of the VectorizeAPI class
      const vectorizeApi = new VectorizeAPI({
        accountId: context.env.CLOUDFLARE_ACCOUNT_ID,
        apiToken: context.env.CLOUDFLARE_API_TOKEN,
        ragName: "default"
      });

      // Find or create the index
      await vectorizeApi.findOrCreateIndex(
        vectorIndex,
        `📊 Vectors for chunks \n\n vectorIndex: ${vectorIndex}`
      );

      console.log(`✅ Successfully found or created vector index: ${vectorIndex}`);

      // Process chunks in batches to avoid hitting API limits
      const batchSize = 10;

      // Make sure the fileId is valid
=======
): Promise<Array<{ id: string, values: number[], metadata: any }>>{
  return await step.do("vectorizeChunks", async ()=>{

    console.log(`🚀 [VECTORIZE] Starting vectorization for ${filteredChunks.length} chunks`);
    console.log(`🚀 [VECTORIZE] ragId: ${vectorizeConfig.ragId || 'none'}`);
    console.log(`🚀 [VECTORIZE] whitelabelId: ${vectorizeConfig.whitelabelId}`);
    console.log(`🚀 [VECTORIZE] fileId: ${initialSetup.fileInfo.fileId}`);

    // Generate embeddings regardless of ragId for future use
    // If no ragId is provided, we'll still generate embeddings and store them in R2
    // so they can be used later when the file is added to a RAG vector
    if(!vectorizeConfig.ragId) {
      console.log("⚠️ No ragId provided, generating embeddings for future RAG vector use");
    }

    let vectorIndex: string | null = null;
    let vectorizeApi: any = null;

    // Only create vector index if we have a ragId
    if(vectorizeConfig.ragId) {
      vectorIndex = `vector-index-${vectorizeConfig.ragId}`;

      // Ensure vector index exists first
      console.log(`🔄 Ensuring vector index exists: ${vectorIndex}`);
      try {
        // Use the Cloudflare API directly
        // Import the VectorizeAPI class from utils
        const { VectorizeAPI } = await import("../../utils/vectorize-api");

        // Create a new instance of the VectorizeAPI class
        vectorizeApi = new VectorizeAPI({
          accountId: context.env.CLOUDFLARE_ACCOUNT_ID,
          apiToken: context.env.CLOUDFLARE_API_TOKEN,
          ragName: "default"
        });

        // Find or create the index
        await vectorizeApi.findOrCreateIndex(
          vectorIndex,
          `📊 Vectors for chunks \n\n vectorIndex: ${vectorIndex}`
        );

        console.log(`✅ Successfully found or created vector index: ${vectorIndex}`);
      }catch(error) {
        console.error("❌ Error finding or creating Vectorize index:", error);
        throw error;
      }
    } else {
      console.log("⚠️ No ragId provided, skipping vector index creation but generating embeddings");
    }

    // Process chunks in batches to avoid hitting API limits
    const batchSize = 10;

    // Store all embeddings to return
    const allEmbeddings: Array<{ id: string, values: number[], metadata: any }> = [];

    // Make sure the fileId is valid
>>>>>>> WA-170_MCP
    let fileId = initialSetup.fileInfo.fileId;
    // Check if the fileId is in a valid MongoDB ObjectId format
    const isValidObjectId = typeof fileId === 'string' && /^[0-9a-fA-F]{24}$/.test(fileId);
    if (!isValidObjectId) {
      console.warn(`⚠️ [VECTORIZE] Invalid ObjectId format detected: ${fileId}`);
      
      // If it contains special characters like '/', attempt to extract a valid ID portion
      if (typeof fileId === 'string' && fileId.includes('/')) {
        const parts = fileId.split('/');
        // Check each part for a valid ObjectId
        for (const part of parts) {
          if (/^[0-9a-fA-F]{24}$/.test(part)) {
            initialSetup.fileInfo.fileId = part;
            console.log(`✅ [VECTORIZE] Extracted valid ObjectId from path: ${part}`);
            break;
          }
        }
      }
    }
<<<<<<< HEAD
    
    // Process chunks in batches
      for(let i = 0; i < filteredChunks.length; i += batchSize) {
        const batch = filteredChunks.slice(i, i + batchSize);

        console.log("🔄 Processing batch for embeddings", {
          batchIndex: i / batchSize + 1,
          batchSize: batch.length,
          fileId: initialSetup.fileInfo.fileId,
          vectorIndex
        });

        // Create embeddings for each chunk
        const vectorsWithEmbeddings = [];

        for(const chunk of batch) {
          try {
            // Generate embedding for the chunk
            let embeddingResult;

            try {
              // Use Cloudflare Workers AI for embeddings
              embeddingResult = await context.env.AI.run(
                "@cf/baai/bge-base-en-v1.5",
                { text: chunk.text }
              );
            }catch(embeddingError) {
              console.error("❌ Error generating embedding with Workers AI:", embeddingError);

              // Fallback to a simpler approach - just use a random vector
              // This is just for testing and should be replaced with a proper fallback
              embeddingResult = Array(384).fill(0).map(()=>Math.random() * 2 - 1);

              console.warn("⚠️ Using fallback random embedding for chunk:", {
                chunkId: chunk.id,
                reason: embeddingError.message
              });
            }

            // Validate embedding result
            if(!embeddingResult || (Array.isArray(embeddingResult) && embeddingResult.length === 0)) {
              console.error("❌ Invalid embedding result:", embeddingResult);
              continue;
            }

            // Extract the actual embedding values
            const embedding = Array.isArray(embeddingResult)
              ? embeddingResult
              : embeddingResult.data && Array.isArray(embeddingResult.data)
                ? embeddingResult.data
                : embeddingResult.embedding && Array.isArray(embeddingResult.embedding)
                  ? embeddingResult.embedding
                  : null;

            if(!embedding) {
              console.error("❌ Could not extract embedding from result:", {
                embeddingResult,
                type: typeof embeddingResult
              });
              continue;
            }

            // Add the vector with embedding to the batch
            vectorsWithEmbeddings.push({
              id: chunk.id,
              values: embedding,
              metadata: {
                text: chunk.text,
                whiteLabelId: vectorizeConfig.whitelabelId,
                originalName: initialSetup.fileInfo.fileName,
                vectorId: chunk.id,
                fileObjectKey: initialSetup.fileInfo.objectKey,
                tokenCount: chunk.metadata?.tokenCount || 0,
                tags: chunk.metadata?.tags || [] // Add empty tags array to satisfy API requirements
              }
            });

            // Log the embedding result to debug
            console.log("Embedding result for chunk:", {
              chunkId: chunk.id,
              embeddingType: typeof embeddingResult,
              embeddingValue: embeddingResult,
              extractedEmbedding: embedding ? `${embedding.length} values` : null
            });
          }catch(error) {
            console.error("❌ Error processing chunk for embedding:", {
              chunkId: chunk.id,
              error
            });
          }
        }

        // Skip if no vectors with embeddings
        if(vectorsWithEmbeddings.length === 0) {
          console.warn("⚠️ No vectors with embeddings in batch, skipping");
          continue;
        }

        try {
          // Log the first vector for debugging
          if(vectorsWithEmbeddings.length > 0) {
            console.log("🔍 First vector to upsert:", {
              id: vectorsWithEmbeddings[0].id,
              valuesLength: vectorsWithEmbeddings[0].values.length,
              metadataKeys: vectorsWithEmbeddings[0].metadata ? Object.keys(vectorsWithEmbeddings[0].metadata) : []
            });
          }

          // Upsert vectors to Vectorize
          await vectorizeApi.upsert(vectorIndex, vectorsWithEmbeddings);

          console.log("✅ Successfully processed and stored batch", {
            batchIndex: i / batchSize + 1,
            totalBatches: Math.ceil(filteredChunks.length / batchSize)
          });
        }catch(error) {
          console.error("❌ Error upserting vectors to Vectorize:", error);
          throw error;
        }
      }
    }catch(error) {
      console.error("❌ Error finding or creating Vectorize index:", error);
      throw error;
    }

    // The vectorization is already handled in the code above.
    // No need to duplicate the process.
=======

    // Process chunks in batches
    for(let i = 0; i < filteredChunks.length; i += batchSize) {
      const batch = filteredChunks.slice(i, i + batchSize);

      console.log("🔄 Processing batch for embeddings", {
        batchIndex: i / batchSize + 1,
        batchSize: batch.length,
        fileId: initialSetup.fileInfo.fileId,
        vectorIndex
      });

      // Create embeddings for each chunk
      const vectorsWithEmbeddings = [];

      for(const chunk of batch) {
        try {
          // Generate embedding for the chunk
          let embeddingResult;

          try {
            // Use Cloudflare Workers AI for embeddings
            embeddingResult = await context.env.AI.run(
              "@cf/baai/bge-base-en-v1.5",
              { text: chunk.text }
            );
          }catch(embeddingError) {
            console.error("❌ Error generating embedding with Workers AI:", embeddingError);

            // Fallback to a simpler approach - just use a random vector
            // This is just for testing and should be replaced with a proper fallback
            embeddingResult = Array(384).fill(0).map(()=>Math.random() * 2 - 1);

            console.warn("⚠️ Using fallback random embedding for chunk:", {
              chunkId: chunk.id,
              reason: embeddingError.message
            });
          }

          // Validate embedding result
          if(!embeddingResult || (Array.isArray(embeddingResult) && embeddingResult.length === 0)) {
            console.error("❌ Invalid embedding result:", embeddingResult);
            continue;
          }

          // Extract the actual embedding values
          const embedding = Array.isArray(embeddingResult)
            ? embeddingResult
            : embeddingResult.data && Array.isArray(embeddingResult.data)
              ? embeddingResult.data
              : embeddingResult.embedding && Array.isArray(embeddingResult.embedding)
                ? embeddingResult.embedding
                : null;

          if(!embedding) {
            console.error("❌ Could not extract embedding from result:", {
              embeddingResult,
              type: typeof embeddingResult
            });
            continue;
          }

          // Create the vector object
          const vectorObject = {
            id: chunk.id,
            values: embedding,
            metadata: {
              text: chunk.text,
              whiteLabelId: vectorizeConfig.whitelabelId,
              originalName: initialSetup.fileInfo.fileName,
              vectorId: chunk.id,
              fileObjectKey: initialSetup.fileInfo.objectKey,
              tokenCount: chunk.metadata?.tokenCount || 0,
              tags: chunk.metadata?.tags || [] // Add empty tags array to satisfy API requirements
            }
          };

          // Add to both the batch and the all embeddings array
          vectorsWithEmbeddings.push(vectorObject);
          allEmbeddings.push(vectorObject);

          // Log the embedding result to debug
          console.log("Embedding result for chunk:", {
            chunkId: chunk.id,
            embeddingType: typeof embeddingResult,
            embeddingValue: embeddingResult,
            extractedEmbedding: embedding ? `${embedding.length} values` : null
          });
        }catch(error) {
          console.error("❌ Error processing chunk for embedding:", {
            chunkId: chunk.id,
            error
          });
        }
      }

      // Skip if no vectors with embeddings
      if(vectorsWithEmbeddings.length === 0) {
        console.warn("⚠️ No vectors with embeddings in batch, skipping");
        continue;
      }

      try {
        // Log the first vector for debugging
        if(vectorsWithEmbeddings.length > 0) {
          console.log("🔍 First vector to upsert:", {
            id: vectorsWithEmbeddings[0].id,
            valuesLength: vectorsWithEmbeddings[0].values.length,
            metadataKeys: vectorsWithEmbeddings[0].metadata ? Object.keys(vectorsWithEmbeddings[0].metadata) : []
          });
        }

        // Only upsert vectors to Vectorize if we have a ragId and vector index
        if(vectorizeApi && vectorIndex) {
          await vectorizeApi.upsert(vectorIndex, vectorsWithEmbeddings);
          console.log("✅ Successfully processed and stored batch in vector index", {
            batchIndex: i / batchSize + 1,
            totalBatches: Math.ceil(filteredChunks.length / batchSize)
          });
        } else {
          console.log("✅ Successfully processed batch (embeddings generated, no vector index)", {
            batchIndex: i / batchSize + 1,
            totalBatches: Math.ceil(filteredChunks.length / batchSize)
          });
        }
      }catch(error) {
        console.error("❌ Error processing batch:", error);
        throw error;
      }
    }

    // Return all embeddings for R2 storage
    console.log(`✅ [VECTORIZE] Generated ${allEmbeddings.length} embeddings for R2 storage`);
    console.log(`🚀 [VECTORIZE] Returning embeddings array with ${allEmbeddings.length} items`);
    return allEmbeddings;
>>>>>>> WA-170_MCP
  });
}
