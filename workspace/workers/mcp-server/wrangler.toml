name = "divinci-mcp-server"
main = "src/index.ts"
compatibility_date = "2024-10-25"
compatibility_flags = ["nodejs_compat"]

[env.local]
name = "divinci-mcp-server-local"

[env.staging]
name = "divinci-mcp-server-staging"

[env.production]
name = "divinci-mcp-server"

# Environment variables (set via wrangler secret)
# AUTH0_DOMAIN
# AUTH0_CLIENT_ID  
# AUTH0_CLIENT_SECRET
# DIVINCI_API_BASE_URL
# DIVINCI_API_KEY
# CLOUDFLARE_ACCOUNT_ID

[vars]
ENVIRONMENT = "development"
MCP_SERVER_VERSION = "1.0.0"

[env.staging.vars]
ENVIRONMENT = "staging"
DIVINCI_API_BASE_URL = "https://staging-api.divinci.ai"

[env.production.vars]
ENVIRONMENT = "production"
DIVINCI_API_BASE_URL = "https://api.divinci.ai"
