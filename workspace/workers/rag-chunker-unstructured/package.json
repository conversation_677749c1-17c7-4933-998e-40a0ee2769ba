{
	"name": "rag-chunker-unstructured",
	"scripts": {
		"dev": "wrangler dev",
		"deploy": "wrangler deploy --minify",
		"cf-typegen": "wrangler types --env-interface CloudflareBindings"
	},
	"dependencies": {
		"@hono/zod-validator": "^0.4.3",
		"hono": "^4.7.4",
		"unstructured-client": "^0.19.0"
	},
	"devDependencies": {
		"@cloudflare/workers-types": "^4.20250303.0",
<<<<<<< HEAD
		"wrangler": "^4.16.1"
=======
		"wrangler": "^4.20.0"
>>>>>>> WA-170_MCP
	}
}