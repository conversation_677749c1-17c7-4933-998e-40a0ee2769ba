/**
 * Whitelabel API Tests
 *
 * This file contains tests for the Whitelabel API.
 */

import { test, expect } from '@playwright/test';
import {
  createWhitelabel,
  getWhitelabel,
  getWhitelabels,
  updateWhitelabel,
  deleteWhitelabel
} from '../api/whitelabel-api';
import { getUserGroup, releaseUserGroup } from '../resources/user-group-pool';

test.describe('Whitelabel API', () => {
  // Edge case tests
  test.describe('Edge Cases', () => {
    test('should handle very long whitelabel names', async () => {
      // Get a user group from the pool
      const userGroup = await getUserGroup('admin');

      try {
        // Create a very long name (500 characters) - should trigger validation error
        const longName = 'Very long whitelabel name '.repeat(20);

        // Try to create a whitelabel with a very long name (should fail)
        let error: Error | null = null;
        try {
          await createWhitelabel('admin', {
            name: longName,
            groupId: userGroup._id
          });
          // If we get here, the validation didn't work
          expect(true).toBe(false);
        } catch (e) {
          error = e as Error;
        }

        // Verify that a validation error was thrown
        expect(error).not.toBeNull();
        expect(error?.message).toContain('cannot exceed 100 characters');

        // Log the expected validation error for debugging
        console.log('Expected validation error:', error?.message);
      } finally {
        // Release the user group back to the pool
        releaseUserGroup('admin');
      }
    });

    test('should handle special characters in whitelabel names', async () => {
      // Get a user group from the pool
      const userGroup = await getUserGroup('admin');

      try {
        // Create a name with special characters
        const specialName = 'Special Characters: !@#$%^&*()_+{}[]|\\:;"\'<>,.?/~`';

        // Create a whitelabel with special characters in the name
        const whitelabel = await createWhitelabel('admin', {
          name: specialName,
          groupId: userGroup._id
        });

        // Verify that the special characters were preserved or sanitized appropriately
        expect(whitelabel).toBeDefined();
        expect(whitelabel.name).toBeDefined();

        // The API might sanitize some special characters, so we check if it contains the base text
        expect(whitelabel.name).toContain('Special Characters');

        // Clean up
        await deleteWhitelabel('admin', whitelabel._id);
      } finally {
        // Release the user group back to the pool
        releaseUserGroup('admin');
      }
    });

    test('should handle emoji characters in whitelabel names', async () => {
      // Get a user group from the pool
      const userGroup = await getUserGroup('admin');

      try {
        // Create a name with emoji characters
        const emojiName = '😀 Whitelabel with Emojis 🚀';

        // Create a whitelabel with emoji in the name
        const whitelabel = await createWhitelabel('admin', {
          name: emojiName,
          groupId: userGroup._id
        });

        // Verify that the emoji were preserved
        expect(whitelabel).toBeDefined();
        expect(whitelabel.name).toBeDefined();

        // The API might handle emoji differently, so we check if it contains the base text
        expect(whitelabel.name).toContain('Whitelabel with Emojis');

        // Clean up
        await deleteWhitelabel('admin', whitelabel._id);
      } finally {
        // Release the user group back to the pool
        releaseUserGroup('admin');
      }
    });

    test('should handle custom slugs with special characters', async () => {
      // Get a user group from the pool
      const userGroup = await getUserGroup('admin');

      try {
        // Create a custom slug with special characters
        const customSlug = 'custom-slug-with-special-chars-123';

        // Create a whitelabel with a custom slug
        const whitelabel = await createWhitelabel('admin', {
          name: 'Whitelabel with Custom Slug',
          groupId: userGroup._id,
          slug: customSlug
        });

        // Verify that the custom slug was preserved or sanitized appropriately
        expect(whitelabel).toBeDefined();
        expect(whitelabel.slug).toBeDefined();

        // The API might sanitize the slug, so we check if it contains the base text
        expect(whitelabel.slug).toContain('custom-slug');

        // Clean up
        await deleteWhitelabel('admin', whitelabel._id);
      } finally {
        // Release the user group back to the pool
        releaseUserGroup('admin');
      }
    });

    test('should handle boundary case of minimum length name', async () => {
      // Get a user group from the pool
      const userGroup = await getUserGroup('admin');

      try {
        // Create a whitelabel with a very short name
        const whitelabel = await createWhitelabel('admin', {
          name: 'A', // Single character name
          groupId: userGroup._id
        });

        // Verify that the short name was accepted
        expect(whitelabel).toBeDefined();
        expect(whitelabel.name).toBe('A');

        // Clean up
        await deleteWhitelabel('admin', whitelabel._id);
      } finally {
        // Release the user group back to the pool
        releaseUserGroup('admin');
      }
    });
  });

  // Error condition tests
  test.describe('Error Conditions', () => {
    test('should handle invalid input when creating a whitelabel', async () => {
      // Get a user group from the pool
      const userGroup = await getUserGroup('admin');

      try {
        // Try to create a whitelabel with missing name
        let error: Error | null = null;
        try {
          // @ts-ignore - intentionally passing invalid data
          await createWhitelabel('admin', {
            groupId: userGroup._id
          });
        } catch (e) {
          error = e as Error;
        }

        // Verify that an error was thrown
        expect(error).not.toBeNull();
        expect(error?.message).toContain('name');
      } finally {
        // Release the user group back to the pool
        releaseUserGroup('admin');
      }
    });

    test('should handle not found error when getting a non-existent whitelabel', async () => {
      // Try to get a non-existent whitelabel
      let error: Error | null = null;
      try {
        await getWhitelabel('admin', 'non-existent-id');
      } catch (e) {
        error = e as Error;
      }

      // Verify that an error was thrown
      expect(error).not.toBeNull();
      expect(error?.message).toContain('not found');
    });

    test('should handle unauthorized access when accessing with wrong role', async () => {
      // Get a user group from the pool
      const userGroup = await getUserGroup('admin');

      try {
        // Create a whitelabel as admin
        const whitelabel = await createWhitelabel('admin', {
          name: 'Auth Test Whitelabel',
          groupId: userGroup._id
        });

        // Try to access it with a different role
        let error: Error | null = null;
        try {
          // This should fail because the whitelabel belongs to the admin
          await getWhitelabel('user', whitelabel._id);
        } catch (e) {
          error = e as Error;
        }

        // Verify that an error was thrown
        expect(error).not.toBeNull();
        expect(error?.message.toLowerCase()).toContain('unauthorized');

        // Clean up
        await deleteWhitelabel('admin', whitelabel._id);
      } finally {
        // Release the user group back to the pool
        releaseUserGroup('admin');
      }
    });

    test('should handle invalid input when updating a whitelabel', async () => {
      // Get a user group from the pool
      const userGroup = await getUserGroup('admin');

      try {
        // Create a whitelabel
        const whitelabel = await createWhitelabel('admin', {
          name: 'Update Error Test',
          groupId: userGroup._id
        });

        // Try to update with invalid data
        let error: Error | null = null;
        try {
          await updateWhitelabel('admin', whitelabel._id, {
            // @ts-ignore - intentionally passing invalid data
            name: '' // Empty name should be invalid
          });
        } catch (e) {
          error = e as Error;
        }

        // Verify that an error was thrown
        expect(error).not.toBeNull();
        expect(error?.message).toContain('name');

        // Clean up
        await deleteWhitelabel('admin', whitelabel._id);
      } finally {
        // Release the user group back to the pool
        releaseUserGroup('admin');
      }
    });

    test('should handle not found error when deleting a non-existent whitelabel', async () => {
      // Try to delete a non-existent whitelabel
      let error: Error | null = null;
      try {
        await deleteWhitelabel('admin', 'non-existent-id');
      } catch (e) {
        error = e as Error;
      }

      // Verify that an error was thrown
      expect(error).not.toBeNull();
      expect(error?.message).toContain('not found');
    });
  });
  test('should create a whitelabel', async () => {
    // Get a user group from the pool
    const userGroup = await getUserGroup('admin');

    try {
      // Create a whitelabel
      const whitelabel = await createWhitelabel('admin', {
        name: 'Test Whitelabel',
        groupId: userGroup._id
      });

      // Verify the whitelabel
      expect(whitelabel).toBeDefined();
      expect(whitelabel._id).toBeDefined();
      expect(whitelabel.name).toBe('Test Whitelabel');
      expect(whitelabel.groupId).toBe(userGroup._id);

      // Clean up
      await deleteWhitelabel('admin', whitelabel._id);
    } finally {
      // Release the user group back to the pool
      releaseUserGroup('admin');
    }
  });

  test('should get a whitelabel by ID', async () => {
    // Get a user group from the pool
    const userGroup = await getUserGroup('admin');

    try {
      // Create a whitelabel
      const createdWhitelabel = await createWhitelabel('admin', {
        name: 'Get Whitelabel Test',
        groupId: userGroup._id
      });

      // Get the whitelabel
      const whitelabel = await getWhitelabel('admin', createdWhitelabel._id);

      // Verify the whitelabel
      expect(whitelabel).toBeDefined();
      expect(whitelabel._id).toBe(createdWhitelabel._id);
      expect(whitelabel.name).toBe('Get Whitelabel Test');
      expect(whitelabel.groupId).toBe(userGroup._id);

      // Clean up
      await deleteWhitelabel('admin', whitelabel._id);
    } finally {
      // Release the user group back to the pool
      releaseUserGroup('admin');
    }
  });

  test('should get all whitelabels for a group', async () => {
    // Get a user group from the pool
    const userGroup = await getUserGroup('admin');

    try {
      // Create two whitelabels
      const whitelabel1 = await createWhitelabel('admin', {
        name: 'Whitelabel 1',
        groupId: userGroup._id
      });

      const whitelabel2 = await createWhitelabel('admin', {
        name: 'Whitelabel 2',
        groupId: userGroup._id
      });

      // Get all whitelabels for the group
      const whitelabels = await getWhitelabels('admin', userGroup._id);

      // Verify the whitelabels
      expect(whitelabels).toBeInstanceOf(Array);
      expect(whitelabels.length).toBeGreaterThanOrEqual(2);

      // Verify that our created whitelabels are in the list
      const whitelabel1InList = whitelabels.some(w => w._id === whitelabel1._id);
      const whitelabel2InList = whitelabels.some(w => w._id === whitelabel2._id);

      expect(whitelabel1InList).toBe(true);
      expect(whitelabel2InList).toBe(true);

      // Clean up
      await deleteWhitelabel('admin', whitelabel1._id);
      await deleteWhitelabel('admin', whitelabel2._id);
    } finally {
      // Release the user group back to the pool
      releaseUserGroup('admin');
    }
  });

  test('should update a whitelabel', async () => {
    // Get a user group from the pool
    const userGroup = await getUserGroup('admin');

    try {
      // Create a whitelabel
      const whitelabel = await createWhitelabel('admin', {
        name: 'Update Whitelabel Test',
        groupId: userGroup._id
      });

      // Update the whitelabel
      const updatedWhitelabel = await updateWhitelabel('admin', whitelabel._id, {
        name: 'Updated Whitelabel'
      });

      // Verify the updated whitelabel
      expect(updatedWhitelabel).toBeDefined();
      expect(updatedWhitelabel._id).toBe(whitelabel._id);
      expect(updatedWhitelabel.name).toBe('Updated Whitelabel');
      expect(updatedWhitelabel.groupId).toBe(userGroup._id);

      // Clean up
      await deleteWhitelabel('admin', whitelabel._id);
    } finally {
      // Release the user group back to the pool
      releaseUserGroup('admin');
    }
  });

  test('should delete a whitelabel', async () => {
    // Get a user group from the pool
    const userGroup = await getUserGroup('admin');

    try {
      // Create a whitelabel
      const whitelabel = await createWhitelabel('admin', {
        name: 'Delete Whitelabel Test',
        groupId: userGroup._id
      });

      // Delete the whitelabel
      await deleteWhitelabel('admin', whitelabel._id);

      // Try to get the deleted whitelabel (should throw an error)
      let error: Error | null = null;
      try {
        await getWhitelabel('admin', whitelabel._id);
      } catch (e) {
        error = e as Error;
      }

      // Verify that an error was thrown
      expect(error).not.toBeNull();
    } finally {
      // Release the user group back to the pool
      releaseUserGroup('admin');
    }
  });
});
