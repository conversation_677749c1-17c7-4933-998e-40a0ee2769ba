import React, { useEffect, useState } from "react";

import { Link, useParams } from "react-router-dom";
import { replaceParams } from "@divinci-ai/utils";
import { PATH_WHITELABEL_DATASOURCE_AUDIO_ITEM } from "../../paths";
import { AudioTranscriptStatus } from "@divinci-ai/models";

import { AudioTranscriptItem } from "../types";
import { useAudioTranscriptItem } from "../../data/AudioTranscriptItem";
// Removed confidence components - not supported by open-source pyannote

import { DisplayItem } from "../../Reused/DisplayItem";
<<<<<<< HEAD
=======
import { VoiceprintSection } from "../VoiceprintSection";
import { ExportModal } from "../ExportModal";
>>>>>>> WA-170_MCP
import styles from "./styles.module.css";

export function AudioTranscriptItemDisplay(){
  const params = useParams();
  const { value } = useAudioTranscriptItem();
  const [isDarkMode, setIsDarkMode] = useState(false);
<<<<<<< HEAD
=======
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
>>>>>>> WA-170_MCP

  // Check if dark mode is enabled
  useEffect(() => {
    const checkDarkMode = () => {
      const isDark = document.documentElement.classList.contains('dark-mode');
      setIsDarkMode(isDark);
    };

    // Check initially
    checkDarkMode();

    // Set up a MutationObserver to detect changes to the class list
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'class') {
          checkDarkMode();
        }
      });
    });

    observer.observe(document.documentElement, { attributes: true });

    // Clean up
    return () => observer.disconnect();
  }, []);

  if(!value) return null;

  // Function to get status badge class based on process status
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case AudioTranscriptStatus.Completed:
        return "is-success";
      case AudioTranscriptStatus.Failed:
        return "is-danger";
      case AudioTranscriptStatus.Diarization:
      case AudioTranscriptStatus.Transcription:
        return "is-warning";
      default:
        return "is-light";
    }
  };

  return (
    <div className={`audio-transcript-container section ${isDarkMode ? styles.darkModeContainer : ''}`}>
      <div className="container">
        <div className={`box has-background-white-in-light has-background-dark-in-dark ${isDarkMode ? styles.darkModeBox : ''}`}>
          <h1 className="title has-text-dark-in-light has-text-white-in-dark">
            <Link to={
              replaceParams(
                PATH_WHITELABEL_DATASOURCE_AUDIO_ITEM,
                { whitelabelId: params.whitelabelId, audioId: params.audioId }
              )
            } className="has-text-dark-in-light has-text-white-in-dark">{value.userInfo.title}</Link>
          </h1>

          <div className="level mb-5">
            <div className="level-left">
              <div className="level-item">
                <div className="tags has-addons">
                  <span className="tag is-medium">Status</span>
                  <span className={`tag is-medium ${getStatusBadgeClass(value.processStatus)}`}>
                    {value.processStatus}
                  </span>
                </div>
              </div>
              <div className="level-item">
                <div className="tags has-addons">
                  <span className="tag is-medium">Source</span>
                  <span className="tag is-medium is-info">
                    {value.sourceOrigin.sourceType}
                  </span>
                </div>
              </div>
              <div className="level-item">
                <div className="tags has-addons">
                  <span className="tag is-medium">Media Type</span>
                  <span className="tag is-medium is-primary">
                    {value.sourceOrigin.mediaType}
                  </span>
                </div>
              </div>
            </div>
<<<<<<< HEAD
          </div>

=======
            <div className="level-right">
              {value.processStatus === AudioTranscriptStatus.Completed && (
                <div className="level-item">
                  <button
                    className="button is-success"
                    onClick={() => setIsExportModalOpen(true)}
                  >
                    <span className="icon">
                      <i className="fas fa-download"></i>
                    </span>
                    <span>Export Transcript</span>
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Note: Confidence analysis not available in open-source pyannote */}

>>>>>>> WA-170_MCP
          <div className="card mb-5 has-background-white-in-light has-background-dark-in-dark">
            <div className="card-content">
              <div className="media">
                <div className="media-content">
                  <p className="title is-4 has-text-dark-in-light has-text-white-in-dark">Audio Player</p>
                </div>
              </div>
              <div className="content has-text-centered">
                <DisplayItem item={value} />
              </div>
            </div>
          </div>

<<<<<<< HEAD
          <RenderContent item={value} />
        </div>
      </div>
=======
          {/* Voiceprint Section - Beautiful speaker identification interface */}
          <VoiceprintSection
            audioItem={{
              _id: value._id,
              voiceprints: value.voiceprints,
              whitelabelId: params.whitelabelId!,
              samples: value.samples,
              processStatus: value.processStatus,
              tools: value.tools // Include tools information for diarizer check
            }}
            onVoiceprintUpdate={(voiceprints) => {
              // Update the audio item context when voiceprints change
              console.log('Voiceprints updated:', voiceprints);
            }}
          />

          <RenderContent item={value} />
        </div>
      </div>

      {/* Export Modal */}
      <ExportModal
        isOpen={isExportModalOpen}
        onClose={() => setIsExportModalOpen(false)}
        audioTranscriptId={value._id}
        audioTranscript={value}
      />
>>>>>>> WA-170_MCP
    </div>
  );
}



import { DisplaySample } from "./DisplaySample/Sample";
import { DisplayIgnoredSample } from "./DisplaySample/IgnoredSample";
import { DisplayFailedSample } from "./DisplaySample/FailedSample";
function RenderContent({ item }: { item: AudioTranscriptItem }){
  switch(item.processStatus){
    case AudioTranscriptStatus.Failed: return !item.errorStatus ? null : (
      <div className="card mb-5">
        <header className="card-header has-background-danger-light">
          <p className="card-header-title">
            <span className="icon mr-2">
              <i className="fas fa-exclamation-triangle"></i>
            </span>
            Error while Processing
          </p>
        </header>
        <div className="card-content">
          <div className="content">
            <div className="field">
              <label className="label">Failed Step</label>
              <div className="control">
                <div className="tags">
                  <span className="tag is-danger">{item.errorStatus.step}</span>
                </div>
              </div>
            </div>
            <div className="field">
              <label className="label">Error Message</label>
              <div className="control">
                <pre className="has-background-danger-light p-4">{item.errorStatus.message}</pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    );

    case AudioTranscriptStatus.Diarization: return (
      <div className="card mb-5">
        <header className="card-header has-background-warning-light">
          <p className="card-header-title">
            <span className="icon mr-2">
              <i className="fas fa-spinner fa-pulse"></i>
            </span>
            Processing - Diarization
          </p>
        </header>
        <div className="card-content">
          <div className="content has-text-centered">
            <p>The audio is currently being processed for speaker diarization.</p>
            <p>This process identifies different speakers in the audio.</p>
            <progress className="progress is-warning" value="25" max="100">25%</progress>
          </div>
        </div>
      </div>
    );

    case AudioTranscriptStatus.Transcription: return (
      <div className="card mb-5">
        <header className="card-header has-background-warning-light">
          <p className="card-header-title">
            <span className="icon mr-2">
              <i className="fas fa-spinner fa-pulse"></i>
            </span>
            Processing - Transcription
          </p>
        </header>
        <div className="card-content">
          <div className="content has-text-centered">
            <p>The audio is currently being transcribed.</p>
            <p>This process converts speech to text for each speaker segment.</p>
            <progress className="progress is-warning" value="75" max="100">75%</progress>
          </div>
        </div>
      </div>
    );

    case AudioTranscriptStatus.Completed: return (
      <>
        {item.samples.length === 0 ? null : (
          <div className="card mb-5 has-background-white-in-light has-background-dark-in-dark">
            <header className="card-header has-background-success-light-in-light has-background-success-dark-in-dark">
              <p className="card-header-title has-text-dark-in-light has-text-white-in-dark">
                <span className="icon mr-2">
                  <i className="fas fa-comment-alt"></i>
                </span>
                Transcript Samples
              </p>
            </header>
            <div className="card-content">
              <div className="content">
                <div className="transcript-row">
                  {item.samples.map((sample, i) => (
                    <div key={i} className="box mb-4 has-background-white-in-light has-background-dark-in-dark">
                      <DisplaySample sample={sample} />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {item.ignoredSamples.length === 0 ? null : (
          <div className="card mb-5">
            <header className="card-header has-background-info-light">
              <p className="card-header-title">
                <span className="icon mr-2">
                  <i className="fas fa-eye-slash"></i>
                </span>
                Ignored Samples
              </p>
            </header>
            <div className="card-content">
              <div className="content">
                {item.ignoredSamples.map((sample, i) => (
                  <div key={i} className="box mb-4">
                    <DisplayIgnoredSample sample={sample} />
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {item.failedSamples.length === 0 ? null : (
          <div className="card mb-5">
            <header className="card-header has-background-danger-light">
              <p className="card-header-title">
                <span className="icon mr-2">
                  <i className="fas fa-exclamation-circle"></i>
                </span>
                Failed Samples
              </p>
            </header>
            <div className="card-content">
              <div className="content">
                {item.failedSamples.map((sample, i) => (
                  <div key={i} className="box mb-4">
                    <DisplayFailedSample sample={sample} />
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </>
    );
  }
}

