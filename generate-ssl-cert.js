const fs = require("fs");
const { execSync } = require("child_process");
const path = require("path");

// Create directory
const certDir = path.join(__dirname, "private-keys", "local-fast");
fs.mkdirSync(certDir, { recursive: true });

console.log("Creating SSL certificates...");

try {
  // Generate SSL certificate using OpenSSL
  execSync(
    `openssl req -x509 -newkey rsa:2048 -keyout server.key -out server.crt -days 365 -nodes -subj "/CN=localhost"`,
    {
      cwd: certDir,
      stdio: "inherit",
    }
  );

  console.log("SSL certificates created successfully!");
  console.log("Certificate path:", path.join(certDir, "server.crt"));
  console.log("Key path:", path.join(certDir, "server.key"));

  // Verify files exist
  if (
    fs.existsSync(path.join(certDir, "server.crt")) &&
    fs.existsSync(path.join(certDir, "server.key"))
  ) {
    console.log("✅ Certificate files verified");
  } else {
    console.log("❌ Certificate files not found");
  }
} catch (error) {
  console.error("Error creating SSL certificates:", error.message);
}
