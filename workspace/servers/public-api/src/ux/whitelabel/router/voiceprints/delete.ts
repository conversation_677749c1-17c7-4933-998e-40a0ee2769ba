import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getWhitelabelTarget } from "../data-source/audio-transcript/util/whitelabel";
import { HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";
import { DataSourceAudioTranscriptModel } from "@divinci-ai/server-models";

interface DeleteVoiceprintParams {
  whitelabelId: string;
  voiceprintId: string;
}

/**
 * Delete a voiceprint job
 * DELETE /white-label/:whitelabelId/voiceprints/:voiceprintId
 */
export const deleteVoiceprint: RequestHandler<DeleteVoiceprintParams> = async (req, res, next) => {
  try {
    const { whitelabelId, voiceprintId } = req.params;

    console.log(`🗑️ [VOICEPRINT-DELETE] Deleting voiceprint: ${voiceprintId} for whitelabel: ${whitelabelId}`);

    // Get the whitelabel target
    const { target } = await getWhitelabelTarget(req);

    // Find the audio document containing this voiceprint
    const audioDoc = await DataSourceAudioTranscriptModel.findOne({
      target: target,
      "voiceprints.jobId": voiceprintId
    });

    if (!audioDoc) {
      console.log(`❌ [VOICEPRINT-DELETE] Voiceprint not found: ${voiceprintId}`);
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND(`Voiceprint with ID ${voiceprintId} not found`);
    }

    // Find the voiceprint in the array
    const voiceprintIndex = audioDoc.voiceprints?.findIndex(vp => vp.jobId === voiceprintId) ?? -1;
    
    if (voiceprintIndex === -1) {
      console.log(`❌ [VOICEPRINT-DELETE] Voiceprint not found in document: ${voiceprintId}`);
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND(`Voiceprint with ID ${voiceprintId} not found`);
    }

    const voiceprint = audioDoc.voiceprints![voiceprintIndex];
    console.log(`🔍 [VOICEPRINT-DELETE] Found voiceprint with status: ${voiceprint.status}`);

    // Remove the voiceprint from the array
    audioDoc.voiceprints!.splice(voiceprintIndex, 1);

    // If this was the only voiceprint and it's a tracking document (no samples), delete the entire document
    const isTrackingDocument = audioDoc.samples?.length === 0;
    const hasOtherVoiceprints = audioDoc.voiceprints && audioDoc.voiceprints.length > 0;

    if (isTrackingDocument && !hasOtherVoiceprints) {
      console.log(`🗑️ [VOICEPRINT-DELETE] Deleting tracking document as it has no content: ${audioDoc._id}`);
      await DataSourceAudioTranscriptModel.deleteOne({ _id: audioDoc._id });
    } else {
      // Save the updated document
      await audioDoc.save();
      console.log(`💾 [VOICEPRINT-DELETE] Updated audio document: ${audioDoc._id}`);
    }

    // TODO: Consider canceling the job on Pyannote API if it's still running
    // This would require implementing a cancel endpoint call to Pyannote
    if (voiceprint.status === 'pending' || voiceprint.status === 'created' || voiceprint.status === 'running') {
      console.log(`⚠️ [VOICEPRINT-DELETE] Voiceprint was in progress (${voiceprint.status}). Consider implementing Pyannote job cancellation.`);
    }

    console.log(`✅ [VOICEPRINT-DELETE] Successfully deleted voiceprint: ${voiceprintId}`);

    res.status(200).json({
      success: true,
      message: "Voiceprint deleted successfully",
      voiceprintId: voiceprintId
    });

  } catch (error) {
    console.error("❌ [VOICEPRINT-DELETE] Deletion failed:", error);
    next(error);
  }
};
