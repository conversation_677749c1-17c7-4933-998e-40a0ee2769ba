const https = require("https");
const fs = require("fs");

const options = {
  key: fs.readFileSync("/workspaces/server/private-keys/local-fast/server.key"),
  cert: fs.readFileSync(
    "/workspaces/server/private-keys/local-fast/server.crt"
  ),
};

const server = https.createServer(options, (req, res) => {
  res.writeHead(200, { "Content-Type": "application/json" });
  res.end(JSON.stringify({ message: "HTTPS is working!", url: req.url }));
});

const PORT = 9443;
server.listen(PORT, () => {
  console.log(`🔒 Test HTTPS server running on https://localhost:${PORT}`);
});

// Handle graceful shutdown
process.on("SIGINT", () => {
  console.log("\n🛑 Shutting down test server...");
  server.close(() => {
    console.log("✅ Test server closed");
    process.exit(0);
  });
});
