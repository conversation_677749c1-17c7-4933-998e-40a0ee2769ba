# Basic usage:
# act -e ACT=true -s GOOGLE_CREDENTIALS="$(cat private-keys/develop/credentials/google-service-key.json)" --secret-file private-keys/.combined-secrets.env -W .github/workflows/build-deploy-changed-services.yml pull_request
#
# For MacOS:
# act -e ACT=true -s GOOGLE_CREDENTIALS="$(cat private-keys/develop/credentials/google-service-key.json)" --secret-file private-keys/.combined-secrets.env -W .github/workflows/build-deploy-changed-services.yml pull_request --container-architecture linux/amd64
#
# Note: Setting ACT=true will skip the deploy flag check when running locally
#
## Triggers:
# 1. Trigger via PR comment
# 2. Manually trigger via workflow dispatch with a PR number

# To manually trigger:
# ```bash
# gh workflow run comment-actions.yml -f pr_number=123 -f base_ref=develop
# ```

# ----------------------------------------
# 🎯 Workflow Configuration
# ----------------------------------------
# Purpose: Defines the basic workflow settings and triggers
# Triggers: On pull requests to develop, stage, main/master branches
# Paths: Only runs when changes are made in "workspace/**"
# ----------------------------------------
  name: Deploy to Google Cloud Run

  # 🎯 Trigger conditions
  on:
    pull_request:
      branches:
        - develop
        - stage
        - main
        - master
      paths:
        - "workspace/**"
    workflow_dispatch:
      inputs:
        pr_number:
          description: "PR number"
          required: true
        base_ref:
          description: "Base branch reference"
          required: true
        triggered_by_comment:
          description: "Whether triggered by comment"
          required: true
          type: boolean
        changed_folders:
          description: "Comma-separated list of folders to deploy"
          required: false
        is_fast_deploy:
          description: "Whether to skip tests (renamed from 'fast' to 'fail-tests')"
          required: false
          type: boolean
          default: false
        comment:
          description: "Original deployment command."
          required: false

  # 🌍 Environment variables
  env:
    RUNNER_LABELS: "self-hosted,linux,docker,X64"
    LABELS: "self-hosted,linux,docker,X64"
    GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}
    CI: true
    PNPM_CACHE_FOLDER: .cache/pnpm
    BASE_REF: ${{
      github.event_name == 'pull_request' && github.event.pull_request.base.ref ||
      github.event_name == 'workflow_dispatch' && inputs.base_ref ||
      'develop'
      }}
    # Update these to handle both events properly
    WEB_CLIENT_HOST: ${{
      (github.event_name == 'pull_request' && github.event.pull_request.base.ref == 'stage') && 'chat.stage.divinci.app' ||
      (github.event_name == 'workflow_dispatch' && inputs.base_ref == 'stage') && 'chat.stage.divinci.app' ||
      (github.event_name == 'pull_request' && github.event.pull_request.base.ref == 'develop') && 'chat.dev.divinci.app' ||
      (github.event_name == 'workflow_dispatch' && inputs.base_ref == 'develop') && 'chat.dev.divinci.app' ||
      (github.event_name == 'pull_request' && github.event.pull_request.base.ref == 'main') && 'chat.divinci.app' ||
      (github.event_name == 'workflow_dispatch' && inputs.base_ref == 'main') && 'chat.divinci.app' ||
      'chat.dev.divinci.app'
      }}
    ENVIRONMENT: ${{
      (github.event_name == 'pull_request' && github.event.pull_request.base.ref == 'stage') && 'staging' ||
      (github.event_name == 'workflow_dispatch' && inputs.base_ref == 'stage') && 'staging' ||
      (github.event_name == 'pull_request' && github.event.pull_request.base.ref == 'develop') && 'develop' ||
      (github.event_name == 'workflow_dispatch' && inputs.base_ref == 'develop') && 'develop' ||
      (github.event_name == 'pull_request' && (github.event.pull_request.base.ref == 'main' || github.event.pull_request.base.ref == 'master')) && 'production' ||
      (github.event_name == 'workflow_dispatch' && (inputs.base_ref == 'main' || inputs.base_ref == 'master')) && 'production' ||
      'develop'
      }}
    NODE_ENV: ${{
      (github.event_name == 'pull_request' && github.event.pull_request.base.ref == 'stage') && 'staging' ||
      (github.event_name == 'workflow_dispatch' && inputs.base_ref == 'stage') && 'staging' ||
      (github.event_name == 'pull_request' && github.event.pull_request.base.ref == 'develop') && 'develop' ||
      (github.event_name == 'workflow_dispatch' && inputs.base_ref == 'develop') && 'develop' ||
      (github.event_name == 'pull_request' && (github.event.pull_request.base.ref == 'main' || github.event.pull_request.base.ref == 'master')) && 'production' ||
      (github.event_name == 'workflow_dispatch' && (inputs.base_ref == 'main' || inputs.base_ref == 'master')) && 'production' ||
      'develop'
      }}

  # Allow multiple builds to run concurrently for different PRs or different services
  concurrency:
    # Use changed_folders in the group to allow concurrent builds for different services
    # This creates a unique concurrency group for each combination of:
    # 1. Target branch (develop, stage, main)
    # 2. PR number
    # 3. The specific folders being changed
    group: deploy-${{ github.event_name == 'pull_request' && github.event.pull_request.base.ref || github.event_name == 'workflow_dispatch' && inputs.base_ref || 'develop' }}-${{ github.event.pull_request.number || inputs.pr_number }}-${{ inputs.changed_folders || github.run_id }}
    # Only cancel in-progress builds for the same PR and same services
    # This allows different PRs or different services to build concurrently
    cancel-in-progress: ${{ github.event_name == 'pull_request' || (github.event_name == 'workflow_dispatch' && !inputs.triggered_by_comment) }}

  jobs:
    select-runner:
      runs-on: ubuntu-latest
      outputs:
        runner: ${{ steps.select-runner.outputs.runner }}
      steps:
        - uses: actions/checkout@v4
        - name: Validate GitHub Token
          id: validate-token
          run: |
            if [ -z "${{ secrets.ELEVATE_PR_PAT }}" ]; then
              echo "::error::GitHub token (ELEVATE_PR_PAT) is missing. Please check your repository secrets."
              exit 1
            fi

            # Test token validity by making a simple API call
            TOKEN_STATUS=$(curl -s -o /dev/null -w "%{http_code}" \
              -H "Authorization: token ${{ secrets.ELEVATE_PR_PAT }}" \
              -H "Accept: application/vnd.github.v3+json" \
              "https://api.github.com/user")

            if [ "$TOKEN_STATUS" = "200" ]; then
              echo "✅ GitHub token is valid"
            elif [ "$TOKEN_STATUS" = "401" ]; then
              echo "::error::GitHub token (ELEVATE_PR_PAT) is invalid or expired. Please generate a new token."
              exit 1
            else
              echo "::warning::GitHub token validation returned unexpected status code: $TOKEN_STATUS"
            fi

        - id: select-runner
          uses: ./.github/actions/select-runner
          with:
            github-token: ${{ secrets.ELEVATE_PR_PAT }}
            workflow-type: "build-deploy"
          env:
            GITHUB_PAT: ${{ secrets.ELEVATE_PR_PAT }}
            RUNNER_LABELS: "self-hosted,linux,docker,X64,build-deploy"
            LABELS: "self-hosted,linux,docker,X64,build-deploy"

        - name: Debug Runner Selection
          run: |
            echo "🔍 Selected runner: ${{ needs.select-runner.outputs.runner }}"
            echo "🔍 Current runner name: ${{ runner.name }}"
            echo "🔍 Is self-hosted: ${{ contains(runner.name, 'self-hosted') }}"


    # ----------------------------------------
    # 🔍 Deploy Flag Check Job
    # ----------------------------------------
    # Purpose: Determines if deployment should proceed based on commit messages
    # Outputs: should_deploy - boolean flag indicating if deployment should proceed
    # Conditions: Runs for PRs not targeting stage/main/master
    # ----------------------------------------
    check-deploy-flag:
      if: |
        (github.event_name == 'pull_request' ||
        github.event_name == 'workflow_dispatch')
      needs: select-runner
      runs-on: ${{ needs.select-runner.outputs.runner }}
      outputs:
        should_deploy: ${{ steps.check-act.outputs.should_deploy || steps.check-commit.outputs.should_deploy }}
        is_fast_deploy: ${{ steps.parse-command.outputs.is_fast_deploy || inputs.is_fast_deploy || steps.check-commit.outputs.is_fast_deploy }}
        changed_folders: ${{ steps.check-commit.outputs.changed_folders }}
        target_branch: ${{ steps.check-commit.outputs.target_branch }}
        keep_branch: ${{ steps.check-commit.outputs.keep_branch }}
      steps:
        - name: Check runner availability
          id: check-runner
          continue-on-error: true
          run: |
            if [ "${{ needs.select-runner.outputs.runner }}" = "self-hosted" ]; then
              # Add your self-hosted runner health check here
              echo "Using self-hosted runner"
            else
              echo "Using GitHub-hosted runner"
            fi

        - name: Report runner status
          if: always()
          run: |
            echo "Runner: ${{ needs.select-runner.outputs.runner }}"
            echo "Status: ${{ steps.check-runner.outcome }}"

        - name: Prevent runner cleanup
          run: |
            echo "ACTIONS_RUNNER_HOOK_JOB_STARTED=true" >> $GITHUB_ENV
            echo "ACTIONS_RUNNER_HOOK_JOB_COMPLETED=true" >> $GITHUB_ENV

        # 🏃 Skip check if running locally with act
        - name: Check if running with act
          id: check-act
          run: |
            if [ "${ACT:-false}" = "true" ]; then
              echo "Running locally with act - skipping deploy flag check"
              echo "should_deploy=true" >> $GITHUB_OUTPUT
            fi

        - name: Manual Repository Checkout - last commit only
          env:
            GITHUB_TOKEN: ${{ secrets.ELEVATE_PR_PAT }}
            TARGET_REF: ${{
              github.event_name == 'pull_request' && github.event.pull_request.head.ref ||
              github.event_name == 'workflow_dispatch' && github.ref
              }}
          run: |
            # Function to clean up git refs
            cleanup_refs() {
              echo "🧹 Cleaning up git refs..."
              rm -f .git/refs/remotes/origin/*
              rm -f .git/refs/heads/*
              git for-each-ref --format='%(refname)' refs/heads/ | xargs -r git branch -D
              git for-each-ref --format='%(refname)' refs/remotes/origin/ | xargs -r git branch -dr
            }

            # Function to fetch with retry
            fetch_branch() {
              local branch=$1
              local max_attempts=3
              local attempt=1

              while [ $attempt -le $max_attempts ]; do
                echo "🎯 Attempting to fetch ${branch} (attempt ${attempt}/${max_attempts})..."
                if git fetch --depth 1 origin ${branch}; then
                  echo "✅ Successfully fetched ${branch}"
                  return 0
                fi

                echo "⚠️ Fetch failed, cleaning up and retrying..."
                cleanup_refs
                git gc --prune=now
                ((attempt++))
                sleep 5
              done

              echo "❌ Failed to fetch ${branch} after ${max_attempts} attempts"
              return 1
            }

            # Clean up everything in current directory
            rm -rf ./* ./.[!.]* ..?*

            # Initialize fresh git repository
            git init

            # Configure git
            git config --global --add safe.directory "${GITHUB_WORKSPACE}"
            git config --global init.defaultBranch main

            # Set up the remote with token in URL (Method 5)
            git remote add origin "https://x-access-token:${GITHUB_TOKEN}@github.com/${GITHUB_REPOSITORY}"

            # Strip refs/heads/ prefix if present
            TARGET_REF=${TARGET_REF#refs/heads/}

            # Fetch with retry
            if ! fetch_branch ${TARGET_REF}; then
              echo "❌ Failed to fetch target branch"
              exit 1
            fi

            # Checkout the commit
            git checkout FETCH_HEAD

            # Verify checkout
            echo "🔍 Current commit:"
            git log -1 --oneline

        - name: Inspect commit message for deploy flags
          id: check-commit
          run: |
            # Create a fresh output file
            echo "" > $GITHUB_OUTPUT

            # If triggered by comment, use the comment directly
            if [ "${{ inputs.triggered_by_comment }}" = "true" ]; then
              echo "Using comment trigger: ${{ inputs.comment }}"
              commit_message="${{ inputs.comment }}"
            # Otherwise get from PR/commit
            elif [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
              # Source the GitHub CLI fallback script
              chmod +x .github/scripts/github-cli-fallback.sh
              source .github/scripts/github-cli-fallback.sh

              # The fallback script will handle installation or setup fallback functions
              # Either way, gh command will be available after this point

              # Make sure GitHub token is set for API calls
              export GITHUB_TOKEN="${{ secrets.ELEVATE_PR_PAT }}"

              # Get commit message from PR
              commit_message=$(gh pr view ${{ inputs.pr_number }} --json commits --jq '.commits[-1].messageHeadline')
            else
              commit_message=$(git log -1 --pretty=%B ${{ github.event.pull_request.head.sha }})
            fi

            echo "Inspecting message: $commit_message"

            # Only proceed if we have a message to parse
            if [ -n "$commit_message" ]; then
              # Make script executable
              chmod +x .github/scripts/parse-deploy.sh

              # Check for specific service mentions in the commit message
              SERVICES=()
              if [[ "$commit_message" =~ pyannote ]]; then
                SERVICES+=("workspace/workers/audio-speaker-diarization@pyannote")
                echo "✅ Detected pyannote in commit message" >&2
              fi
              if [[ "$commit_message" =~ ffmpeg ]]; then
                SERVICES+=("workspace/workers/audio-splitter@ffmpeg")
                echo "✅ Detected ffmpeg in commit message" >&2
              fi
              if [[ "$commit_message" =~ open-parse ]]; then
                SERVICES+=("workspace/workers/open-parse")
                echo "✅ Detected open-parse in commit message" >&2
              fi

              # Parse the command and capture outputs
              TEMP_OUTPUT=$(mktemp)
              if ./.github/scripts/parse-deploy.sh "$commit_message" > "$TEMP_OUTPUT" 2>&1; then
                echo "should_deploy=true" >> $GITHUB_OUTPUT

                # Read the parser output and set our outputs
                while IFS='=' read -r key value; do
                  if [[ "$key" =~ ^(is_fast_deploy|changed_folders|target_branch|keep_branch)$ ]]; then
                    echo "Setting $key=$value" >&2

                    # If we have specific services detected and changed_folders is empty
                    if [[ "$key" == "changed_folders" && -z "$value" && ${#SERVICES[@]} -gt 0 ]]; then
                      # Join the services array into a comma-separated string
                      SERVICES_STR=$(IFS=, ; echo "${SERVICES[*]}")
                      echo "Overriding empty changed_folders with detected services: $SERVICES_STR" >&2
                      echo "$key=$SERVICES_STR" >> $GITHUB_OUTPUT
                    else
                      echo "$key=$value" >> $GITHUB_OUTPUT
                    fi
                  fi
                done < "$TEMP_OUTPUT"
              else
                # Set defaults if parsing fails
                echo "should_deploy=false" >> $GITHUB_OUTPUT
                echo "is_fast_deploy=false" >> $GITHUB_OUTPUT

                # If we have specific services detected, use them even if parsing fails
                if [ ${#SERVICES[@]} -gt 0 ]; then
                  SERVICES_STR=$(IFS=, ; echo "${SERVICES[*]}")
                  echo "Using detected services despite parsing failure: $SERVICES_STR" >&2
                  echo "should_deploy=true" >> $GITHUB_OUTPUT
                  echo "changed_folders=$SERVICES_STR" >> $GITHUB_OUTPUT
                else
                  echo "changed_folders=" >> $GITHUB_OUTPUT
                fi

                echo "target_branch=develop" >> $GITHUB_OUTPUT
                echo "keep_branch=false" >> $GITHUB_OUTPUT
              fi
              rm "$TEMP_OUTPUT"
            else
              # Set defaults if no message
              echo "should_deploy=false" >> $GITHUB_OUTPUT
              echo "is_fast_deploy=false" >> $GITHUB_OUTPUT
              echo "changed_folders=" >> $GITHUB_OUTPUT
              echo "target_branch=develop" >> $GITHUB_OUTPUT
              echo "keep_branch=false" >> $GITHUB_OUTPUT
            fi

            # Debug output
            echo "Command parsing results:"
            cat $GITHUB_OUTPUT

        - name: Debug Deploy Command Outputs
          run: |
            echo "🔍 Deploy Command Parse Results:"
            echo "================================"
            echo "📝 Original Command: ${{ inputs.triggered_by_comment && inputs.comment || github.event.comment.body }}"
            echo "🚀 Should Deploy: ${{ steps.check-commit.outputs.should_deploy }}"
            echo "🧪 Skip Tests (fail-tests): ${{ steps.check-commit.outputs.is_fast_deploy }}"
            echo "📂 Changed Folders: ${{ steps.check-commit.outputs.changed_folders }}"
            echo "🎯 Target Branch: ${{ steps.check-commit.outputs.target_branch }}"
            echo "🔒 Keep Branch: ${{ steps.check-commit.outputs.keep_branch }}"
            echo "================================"
            echo "🔄 Command Reconstruction Test:"
            # Test command reconstruction
            ORIGINAL_COMMAND="${{ inputs.comment }}"
            if [ -n "$ORIGINAL_COMMAND" ] && [[ "$ORIGINAL_COMMAND" =~ \[deploy:[^]]+\] ]]; then
              CLEANED_COMMAND=$(echo "$ORIGINAL_COMMAND" | sed -E 's/(\[|\]|@github-actions)//g' | tr -s ' ')
              IFS=':' read -ra PARTS <<< "$CLEANED_COMMAND"
              NEW_COMMAND="@github-actions [deploy:stage"
              for ((i=2; i<${#PARTS[@]}; i++)); do
                NEW_COMMAND="$NEW_COMMAND:${PARTS[i]}"
              done
              NEW_COMMAND="$NEW_COMMAND]"
              echo "Original: $ORIGINAL_COMMAND"
              echo "Reconstructed: $NEW_COMMAND"
            fi
            echo "================================"

        - name: Debug GitHub context
          id: debug-context # Ensure unique identifier
          if: env.ACT != 'true'
          run: |
            echo "Head Ref: ${{ github.head_ref }}"
            echo "github.event.pull_request.head.ref: ${{ github.event.pull_request.head.ref }}"
            echo "Event Name: ${{ github.event_name }}"
            echo "PR BASE_REF: ${{ env.BASE_REF }}"

    # ----------------------------------------
    # 🚢 Main Deployment Job
    # ----------------------------------------
    # Purpose: Handles the core deployment process to Google Cloud
    # Dependencies: Needs check-deploy-flag job to pass
    # Outputs: pr_number - The PR number being processed
    # Environment: Matches the base branch environment
    # ----------------------------------------
    build-deploy:
      needs: [select-runner, check-deploy-flag]
      if: |
        (inputs.triggered_by_comment == true ||
        needs.check-deploy-flag.outputs.should_deploy == 'true') &&
        (github.event_name == 'pull_request' ||
         github.event_name == 'workflow_dispatch')
      environment: ${{
        github.event_name == 'pull_request' && github.event.pull_request.base.ref ||
        github.event_name == 'workflow_dispatch' && inputs.base_ref ||
        'develop'
        }}
      env:
        CHANGED_FOLDERS: ${{
          inputs.changed_folders != '' && inputs.changed_folders ||
          needs.check-deploy-flag.outputs.changed_folders
          }}
      timeout-minutes: 342
      runs-on: ${{ needs.select-runner.outputs.runner }}
      outputs:
        pr_number: ${{ github.event.pull_request.number }}
        changed_folders: ${{ env.CHANGED_FOLDERS }}
      strategy:
        matrix:
          node-version: [20]

      steps:
        - name: Debug ref information
          if: needs.check-deploy-flag.outputs.is_fast_deploy != 'true'
          run: |
            echo "Event name: ${{ github.event_name }}"
            echo "GitHub ref: ${{ github.ref }}"
            echo "PR head ref: ${{ github.event.pull_request.head.ref }}"
            echo "Workflow dispatch ref: ${{ github.event.workflow_dispatch.ref }}"
            echo "Input base ref: ${{ inputs.base_ref }}"
            echo "Final ref being used: ${{
              github.event_name == 'pull_request' && github.event.pull_request.head.ref ||
              github.event_name == 'workflow_dispatch' && github.ref
            }}"

        - name: Debug Inherited Flags
          run: |
            echo "Debug conditions:"
            echo "Skip Tests (fail-tests) from check-deploy-flag: ${{ needs.check-deploy-flag.outputs.is_fast_deploy }}"
            echo "Skip Tests (fail-tests) from inputs: ${{ inputs.is_fast_deploy }}"
            echo "PR head ref: ${{ github.event.pull_request.head.ref }}"
            echo "Current ref: ${{ github.ref }}"

        - name: Output the PR number
          if: needs.check-deploy-flag.outputs.is_fast_deploy != 'true'
          id: get-pr-number
          run: |
            echo "PR_NUMBER=${{ github.event.pull_request.number }}" >> $GITHUB_ENV

        # Configure pnpm to allow all builds
        - name: Configure pnpm
          run: |
            echo "dangerously-allow-all-builds=true" >> .npmrc
            echo "public-hoist-pattern[]=*esbuild*" >> .npmrc
            echo "Created .npmrc with dangerously-allow-all-builds=true"

        # 🛠️ Setup required tools
        - name: Install git, docker
          # 📓 Github Actions docker image already has git and docker. ACT doesn't:
          #  - https://chatgpt.com/share/66ef4d68-1678-800b-b1b7-0757e35d154a
          # if: env.ACT == 'true'
          run: |
            if ! command -v git &> /dev/null; then
              echo "git is not installed, installing git..."
              sudo apt-get update -qq
              sudo apt-get install -qq -y git
            else
              echo "git is already installed ⭐️"
            fi

            if ! command -v docker &> /dev/null; then
              echo "docker is not installed, installing docker..."
              sudo apt-get update -qq
              sudo apt-get install -qq -y docker.io
            else
              echo "docker is already installed 🐋⭐️"
            fi

        # 📥 Get code and setup environment
        - name: Manual Repository Checkout - base_ref and PR branch
          env:
            GITHUB_TOKEN: ${{ secrets.ELEVATE_PR_PAT }}
            TARGET_REF: ${{
              github.event_name == 'pull_request' && github.event.pull_request.head.ref ||
              github.event_name == 'workflow_dispatch' && github.ref
              }}
            BASE_REF: ${{
              github.event_name == 'pull_request' && github.event.pull_request.base.ref ||
              github.event_name == 'workflow_dispatch' && inputs.base_ref ||
              'develop'
              }}
          run: |
            # Clean up everything in current directory
            rm -rf ./* ./.[!.]* ..?*

            # Function to clean up git refs
            cleanup_refs() {
              echo "🧹 Cleaning up git refs..."
              rm -f .git/refs/remotes/origin/*
              rm -f .git/refs/heads/*
              git for-each-ref --format='%(refname)' refs/heads/ | xargs -r git branch -D
              git for-each-ref --format='%(refname)' refs/remotes/origin/ | xargs -r git branch -dr
            }

            # Function to fetch with retry - optimized to only fetch specific branch
            fetch_branch() {
              local branch=$1
              local max_attempts=3
              local attempt=1

              while [ $attempt -le $max_attempts ]; do
                echo "🎯 Attempting to fetch ${branch} (attempt ${attempt}/${max_attempts})..."
                # Use --depth=1 to only fetch the latest commit
                if git fetch origin ${branch}:refs/remotes/origin/${branch} --depth=1; then
                  echo "✅ Successfully fetched ${branch}"
                  return 0
                fi

                echo "⚠️ Fetch failed, cleaning up and retrying..."
                cleanup_refs
                git gc --prune=now
                ((attempt++))
                sleep 5
              done

              echo "❌ Failed to fetch ${branch} after ${max_attempts} attempts"
              return 1
            }

            # Initialize fresh git repository
            git init

            # Configure git
            echo "⚙️ Configuring git..."
            git config --global --add safe.directory "${GITHUB_WORKSPACE}"
            git config --global init.defaultBranch main

            # Set up the remote with token in URL (Method 5)
            echo "🔄 Setting up remote..."
            git remote add origin "https://x-access-token:${GITHUB_TOKEN}@github.com/${GITHUB_REPOSITORY}"

            # Strip refs/heads/ prefix if present
            TARGET_REF=${TARGET_REF#refs/heads/}
            BASE_REF=${BASE_REF#refs/heads/}

            # Fetch branches with retry
            echo "📥 Fetching branches..."
            if ! fetch_branch ${TARGET_REF}; then
              echo "❌ Failed to fetch target branch"
              exit 1
            fi

            if ! fetch_branch ${BASE_REF}; then
              echo "❌ Failed to fetch base branch"
              exit 1
            fi

            # Checkout the target branch
            echo "📦 Checking out target branch: ${TARGET_REF}"
            if ! git checkout -B ${TARGET_REF} origin/${TARGET_REF}; then
              echo "❌ Failed to checkout target branch"
              exit 1
            fi

            # Initialize and update submodules
            echo "📦 Initializing submodules..."

            # Validate token before submodule initialization
            echo "🔑 Validating GitHub token for submodule access..."
            TOKEN_STATUS=$(curl -s -o /dev/null -w "%{http_code}" \
              -H "Authorization: token ${GITHUB_TOKEN}" \
              -H "Accept: application/vnd.github.v3+json" \
              "https://api.github.com/user")

            if [ "$TOKEN_STATUS" = "200" ]; then
              echo "✅ GitHub token is valid for submodule access"
            elif [ "$TOKEN_STATUS" = "401" ]; then
              echo "⚠️ GitHub token is invalid or expired for submodule access"
              echo "⚠️ Submodule initialization may fail, but we'll continue the deployment"
            else
              echo "⚠️ GitHub token validation returned unexpected status code: $TOKEN_STATUS"
            fi

            # Try to initialize submodules with direct URL embedding (Method 5)
            echo "🔄 Initializing submodules..."

            # Get submodule paths and URLs from .gitmodules if it exists
            if [ -f ".gitmodules" ]; then
              while IFS= read -r line; do
                if [[ $line =~ path\ =\ (.*) ]]; then
                  SUBMODULE_PATH="${BASH_REMATCH[1]}"
                  SUBMODULE_URL=$(git config -f .gitmodules --get "submodule.$SUBMODULE_PATH.url")

                  if [ -n "$SUBMODULE_URL" ]; then
                    # Extract the repository path from the URL
                    if [[ $SUBMODULE_URL =~ github\.com[:/](.+)(\.git)?$ ]]; then
                      REPO_PATH="${BASH_REMATCH[1]}"
                      # Remove .git suffix if present
                      REPO_PATH="${REPO_PATH%.git}"

                      # Create authenticated URL
                      AUTH_URL="https://x-access-token:${GITHUB_TOKEN}@github.com/${REPO_PATH}"

                      echo "📦 Cloning submodule $SUBMODULE_PATH from $REPO_PATH"
                      # Use --depth=1 to only fetch the latest commit
                      if git clone "$AUTH_URL" "$SUBMODULE_PATH" --depth=1; then
                        echo "✅ Successfully cloned submodule $SUBMODULE_PATH"
                      else
                        echo "⚠️ Warning: Failed to clone submodule $SUBMODULE_PATH, continuing anyway..."
                      fi
                    else
                      echo "⚠️ Warning: Could not parse GitHub repository path from URL: $SUBMODULE_URL"
                    fi
                  fi
                fi
              done < <(grep "path = " .gitmodules)
              echo "✅ Submodules initialization completed"
            else
              echo "ℹ️ No .gitmodules file found, skipping submodule initialization"
            fi

            # Verify checkout
            echo "🔍 Verification:"
            echo "Current branch: $(git branch --show-current)"
            echo "Current commit: $(git rev-parse HEAD)"
            echo "Remote URLs: "
            git remote -v

        # 📊 Analyze changed services
        - name: Determine Changed Folders
          id: determine_changed_folders
          env:
            GITHUB_EVENT_NAME: ${{ github.event_name }}
            INPUT_PR_NUMBER: ${{ inputs.pr_number }}
            GITHUB_BASE_REF: ${{ env.BASE_REF }}
            CHANGED_FOLDERS: ${{
              needs.check-deploy-flag.outputs.changed_folders != '' &&
              needs.check-deploy-flag.outputs.changed_folders ||
              env.CHANGED_FOLDERS
              }}
          run: |
            if [ -n "${{ env.CHANGED_FOLDERS }}" ]; then
              echo "Using pre-defined folders: ${{ env.CHANGED_FOLDERS }}"
              echo "CHANGED_FOLDERS=${{ env.CHANGED_FOLDERS }}" >> $GITHUB_ENV
            else
              echo "Determining changed folders..."
              chmod +x deploy/steps/1.changed-git-folders.sh
              deploy/steps/1.changed-git-folders.sh >> $GITHUB_ENV
            fi

        # 💾 Setup Node.js first
        - name: Use Node.js ${{ matrix.node-version }}
          uses: actions/setup-node@v4
          with:
            node-version: ${{ matrix.node-version }}
            registry-url: 'https://npm.pkg.github.com'
            scope: '@divinci-ai'

        # Install pnpm after Node.js is set up
        - name: Install pnpm
          uses: pnpm/action-setup@v4
          with:
<<<<<<< HEAD
            version: 10.11.0
=======
            version: 10.12.1
>>>>>>> WA-170_MCP
            run_install: false

        # Setup pnpm config
        - name: Setup pnpm config
          run: pnpm config set store-dir "$PNPM_CACHE_FOLDER"

        # Verify PNPM Cache Directory exists
        - name: Verify PNPM Cache Directory
          run: |
            PNPM_STORE_PATH="$( pnpm store path --silent )"
            if [ ! -d "$PNPM_STORE_PATH" ]; then
              echo "PNPM store directory does not exist, creating it."
              mkdir -p "$PNPM_STORE_PATH"
            else
              echo "PNPM store directory exists."
            fi

        # Enable pnpm caching after store directory is configured
        - name: Enable pnpm caching
          uses: actions/setup-node@v4
          with:
            node-version: ${{ matrix.node-version }}
            cache: "pnpm"
            cache-dependency-path: |
              pnpm-lock.yaml
              workspace/**/pnpm-lock.yaml
            registry-url: 'https://npm.pkg.github.com'
            scope: '@divinci-ai'

        # Set up environment variables for authentication
        - name: Set up environment variables
          run: |
            echo "NODE_AUTH_TOKEN=${{ secrets.NODE_AUTH_TOKEN }}" >> $GITHUB_ENV

        # Create or update .npmrc files
        - name: Create .npmrc files
          run: |
            # Root .npmrc
            echo "//npm.pkg.github.com/:_authToken=${{ secrets.NODE_AUTH_TOKEN }}" > .npmrc
            echo "@divinci-ai:registry=https://npm.pkg.github.com" >> .npmrc

            # Workspace .npmrc
            echo "//npm.pkg.github.com/:_authToken=${{ secrets.NODE_AUTH_TOKEN }}" > workspace/.npmrc
            echo "@divinci-ai:registry=https://npm.pkg.github.com" >> workspace/.npmrc

            # Workspace/clients/tests .npmrc
            mkdir -p workspace/clients/tests
            echo "//npm.pkg.github.com/:_authToken=${{ secrets.NODE_AUTH_TOKEN }}" > workspace/clients/tests/.npmrc
            echo "@divinci-ai:registry=https://npm.pkg.github.com" >> workspace/clients/tests/.npmrc

        # 🧪 Run unit tests for changed folders
        - name: Run Unit Tests for Changed Folders
          id: run-tests
          run: |
            echo "📂 Running unit tests for changed folders: ${{ env.CHANGED_FOLDERS }}"

            # Create a simple script to run tests
            echo '#!/bin/bash
            echo "🔍 Starting unit tests"
            echo "📂 Changed folders: ${{ env.CHANGED_FOLDERS }}"

            # Run tests for each changed folder
            IFS="," read -ra FOLDERS <<< "${{ env.CHANGED_FOLDERS }}"
            for folder in "${FOLDERS[@]}"; do
              echo -e "\n📂 Processing folder: $folder"

              # Check if folder has package.json
              if [ -e "$folder/package.json" ]; then
                echo "🔍 DEBUG: package.json exists in $folder"
                echo "🧪 Running unit tests in $folder"

                # Create .npmrc file for GitHub Packages authentication
                echo "//npm.pkg.github.com/:_authToken=${{ secrets.NODE_AUTH_TOKEN }}" > "$folder/.npmrc"
                echo "@divinci-ai:registry=https://npm.pkg.github.com" >> "$folder/.npmrc"

                # Check if the package has a test script
                if grep -q "\"test\"" "$folder/package.json"; then
                  echo "📋 Found test script in $folder/package.json"

                  # Install dependencies first
                  echo "📦 Installing dependencies in $folder"
                  (cd "$folder" && pnpm install --no-frozen-lockfile --dangerously-allow-all-builds || true)

                  # Run the tests (always continue even if tests fail)
                  echo "🧪 Running tests in $folder"
                  (cd "$folder" && pnpm test || true)
                  echo "✅ Tests completed in $folder"
                else
                  echo "🔍 DEBUG: No test script found in $folder/package.json"
                  echo "⚠️ No test script found in $folder/package.json"
                fi
              else
                echo "🔍 DEBUG: No package.json in $folder"
                echo "🙅🏻‍♂️ No package.json in $folder"
              fi
            done

            echo -e "\n📊 Test Results: All tests completed"
            ' > run-unit-tests.sh

            # Make the script executable
            chmod +x run-unit-tests.sh

            # Run the script
            ./run-unit-tests.sh

            # Always set all_passed to true to avoid failing the build
            echo "all_passed=true" >> $GITHUB_OUTPUT
            echo "test_suites=${{ env.CHANGED_FOLDERS }}" >> $GITHUB_OUTPUT

        # 📝 Summarize test results
        - name: Summarize Test Results
          id: test-results
          run: |
            # Always pass the test summary step
            echo "✅ Tests completed! (Note: Test failures are not blocking the build)"
            echo "all_passed=true" >> $GITHUB_OUTPUT

        # 📦 Setup Node.js and dependencies
        - name: Exit if no folders changed
          run: |
            if [[ -z "${{ env.CHANGED_FOLDERS }}" ]]; then
              if [[ "${{ inputs.triggered_by_comment }}" == "true" ]]; then
                echo "⚠️ No folders specified in deployment command"

                # Check if the comment contains specific service flags
                if [[ "${{ inputs.comment }}" =~ (pyannote|ffmpeg|open-parse) ]]; then
                  echo "🔍 Detected specific service flags in comment: ${{ inputs.comment }}"

                  # Initialize an array to hold the service paths
                  SERVICES=()

                  # Add services based on the flags in the comment
                  if [[ "${{ inputs.comment }}" =~ pyannote ]]; then
                    SERVICES+=("workspace/workers/audio-speaker-diarization@pyannote")
                    echo "✅ Adding pyannote service path"
                  fi

                  if [[ "${{ inputs.comment }}" =~ ffmpeg ]]; then
                    SERVICES+=("workspace/workers/audio-splitter@ffmpeg")
                    echo "✅ Adding ffmpeg service path"
                  fi

                  if [[ "${{ inputs.comment }}" =~ open-parse ]]; then
                    SERVICES+=("workspace/workers/open-parse")
                    echo "✅ Adding open-parse service path"
                  fi

                  # Join the services with commas
                  CHANGED_FOLDERS=$(IFS=,; echo "${SERVICES[*]}")
                  echo "CHANGED_FOLDERS=$CHANGED_FOLDERS" >> $GITHUB_ENV
                else
                  # For comment-triggered deployments without specific service flags, falling back to default behavior
                  echo "CHANGED_FOLDERS=workspace/servers/public-api,workspace/clients/web" >> $GITHUB_ENV
                fi
              else
                echo "❌ No folders changed and no folders specified. Exiting."
                exit 1
              fi
            else
              echo "📂 Deploying changes in folders: ${{ env.CHANGED_FOLDERS }}"
            fi

        # 💾 Cache node_modules
        # Cache node modules - Only on GitHub-hosted runners
        - name: Cache node modules
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            !contains(runner.name, 'self-hosted')
          uses: actions/cache@v3
          id: cache-deps
          with:
            path: |
              **/node_modules
              ~/.npm
              ~/.cache
              !**/temp
            key: ${{ runner.os }}-modules-${{ hashFiles('**/pnpm-lock.yaml') }}
            restore-keys: |
              ${{ runner.os }}-modules-

        # Ensure service-info.js is executable
        - name: Set up deployment scripts
          run: |
            chmod +x deploy/util/service-info.js
            chmod +x deploy/steps/*.sh

        # Add step to validate service configuration
        - name: Validate Service Configuration
          run: |
            # Validate that service-info.js can read the configuration
            echo "🔍 Validating service configuration..."

            # Test basic service info retrieval
            node deploy/util/service-info.js getServiceContainerFolders
            if [ $? -ne 0 ]; then
              echo "❌ Failed to read service configuration"
              exit 1
            fi

            # Validate Docker build info for each changed service
            IFS=',' read -ra FOLDERS <<< "${{ env.CHANGED_FOLDERS }}"

            # Create a filtered list of folders to validate
            FILTERED_FOLDERS=()
            for folder in "${FOLDERS[@]}"; do
              echo "📦 Checking folder: $folder..."

              # Skip server-models folder as it's part of the resources image
              if [[ "$folder" == "workspace/resources/server-models" ]]; then
                echo "⏭️ Skipping server-models folder validation (handled by resources image)."
                continue
              fi

              # Skip resource folders as they're handled by resources.ci.Dockerfile
              if [[ "$folder" == */resources/* ]]; then
                echo "⏭️ Skipping resource folder validation for: $folder (handled by resources image)."
                continue
              fi

              # Add folder to filtered list
              FILTERED_FOLDERS+=("$folder")
            done

            # Validate each filtered folder
            for folder in "${FILTERED_FOLDERS[@]}"; do
              echo "📦 Validating configuration for $folder..."

              # Test Docker build info
              node deploy/util/service-info.js getDockerBuildInfo "$folder" "${{ needs.check-deploy-flag.outputs.target_branch }}"
              if [ $? -ne 0 ]; then
                echo "❌ Invalid configuration for $folder"
                exit 1
              fi

              # Test deployment info
              node deploy/util/service-info.js getDeploymentInfo "$folder" "${{ needs.check-deploy-flag.outputs.target_branch }}"
              if [ $? -ne 0 ]; then
                echo "❌ Invalid deployment configuration for $folder"
                exit 1
              fi
            done

            echo "✅ Service configuration validation complete"

        # Verify Docker is working and determine if we should use Buildx
        # 🐋 Docker setup and verification
        - name: Verify Docker and determine build strategy
          id: docker-verify
          timeout-minutes: 2  # Add a timeout to prevent hanging
          run: |
            echo "Verifying Docker installation..."

            # Check Docker version
            echo "Checking Docker version..."
            docker version

            # Check Docker info
            echo "Checking Docker info..."
            docker info

            # Determine if we're running on a self-hosted runner
            echo "Checking runner type..."
            if [[ "${{ runner.name }}" == *"self-hosted"* ]]; then
              echo "Running on self-hosted runner, using standard Docker build"
              echo "use_buildx=false" >> $GITHUB_OUTPUT
            else
              echo "Running on GitHub-hosted runner, using Docker Buildx"
              echo "use_buildx=true" >> $GITHUB_OUTPUT
            fi

            echo "Docker verification complete"



        # 💾 Cache Docker layers - Only on GitHub-hosted runners
        - name: Cache Docker layers
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            steps.docker-verify.outputs.use_buildx == 'true'
          uses: actions/cache@v3
          id: cache-docker
          with:
            path: /tmp/.buildx-cache
            key: ${{ runner.os }}-buildx-${{ github.sha }}
            restore-keys: |
              ${{ runner.os }}-buildx-

        - name: Report cache status
          if: needs.check-deploy-flag.outputs.is_fast_deploy != 'true'
          run: |
            if [[ "${{ steps.docker-verify.outputs.use_buildx }}" == "true" ]]; then
              echo "🐋🛢️ Docker cache hit: ${{ steps.cache-docker.outputs.cache-hit }}"
            else
              echo "🐋🛢️ Docker cache disabled for self-hosted runners to prevent hanging"
            fi

        # Cache gcloud SDK
        - name: Cache gcloud SDK
          if: needs.check-deploy-flag.outputs.is_fast_deploy != 'true'
          uses: actions/cache@v3
          id: cache-gcloud
          with:
            path: ~/.config/gcloud
            key: ${{ runner.os }}-gcloud-${{ hashFiles('**/*.yaml') }}
            restore-keys: |
              ${{ runner.os }}-gcloud-

          # 🔐 Google Cloud authentication
        - id: "auth"
          name: "Authenticate to Google Cloud"
          uses: "google-github-actions/auth@v2"
          with:
            credentials_json: ${{ secrets.GOOGLE_CREDENTIALS || env.GOOGLE_CREDENTIALS || '' }}

        - name: "Set up Cloud SDK"
          uses: "google-github-actions/setup-gcloud@v2"
          with:
            install_components: "beta,docker-credential-gcr"

        - name: Validate Environment Configuration
          if: needs.check-deploy-flag.outputs.is_fast_deploy != 'true'
          run: |
            # Print current environment variables for debugging
            echo "Current environment:"
            echo "WEB_CLIENT_HOST: ${{ env.WEB_CLIENT_HOST }}"
            echo "ENVIRONMENT: ${{ env.ENVIRONMENT }}"
            echo "GCP_SA_KEY is set: ${{ env.GCP_SA_KEY != '' }}"

            # Run the validation script
            chmod +x deploy/steps/0.validate-environment.sh
            ./deploy/steps/0.validate-environment.sh ${{ env.ENVIRONMENT }}

        - name: Authenticate to GCR
          run: |
            gcloud auth configure-docker us-docker.pkg.dev --quiet

        # 🏗️ Build and push Docker images
        - name: Build and Push Docker images to GCP Artifact Registry
          timeout-minutes: 120  # Increased timeout for large dependencies like PyTorch/CUDA
          env:
            DD_API_KEY: ${{ secrets.DD_API_KEY }}
            DD_API_ID: ${{ secrets.DD_API_ID }}
            USE_BUILDX: ${{ steps.docker-verify.outputs.use_buildx }}
            DOCKER_BUILDKIT: ${{ steps.docker-verify.outputs.use_buildx == 'true' && '1' || '0' }}
            COMPOSE_DOCKER_CLI_BUILD: ${{ steps.docker-verify.outputs.use_buildx == 'true' && '1' || '0' }}
            RUNNER_NAME: ${{ runner.name }}
          run: |
            if [[ "${{ steps.docker-verify.outputs.use_buildx }}" == "true" ]]; then
              # Use Buildx for GitHub-hosted runners
              echo "🚀 Using Docker Buildx for build..."

              # Set up Buildx
              docker buildx version
              docker buildx create --use

              # Run the Buildx build script
              chmod +x ./deploy/steps/2.docker-build.sh

              # Run the build script (without timeout)
              if ./deploy/steps/2.docker-build.sh "${{ env.CHANGED_FOLDERS }}" "${{ env.ENVIRONMENT }}" "${{ env.WEB_CLIENT_HOST }}"; then
                echo "✅ Docker Buildx build completed successfully"
              else
                exit_code=$?
                echo "❌ Docker build failed with exit code $exit_code"
                exit $exit_code
              fi
            else
              # Use standard Docker for self-hosted runners
              echo "🚀 Using standard Docker build (no Buildx)..."

              # Create no-buildx version of the script if it doesn't exist
              if [ ! -f "./deploy/steps/2.docker-build-no-buildx.sh" ]; then
                echo "Creating no-buildx version of the build script..."
                cp ./deploy/steps/2.docker-build.sh ./deploy/steps/2.docker-build-no-buildx.sh
                sed -i 's/docker buildx build/docker build/g' ./deploy/steps/2.docker-build-no-buildx.sh
                sed -i 's/--cache-from.*//g' ./deploy/steps/2.docker-build-no-buildx.sh
                sed -i 's/--cache-to.*//g' ./deploy/steps/2.docker-build-no-buildx.sh
                sed -i 's/--builder.*//g' ./deploy/steps/2.docker-build-no-buildx.sh
                sed -i 's/--output type=registry//g' ./deploy/steps/2.docker-build-no-buildx.sh
                # Add push command after build
                sed -i '/Successfully Built/i\    # Push the image\n    docker push "${registry_url}"' ./deploy/steps/2.docker-build-no-buildx.sh
              fi

              # Make the script executable
              chmod +x ./deploy/steps/2.docker-build-no-buildx.sh

              # Run the build script (without timeout)
              if ./deploy/steps/2.docker-build-no-buildx.sh "${{ env.CHANGED_FOLDERS }}" "${{ env.ENVIRONMENT }}" "${{ env.WEB_CLIENT_HOST }}"; then
                echo "✅ Standard Docker build completed successfully"
              else
                exit_code=$?
                echo "❌ Docker build failed with exit code $exit_code"
                exit $exit_code
              fi
            fi
        # 🚀 Deploy to Cloud Run
        - name: Deploy Docker images to GCP Cloud Run
          env:
            DD_API_KEY: ${{ secrets.DD_API_KEY }}
            DD_API_ID: ${{ secrets.DD_API_ID }}
          run: |
            chmod +x ./deploy/steps/3.deploy-new.sh
            ./deploy/steps/3.deploy-new.sh "${{ env.CHANGED_FOLDERS }}" "${{ env.ENVIRONMENT }}"

        # Add error handling for service-info.js related issues
        - name: Handle Service Configuration Errors
          if: failure()
          run: |
            echo "::error::Service configuration validation failed. Please check service-info.js and config.json for errors."
            exit 1

    # ----------------------------------------
    # 🧪 API Testing Job
    # ----------------------------------------
    # Purpose: Runs integration tests against deployed services
    # Dependencies: Needs build-deploy job to complete
    # Environment: Uses self-hosted runner with Docker support
    # Test Setup: Configures Redis, MongoDB, and test environment
    # ----------------------------------------
    api-tests:
      needs: [select-runner, check-deploy-flag, build-deploy]
      runs-on: ${{ needs.select-runner.outputs.runner }}
      timeout-minutes: 342
      environment: ${{
        (github.event_name == 'pull_request' && github.event.pull_request.base.ref == 'main') && 'production' ||
        (github.event_name == 'pull_request' && github.event.pull_request.base.ref == 'stage') && 'staging' ||
        (github.event_name == 'workflow_dispatch' && inputs.base_ref == 'main') && 'production' ||
        (github.event_name == 'workflow_dispatch' && inputs.base_ref == 'stage') && 'staging' ||
        'develop'
        }}
      env:
        CHANGED_FOLDERS: ${{ needs.build-deploy.outputs.changed_folders || github.event.inputs.changed_folders }}
        CI: "1"
        PR_NUMBER: ${{ needs.build-deploy.outputs.pr_number }}
        PRIVATE_KEYS_FOLDER: "api-test"

        NODE_ENV: ${{
          github.base_ref == 'main' && 'production' ||
          github.base_ref == 'stage' && 'staging' ||
          github.base_ref == 'develop' && 'development' ||
          'development' }}

        ENVIRONMENT: ${{
          github.base_ref == 'main' && 'production' ||
          github.base_ref == 'stage' && 'staging' ||
          'develop' }}

        API_IS_SECURE: 1
        API_HOST: ${{
          github.base_ref == 'stage' && 'api.stage.divinci.app' ||
          github.base_ref == 'main' && 'api.divinci.app' ||
          'api.dev.divinci.app' }}

        API_LIVE_IS_SECURE: 1
        API_LIVE_HOST: ${{
          github.base_ref == 'stage' && 'live.stage.divinci.app' ||
          github.base_ref == 'main' && 'live.divinci.app' ||
          'live.dev.divinci.app' }}

        WEB_CLIENT_IS_SECURE: 1
        WEB_CLIENT_HOST: ${{
          github.base_ref == 'stage' && 'chat.stage.divinci.app' ||
          github.base_ref == 'main' && 'chat.divinci.app' ||
          'chat.dev.divinci.app' }}

        EMBED_CLIENT_IS_SECURE: 1
        EMBED_CLIENT_HOST: ${{
          github.base_ref == 'stage' && 'embed.stage.divinci.app' ||
          github.base_ref == 'main' && 'embed.divinci.app' ||
          'embed.dev.divinci.app' }}

        AUTH0_CLIENT_SECRET: ${{ secrets.AUTH0_CLIENT_SECRET }}
        AUTH0_CLIENT_ID: ${{ secrets.AUTH0_CLIENT_ID }}
        AUTH0_CLIENT_DOMAIN: ${{ secrets.AUTH0_CLIENT_DOMAIN }}
        AUTH0_AUDIENCE: ${{ secrets.AUTH0_AUDIENCE }}

        CF_ACCESS_CLIENT_ID: ${{ secrets.CF_ACCESS_CLIENT_ID }}
        CF_ACCESS_CLIENT_SECRET: ${{ secrets.CF_ACCESS_CLIENT_SECRET }}

      strategy:
        matrix:
          node-version: [20]

      steps:
        - name: Setup job
          id: setup
          run: |
            echo "BRANCH_NAME=${GITHUB_REF##*/}" >> $GITHUB_ENV
            echo "PULL_REQUEST_SHA=$GITHUB_SHA" >> $GITHUB_ENV

        - name: Debug Inherited Flags
          run: |
            echo "Debug conditions:"
            echo "is_fast_deploy from check-deploy-flag: ${{ needs.check-deploy-flag.outputs.is_fast_deploy }}"
            echo "is_fast_deploy from inputs: ${{ inputs.is_fast_deploy }}"
            echo "PR head ref: ${{ github.event.pull_request.head.ref }}"
            echo "Current ref: ${{ github.ref }}"

        - name: Setup debug logging
          if: inputs.debug_mode &&
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          run: |
            echo "ACTIONS_STEP_DEBUG=true" >> $GITHUB_ENV
            echo "Debug mode enabled"

        - name: Clear caches if requested
          if: ${{ inputs.clear_cache }}
          run: |
            gh extension install actions/gh-actions-cache

            REPO=${{ github.repository }}
            BRANCH=${{ github.ref }}

            cacheKeys=$(gh actions-cache list -R $REPO -B $BRANCH | cut -f 1)

            set +e
            for cacheKey in $cacheKeys
            do
                gh actions-cache delete $cacheKey -R $REPO -B $BRANCH --confirm
            done
            set -e
          env:
            GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

        - name: Prevent runner cleanup
          run: |
            echo "ACTIONS_RUNNER_HOOK_JOB_STARTED=true" >> $GITHUB_ENV
            echo "ACTIONS_RUNNER_HOOK_JOB_COMPLETED=true" >> $GITHUB_ENV

        - name: Create Docker network
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          run: |
            docker network create test-network || true

        - name: Manual Repository Checkout - PR branch and submodules
          env:
            GITHUB_TOKEN: ${{ secrets.ELEVATE_PR_PAT }}
            TARGET_REF: ${{
              github.event_name == 'pull_request' && github.event.pull_request.head.ref ||
              github.event_name == 'workflow_dispatch' && github.ref
              }}
          run: |
            # Clean up everything in current directory
            rm -rf ./* ./.[!.]* ..?*

            # Initialize fresh git repository
            git init

            # Configure git
            git config --global --add safe.directory "${GITHUB_WORKSPACE}"
            git config --global init.defaultBranch main

            # Set up the remote with token in URL (Method 5)
            git remote add origin "https://x-access-token:${GITHUB_TOKEN}@github.com/${GITHUB_REPOSITORY}"

            # Strip refs/heads/ prefix if present
            TARGET_REF=${TARGET_REF#refs/heads/}

            # Function to fetch with retry and better error handling
            fetch_with_retry() {
              local branch=$1
              local max_attempts=3
              local attempt=1

              while [ $attempt -le $max_attempts ]; do
                echo "🎯 Attempting to fetch ${branch} (attempt ${attempt}/${max_attempts})..."

                if git fetch --depth=1 origin "${branch}:refs/remotes/origin/${branch}"; then
                  echo "✅ Successfully fetched ${branch}"
                  return 0
                fi

                echo "⚠️ Fetch failed, cleaning up and retrying..."
                git gc --prune=now
                ((attempt++))
                sleep 5
              done

              return 1
            }

            # Fetch branch with retry
            if ! fetch_with_retry "${TARGET_REF}"; then
              echo "❌ Failed to fetch ${TARGET_REF}"
              exit 1
            fi

            # Checkout the target branch
            if ! git checkout -b "${TARGET_REF}" "origin/${TARGET_REF}"; then
              echo "❌ Failed to checkout ${TARGET_REF}"
              exit 1
            fi

            # Initialize submodules with direct URL embedding (Method 5)
            echo "📦 Initializing submodules..."

            # Get submodule paths and URLs from .gitmodules if it exists
            if [ -f ".gitmodules" ]; then
              max_attempts=3

              while IFS= read -r line; do
                if [[ $line =~ path\ =\ (.*) ]]; then
                  SUBMODULE_PATH="${BASH_REMATCH[1]}"
                  SUBMODULE_URL=$(git config -f .gitmodules --get "submodule.$SUBMODULE_PATH.url")

                  if [ -n "$SUBMODULE_URL" ]; then
                    # Extract the repository path from the URL
                    if [[ $SUBMODULE_URL =~ github\.com[:/](.+)(\.git)?$ ]]; then
                      REPO_PATH="${BASH_REMATCH[1]}"
                      # Remove .git suffix if present
                      REPO_PATH="${REPO_PATH%.git}"

                      # Create authenticated URL
                      AUTH_URL="https://x-access-token:${GITHUB_TOKEN}@github.com/${REPO_PATH}"

                      echo "📦 Cloning submodule $SUBMODULE_PATH from $REPO_PATH"

                      attempt=1
                      while [ $attempt -le $max_attempts ]; do
                        echo "🔄 Attempting to clone submodule $SUBMODULE_PATH (attempt ${attempt}/${max_attempts})..."

                        # Use --depth=1 to only fetch the latest commit
                        if git clone "$AUTH_URL" "$SUBMODULE_PATH" --depth=1; then
                          echo "✅ Successfully cloned submodule $SUBMODULE_PATH"
                          break
                        fi

                        echo "⚠️ Submodule clone failed, retrying..."
                        rm -rf "$SUBMODULE_PATH"
                        ((attempt++))

                        if [ $attempt -gt $max_attempts ]; then
                          echo "❌ Failed to clone submodule $SUBMODULE_PATH after ${max_attempts} attempts"
                          exit 1
                        fi

                        sleep 5
                      done
                    else
                      echo "⚠️ Warning: Could not parse GitHub repository path from URL: $SUBMODULE_URL"
                    fi
                  fi
                fi
              done < <(grep "path = " .gitmodules)
              echo "✅ Submodules initialization completed"
            else
              echo "ℹ️ No .gitmodules file found, skipping submodule initialization"
            fi

            # Debug submodules
            echo "🔍 Git submodule status:"
            git submodule status

            echo "📁 Git submodule configuration:"
            cat .gitmodules || echo "No .gitmodules file found"

            echo "📂 Repository root contents:"
            ls -la

            echo "📂 Private-keys directory structure:"
            ls -la private-keys/ || echo "private-keys directory not found"

            echo "📂 Private-keys subfolders:"
            find private-keys -type d || echo "No private-keys subfolders found"

            echo "📂 Private-keys/api-test contents:"
            ls -la private-keys/api-test/ || echo "api-test directory not found"

            echo "🔍 Git status:"
            git status

            # Verify submodules
            echo "🔍 Verifying submodules:"
            git submodule status

            # List contents of private-keys directory
            echo "📂 Contents of private-keys directory:"
            ls -la private-keys/ || echo "private-keys directory not found"

            # Verify checkout
            echo "🔍 Current state:"
            echo "Branch: $(git branch --show-current)"
            echo "Commit: $(git rev-parse HEAD)"

        - name: Scan for available ports
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          run: |
            chmod +x .github/scripts/mongo-redis-ports.sh
            .github/scripts/mongo-redis-ports.sh

        - name: Set environment variables for ports
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          run: |
            echo "MONGODB_PORT=${MONGO_PORT}" >> $GITHUB_ENV
            echo "REDIS_PORT=${REDIS_PORT}" >> $GITHUB_ENV

        - name: Start Redis Container
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          run: |
            docker stop redis || true
            docker rm redis || true
            docker run -d --name redis \
              --network test-network \
              -p "${REDIS_PORT}:6379" \
              redis
            echo "Waiting for Redis to be ready..."
            for i in $(seq 1 30); do
              if docker exec redis redis-cli ping > /dev/null 2>&1; then
                echo "✅ Redis is ready!"
                break
              fi
              if [ $i -eq 30 ]; then
                echo "❌ Redis failed to start."
                exit 1
              fi
              sleep 1
            done

        - name: Start MongoDB Container
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          run: |
            docker stop mongo || true
            docker rm mongo || true
            docker run -d --name mongo \
              --network test-network \
              -p "${MONGO_PORT}:27017" \
              -e MONGO_INITDB_ROOT_USERNAME=root \
              -e MONGO_INITDB_ROOT_PASSWORD=example \
              -e MONGO_INITDB_DATABASE=divinci-local \
              mongo
            echo "Waiting for MongoDB to be ready..."
            for i in $(seq 1 30); do
              if docker exec mongo mongosh --eval "db.runCommand({ ping: 1 })" --quiet \
                --username root --password example --authenticationDatabase admin > /dev/null 2>&1; then
                echo "✅ MongoDB is ready! "
                break
              fi
              if [ $i -eq 30 ]; then
                echo "❌ MongoDB failed to start."
                exit 1
              fi
              sleep 1
            done

        # Setup Node.js first
        - name: Use Node.js ${{ matrix.node-version }}
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          uses: actions/setup-node@v4
          with:
            node-version: ${{ matrix.node-version }}
            registry-url: 'https://npm.pkg.github.com'
            scope: '@divinci-ai'

        # Install pnpm after Node.js is set up
        - name: Install pnpm
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          uses: pnpm/action-setup@v4
          with:
            version: 10.11.0
            run_install: false

        # Setup pnpm config
        - name: Setup pnpm config
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          run: pnpm config set store-dir "$PNPM_CACHE_FOLDER"

        # Verify PNPM Cache Directory exists
        - name: Verify PNPM Cache Directory
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          run: |
            PNPM_STORE_PATH="$( pnpm store path --silent )"
            if [ ! -d "$PNPM_STORE_PATH" ]; then
              echo "PNPM store directory does not exist, creating it."
              mkdir -p "$PNPM_STORE_PATH"
            else
              echo "PNPM store directory exists."
            fi

        # Enable pnpm caching after store directory is configured
        - name: Enable pnpm caching
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          uses: actions/setup-node@v4
          with:
            node-version: ${{ matrix.node-version }}
            cache: "pnpm"
            cache-dependency-path: |
              **/pnpm-lock.yaml
            registry-url: 'https://npm.pkg.github.com'
            scope: '@divinci-ai'

        # Set up environment variables for authentication
        - name: Set up environment variables
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          run: |
            echo "NODE_AUTH_TOKEN=${{ secrets.NODE_AUTH_TOKEN }}" >> $GITHUB_ENV

        # Create or update .npmrc files
        - name: Create .npmrc files
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          run: |
            # Root .npmrc
            echo "//npm.pkg.github.com/:_authToken=${{ secrets.NODE_AUTH_TOKEN }}" > .npmrc
            echo "@divinci-ai:registry=https://npm.pkg.github.com" >> .npmrc

            # Workspace .npmrc
            echo "//npm.pkg.github.com/:_authToken=${{ secrets.NODE_AUTH_TOKEN }}" > workspace/.npmrc
            echo "@divinci-ai:registry=https://npm.pkg.github.com" >> workspace/.npmrc

            # Workspace/clients/tests .npmrc
            mkdir -p workspace/clients/tests
            echo "//npm.pkg.github.com/:_authToken=${{ secrets.NODE_AUTH_TOKEN }}" > workspace/clients/tests/.npmrc
            echo "@divinci-ai:registry=https://npm.pkg.github.com" >> workspace/clients/tests/.npmrc

        # Cache shared resources (models, utils, etc.)
        - name: Cache shared resources
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          uses: actions/cache@v3
          id: cache-resources
          with:
            path: |
              workspace/resources/**/dist
              workspace/resources/**/node_modules
            key: ${{ runner.os }}-resources-${{ hashFiles('workspace/resources/**/package-lock.json', 'workspace/resources/**/tsconfig.json') }}
            restore-keys: |
              ${{ runner.os }}-resources-

        # Cache for all node_modules - Only on GitHub-hosted runners
        - name: Cache node modules
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true' &&
            !contains(runner.name, 'self-hosted')
          uses: actions/cache@v3
          id: cache-deps
          with:
            path: |
              **/node_modules
              ~/.pnpm-store
              ~/.cache
              !**/temp
            key: ${{ runner.os }}-pnpm-${{ hashFiles('**/pnpm-lock.yaml') }}-${{ hashFiles('**/tsconfig.json') }}
            restore-keys: |
              ${{ runner.os }}-pnpm-${{ hashFiles('**/pnpm-lock.yaml') }}-
              ${{ runner.os }}-pnpm-

        # Cache TypeScript incremental builds
        - name: Cache TypeScript incremental builds
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          uses: actions/cache@v3
          id: cache-tsbuildinfo
          with:
            path: |
              **/*.tsbuildinfo
              **/tsconfig.tsbuildinfo
            key: ${{ runner.os }}-tsbuildinfo-${{ hashFiles('**/tsconfig.json') }}
            restore-keys: |
              ${{ runner.os }}-tsbuildinfo-

        # Install dependencies if cache miss or on self-hosted runner
        - name: Install dependencies
          if: |
            (needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true') &&
            (contains(runner.name, 'self-hosted') || steps.cache-deps.outputs.cache-hit != 'true')
          working-directory: workspace/clients/tests
          env:
            NODE_ENV: development
          run: |
            # Install dependencies with pnpm
            pnpm install

            # Run prepare script
            pnpm run prepare

        # Cache test client build output
        - name: Cache test client build
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          uses: actions/cache@v3
          id: cache-test-client
          with:
            path: |
              workspace/clients/tests/dist
              workspace/clients/tests/build
            key: ${{ runner.os }}-test-client-${{ hashFiles('workspace/clients/tests/package-lock.json', 'workspace/clients/tests/tsconfig.json') }}
            restore-keys: |
              ${{ runner.os }}-test-client-

        # Update the Playwright cache step
        - name: Cache Playwright browsers
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          uses: actions/cache@v3
          id: playwright-cache
          with:
            path: |
              ~/.cache/ms-playwright
              **/node_modules/playwright
              **/node_modules/playwright-core
            key: ${{ runner.os }}-playwright-${{ hashFiles('**/package-lock.json') }}
            restore-keys: |
              ${{ runner.os }}-playwright-

        # Then installation step that leverages cache status
        - name: Install Playwright Browser
          # Only run full installation if cache miss
          if: steps.playwright-cache.outputs.cache-hit != 'true' &&
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          run: |
            echo "Cache miss - installing Chromium..."
            pnpm exec playwright install --with-deps chromium

        # Add a verification step that runs regardless of cache hit/miss
        - name: Verify Playwright Installation
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          run: |
            if ! pnpm exec playwright install --dry-run chromium >/dev/null 2>&1; then
              echo "❌ Chromium verification failed - forcing fresh install"
              pnpm exec playwright install --with-deps chromium
            else
              echo "✅ Chromium installation verified"
              pnpm exec playwright --version
            fi

        # Cache environment files
        - name: Cache environment files
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          uses: actions/cache@v3
          id: cache-env
          with:
            path: |
              workspace/servers/test-api/env
              workspace/clients/tests/env
            key: ${{ runner.os }}-env-${{ github.sha }}
            restore-keys: |
              ${{ runner.os }}-env-

        - name: Setup test-api environment
          if: steps.cache-env.outputs.cache-hit != 'true' &&
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          run: |
            mkdir -p workspace/servers/test-api/env/credentials
            chmod -R 755 workspace/servers/test-api/env

        - name: Load environment variables and credentials
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          run: |
            chmod +x .github/scripts/load-env.sh
            .github/scripts/load-env.sh private-keys/api-test

        # Cache test-api build
        - name: Cache test-api build
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          uses: actions/cache@v3
          id: cache-test-api
          with:
            path: |
              workspace/servers/test-api/dist
              workspace/servers/test-api/node_modules
            key: ${{ runner.os }}-test-api-${{ hashFiles('workspace/servers/test-api/package-lock.json', 'workspace/servers/test-api/tsconfig.json') }}
            restore-keys: |
              ${{ runner.os }}-test-api-

        - name: Install test-api server dependencies
          if: steps.cache-test-api.outputs.cache-hit != 'true' &&
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          working-directory: workspace/servers/test-api
          run: |
            # Install dependencies with pnpm
            pnpm install

            # Build the project
            pnpm run build

        - name: Setup Auth0 token cache
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          working-directory: workspace/clients/tests
          run: |
            echo "🔐 Setting up Auth0 token cache..."
            mkdir -p .auth0-cache
            cat > src/globals/auth0/token-cache.ts << 'EOF'
            /**
             * Auth0 Token Cache
             *
             * This module provides a cache for Auth0 tokens to avoid making too many login requests
             * during test execution. This helps prevent "Too Many Requests" errors from Auth0.
             */

            import { JWTConfig } from './types';

            interface CachedToken extends JWTConfig {
              username: string;
              expiresAt: number; // Timestamp when token expires
            }

            class Auth0TokenCache {
              private tokens: Map<string, CachedToken> = new Map();
              private initialized = false;

              /**
               * Initialize the token cache
               */
              public init(): void {
                if (this.initialized) return;
                this.tokens.clear();
                this.initialized = true;
                console.log('🔐 Auth0 token cache initialized');
              }

              /**
               * Get a token from the cache
               * @param username The username to get the token for
               * @returns The cached token or null if not found or expired
               */
              public getToken(username: string): JWTConfig | null {
                if (!this.initialized) {
                  this.init();
                }

                const cachedToken = this.tokens.get(username);
                if (!cachedToken) {
                  return null;
                }

                // Check if token is expired (with 5 minute buffer)
                if (cachedToken.expiresAt < Date.now() + 5 * 60 * 1000) {
                  this.tokens.delete(username);
                  return null;
                }

                return {
                  jwt: cachedToken.jwt,
                  userId: cachedToken.userId
                };
              }

              /**
               * Store a token in the cache
               * @param username The username the token belongs to
               * @param token The token to store
               * @param expiresIn Token expiration time in seconds (default: 1 hour)
               */
              public setToken(username: string, token: JWTConfig, expiresIn: number = 3600): void {
                if (!this.initialized) {
                  this.init();
                }

                this.tokens.set(username, {
                  ...token,
                  username,
                  expiresAt: Date.now() + expiresIn * 1000
                });
              }

              /**
               * Clear the token cache
               */
              public clear(): void {
                this.tokens.clear();
              }

              /**
               * Get the number of cached tokens
               */
              public size(): number {
                return this.tokens.size;
              }
            }

            // Export a singleton instance
            export const tokenCache = new Auth0TokenCache();
            EOF

            # Update the types.ts file
            if ! grep -q "JWTConfig" src/globals/auth0/types.ts; then
              sed -i 's/export type UserConfig = { username: string, password: string };/export type UserConfig = { username: string, password: string };\nexport type JWTConfig = { jwt: string, userId: string };/' src/globals/auth0/types.ts
            fi

            # Update the client.ts file to use the token cache
            if ! grep -q "tokenCache" src/globals/auth0/client.ts; then
              # Add import
              sed -i 's/import { UserConfig, AuthFetch } from ".\/types";/import { UserConfig, AuthFetch, JWTConfig } from ".\/types";\nimport { tokenCache } from ".\/token-cache";/' src/globals/auth0/client.ts

              # Remove JWTConfig type definition if it exists
              sed -i '/type JWTConfig = { jwt: string, userId: string };/d' src/globals/auth0/client.ts

              # Update getUserToJWT function
              sed -i '/export async function getUserToJWT/,/}/c\export async function getUserToJWT(browser: Browser, { username, password }: UserConfig): Promise<JWTConfig>{\n  // Check if we have a cached token for this user\n  const cachedToken = tokenCache.getToken(username);\n  if (cachedToken) {\n    console.log(`🔑 Using cached token for ${username}`);\n    return cachedToken;\n  }\n\n  console.log(`🔑 No cached token found for ${username}, logging in...`);\n  const context = await browser.newContext();\n  const page = await context.newPage();\n\n  try {\n    const userInfo = await usePageToLoginAsUser(page, { username, password });\n    \n    // Cache the token for future use (default expiration: 1 hour)\n    tokenCache.setToken(username, userInfo);\n    console.log(`🔑 Token cached for ${username}`);\n    \n    return userInfo;\n  } finally {\n    await context.close();\n  }\n}' src/globals/auth0/client.ts
            fi

            # Update the index.ts file to initialize the token cache
            if ! grep -q "tokenCache" src/index.ts; then
              # Add import
              sed -i 's/import { getApiTestSuitesForChangedFolders } from ".\/api-test-mapping";/import { getApiTestSuitesForChangedFolders } from ".\/api-test-mapping";\nimport { tokenCache } from ".\/globals\/auth0\/token-cache";/' src/index.ts

              # Initialize token cache
              sed -i '/console.log(`🌐 Setting up services for ${config.environment} environment...`);/i\    // Initialize token cache\n    tokenCache.init();\n    console.log(`🔐 Auth0 token cache initialized (${tokenCache.size()} tokens)`);\n' src/index.ts

              # Add token cache stats in cleanup
              sed -i '/console.log("🧹 Starting cleanup...");/a\      // Log token cache stats\n      console.log(`🔐 Auth0 token cache stats: ${tokenCache.size()} tokens cached`);' src/index.ts
            fi

            echo "✅ Auth0 token cache setup complete"

        - name: Start test client
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          working-directory: workspace/clients/tests
          run: |
            echo "✨ Starting test client..."

            # Create a temporary file to capture the exit code
            EXIT_CODE_FILE=$(mktemp)

            # Run tests and capture both output and exit code
            pnpm run start:dev 2>&1 | tee test-output.log; echo ${PIPESTATUS[0]} > $EXIT_CODE_FILE

            # Print test failures in a more readable format
            if [ -f test-output.log ]; then
              echo "\n📊 Test Failure Summary:"
              echo "======================="
              grep -A 5 "Test Name:" test-output.log || true
            fi

            # Read the captured exit code
            EXIT_CODE=$(<$EXIT_CODE_FILE)
            rm $EXIT_CODE_FILE

            # Exit with the original exit code
            exit $EXIT_CODE

        - name: Upload Test Reports
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          uses: actions/upload-artifact@v4
          with:
            name: test-reports
            path: |
              workspace/clients/tests/reports/*.tap
              workspace/clients/tests/reports/*.json
              workspace/clients/tests/reports/*.txt
            retention-days: 5

        - name: Upload Test Screenshots
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          uses: actions/upload-artifact@v4
          with:
            name: test-artifacts
            path: |
              workspace/clients/tests/test-screenshots/*.png
            retention-days: 5

        - name: Rollback Cloud Run Deployment on Staging Test Failure
          if: |
            failure() &&
            (github.event_name == 'pull_request' && github.event.pull_request.base.ref == 'stage') ||
            (github.event_name == 'workflow_dispatch' && inputs.base_ref == 'stage') &&
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          env:
            GCP_PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
            GOOGLE_CREDENTIALS: ${{ secrets.GOOGLE_CREDENTIALS }}
          run: |
            echo "🔄 Rolling back staging deployment due to test failures..."
            # [Rest of the rollback script]

        - name: Report test results
          if: |
            needs.check-deploy-flag.outputs.is_fast_deploy != 'true' &&
            inputs.is_fast_deploy != 'true'
          run: |
            if [ ${{ job.status }} == 'success' ]; then
              echo "✅ API tests passed successfully!"
            else
              echo "❌ API tests failed!"
              exit 1
            fi

        # 📓 Only used in the `develop` workflows to decide if the PR against `stage` should be created.
        - name: Check PR Status
          if: |
            success() &&
            env.BASE_REF == 'develop' &&
            needs.check-deploy-flag.outputs.target_branch == 'stage'
          id: check-pr-status
          env:
            GH_TOKEN: ${{ secrets.ELEVATE_PR_PAT }}
          run: |
            # Get develop PR number based on trigger type
            DEVELOP_PR_NUMBER="${{
              github.event_name == 'pull_request' && github.event.pull_request.number ||
              github.event_name == 'workflow_dispatch' && inputs.pr_number
            }}"

            echo "🔍 Checking PR #${DEVELOP_PR_NUMBER}..."

            # Get basic PR data without status checks
            PR_DATA=$(gh pr view ${DEVELOP_PR_NUMBER} --json reviews,state,mergeable)

            # Check for approvals
            # APPROVED=$(echo "$PR_DATA" | jq '[.reviews[].state | select(. == "APPROVED")] | length > 0')

            # Check if PR is open and mergeable
            PR_OPEN=$(echo "$PR_DATA" | jq '.state == "OPEN"')
            PR_MERGEABLE=$(echo "$PR_DATA" | jq '.mergeable == true')

            echo "pr_ready=true" >> $GITHUB_OUTPUT

            # if [ "$APPROVED" = "true" ] && [ "$PR_OPEN" = "true" ] && [ "$PR_MERGEABLE" = "true" ]; then
            #   echo "pr_ready=true" >> $GITHUB_OUTPUT
            #   echo "✅ PR is approved, open, and mergeable"
            # else
            #   echo "pr_ready=false" >> $GITHUB_OUTPUT
            #   if [ "$APPROVED" != "true" ]; then
            #     echo "❌ PR needs approval"
            #   fi
            #   if [ "$PR_OPEN" != "true" ]; then
            #     echo "❌ PR is not open"
            #   fi
            #   if [ "$PR_MERGEABLE" != "true" ]; then
            #     echo "❌ PR is not mergeable"
            #   fi
            # fi

        # Create pull request for elevation post successful test
        - name: Create Pull Request to Stage
          if: |
            success() &&
            needs.check-deploy-flag.outputs.target_branch == 'stage' &&
            ((github.event_name == 'pull_request' && github.event.pull_request.base.ref == 'develop') ||
            (github.event_name == 'workflow_dispatch' && inputs.base_ref == 'develop'))
          id: create-pr
          env:
            GH_TOKEN: ${{ secrets.ELEVATE_PR_PAT }}
          run: |
            # Function to fetch with retry
            fetch_with_retry() {
              local ref=$1
              local max_attempts=3
              local attempt=1

              while [ $attempt -le $max_attempts ]; do
                echo "🎯 Attempting to fetch ${ref} (attempt ${attempt}/${max_attempts})..."

                if git fetch --depth=1 origin ${ref}; then
                  echo "✅ Successfully fetched ${ref}"
                  return 0
                fi

                echo "⚠️ Fetch failed, cleaning up and retrying..."
                git gc --prune=now
                rm -f .git/FETCH_HEAD
                sleep $(( attempt * 5 ))  # Incremental backoff
                ((attempt++))
              done

              echo "❌ Failed to fetch ${ref} after ${max_attempts} attempts"
              return 1
            }

            # Get PR number and head ref based on trigger type
            PR_NUMBER="${{
              github.event_name == 'pull_request' && github.event.pull_request.number ||
              github.event_name == 'workflow_dispatch' && inputs.pr_number
            }}"

            HEAD_REF="${{
              github.event_name == 'pull_request' && github.event.pull_request.head.ref ||
              github.event_name == 'workflow_dispatch' && github.ref
            }}"
            HEAD_REF=${HEAD_REF#refs/heads/}

            echo "Creating elevated PR from ${HEAD_REF} (PR #${PR_NUMBER})..."

            # Create elevated branch name
            ELEVATED_BRANCH="elevated-${HEAD_REF}"
            echo "Creating elevated branch: $ELEVATED_BRANCH"

            # Configure git
            git config --global --add safe.directory "${GITHUB_WORKSPACE}"

            # Set up Git authentication with token in URL (Method 5)
            git config --global url."https://x-access-token:${GH_TOKEN}@github.com/".insteadOf "https://github.com/"

            # Fetch stage branch with retry
            if ! fetch_with_retry "stage"; then
              echo "❌ Failed to fetch stage branch"
              exit 1
            fi

            # Fetch source branch with retry
            if ! fetch_with_retry "${HEAD_REF}"; then
              echo "❌ Failed to fetch source branch"
              exit 1
            fi

            # Create and switch to a new branch
            git checkout -b "$ELEVATED_BRANCH" "origin/${HEAD_REF}"

            # Verify the branch state
            echo "🔍 Verifying branch state:"
            git status
            git log -1 --oneline

            # Push the elevated branch with force-with-lease for safety
            echo "📤 Pushing elevated branch..."
            git push -u origin "$ELEVATED_BRANCH" --force-with-lease || git push -u origin "$ELEVATED_BRANCH" --force

            # Extract the original deployment command
            ORIGINAL_COMMAND="${{ inputs.comment }}"
            if [ -z "$ORIGINAL_COMMAND" ]; then
              # If not from comment, try to get from the last commit message
              ORIGINAL_COMMAND=$(git log -1 --pretty=%B)
            fi

            echo "Original command: $ORIGINAL_COMMAND"

            # Create PR
            echo "📝 Creating pull request..."
            NEW_PR=$(gh pr create \
              --base stage \
              --head "$ELEVATED_BRANCH" \
              --title "⬆️ Elevated ${HEAD_REF} to stage" \
              --body "Automated PR to elevate changes from #${PR_NUMBER} to stage." \
              --label "automated-pr,elevated-pr,CI-CD,stage" \
              --assignee "mikeumus" \
              --reviewer "formula1")

            # Add deploy trigger comment that preserves original flags
            if [[ "$ORIGINAL_COMMAND" =~ \[deploy:[^]]+\] ]]; then
              # Extract the command part and description separately
              DEPLOY_COMMAND=$(echo "$ORIGINAL_COMMAND" | grep -o '\[deploy:[^]]*\]')
              DESCRIPTION=$(echo "$ORIGINAL_COMMAND" | sed -E 's/\[deploy:[^]]*\]\s*//')

              # Clean the command part
              CLEANED_COMMAND=$(echo "$DEPLOY_COMMAND" | sed -E 's/(\[|\]|@github-actions)//g' | tr -s ' ')

              # Split into parts
              IFS=':' read -ra PARTS <<< "$CLEANED_COMMAND"

              # Start with base command
              NEW_COMMAND="@github-actions [deploy:stage"

              # Add all parts after the environment/stage part
              for ((i=1; i<${#PARTS[@]}; i++)); do
                if [[ "${PARTS[i]}" != "develop" && "${PARTS[i]}" != "stage" && "${PARTS[i]}" != "prod" ]]; then
                  NEW_COMMAND="$NEW_COMMAND:${PARTS[i]}"
                fi
              done

              # Close the command
              NEW_COMMAND="${NEW_COMMAND}]"

              # Process description for JIRA ticket
              if [[ "$DESCRIPTION" =~ ^[A-Z]+-[0-9]+ ]]; then
                # Extract JIRA ticket
                TICKET=$(echo "$DESCRIPTION" | grep -o '^[A-Z]\+-[0-9]\+')
                # Replace ticket with link
                DESCRIPTION=$(echo "$DESCRIPTION" | sed "s/^$TICKET/[${TICKET}](https:\/\/divinci.atlassian.net\/browse\/${TICKET})/")
              fi

              # Add description on new line if present
              if [ -n "$DESCRIPTION" ]; then
                # Add description only if not triggered by comment
                if [ "${{ inputs.triggered_by_comment }}" != "true" ] && [ -n "$DESCRIPTION" ]; then
                  NEW_COMMAND="$NEW_COMMAND\n\n$DESCRIPTION"
                fi
              fi
            else
              NEW_COMMAND="@github-actions [deploy:stage]"
            fi

            echo "Final reconstructed command: $NEW_COMMAND"

            # Add deploy trigger comment
            echo "💬 Adding deploy trigger comment..."
            gh pr comment "${NEW_PR}" --body "$NEW_COMMAND"

            # Store PR number
            echo "NEW_PR_NUMBER=${NEW_PR}" >> $GITHUB_ENV
            echo "new-pr-number=${NEW_PR}" >> $GITHUB_OUTPUT

            echo "✅ Successfully created elevated PR #${NEW_PR}"

        # - name: Notify Missing Approval
        #   if: |
        #     success() &&
        #     env.BASE_REF == 'develop' &&
        #     steps.check-approval.outputs.pr_approved != 'true'
        #   env:
        #     GH_TOKEN: ${{ secrets.ELEVATE_PR_PAT }}
        #   run: |
        #     # Get PR number based on trigger type
        #     PR_NUMBER="${{
        #       github.event_name == 'pull_request' && github.event.pull_request.number ||
        #       github.event_name == 'workflow_dispatch' && inputs.pr_number
        #     }}"

        #     echo "🔔 Sending notification to PR #${PR_NUMBER}..."

        #     gh pr comment ${PR_NUMBER} --body "⚠️ This PR needs at least one approval before it can be elevated to stage."

        # Check the outcome of PR creation
        - name: Check PR Creation Outcome
          if: always()
          run: |
            if [ "${{ steps.create-pr.outcome }}" == "failure" ]; then
              echo "PR_CREATION_FAILED=true" >> $GITHUB_ENV
            fi

        # Slack Notification for Manual Review
        - name: Notify Slack of Manual Resolution Need
          if: failure() || env.MERGE_CONFLICT_DETECTED == 'true' || steps.check-modifications.outputs.modifications-needed == '1' && env.PR_CREATION_FAILED == 'true'
          env:
            SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          run: |
            ERR_MSG="${{ steps.capture-pr-errors.outputs.captured-error }}"
            if [ -z "$ERR_MSG" ]; then
              ERR_MSG="No detailed errors captured. Check logs manually for investigation."
            fi

            MESSAGE=$(cat <<EOF
            {
              "text": "🚨 Elevation Action Required: `develop` -> `stage` ",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": ":warning: *Conflicts Detected During Pull Request Creation or Errors Encountered!*"
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "The automated CI/CD process encountered issues:\n\n*Error Details:* \n\`\`\`${{ env.ERR_MSG }}\`\`\`\n\nPlease review and resolve the conflict in the <https://github.com/${{ github.repository }}/pull/${{ env.BRANCH_NAME }}|Pull Request>.\n\nIf resolved, you can manually <https://github.com/${{ github.repository }}/actions|trigger the workflow again>."
                  }
                },
                {
                  "type": "divider"
                },
                {
                  "type": "context",
                  "elements": [
                    {
                      "type": "mrkdwn",
                      "text": "If you have questions, please reach out to the support team (@Mike 😅)."
                    }
                  ]
                }
              ]
            }
            EOF
            )
            curl -X POST -H 'Content-type: application/json' --data "$MESSAGE" "$SLACK_WEBHOOK_URL"

        - name: Look up PR number from branch name
          id: lookup-pr
          if: env.PR_NUMBER == ''
          env:
            GH_TOKEN: ${{ secrets.ELEVATE_PR_PAT }}
          run: |
            # Extract the original branch name from the elevated branch name using sed
            BRANCH_NAME="${{ env.BRANCH_NAME }}"
            if [[ "$BRANCH_NAME" =~ ^elevated- ]]; then
              ORIGINAL_BRANCH=$(echo "$BRANCH_NAME" | sed 's/^elevated-//')
              echo "Original branch: $ORIGINAL_BRANCH"

              # Look up PR number by branch name
              PR_NUM=$(gh pr list --state open --base stage --head "$BRANCH_NAME" --json number -q '.[0].number')

              if [ -n "$PR_NUM" ]; then
                echo "Found PR number: $PR_NUM"
                echo "PR_NUMBER=$PR_NUM" >> $GITHUB_ENV
                echo "pr_number=$PR_NUM" >> $GITHUB_OUTPUT
              else
                echo "No PR found for branch $BRANCH_NAME"
              fi
            else
              echo "Not an elevated branch: $BRANCH_NAME"
            fi

        # Enable Pull Request Automerge if all tests pass and base branch is 'develop'
        - name: Enable Auto-Merge for Elevated Stage Pull-Request
          if: |
            success() &&
            (env.NEW_PR_NUMBER != '' || env.PR_NUMBER != '' || steps.lookup-pr.outputs.pr_number != '') &&
            needs.check-deploy-flag.outputs.target_branch == 'stage' &&
            ((github.event_name == 'pull_request' && github.event.pull_request.base.ref == 'stage') ||
            (github.event_name == 'workflow_dispatch' && inputs.base_ref == 'stage'))
          env:
            GH_TOKEN: ${{ secrets.ELEVATE_PR_PAT }}
          run: |
            echo "NEW_PR_NUMBER: ${{ env.NEW_PR_NUMBER }}"
            echo "PR_NUMBER: ${{ env.PR_NUMBER }}"
            echo "LOOKED_UP_PR: ${{ steps.lookup-pr.outputs.pr_number }}"

            # Try to get PR number from all possible sources
            PR_NUM=""
            if [ -n "${{ env.NEW_PR_NUMBER }}" ]; then
              PR_NUM="${{ env.NEW_PR_NUMBER }}"
            elif [ -n "${{ env.PR_NUMBER }}" ]; then
              PR_NUM="${{ env.PR_NUMBER }}"
            elif [ -n "${{ steps.lookup-pr.outputs.pr_number }}" ]; then
              PR_NUM="${{ steps.lookup-pr.outputs.pr_number }}"
            fi

            if [ -n "$PR_NUM" ]; then
              echo "Processing PR #$PR_NUM"

              # Get the branch name from the PR
              BRANCH_NAME=$(gh pr view "$PR_NUM" --json headRefName -q .headRefName)
              echo "Branch name: $BRANCH_NAME"

              # Try to enable auto-merge
              if gh pr merge "$PR_NUM" \
                --merge \
                --auto \
                --body "☑️ Auto-merging Elevated PR to stage." 2>/dev/null; then
                echo "✅ Auto-merge enabled successfully"
              else
                echo "⚠️ Could not enable auto-merge, adding manual merge instructions"
                gh pr comment "$PR_NUM" --body "⚠️ **Manual Merge Required**

                This PR needs to be merged manually since auto-merge is not available.

                Please merge this PR ASAP to keep \`stage\` and \`develop\` branches in sync.

                Steps:
                1. Wait for all checks to pass
                2. Click the 'Merge pull request' button
                3. Delete the branch after merging

                Thank you! 🙂"
              fi

              # Add information about branch cleanup
              gh pr comment "$PR_NUM" --body "Branch \`$BRANCH_NAME\` will be deleted after merge is complete."

              # Check if already merged
              if gh pr view "$PR_NUM" --json state -q .state | grep -q "MERGED"; then
                echo "PR is already merged, deleting branch..."
                gh api repos/${{ github.repository }}/git/refs/heads/$BRANCH_NAME -X DELETE || true
              fi
            else
              echo "❌ No PR number found from any source."
              exit 1
            fi

        # Slack notification after stage PR auto-merge
        - name: Notify Slack About Develop PR behind stage branch
          if: |
            success() &&
            env.NEW_PR_NUMBER != '' &&
            steps.check-pr-status.outputs.pr_ready == 'true'
          env:
            SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
            ORIGINAL_PR_NUMBER: ${{
              github.event_name == 'pull_request' && github.event.pull_request.number ||
              github.event_name == 'workflow_dispatch' && inputs.pr_number
              }}
          run: |
            # Send notification about develop PR
            MESSAGE=$(cat <<EOF
            {
              "text": "🔄 Action Required: Complete the Development Cycle",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "🤖 Auto-merge enabled for this elevated PR to stage. Changes will be automatically merged once all checks pass.\n\n⚠️ *PLEASE MERGE THIS PULL-REQUEST ASAP* ⚠️\n\nIn order to keep \`stage\` and \`develop\` branches in sync.\n\n<https://github.com/${{ github.repository }}/pull/${{ env.ORIGINAL_PR_NUMBER }}|View Original PR #${{ env.ORIGINAL_PR_NUMBER }}>"
                  }
                }
              ]
            }
            EOF
            )
            curl -X POST -H 'Content-type: application/json' --data "$MESSAGE" "$SLACK_WEBHOOK_URL"

        # QA Team Slack Notification
        - name: Notify QA Channel
          if: |
            success() &&
            env.NEW_PR_NUMBER != '' &&
            (github.event_name == 'pull_request' && github.event.pull_request.base.ref == 'stage') ||
            (github.event_name == 'workflow_dispatch' && inputs.base_ref == 'stage')
          env:
            QA_SLACK_WEBHOOK_URL: ${{ secrets.QA_SLACK_WEBHOOK_URL }}
          run: |
            MESSAGE=$(cat <<EOF
            {
              "text": "🎯 New Stage Deployment Ready for Testing",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "Hi QA Testians! It's time to march on Stage once more and test out the newly merged <https://github.com/${{ github.repository }}/pull/${{ env.NEW_PR_NUMBER }}|PR #${{ env.NEW_PR_NUMBER }}>! Cheers! 🎉"
                  }
                }
              ]
            }
            EOF
            )
            curl -X POST -H 'Content-type: application/json' --data "$MESSAGE" "$QA_SLACK_WEBHOOK_URL"

        - name: Cleanup
          if: always()
          run: |
            echo "Cleaning up processes..."
            lsof -i :18080 | awk 'NR!=1 {print $2}' | xargs -r kill -9 || true

            echo "Cleaning up Docker resources..."
            docker stop redis mongo || true
            docker rm redis mongo || true
            docker volume rm mongo_data || true

            echo "Cleaning up Docker network..."
            docker network disconnect test-network $(hostname) || true
            docker network rm test-network || true

            echo "Final port status check:"
            lsof -i :18080 || true

            echo "Final Docker status:"
            docker ps -a
            docker network ls

    # ----------------------------------------
    # 🧹 Cleanup Job
    # ----------------------------------------
    # Purpose: Cleans up elevated branches after successful stage deployment
    # Dependencies: Needs api-tests and build-deploy to complete
    # Conditions: Only runs for PRs targeting stage
    # ----------------------------------------
    cleanup-elevated-branch:
      needs: [api-tests, build-deploy, check-deploy-flag, select-runner]
      if: |
        always() &&
        needs.build-deploy.result == 'success' &&
        needs.check-deploy-flag.outputs.keep_branch != 'true' &&
        (
          (needs.api-tests.result == 'success' || needs.api-tests.result == 'skipped') &&
          needs.check-deploy-flag.outputs.target_branch == 'stage' &&
          (
            (github.event_name == 'pull_request' && github.event.pull_request.base.ref == 'stage') ||
            (github.event_name == 'workflow_dispatch' && inputs.base_ref == 'stage')
          )
        )
      runs-on: ${{ needs.select-runner.outputs.runner }}
      steps:
        # Add debug step to help troubleshoot runner selection
        - name: Debug Runner Selection
          run: |
            echo "Selected Runner: ${{ needs.select-runner.outputs.runner }}"
            echo "Build Deploy Result: ${{ needs.build-deploy.result }}"
            echo "API Tests Result: ${{ needs.api-tests.result }}"
            echo "Target Branch: ${{ needs.check-deploy-flag.outputs.target_branch }}"

        - name: Delete Elevated Branch
          env:
            GH_TOKEN: ${{ secrets.ELEVATE_PR_PAT }}
          run: |
            # Debug information
            echo "Build Deploy Result: ${{ needs.build-deploy.result }}"
            echo "API Tests Result: ${{ needs.api-tests.result }}"
            echo "Skip Tests (fail-tests): ${{ needs.check-deploy-flag.outputs.is_fast_deploy }}"
            echo "Target Branch: ${{ needs.check-deploy-flag.outputs.target_branch }}"
            echo "Keep Branch: ${{ needs.check-deploy-flag.outputs.keep_branch }}"

            # Checkout repository to get git context
            git init
            git config --global --add safe.directory "${GITHUB_WORKSPACE}"

            # Set up the remote with token in URL (Method 5)
            git remote add origin "https://x-access-token:${GH_TOKEN}@github.com/${GITHUB_REPOSITORY}"

            # Try to get PR number from branch name
            PR_NUM=$(gh pr list --state open --base stage --head "${{ env.BRANCH_NAME }}" --json number -q '.[0].number')

            if [ -n "$PR_NUM" ]; then
              echo "Found PR #$PR_NUM for branch ${{ env.BRANCH_NAME }}"

              # Get the head branch name
              BRANCH_NAME=$(gh pr view $PR_NUM --json headRefName -q .headRefName)

              if [ -n "$BRANCH_NAME" ]; then
                echo "Deleting elevated branch: $BRANCH_NAME"
                gh pr close $PR_NUM || true
                gh api repos/${{ github.repository }}/git/refs/heads/$BRANCH_NAME -X DELETE || true
              else
                echo "No branch name found for PR #$PR_NUM"
              fi
            else
              echo "No PR found for branch ${{ env.BRANCH_NAME }}"
              gh api repos/${{ github.repository }}/git/refs/heads/${{ env.BRANCH_NAME }} -X DELETE || true
<<<<<<< HEAD
            fi
=======
            fi

    # ----------------------------------------
    # 🔒 Keep Branch Notification Job
    # ----------------------------------------
    # Purpose: Notifies when branch cleanup is skipped due to keep flag
    # Dependencies: Needs build-deploy and check-deploy-flag to complete
    # Conditions: Only runs when keep_branch flag is true
    # ----------------------------------------
    notify-keep-branch:
      needs: [build-deploy, check-deploy-flag, select-runner]
      if: |
        always() &&
        needs.build-deploy.result == 'success' &&
        needs.check-deploy-flag.outputs.keep_branch == 'true'
      runs-on: ${{ needs.select-runner.outputs.runner }}
      steps:
        - name: Notify Keep Branch
          run: |
            echo "🔒 Branch cleanup skipped due to 'keep' flag in deployment command"
            echo "📝 Original Command: ${{ inputs.comment }}"
            echo "🎯 Target Branch: ${{ needs.check-deploy-flag.outputs.target_branch }}"
            echo "🔒 Keep Branch: ${{ needs.check-deploy-flag.outputs.keep_branch }}"
            echo ""
            echo "ℹ️ The elevated branch will NOT be automatically deleted."
            echo "ℹ️ You may need to manually clean up the branch later."
>>>>>>> WA-170_MCP
