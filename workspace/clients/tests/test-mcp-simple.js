#!/usr/bin/env node

/**
 * Simple MCP Server Test Script
 * 
 * This script tests the MCP server functionality without requiring
 * a full browser setup. It's perfect for quick validation and CI/CD.
 */

const https = require('https');
const http = require('http');

// Configuration
const MCP_SERVER_URL = 'http://localhost:8793';
const COLORS = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  blue: '\x1b[34m',
  yellow: '\x1b[33m'
};

// Helper function to make HTTP requests
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const requestModule = url.startsWith('https') ? https : http;
    const requestOptions = {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    };

    const req = requestModule.request(url, requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = data ? JSON.parse(data) : {};
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData,
            raw: data
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: null,
            raw: data,
            parseError: error.message
          });
        }
      });
    });

    req.on('error', reject);

    if (options.body) {
      req.write(typeof options.body === 'string' ? options.body : JSON.stringify(options.body));
    }

    req.end();
  });
}

// Test functions
async function testServerHealth() {
  console.log(`${COLORS.blue}🏥 Testing MCP Server Health...${COLORS.reset}`);
  
  try {
    const response = await makeRequest(`${MCP_SERVER_URL}/health`);
    
    if (response.status === 200 && response.data.success) {
      console.log(`${COLORS.green}✅ Server is healthy${COLORS.reset}`);
      console.log(`   Service: ${response.data.service}`);
      console.log(`   Version: ${response.data.version}`);
      console.log(`   Status: ${response.data.status}`);
      return true;
    } else {
      console.log(`${COLORS.red}❌ Server health check failed${COLORS.reset}`);
      console.log(`   Status: ${response.status}`);
      console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);
      return false;
    }
  } catch (error) {
    console.log(`${COLORS.red}❌ Server health check error: ${error.message}${COLORS.reset}`);
    return false;
  }
}

async function testToolsListing() {
  console.log(`${COLORS.blue}🔍 Testing Tools Listing...${COLORS.reset}`);
  
  try {
    const response = await makeRequest(`${MCP_SERVER_URL}/sse`, {
      method: 'POST',
      body: {
        jsonrpc: '2.0',
        id: 1,
        method: 'tools/list',
        params: {}
      }
    });
    
    if (response.status === 200 && response.data.result && response.data.result.tools) {
      const tools = response.data.result.tools;
      console.log(`${COLORS.green}✅ Found ${tools.length} tools${COLORS.reset}`);
      
      tools.forEach((tool, index) => {
        console.log(`   ${index + 1}. ${tool.name}: ${tool.description}`);
      });
      
      // Verify expected tools
      const expectedTools = ['get_user_profile', 'create_chat', 'send_message', 'list_chats'];
      const foundTools = tools.map(t => t.name);
      const missingTools = expectedTools.filter(t => !foundTools.includes(t));
      
      if (missingTools.length === 0) {
        console.log(`${COLORS.green}✅ All expected tools found${COLORS.reset}`);
        return true;
      } else {
        console.log(`${COLORS.yellow}⚠️  Missing tools: ${missingTools.join(', ')}${COLORS.reset}`);
        return false;
      }
    } else {
      console.log(`${COLORS.red}❌ Tools listing failed${COLORS.reset}`);
      console.log(`   Status: ${response.status}`);
      console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);
      return false;
    }
  } catch (error) {
    console.log(`${COLORS.red}❌ Tools listing error: ${error.message}${COLORS.reset}`);
    return false;
  }
}

async function testToolExecution(toolName, args = {}) {
  console.log(`${COLORS.blue}🛠️  Testing Tool: ${toolName}...${COLORS.reset}`);
  
  try {
    const response = await makeRequest(`${MCP_SERVER_URL}/sse`, {
      method: 'POST',
      body: {
        jsonrpc: '2.0',
        id: Date.now(),
        method: 'tools/call',
        params: {
          name: toolName,
          arguments: args
        }
      }
    });
    
    if (response.status === 200 && response.data.result && response.data.result.content) {
      const content = response.data.result.content[0];
      console.log(`${COLORS.green}✅ Tool executed successfully${COLORS.reset}`);
      console.log(`   Response: ${content.text.substring(0, 100)}${content.text.length > 100 ? '...' : ''}`);
      return true;
    } else {
      console.log(`${COLORS.red}❌ Tool execution failed${COLORS.reset}`);
      console.log(`   Status: ${response.status}`);
      console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);
      return false;
    }
  } catch (error) {
    console.log(`${COLORS.red}❌ Tool execution error: ${error.message}${COLORS.reset}`);
    return false;
  }
}

async function testErrorHandling() {
  console.log(`${COLORS.blue}⚠️  Testing Error Handling...${COLORS.reset}`);
  
  try {
    // Test invalid tool
    const response1 = await makeRequest(`${MCP_SERVER_URL}/sse`, {
      method: 'POST',
      body: {
        jsonrpc: '2.0',
        id: 1,
        method: 'tools/call',
        params: {
          name: 'non_existent_tool',
          arguments: {}
        }
      }
    });
    
    if (response1.status === 200 && response1.data.error && response1.data.error.code === -32603) {
      console.log(`${COLORS.green}✅ Invalid tool error handled correctly${COLORS.reset}`);
    } else {
      console.log(`${COLORS.yellow}⚠️  Unexpected response for invalid tool${COLORS.reset}`);
    }
    
    // Test malformed JSON
    const response2 = await makeRequest(`${MCP_SERVER_URL}/sse`, {
      method: 'POST',
      body: 'invalid json'
    });
    
    if (response2.status === 400) {
      console.log(`${COLORS.green}✅ Malformed JSON error handled correctly${COLORS.reset}`);
      return true;
    } else {
      console.log(`${COLORS.yellow}⚠️  Unexpected response for malformed JSON${COLORS.reset}`);
      return false;
    }
  } catch (error) {
    console.log(`${COLORS.red}❌ Error handling test error: ${error.message}${COLORS.reset}`);
    return false;
  }
}

async function testPerformance() {
  console.log(`${COLORS.blue}⚡ Testing Performance...${COLORS.reset}`);
  
  const startTime = Date.now();
  const promises = [];
  
  // Execute 10 concurrent requests
  for (let i = 0; i < 10; i++) {
    promises.push(makeRequest(`${MCP_SERVER_URL}/sse`, {
      method: 'POST',
      body: {
        jsonrpc: '2.0',
        id: i,
        method: 'tools/call',
        params: {
          name: 'get_user_profile',
          arguments: {}
        }
      }
    }));
  }
  
  try {
    const results = await Promise.all(promises);
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    const successCount = results.filter(r => r.status === 200 && r.data.result).length;
    const successRate = (successCount / results.length) * 100;
    
    console.log(`${COLORS.green}✅ Performance test completed${COLORS.reset}`);
    console.log(`   Requests: ${results.length}`);
    console.log(`   Successful: ${successCount}`);
    console.log(`   Success rate: ${successRate.toFixed(1)}%`);
    console.log(`   Total time: ${duration}ms`);
    console.log(`   Average time: ${(duration / results.length).toFixed(1)}ms per request`);
    
    return successRate >= 90; // 90% success rate threshold
  } catch (error) {
    console.log(`${COLORS.red}❌ Performance test error: ${error.message}${COLORS.reset}`);
    return false;
  }
}

// Main test runner
async function runTests() {
  console.log(`${COLORS.blue}🧪 MCP Server Simple Test Suite${COLORS.reset}`);
  console.log('=====================================');
  
  const tests = [
    { name: 'Server Health', fn: testServerHealth },
    { name: 'Tools Listing', fn: testToolsListing },
    { name: 'Get User Profile', fn: () => testToolExecution('get_user_profile') },
    { name: 'Create Chat', fn: () => testToolExecution('create_chat', { title: 'Test Chat', model: 'gpt-4' }) },
    { name: 'Send Message', fn: () => testToolExecution('send_message', { chatId: 'test_123', message: 'Hello!' }) },
    { name: 'List Chats', fn: () => testToolExecution('list_chats', { limit: 5 }) },
    { name: 'Error Handling', fn: testErrorHandling },
    { name: 'Performance', fn: testPerformance }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    console.log('');
    const result = await test.fn();
    if (result) {
      passed++;
    } else {
      failed++;
    }
  }
  
  console.log('');
  console.log('=====================================');
  console.log(`${COLORS.blue}📊 Test Results${COLORS.reset}`);
  console.log('=====================================');
  console.log(`Total Tests: ${tests.length}`);
  console.log(`${COLORS.green}Passed: ${passed}${COLORS.reset}`);
  console.log(`${COLORS.red}Failed: ${failed}${COLORS.reset}`);
  
  if (failed === 0) {
    console.log('');
    console.log(`${COLORS.green}🎉 All tests passed!${COLORS.reset}`);
    console.log(`${COLORS.green}✅ Your MCP server is working correctly${COLORS.reset}`);
    console.log('');
    console.log(`${COLORS.blue}🚀 Next Steps:${COLORS.reset}`);
    console.log('1. Test with MCP Inspector: npx @modelcontextprotocol/inspector@latest');
    console.log('2. Configure Claude Desktop with: npx mcp-remote http://localhost:8793/sse');
    console.log('3. Run full E2E tests: ./run-mcp-tests.sh');
    process.exit(0);
  } else {
    console.log('');
    console.log(`${COLORS.red}❌ Some tests failed. Please check the MCP server.${COLORS.reset}`);
    process.exit(1);
  }
}

// Check if MCP server URL is provided
if (process.argv[2]) {
  const customUrl = process.argv[2];
  console.log(`Using custom MCP server URL: ${customUrl}`);
  MCP_SERVER_URL = customUrl;
}

// Run the tests
runTests().catch(error => {
  console.error(`${COLORS.red}❌ Test runner error: ${error.message}${COLORS.reset}`);
  process.exit(1);
});
