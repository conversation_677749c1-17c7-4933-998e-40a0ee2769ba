{"name": "dropbox-sync-worker-staging", "version": "1.0.0", "description": "Staging version of Dropbox sync worker", "main": "src/index.ts", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "deploy:staging": "wrangler deploy --env staging", "prepare": "echo 'Staging worker ready'"}, "dependencies": {"hono": "^4.6.3"}, "devDependencies": {"@cloudflare/workers-types": "^4.20241106.0", "typescript": "^5.6.3", "wrangler": "^3.114.9"}}