# E2E Testing Against Remote Cloud Targets with Act

## Overview
This plan outlines how to enhance your existing E2E test suite to run against remote cloud environments using `act` to simulate GitHub Actions deployment workflows.

## Current Infrastructure Assessment

### ✅ What You Already Have
- **E2E Test Suite**: Playwright tests in `workspace/clients/tests/src/e2e/`
- **Deployment Triggers**: `@github-actions [deploy]` comment system
- **Act Setup**: Local GitHub Actions simulation in `.github/act/`
- **Multi-Environment Support**: Tests can target different environments
- **Resource Pooling**: Advanced test resource management

### 🎯 Enhancement Goals
1. **Automated Deploy-Test Cycles**: Deploy to staging/develop and run E2E tests
2. **Cloud Target Validation**: Verify deployments work in real cloud environments
3. **Rollback on Test Failure**: Automatic rollback if E2E tests fail
4. **Parallel Test Execution**: Run tests against multiple environments
5. **Comprehensive Reporting**: Detailed test results and deployment status

## Implementation Strategy

### Phase 1: Enhanced Act Configuration

#### 1.1 Create Environment-Specific Event Files
```bash
# Create event files for different deployment scenarios
.github/act/events/
├── deploy-develop-with-e2e.json
├── deploy-staging-with-e2e.json
├── deploy-production-with-e2e.json
└── e2e-only-test.json
```

#### 1.2 Enhanced Secrets Configuration
```bash
# Add cloud-specific secrets for testing
.github/act/secrets/
├── develop.env
├── staging.env
├── production.env
└── e2e-test.env
```

### Phase 2: E2E Test Enhancement

#### 2.1 Cloud Environment Test Configuration
- **Environment Detection**: Auto-detect target environment
- **Service Health Checks**: Verify services are ready before testing
- **Dynamic Endpoint Configuration**: Use environment-specific URLs
- **Authentication Handling**: Environment-specific Auth0 configs

#### 2.2 Test Categories
- **Smoke Tests**: Quick validation of core functionality
- **Integration Tests**: Full workflow testing
- **Performance Tests**: Load and response time validation
- **Security Tests**: Authentication and authorization validation

### Phase 3: Deployment Integration

#### 3.1 Deploy-Test Workflow
```yaml
# Enhanced workflow that combines deployment with E2E testing
1. Trigger deployment via comment
2. Wait for deployment completion
3. Run health checks
4. Execute E2E test suite
5. Report results
6. Rollback on failure (staging only)
```

#### 3.2 Test Mapping
- **Service-Specific Tests**: Run tests based on changed services
- **Dependency Testing**: Test downstream effects of changes
- **Cross-Service Integration**: Validate service interactions

## Detailed Implementation

### 1. Enhanced Act Event Files

#### Deploy with E2E Testing Event
```json
{
  "action": "created",
  "issue": {
    "number": 579,
    "pull_request": {
      "url": "https://api.github.com/repos/Divinci-AI/server/pulls/579"
    }
  },
  "comment": {
    "body": "@github-actions [deploy:develop:e2e]"
  },
  "repository": {
    "full_name": "Divinci-AI/server"
  }
}
```

#### E2E Only Testing Event
```json
{
  "action": "created",
  "issue": {
    "number": 579,
    "pull_request": {
      "url": "https://api.github.com/repos/Divinci-AI/server/pulls/579"
    }
  },
  "comment": {
    "body": "@github-actions [e2e:staging]"
  },
  "repository": {
    "full_name": "Divinci-AI/server"
  }
}
```

### 2. Enhanced Test Scripts

#### Cloud Environment E2E Test Runner
```bash
#!/bin/bash
# run-e2e-against-cloud.sh

ENVIRONMENT=${1:-develop}
TARGET_SERVICES=${2:-all}
TEST_SUITE=${3:-smoke}

echo "🚀 Running E2E tests against $ENVIRONMENT environment"
echo "📦 Target services: $TARGET_SERVICES"
echo "🧪 Test suite: $TEST_SUITE"

# Set environment-specific configuration
export TEST_ENV=$ENVIRONMENT
export TARGET_ENVIRONMENT=$ENVIRONMENT
export CHANGED_FOLDERS=$TARGET_SERVICES

# Wait for services to be ready
./scripts/wait-for-services.sh $ENVIRONMENT

# Run the appropriate test suite
case $TEST_SUITE in
  "smoke")
    npx playwright test --project=e2e-tests --grep="@smoke"
    ;;
  "integration")
    npx playwright test --project=e2e-tests --grep="@integration"
    ;;
  "full")
    npx playwright test --project=e2e-tests
    ;;
  *)
    npx playwright test --project=e2e-tests --grep="$TEST_SUITE"
    ;;
esac
```

### 3. Service Health Check Script
```bash
#!/bin/bash
# wait-for-services.sh

ENVIRONMENT=$1
MAX_WAIT=300  # 5 minutes
WAIT_INTERVAL=10

case $ENVIRONMENT in
  "develop")
    BASE_URL="https://api.dev.divinci.app"
    WEB_URL="https://chat.dev.divinci.app"
    ;;
  "staging")
    BASE_URL="https://api.stage.divinci.app"
    WEB_URL="https://chat.stage.divinci.app"
    ;;
  "production")
    BASE_URL="https://api.divinci.app"
    WEB_URL="https://chat.divinci.app"
    ;;
esac

echo "🔍 Waiting for services to be ready in $ENVIRONMENT environment..."

# Check API health
wait_for_service() {
  local url=$1
  local service_name=$2
  local elapsed=0
  
  while [ $elapsed -lt $MAX_WAIT ]; do
    if curl -f -s "$url/health" > /dev/null; then
      echo "✅ $service_name is ready"
      return 0
    fi
    
    echo "⏳ Waiting for $service_name... (${elapsed}s/${MAX_WAIT}s)"
    sleep $WAIT_INTERVAL
    elapsed=$((elapsed + WAIT_INTERVAL))
  done
  
  echo "❌ $service_name failed to become ready within ${MAX_WAIT}s"
  return 1
}

wait_for_service "$BASE_URL" "API Service"
wait_for_service "$WEB_URL" "Web Client"

echo "🎉 All services are ready!"
```

### 4. Enhanced Playwright Configuration

#### Environment-Specific Test Config
```typescript
// playwright.config.cloud.ts
import { defineConfig } from '@playwright/test';
import { getCloudTestConfig } from './src/config/cloud-config';

const cloudConfig = getCloudTestConfig();

export default defineConfig({
  testDir: './src/e2e',
  timeout: 60000,
  retries: process.env.CI ? 3 : 1, // More retries for cloud testing
  workers: process.env.CI ? 2 : 1, // Parallel execution
  
  projects: [
    // Smoke tests - quick validation
    {
      name: 'smoke-tests',
      testMatch: /.*\.smoke\.spec\.ts/,
      use: {
        browserName: 'chromium',
        baseURL: cloudConfig.baseURL,
        extraHTTPHeaders: cloudConfig.headers,
      },
    },
    
    // Integration tests - full workflows
    {
      name: 'integration-tests',
      testMatch: /.*\.integration\.spec\.ts/,
      use: {
        browserName: 'chromium',
        baseURL: cloudConfig.baseURL,
        extraHTTPHeaders: cloudConfig.headers,
      },
      dependencies: ['smoke-tests'],
    },
    
    // Performance tests
    {
      name: 'performance-tests',
      testMatch: /.*\.performance\.spec\.ts/,
      use: {
        browserName: 'chromium',
        baseURL: cloudConfig.baseURL,
        extraHTTPHeaders: cloudConfig.headers,
      },
    },
  ],
  
  use: {
    trace: 'retain-on-failure',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
  },
});
```

### 5. Cloud Configuration Module
```typescript
// src/config/cloud-config.ts
export interface CloudTestConfig {
  environment: string;
  baseURL: string;
  apiURL: string;
  headers: Record<string, string>;
  timeouts: {
    test: number;
    expect: number;
    navigation: number;
  };
}

export function getCloudTestConfig(): CloudTestConfig {
  const environment = process.env.TARGET_ENVIRONMENT || 'develop';
  
  const configs: Record<string, CloudTestConfig> = {
    develop: {
      environment: 'develop',
      baseURL: 'https://chat.dev.divinci.app',
      apiURL: 'https://api.dev.divinci.app',
      headers: {
        'CF-Access-Client-Id': process.env.CF_ACCESS_CLIENT_ID_DEV || '',
        'CF-Access-Client-Secret': process.env.CF_ACCESS_CLIENT_SECRET_DEV || '',
      },
      timeouts: {
        test: 60000,
        expect: 10000,
        navigation: 30000,
      },
    },
    staging: {
      environment: 'staging',
      baseURL: 'https://chat.stage.divinci.app',
      apiURL: 'https://api.stage.divinci.app',
      headers: {
        'CF-Access-Client-Id': process.env.CF_ACCESS_CLIENT_ID_STAGING || '',
        'CF-Access-Client-Secret': process.env.CF_ACCESS_CLIENT_SECRET_STAGING || '',
      },
      timeouts: {
        test: 90000,
        expect: 15000,
        navigation: 45000,
      },
    },
    production: {
      environment: 'production',
      baseURL: 'https://chat.divinci.app',
      apiURL: 'https://api.divinci.app',
      headers: {
        'CF-Access-Client-Id': process.env.CF_ACCESS_CLIENT_ID_PROD || '',
        'CF-Access-Client-Secret': process.env.CF_ACCESS_CLIENT_SECRET_PROD || '',
      },
      timeouts: {
        test: 120000,
        expect: 20000,
        navigation: 60000,
      },
    },
  };
  
  return configs[environment] || configs.develop;
}
```

## Usage Examples

### 1. Deploy and Test Development Environment
```bash
# Using act to simulate deployment with E2E testing
cd .github/act
./test-deploy-command.sh --event issue_comment --file events/deploy-develop-with-e2e.json
```

### 2. Run E2E Tests Against Existing Staging Environment
```bash
# Test existing staging deployment
cd workspace/clients/tests
TARGET_ENVIRONMENT=staging npm run test:e2e:cloud
```

### 3. Full Deploy-Test-Rollback Cycle
```bash
# Simulate full cycle with act
act issue_comment \
  -e .github/act/events/deploy-staging-with-e2e.json \
  --secret-file .github/act/secrets/staging.env \
  --container-architecture linux/amd64 \
  -j handle-deploy-command
```

## Benefits

### 🚀 **Faster Feedback**
- Immediate validation of deployments
- Early detection of integration issues
- Reduced time between code and production

### 🛡️ **Risk Mitigation**
- Automatic rollback on test failures
- Validation before production deployment
- Comprehensive test coverage

### 🔄 **Continuous Integration**
- Seamless integration with existing workflows
- Automated testing pipeline
- Consistent deployment validation

### 📊 **Better Visibility**
- Detailed test reports
- Deployment status tracking
- Performance metrics collection

## Next Steps

1. **Implement Phase 1**: Create enhanced act configurations
2. **Develop Cloud Test Scripts**: Build environment-specific test runners
3. **Enhance E2E Tests**: Add cloud-specific test scenarios
4. **Integration Testing**: Validate the complete workflow
5. **Documentation**: Create usage guides and troubleshooting docs
6. **Monitoring**: Add metrics and alerting for test failures

This plan leverages your existing infrastructure while adding powerful cloud testing capabilities that will significantly improve your deployment confidence and reduce production issues.
