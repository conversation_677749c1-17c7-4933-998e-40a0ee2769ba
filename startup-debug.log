+ set -e
+ for envfile in /workspaces/server/private-keys/local-fast/*.env
+ '[' -f /workspaces/server/private-keys/local-fast/api-dev.env ']'
+ set -a
+ . /workspaces/server/private-keys/local-fast/api-dev.env
++ NODE_ENV=development
++ LOG_DEBUG=1
++ ENVIRONMENT=local
++ ENABLE_MTLS=0
++ MTLS_CERT_DIR=/Users/<USER>/Documents/Divinci/server3/server/private-keys/local/certs/mtls
++ AUTH0_BASE_URL=https://dev-46tiys6hnb6vbg17.us.auth0.com
++ AUTH0_AUDIENCE=http://localhost:8081
++ AUTH0_CLIENT_ID=aPi4fs185lbPe8ZtQuQlG2w4lTVem0Gl
++ AUTH0_CLIENT_SECRET=****************************************************************
++ AUTH0_S2S_BASE_URL=https://dev-46tiys6hnb6vbg17.us.auth0.com
++ AUTH0_S2S_AUDIENCE=https://dev-46tiys6hnb6vbg17.us.auth0.com/api/v2/
++ AUTH0_S2S_CLIENT_ID=krlVBet0lsm75UWfoO2MIfmgXkBaFujM
++ AUTH0_S2S_CLIENT_SECRET=****************************************************************
++ OPENAI_API_KEY=***************************************************
++ OPENAI_ORGANIZATION=org-hnPNG30gqq7KYSfaJ8v92w1i
++ ANTHROPIC_SECRET_KEY=************************************************************************************************************
++ GOOGLE_CLOUD_STORAGE_PROJECT_ID=openai-api-4375643
++ GOOGLE_CLOUD_STORAGE_BUCKET_NAME=divinci-aichat-response-bucket-development
++ GOOGLE_CLOUD_DEFAULT_REGION=us-central1
++ UNSTRUCTURED_API_KEY=tN8h6hMJYjqdaSdT5BrA8XQaqav6hB
++ UNSTRUCTURED_API_URL=https://api.unstructuredapp.io/general/v0/general
++ UNSTRUCTURED_WORKER_URL=https://api.unstructuredapp.io/general/v0/general
++ STRIPE_API_PUBLIC_KEY=pk_test_51NkdNpIzNdoxvIKmyRJDOyY1yEv2rB5VsscNvsai5UrEoIBrQGSr9fbLnGjfBRtuwax27yAEUYgc9xqmgA8fZopG00Qh7KG0Py
++ STRIPE_API_SECRET_KEY=sk_test_51NkdNpIzNdoxvIKmOxGK0eGgON9EG1mocTwAWDminaygiFMVfBlZUGJkbEzvjDeCA6yncOWQQWXPt9h8WrilgX25002zyBz5ZC
++ TWILIO_ACCOUNT_SID=**********************************
++ TWILIO_AUTH_TOKEN=55ff28265d0a1c0a6f4daf9da395fa87
++ TWILIO_PHONE_NUMBER=***********
++ JSON_WEBTOKEN_SECRET=NOT_VERY_SECRET
+ set +a
+ for envfile in /workspaces/server/private-keys/local-fast/*.env
+ '[' -f /workspaces/server/private-keys/local-fast/audio-splitter-ffmpeg.env ']'
+ set -a
+ . /workspaces/server/private-keys/local-fast/audio-splitter-ffmpeg.env
++ MINIO_ROOT_USER=minioadmin
++ MINIO_ROOT_PASSWORD=minioadmin
++ MINIO_HOST=minio.divinci.local
++ MINIO_PORT=9000
+ set +a
+ for envfile in /workspaces/server/private-keys/local-fast/*.env
+ '[' -f /workspaces/server/private-keys/local-fast/cloudflare.env ']'
+ set -a
+ . /workspaces/server/private-keys/local-fast/cloudflare.env
++ CLOUDFLARE_ACCOUNT_ID=14a6fa23390363382f378b5bd4a0f849
++ CLOUDFLARE_API_KEY=****************************************
++ CLOUDFLARE_API_URL=https://api.cloudflare.com/client/v4
++ CLOUDFLARE_ACCESS_KEY_ID=251ae5ad677978bf68950c7448dd1e20
++ CLOUDFLARE_ACCESS_KEY_SECRET=2757474388c3c9f6f7638824995f87c3efeea2a36e895cccdb06b40886f1a4f9
++ CLOUDFLARE_WHITELABEL_VECTOR_ACCESS_KEY_ID=ad8ebe3b4611674996e0b487cea11061
++ CLOUDFLARE_WHITELABEL_VECTOR_ACCESS_KEY_SECRET=36d1719d5e54a7e1b8a8935c8ddf29a80fc2d54f553abd1b98167b77c3ca91d6
++ CLOUDFLARE_PUBLIC_TRANSCRIPT_FILE_ACCESS_KEY_ID=7a4b9ed4e9f2d02de1d6484e6b433f02
++ CLOUDFLARE_PUBLIC_TRANSCRIPT_FILE_ACCESS_KEY_SECRET=8f28017d6a8e9135a027cd28117015c7805dd6b663d6788af56bb44196fae7a3
++ CLOUDFLARE_PUBLIC_TRANSCRIPT_FILE_ACCESS_BUCKET=public-files
++ CLOUDFLARE_PUBLIC_TRANSCRIPT_FILE_ACCESS_PUBLIC_HOST=chat-uploads.divinci.app
++ CLOUDFLARE_WORKER_X_AUTH_DEV=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.4ygtkAA4RmHtGAT5jHKzpr_3HgpaVSVKQ-IqzFJA7TI2
++ CLOUDFLARE_D1_WORKER_BASE_URL=http://local-d1-rag:8787
++ CLOUDFLARE_EMAIL_WORKER_BASE_URL=https://email.divinci.app/send-notification
++ CLOUDFLARE_FINE_TUNE_TOKEN=****************************************
++ CLOUDFLARE_FINE_TUNE_ACCESS_KEY=ec82dc7efdfb3a638ea9559900cd323b
++ CLOUDFLARE_FINE_TUNE_SECRET_ACCESS_KEY=678b8632b6e4704edfd57a491882a752ce85cc61cc33af691a8706b2f8a05cce
++ CLOUDFLARE_FINE_TUNE_S3=https://14a6fa23390363382f378b5bd4a0f849.r2.cloudflarestorage.com
++ CLOUDFLARE_AUDIO_TOKEN=****************************************
++ CLOUDFLARE_AUDIO_ACCESS_KEY=6d03e15966ae6bab3a39c47b8808ef7e
++ CLOUDFLARE_AUDIO_SECRET_ACCESS_KEY=62ab47e8ce13db8bcae79ef6ec372bcd734f3b8261aeebf077787d57712a1a57
++ CLOUDFLARE_AUDIO_S3=https://14a6fa23390363382f378b5bd4a0f849.r2.cloudflarestorage.com
++ CLOUDFLARE_AUDIO_PUBLIC_URL=https://pub-2a04833210c8489198d3b76b8fb4f12d.r2.dev
++ R2_ACCESS_KEY_ID=7a4b9ed4e9f2d02de1d6484e6b433f02
++ R2_SECRET_ACCESS_KEY=8f28017d6a8e9135a027cd28117015c7805dd6b663d6788af56bb44196fae7a3
+ set +a
+ for envfile in /workspaces/server/private-keys/local-fast/*.env
+ '[' -f /workspaces/server/private-keys/local-fast/cloudflare.worker.env ']'
+ set -a
+ . /workspaces/server/private-keys/local-fast/cloudflare.worker.env
++ CLOUDFLARE_ACCOUNT_ID=14a6fa23390363382f378b5bd4a0f849
++ CLOUDFLARE_API_URL=https://api.cloudflare.com/client/v4
++ CLOUDFLARE_API_TOKEN=****************************************
++ CLOUDFLARE_WORKER_X_AUTH_DEV=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.4ygtkAA4RmHtGAT5jHKzpr_3HgpaVSVKQ-IqzFJA7TI2
++ CHUNKS_VECTORIZED_WORKFLOW_URL=http://local-chunks-vectorized:8791
++ CLOUDFLARE_D1_API_URL=http://local-d1-rag:8787
++ R2_BUCKET_URL=http://local-minio:9000
++ R2_ACCESS_KEY_ID=minioadmin
++ R2_SECRET_ACCESS_KEY=minioadmin
+ set +a
+ for envfile in /workspaces/server/private-keys/local-fast/*.env
+ '[' -f /workspaces/server/private-keys/local-fast/cors.env ']'
+ set -a
+ . /workspaces/server/private-keys/local-fast/cors.env
++ CORS_SUBDOMAINS=chat,api,live,webhook,login,rag-workflow,pyannote,ffmpeg,open-parse
++ CORS_SUBDOMAIN_ENVS=stage,dev,local
++ CORS_IP_ADDRESSES=************,*************,*************
++ CORS_PORTS=8080,8081,8082,8083,8084,9080,9081,8787,8788,8789,8790,9000
++ CORS_FULL_ORIGINS=api.slack.com,dev-46tiys6hnb6vbg17-cd-hljzhimsn7o28fvq.edge.tenants.us.auth0.com,dev-46tiys6hnb6vbg17.us.auth0.com,local-chunks-vectorized,127.0.0.1:
+ set +a
+ for envfile in /workspaces/server/private-keys/local-fast/*.env
+ '[' -f /workspaces/server/private-keys/local-fast/divinci.env ']'
+ set -a
+ . /workspaces/server/private-keys/local-fast/divinci.env
++ PRESIGNED_SECRET_LOCATION=HLZo8ChhgsA6ENVasg8zrOyZ094OJlU8
++ PRESIGNED_SECRET_SIGNITURE=0SfDgtPgBOYXLTS6yT1hQNWQOWhmrEsI
+ set +a
+ for envfile in /workspaces/server/private-keys/local-fast/*.env
+ '[' -f /workspaces/server/private-keys/local-fast/dropbox.env ']'
+ set -a
+ . /workspaces/server/private-keys/local-fast/dropbox.env
++ DROPBOX_CLIENT_ID=snqjeycw1gf5qr1
++ DROPBOX_CLIENT_SECRET=r7dzhtjoepm75jf
++ DROPBOX_REDIRECT_URI=http://localhost:9080/auth/dropbox/callback
+ set +a
+ for envfile in /workspaces/server/private-keys/local-fast/*.env
+ '[' -f /workspaces/server/private-keys/local-fast/endpoints.shared.env ']'
+ set -a
+ . /workspaces/server/private-keys/local-fast/endpoints.shared.env
++ RAW_CLIENT_IS_SECURE=1
++ RAW_CLIENT_HOST=localhost
++ WEB_CLIENT_PORT=8080
++ WEB_CLIENT_IS_SECURE=1
++ WEB_CLIENT_HOST=localhost:8080
++ EMBED_CLIENT_IS_SECURE=1
++ EMBED_CLIENT_HOST=localhost:8081
++ API_IS_SECURE=1
++ API_HOST=redesigned-engine-qjjxw7w9pjvc65gj-9080.app.github.dev
++ API_LIVE_IS_SECURE=1
++ API_LIVE_HOST=localhost:9081
++ API_TEST_IS_SECURE=1
++ API_TEST_PORT=18084
++ API_TEST_HOST=localhost:18084
+ set +a
+ for envfile in /workspaces/server/private-keys/local-fast/*.env
+ '[' -f /workspaces/server/private-keys/local-fast/firecrawl.env ']'
+ set -a
+ . /workspaces/server/private-keys/local-fast/firecrawl.env
++ FIRECRAWL_APIKEY=fc-c9f16db2cd9749c4b34c62f07d537740
+ set +a
+ for envfile in /workspaces/server/private-keys/local-fast/*.env
+ '[' -f /workspaces/server/private-keys/local-fast/hugging-face.env ']'
+ set -a
+ . /workspaces/server/private-keys/local-fast/hugging-face.env
++ HUGGING_FACE_ACCESS_TOKEN=*************************************
+ set +a
+ for envfile in /workspaces/server/private-keys/local-fast/*.env
+ '[' -f /workspaces/server/private-keys/local-fast/internal-workers.env ']'
+ set -a
+ . /workspaces/server/private-keys/local-fast/internal-workers.env
++ ENVIRONMENT=development
++ DIVINCI_AUDIO_DIARIZER_PYANNOTE_URL=http://audio-speak-dia-pyannote:5001
++ DIVINCI_AUDIO_DIARIZER_PYANNOTE_HTTP_PORT=5001
++ DIVINCI_AUDIO_SPLITTER_FFMPEG_URL=http://audio-splitter-ffmpeg:5002
++ DIVINCI_AUDIO_SPLITTER_FFMPEG_HTTP_PORT=5002
++ DIVINCI_AUDIO_TRANSCRIBER_WHISPER_URL=http://transcript-whisper-large-v3:5003
++ DIVINCI_AUDIO_TRANSCRIBER_WHISPER_HTTP_PORT=5003
++ OPENPARSE_API_URL=https://open-parse.dev.divinci.app
++ OPENPARSE_CORS_ORIGINS=http://localhost:8080,http://localhost:8081,http://localhost:8082,http://localhost:8083,http://localhost:8084,http://localhost:9080,http://localhost:9081,http://localhost:8787,http://localhost:8789
++ OPENPARSE_HTTP_PORT=8084
++ OLLAMA_API_URL=http://local-ollama:11434
++ WORKFLOW_API_URL=http://local-chunks-vectorized:8791
+ set +a
+ for envfile in /workspaces/server/private-keys/local-fast/*.env
+ '[' -f /workspaces/server/private-keys/local-fast/minio.env ']'
+ set -a
+ . /workspaces/server/private-keys/local-fast/minio.env
++ MINIO_ROOT_USER=minioadmin
++ MINIO_ROOT_PASSWORD=minioadmin
++ MINIO_ENDPOINT=http://minio.divinci.local:9000
+ set +a
+ for envfile in /workspaces/server/private-keys/local-fast/*.env
+ '[' -f /workspaces/server/private-keys/local-fast/mongodb.env ']'
+ set -a
+ . /workspaces/server/private-keys/local-fast/mongodb.env
++ MONGO_DOMAIN_HOSTNAME=serverlessinstance0.c4pobzg.mongodb.net
++ MONGO_INITDB_ROOT_USERNAME=divinciUser1
++ MONGO_INITDB_ROOT_PASSWORD=GYy2zwO9pJJtts0s
++ MONGO_IS_SRV=1
++ MONGODB_USERNAME=divinciUser1
++ MONGODB_PASSWORD=GYy2zwO9pJJtts0s
+ set +a
+ for envfile in /workspaces/server/private-keys/local-fast/*.env
+ '[' -f /workspaces/server/private-keys/local-fast/open-parse.env ']'
+ set -a
+ . /workspaces/server/private-keys/local-fast/open-parse.env
++ OLLAMA_API_URL=http://local-ollama:11434
++ MINIO_HOST=minio.divinci.local
++ MINIO_PORT=9000
++ MINIO_ROOT_USER=minioadmin
++ MINIO_ROOT_PASSWORD=minioadmin
++ CLOUDFLARE_API_KEY=****************************************
++ CLOUDFLARE_ACCOUNT_ID=14a6fa23390363382f378b5bd4a0f849
++ R2_ACCESS_KEY_ID=7a4b9ed4e9f2d02de1d6484e6b433f02
++ R2_SECRET_ACCESS_KEY=8f28017d6a8e9135a027cd28117015c7805dd6b663d6788af56bb44196fae7a3
+ set +a
+ for envfile in /workspaces/server/private-keys/local-fast/*.env
+ '[' -f /workspaces/server/private-keys/local-fast/postgres.env ']'
+ set -a
+ . /workspaces/server/private-keys/local-fast/postgres.env
++ POSTGRES_DOMAIN_HOSTNAME=local-postgres
++ POSTGRES_USER=postgres-user
++ POSTGRES_PASSWORD=example
+ set +a
+ for envfile in /workspaces/server/private-keys/local-fast/*.env
+ '[' -f /workspaces/server/private-keys/local-fast/pyannote.env ']'
+ set -a
+ . /workspaces/server/private-keys/local-fast/pyannote.env
++ DIVINCI_AUDIO_DIARIZER_PYANNOTE_HTTP_PORT=8085
++ PYANNOTE_APIKEY=*************************************
++ HUGGING_FACE_ACCESS_TOKEN=*************************************
++ DIVINCI_AUDIO_DIARIZER_PYANNOTE_URL=http://audio-speak-dia-pyannote:5001
++ LOG_LEVEL=INFO
++ MTLS_ENABLED=false
++ HOME=/home/<USER>
++ MPLCONFIGDIR=/home/<USER>/.cache/matplotlib
++ HF_HOME=/home/<USER>/.cache/huggingface
++ XDG_CACHE_HOME=/home/<USER>/.cache
+ set +a
+ for envfile in /workspaces/server/private-keys/local-fast/*.env
+ '[' -f /workspaces/server/private-keys/local-fast/rag.env ']'
+ set -a
+ . /workspaces/server/private-keys/local-fast/rag.env
++ QDRANT__SERVICE__API_KEY=secret-qdrant-rag-api-key
++ QDRANT_SECURE=0
++ QDRANT_HOSTNAME=local-qdrant
++ QDRANT_PORT=6333
+ set +a
+ for envfile in /workspaces/server/private-keys/local-fast/*.env
+ '[' -f /workspaces/server/private-keys/local-fast/redis.env ']'
+ set -a
+ . /workspaces/server/private-keys/local-fast/redis.env
++ REDIS_USERNAME=
++ REDIS_PASSWORD=
++ REDIS_DOMAIN_HOSTNAME=local-redis
++ REDIS_PORT=6379
+ set +a
+ for envfile in /workspaces/server/private-keys/local-fast/*.env
+ '[' -f /workspaces/server/private-keys/local-fast/stripe-products.env ']'
+ set -a
+ . /workspaces/server/private-keys/local-fast/stripe-products.env
++ STRIPE_PRODUCT_BASIC_TOKEN_SUBSCRIPTION=price_1PKTITIzNdoxvIKm0Wh6MHn3
+ set +a
+ for envfile in /workspaces/server/private-keys/local-fast/*.env
+ '[' -f /workspaces/server/private-keys/local-fast/test.env ']'
+ set -a
+ . /workspaces/server/private-keys/local-fast/test.env
++ TEST_ENV=local
++ ENVIRONMENT=local
++ AUTH0_TEST_USER_EMAIL=<EMAIL>
++ AUTH0_TEST_USER_PASSWORD=(abc123ABC)
++ DIVINCI_TEST_ENVIRONMENT=false
++ DIVINCI_TEST_PROCESS_DELAY=500
++ CF_ACCESS_CLIENT_ID=f13e5d39e1997a5ddb674362e73199c5.access
++ CF_ACCESS_CLIENT_SECRET=****************************************************************
++ WEB_TEST_CLIENT_IS_SECURE=1
++ WEB_TEST_CLIENT_PORT=18080
++ WEB_TEST_CLIENT_HOST=localhost
+ set +a
+ for envfile in /workspaces/server/private-keys/local-fast/*.env
+ '[' -f /workspaces/server/private-keys/local-fast/unit-test.env ']'
+ set -a
+ . /workspaces/server/private-keys/local-fast/unit-test.env
++ ENV_FOLDER=/Users/<USER>/Documents/Divinci/server/private-keys/local
+ set +a
+ for envfile in /workspaces/server/private-keys/local-fast/*.env
+ '[' -f /workspaces/server/private-keys/local-fast/web-client-dev.env ']'
+ set -a
+ . /workspaces/server/private-keys/local-fast/web-client-dev.env
++ ENVIRONMENT=local
++ LOG_DEBUG=1
++ AUTH0_CLIENT_ID=aPi4fs185lbPe8ZtQuQlG2w4lTVem0Gl
++ AUTH0_CLIENT_DOMAIN=dev-46tiys6hnb6vbg17.us.auth0.com
++ AUTH0_AUDIENCE=http://localhost:8081
++ SPEECHLY_APP_ID=39580c4d-c98a-4924-ba0c-153809a8648d
++ CLOUDFLARE_API_URL=https://api.cloudflare.com/client/v4
++ CLOUDFLARE_WORKER_X_AUTH_DEV=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.4ygtkAA4RmHtGAT5jHKzpr_3HgpaVSVKQ-IqzFJA7TI2
++ API_IS_SECURE=1
++ API_HOST=localhost:9080
++ API_LIVE_IS_SECURE=1
++ API_LIVE_HOST=localhost:9081
++ API_MTLS_ENABLED=0
++ DD_API_KEY=pubba62e7aeff00ed649dff480bfc4093e4
++ DD_API_ID=b5f8affa-cb8b-4d69-bc04-2b18b12b9fb5
++ CLOUDFLARE_API_URL=https://api.cloudflare.com/client/v4
++ CLOUDFLARE_ACCOUNT_ID=14a6fa23390363382f378b5bd4a0f849
++ CLOUDFLARE_ZONE_ID=9b26e2c415f36b0f656204133c8ab87c
++ CLOUDFLARE_API_TOKEN=****************************************
++ CLOUDFLARE_PUBLIC_WEB_APP_BUCKET=public-assets
++ CLOUDFLARE_PUBLIC_WEB_APP_HOST=assets.local.divinci.app
++ CLOUDFLARE_PUBLIC_WEB_APP_ACCESS_KEY_ID=30a88e14196735b4ba4eb3250d2f73b9
++ CLOUDFLARE_PUBLIC_WEB_APP_ACCESS_KEY_SECRET=36cff2235c19f69ea1d124bd50dac32e641c9b90300eb44e01621382cb0403df
++ COPILOTKIT_API_KEY=ck_pub_d77b439c42b82722dba7ef56db1ca4e2
++ CHUNKS_VECTORIZED_WORKFLOW_URL=http://localhost:8791
+ set +a
+ cd /workspaces/server
+ pnpm install
workspace/resources/models               |  WARN  The field "pnpm.neverBuiltDependencies" was found in /workspaces/server/workspace/resources/models/package.json. This will not take effect. You should configure "pnpm.neverBuiltDependencies" at the root of the workspace instead.
workspace/resources/server-globals       |  WARN  The field "pnpm.neverBuiltDependencies" was found in /workspaces/server/workspace/resources/server-globals/package.json. This will not take effect. You should configure "pnpm.neverBuiltDependencies" at the root of the workspace instead.
workspace/resources/server-permissions   |  WARN  The field "pnpm.neverBuiltDependencies" was found in /workspaces/server/workspace/resources/server-permissions/package.json. This will not take effect. You should configure "pnpm.neverBuiltDependencies" at the root of the workspace instead.
workspace/resources/server-tools         |  WARN  The field "pnpm.neverBuiltDependencies" was found in /workspaces/server/workspace/resources/server-tools/package.json. This will not take effect. You should configure "pnpm.neverBuiltDependencies" at the root of the workspace instead.
workspace/resources/utils                |  WARN  The field "pnpm.neverBuiltDependencies" was found in /workspaces/server/workspace/resources/utils/package.json. This will not take effect. You should configure "pnpm.neverBuiltDependencies" at the root of the workspace instead.
Scope: all 19 workspace projects
Progress: resolved 1, reused 0, downloaded 0, added 0

   ╭──────────────────────────────────────────╮
   │                                          │
   │   Update available! 10.11.0 → 10.11.1.   │
   │   Changelog: https://pnpm.io/v/10.11.1   │
   │     To update, run: pnpm add -g pnpm     │
   │                                          │
   ╰──────────────────────────────────────────╯

 WARN  deprecated eslint@8.57.1: This version is no longer supported. Please see https://eslint.org/version-support for other options.
workspace/clients/embed                  |  WARN  deprecated @types/dompurify@3.2.0
Progress: resolved 71, reused 0, downloaded 0, added 0
workspace/resources/server-globals       |  WARN  deprecated @types/redis@4.0.11
workspace/resources/server-globals       |  WARN  deprecated multer@1.4.5-lts.2
Progress: resolved 168, reused 1, downloaded 0, added 0
workspace/servers/public-api-live        |  WARN  deprecated @types/cookie@1.0.0
Progress: resolved 334, reused 1, downloaded 0, added 0
Progress: resolved 626, reused 1, downloaded 0, added 0
Progress: resolved 877, reused 1, downloaded 0, added 0
Progress: resolved 1227, reused 1, downloaded 0, added 0
Progress: resolved 1579, reused 1, downloaded 0, added 0
Progress: resolved 1937, reused 1, downloaded 0, added 0
Progress: resolved 2114, reused 1, downloaded 0, added 0
 WARN  15 deprecated subdependencies found: @humanwhocodes/config-array@0.13.0, @humanwhocodes/object-schema@2.0.3, @types/long@5.0.0, abab@2.0.6, domexception@4.0.0, glob@7.2.3, google-p12-pem@4.0.1, inflight@1.0.6, lodash.get@4.4.2, node-domexception@1.0.0, read-package-json@7.0.1, rimraf@2.7.1, rimraf@3.0.2, superagent@8.1.2, trim@0.0.1

Progress: resolved 2115, reused 14, downloaded 0, added 0
Progress: resolved 2115, reused 79, downloaded 0, added 0
Packages: +11 -81
+++++++++-----------------------------------------------------------------------
Progress: resolved 2115, reused 91, downloaded 0, added 11, done
workspace/resources/utils prepare$ rimraf ./dist && tsc
. prepare$ husky
. prepare: Done
workspace/resources/utils prepare: Done
workspace/resources/models prepare$ rimraf ./dist && tsc
workspace/resources/server-utils prepare$ rimraf ./dist && tsc
workspace/resources/models prepare: Done
workspace/resources/server-utils prepare: Done
workspace/resources/actions prepare$ rimraf ./dist && tsc
workspace/clients/embed prepare$ rimraf ./dist && tsc --project tsconfig.json
workspace/resources/server-globals prepare$ rimraf ./dist && tsc
workspace/resources/tools prepare$ rimraf ./dist && tsc
workspace/resources/actions prepare: Done
workspace/resources/tools prepare: Done
workspace/clients/embed prepare: Done
workspace/resources/server-globals prepare: Done
workspace/resources/mtls prepare$ npm run build
workspace/clients/web prepare$ rimraf ./dist && tsc --skipLibCheck
workspace/resources/server-tools prepare$ rimraf ./dist && tsc --skipLibCheck
workspace/resources/mtls prepare: > @divinci-ai/mtls@0.1.0 build
workspace/resources/mtls prepare: > tsc
workspace/resources/mtls prepare: npm notice
workspace/resources/mtls prepare: npm notice New major version of npm available! 10.9.2 -> 11.4.1
workspace/resources/mtls prepare: npm notice Changelog: https://github.com/npm/cli/releases/tag/v11.4.1
workspace/resources/mtls prepare: npm notice To update run: npm install -g npm@11.4.1
workspace/resources/mtls prepare: npm notice
workspace/resources/mtls prepare: Done
workspace/resources/server-tools prepare: Done
workspace/clients/web prepare: Done
workspace/clients/tests prepare$ rimraf ./dist && tsc && node scripts/copy-html.js || echo 'TypeScript errors ignored'
workspace/resources/server-models prepare$ rimraf ./dist && tsc --skipLibCheck
workspace/clients/tests prepare: Copied src/globals/auth0/index.html to dist/globals/auth0/index.html
workspace/clients/tests prepare: Copied src/globals/stripe-subscription/index.html to dist/globals/stripe-subscription/index.html
workspace/clients/tests prepare: Copied src/globals/stripe-payment-method/index.html to dist/globals/stripe-payment-method/index.html
workspace/clients/tests prepare: Done
workspace/resources/server-models prepare: Done
workspace/resources/server-permissions prepare$ rimraf ./dist && tsc --skipLibCheck
workspace/servers/public-api-webhook prepare$ rimraf ./dist && tsc
workspace/servers/test-api prepare$ rimraf ./dist && tsc || echo 'ℹ️ TypeScript errors ignored'
workspace/resources/server-permissions prepare: Done
workspace/servers/public-api-webhook prepare: Done
workspace/servers/test-api prepare: Done
workspace/servers/public-api prepare$ rimraf ./dist && tsc
workspace/servers/public-api-live prepare$ rimraf ./dist && tsc
workspace/servers/public-api-live prepare: Done
workspace/servers/public-api prepare: Done
Done in 2m 1.4s using pnpm v10.11.0
+ cd /workspaces/server/workspace/servers/public-api
+ echo '[public-api] Building...'
[public-api] Building...
+ pnpm run build

> @divinci-ai/public-api@0.3.0 build /workspaces/server/workspace/servers/public-api
> tsc

+ echo '[public-api] Starting...'
[public-api] Starting...
+ export HTTP_PORT=8081
+ HTTP_PORT=8081
+ PUBLIC_API_PID=67407
+ cd /workspaces/server/workspace/servers/public-api-live
+ pnpm start
+ echo '[public-api-live] Building...'
[public-api-live] Building...
+ pnpm run build

> @divinci-ai/public-api-live@0.3.0 build /workspaces/server/workspace/servers/public-api-live
> tsc

+ echo '[public-api-live] Starting...'
[public-api-live] Starting...
+ export HTTP_PORT=8082
+ HTTP_PORT=8082
+ PUBLIC_API_LIVE_PID=67508
+ pnpm start
+ cd /workspaces/server/workspace/servers/public-api-webhook
+ echo '[public-api-webhook] Building...'
[public-api-webhook] Building...
+ pnpm run build

> @divinci-ai/public-api-webhook@0.3.0 build /workspaces/server/workspace/servers/public-api-webhook
> tsc

+ echo '[public-api-webhook] Starting...'
[public-api-webhook] Starting...
+ export HTTP_PORT=8083
+ HTTP_PORT=8083
+ PUBLIC_API_WEBHOOK_PID=67616
+ SRC_KEY=/workspaces/server/private-keys/local-fast/credentials/google-service-key.json
+ DEST_KEY=/workspaces/server/private-keys/local/credentials/google-service-key.json
+ pnpm start
++ dirname '"/workspaces/server/private-keys/local/credentials/google-service-key.json"'
+ mkdir -p '"/workspaces/server/private-keys/local/credentials'
++ realpath /workspaces/server/private-keys/local-fast/credentials/google-service-key.json
++ realpath /workspaces/server/private-keys/local/credentials/google-service-key.json
+ '[' /workspaces/server/private-keys/local-fast/credentials/google-service-key.json '!=' /workspaces/server/private-keys/local-fast/credentials/google-service-key.json ']'
+ cd /workspaces/server/workspace/clients/web
+ export ENV_FOLDER=/workspaces/server/workspace/clients/web/env
+ ENV_FOLDER=/workspaces/server/workspace/clients/web/env
+ echo '[web-client] Building...'
[web-client] Building...
+ pnpm run build

> @divinci-ai/web-client@0.3.0 build /workspaces/server/workspace/clients/web
> webpack --mode development --config webpack.config.js

🐞LOG_DEBUG MODE:  1
set __env: /workspaces/server/workspace/clients/web/env
assets by status 30.9 KiB [cached] 3 assets
assets by chunk 16.5 MiB (id hint: vendors)
  assets by status 1.24 MiB [emitted]
    asset vendors-node_modules_mermaid_dist_createText-2e5e7dd3_js.hidden.build.js 1.11 MiB [emitted] [compared for emit] (id hint: vendors)
    asset vendors-node_modules_mermaid_dist_sankeyDiagram-04a897e0_js.hidden.build.js 131 KiB [emitted] (id hint: vendors)
  + 30 assets
asset hidden.build.js 48.7 MiB [emitted] [compared for emit] (name: main)
asset node_modules_mermaid_dist_classDiagram-v2-f2320105_js.hidden.build.js 30.1 KiB [compared for emit]
asset node_modules_mermaid_dist_flowDiagram-v2-96b9c2cf_js.hidden.build.js 6.12 KiB [compared for emit]
asset _6876-_31c0-_8290-_c430-_97c6-_cd79.hidden.build.js 2.41 KiB [compared for emit]
asset index.html 1.69 KiB [compared for emit]
asset _4860.hidden.build.js 691 bytes [compared for emit]
asset _411a.hidden.build.js 647 bytes [compared for emit]
orphan modules 1.2 MiB [orphan] 1100 modules
runtime modules 7.46 KiB 13 modules
modules by path ../../ 21.9 MiB (javascript) 30.9 KiB (asset)
  modules by path ../../../node_modules/ 21.6 MiB (javascript) 30.9 KiB (asset)
    javascript modules 21.5 MiB 1997 modules
    json modules 99.9 KiB 8 modules
    asset modules 30.9 KiB (asset) 126 bytes (javascript) 3 modules
  modules by path ../../resources/ 272 KiB 230 modules
modules by path ./ 1.92 MiB 735 modules
modules by mime type image/svg+xml 6.61 KiB
  data:image/svg+xml;base64,PHN2ZyB3aWR0aD0i.. 6.36 KiB [built] [code generated]
  data:image/svg+xml;utf8,<svg xmlns="http.. 252 bytes [built] [code generated]
+ 10 modules
webpack 5.99.9 compiled successfully in 35707 ms
+ echo '[web-client] Starting...'
[web-client] Starting...
+ export HTTP_PORT=8080
+ HTTP_PORT=8080
+ WEB_CLIENT_PID=67872
+ pnpm start
+ cd /workspaces/server
+ echo 'All services started. Logs:'
All services started. Logs:
+ echo '  public-api:         tail -f /workspaces/server/public-api.log'
  public-api:         tail -f /workspaces/server/public-api.log
+ echo '  public-api-live:    tail -f /workspaces/server/public-api-live.log'
  public-api-live:    tail -f /workspaces/server/public-api-live.log
+ echo '  public-api-webhook: tail -f /workspaces/server/public-api-webhook.log'
  public-api-webhook: tail -f /workspaces/server/public-api-webhook.log
+ echo '  web-client:         tail -f /workspaces/server/web-client.log'
  web-client:         tail -f /workspaces/server/web-client.log
+ echo 'To stop all: kill 67407 67508 67616 67872'
To stop all: kill 67407 67508 67616 67872
+ sleep 5
+ SERVICES=(8080 8081 8082 8083)
+ ALL_HEALTHY=true
+ for PORT in "${SERVICES[@]}"
++ curl -s -o /dev/null -w '%{http_code}' http://localhost:8080/
+ STATUS=200
+ '[' 200 '!=' 200 ']'
+ echo '✅ Service on port 8080 responded (HTTP 200)'
✅ Service on port 8080 responded (HTTP 200)
+ for PORT in "${SERVICES[@]}"
++ curl -s -o /dev/null -w '%{http_code}' http://localhost:8081/
++ echo 000
+ STATUS=000000
+ '[' 000000 '!=' 200 ']'
+ '[' 000000 '!=' 404 ']'
+ echo '❌ Service on port 8081 is not healthy (HTTP 000000)'
❌ Service on port 8081 is not healthy (HTTP 000000)
+ ALL_HEALTHY=false
+ for PORT in "${SERVICES[@]}"
++ curl -s -o /dev/null -w '%{http_code}' http://localhost:8082/
++ echo 000
+ STATUS=000000
+ '[' 000000 '!=' 200 ']'
+ '[' 000000 '!=' 404 ']'
+ echo '❌ Service on port 8082 is not healthy (HTTP 000000)'
❌ Service on port 8082 is not healthy (HTTP 000000)
+ ALL_HEALTHY=false
+ for PORT in "${SERVICES[@]}"
++ curl -s -o /dev/null -w '%{http_code}' http://localhost:8083/
++ echo 000
+ STATUS=000000
+ '[' 000000 '!=' 200 ']'
+ '[' 000000 '!=' 404 ']'
+ echo '❌ Service on port 8083 is not healthy (HTTP 000000)'
❌ Service on port 8083 is not healthy (HTTP 000000)
+ ALL_HEALTHY=false
+ '[' false = true ']'
+ echo 'Some services failed health checks. See logs above.'
Some services failed health checks. See logs above.
