/**
 * Refresh Dropbox access token
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { dropboxOAuth } from "@divinci-ai/server-globals";
import { DropboxConnectionModel } from "@divinci-ai/server-models";
import { ensureWhitelabel } from "../util/ensure-whitelabel";

export const refreshDropboxToken: RequestHandler = async function(req, res, next){
  try {
    const { whitelabel, userId } = await ensureWhitelabel(req);

    // Find active connection for this user
    if(!userId) {
      return res.status(400).json({
        success: false,
        error: "user_id_missing",
        message: "User ID is required"
      });
    }

    const connection = await DropboxConnectionModel.findActiveConnection(
      whitelabel._id?.toString() || "",
      userId
    );

    if(!connection) {
      return res.status(404).json({
        success: false,
        error: "connection_not_found",
        message: "No active Dropbox connection found"
      });
    }

    if(!connection.refreshToken) {
      return res.status(400).json({
        success: false,
        error: "no_refresh_token",
        message: "No refresh token available for this connection"
      });
    }

    // Refresh the token
    const tokenResponse = await dropboxOAuth.refreshToken(connection.refreshToken);

    // Update connection with new tokens
    await connection.updateTokens(
      tokenResponse.access_token,
      tokenResponse.refresh_token || connection.refreshToken,
      tokenResponse.expires_in ? new Date(Date.now() + tokenResponse.expires_in * 1000) : connection.expiresAt
    );

    res.status(200).json({
      success: true,
      data: {
        connectionId: connection._id.toString(),
        refreshedAt: new Date(),
        expiresAt: connection.expiresAt,
        message: "Access token refreshed successfully"
      }
    });

  }catch(error) {
    console.error("Token refresh error:", error);

    // If refresh fails, mark connection as expired
    try {
      const connection = await DropboxConnectionModel.findActiveConnection(
        req.params.whitelabelId,
        (req as any).user?.sub
      );
      if(connection) {
        await connection.updateStatus("expired" as any, "Token refresh failed");
      }
    }catch(updateError) {
      console.error("Failed to update connection status:", updateError);
    }

    res.status(500).json({
      success: false,
      error: "token_refresh_failed",
      message: error instanceof Error ? error.message : "Token refresh failed"
    });
  }
};
