<<<<<<< HEAD
import React from "react";
import { InputProps } from "../../../../../../util/react/input";
import { PublicToolInfoDisplay } from "../../../../../../components/displays/PublicToolInfo";
import { useAudioTools } from "../data/AudioTools";
import { useHealthCheck } from "../../../../../../contexts/HealthCheckContext";
=======
import React, { useState } from "react";
// Removed unused InputProps import
import { PublicToolInfoDisplay } from "../../../../../../components/displays/PublicToolInfo";
import { useAudioTools } from "../data/AudioTools";
import { useHealthCheck } from "../../../../../../contexts/HealthCheckContext";
import { ConfidenceSettings } from "./ConfidenceSettings";
>>>>>>> WA-170_MCP
import styles from "./diarizeTool.module.css";

// Define the ToolInfo interface
interface ToolInfo {
  title: string;
  [key: string]: any;
}

// Constants for tool IDs
const OFFICIAL_PYANNOTE_ID = "@pyannote/pyannote-segmentation";
const DIVINCI_PYANNOTE_ID = "@divinci-ai/pyannote-segmentation";

// Separate component for status indicator to avoid hooks in the main component
const StatusIndicator = ({ toolId }: { toolId: string }) => {
  const { serviceStatuses } = useHealthCheck();

  // Map tool IDs to service status keys
  const serviceKey = toolId === OFFICIAL_PYANNOTE_ID ? 'pyannote' :
                     toolId === DIVINCI_PYANNOTE_ID ? 'divinci_pyannote' : null;

  if (!serviceKey) return null;

  const status = serviceStatuses[serviceKey]?.status || 'checking';

  if (status === 'checking') {
    return (
      <div className={styles.statusContainer} title="Checking service status...">
        <div className={styles.spinner} />
        <span className={styles.statusText}>Checking...</span>
      </div>
    );
  } else if (status === 'available') {
    return (
      <div className={styles.statusContainer} title="Service is available">
        <div
          className={styles.statusCircle}
          style={{ backgroundColor: '#48c774' }}
        />
        <span className={styles.statusText}>Ready</span>
      </div>
    );
  } else {
    return (
      <div className={styles.statusContainer} title="Service is unavailable">
        <div
          className={styles.statusCircle}
          style={{ backgroundColor: '#f14668' }}
        />
        <span className={styles.statusText}>Offline</span>
      </div>
    );
  }
};

// Separate component for notifying parent components
const ServiceStatusNotifier = () => {
  const { serviceStatuses } = useHealthCheck();

  // Send message to parent components
  React.useEffect(() => {
    const pyannoteAvailable = serviceStatuses.pyannote?.status === 'available';
    window.postMessage({
      type: 'SERVICE_STATUS_CHANGE',
      available: pyannoteAvailable
    }, '*');
  }, [serviceStatuses]);

  return null;
};

// Separate component for setting default tool
const DefaultToolSetter = ({ value, onChange }: { value: string, onChange: (value: string) => void }) => {
  const { value: audioTools } = useAudioTools();

  React.useEffect(() => {
    if (!audioTools) return;
    const { diarizers: available } = audioTools;
    if (Object.entries(available).length === 0) return;
    if (value) return;

    // Set Official Pyannote as default if available
    if (available[OFFICIAL_PYANNOTE_ID]) {
      onChange(OFFICIAL_PYANNOTE_ID);
    } else {
      // Fallback to first available option
      onChange(Object.keys(available)[0]);
    }
  }, [audioTools, onChange, value]);

  return null;
};

<<<<<<< HEAD
export function DiarizeTool({ value, onChange }: InputProps<string>) {
  const { value: audioTools } = useAudioTools();
=======
// Enhanced configuration interface supporting both open-source and official pyannote
interface DiarizeConfig {
  tool: string;
  numSpeakers?: 1 | 2;
  showAdvanced: boolean;
  // Confidence settings (only available for Official Pyannote API)
  confidence?: {
    enabled: boolean;
    threshold: number;
    includeInProcessing: boolean;
  };
}

interface DiarizeToolProps {
  value: DiarizeConfig;
  onChange: (config: DiarizeConfig) => void;
}

export function DiarizeTool({ value, onChange }: DiarizeToolProps) {
  const { value: audioTools } = useAudioTools();
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(value.showAdvanced || false);
>>>>>>> WA-170_MCP

  // Check if an option should be disabled
  const isOptionDisabled = (toolId: string) => {
    // return toolId === DIVINCI_PYANNOTE_ID;
    return false;
  };

<<<<<<< HEAD
=======
  // Handle tool selection change
  const handleToolChange = (newTool: string) => {
    onChange({
      ...value,
      tool: newTool
    });
  };

  // Handle confidence settings change (only for Official Pyannote API)
  const handleConfidenceChange = (confidence: DiarizeConfig['confidence']) => {
    onChange({
      ...value,
      confidence
    });
  };

  // Handle speaker count change
  const handleSpeakerCountChange = (numSpeakers?: 1 | 2) => {
    onChange({
      ...value,
      numSpeakers
    });
  };

  // Toggle advanced options
  const toggleAdvancedOptions = () => {
    const newShowAdvanced = !showAdvancedOptions;
    setShowAdvancedOptions(newShowAdvanced);
    onChange({
      ...value,
      showAdvanced: newShowAdvanced
    });
  };

>>>>>>> WA-170_MCP
  if (!audioTools) return <div>Loading...</div>;

  // Type assertion for audioTools.diarizers
  const diarizers = audioTools.diarizers as Record<string, ToolInfo>;

  return (
    <div className="diarizeTool">
      {/* Components with hooks */}
<<<<<<< HEAD
      <DefaultToolSetter value={value} onChange={onChange} />
      <ServiceStatusNotifier />

      <h1 className="title">Choose your Speaker Diarizer</h1>
=======
      <DefaultToolSetter value={value.tool} onChange={handleToolChange} />
      <ServiceStatusNotifier />

      <h1 className="title">Choose your Speaker Diarizer</h1>

      {/* Tool selection */}
>>>>>>> WA-170_MCP
      <div className="field">
        <div className="control">
          <div className={`select ${styles.selectWithStatus}`}>
            <select
<<<<<<< HEAD
              value={value}
              className={`select ${styles.selectWidth}`}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => onChange(e.target.value)}
=======
              value={value.tool}
              className={`select ${styles.selectWidth}`}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => handleToolChange(e.target.value)}
>>>>>>> WA-170_MCP
            >
              {Object.entries(diarizers).map(([key, toolInfo]) => (
                <option
                  key={key}
                  value={key}
                  disabled={isOptionDisabled(key)}
                  className={isOptionDisabled(key) ? styles.disabledOption : ''}
                >
                  {toolInfo.title} {isOptionDisabled(key) ? '(Unavailable)' : ''}
                </option>
              ))}
            </select>
            <span className={styles.statusIndicator}>
<<<<<<< HEAD
              <StatusIndicator toolId={value} />
=======
              <StatusIndicator toolId={value.tool} />
>>>>>>> WA-170_MCP
            </span>
          </div>
        </div>
      </div>
<<<<<<< HEAD
      {!value ? null : <PublicToolInfoDisplay tool={diarizers[value] as any} />}
=======

      {/* Tool info display */}
      {!value.tool ? null : <PublicToolInfoDisplay tool={diarizers[value.tool] as any} />}

      {/* Advanced options toggle */}
      <div className={styles.advancedToggle}>
        <button
          type="button"
          onClick={toggleAdvancedOptions}
          className={styles.advancedButton}
        >
          <span className="icon">
            <i className={`fas fa-chevron-${showAdvancedOptions ? 'up' : 'down'}`}></i>
          </span>
          Advanced Options
        </button>
      </div>

      {/* Advanced options */}
      {showAdvancedOptions && (
        <div className={styles.advancedOptions}>
          {/* Speaker count setting */}
          <div className={styles.speakerCountSection}>
            <h4 className={styles.sectionTitle}>
              <span className="icon">
                <i className="fas fa-users"></i>
              </span>
              Number of Speakers
            </h4>
            <div className={styles.speakerCountOptions}>
              <label className={styles.radioLabel}>
                <input
                  type="radio"
                  name="speakerCount"
                  checked={value.numSpeakers === undefined}
                  onChange={() => handleSpeakerCountChange(undefined)}
                  className={styles.radioInput}
                />
                <span className={styles.radioText}>Auto-detect</span>
                <span className={styles.radioHelp}>Let the system determine the number of speakers</span>
              </label>
              <label className={styles.radioLabel}>
                <input
                  type="radio"
                  name="speakerCount"
                  checked={value.numSpeakers === 1}
                  onChange={() => handleSpeakerCountChange(1)}
                  className={styles.radioInput}
                />
                <span className={styles.radioText}>1 Speaker</span>
                <span className={styles.radioHelp}>Single speaker audio (monologue)</span>
              </label>
              <label className={styles.radioLabel}>
                <input
                  type="radio"
                  name="speakerCount"
                  checked={value.numSpeakers === 2}
                  onChange={() => handleSpeakerCountChange(2)}
                  className={styles.radioInput}
                />
                <span className={styles.radioText}>2 Speakers</span>
                <span className={styles.radioHelp}>Two-person conversation (interview, call)</span>
              </label>
            </div>
          </div>

          {/* Show different content based on selected tool */}
          {value.tool === DIVINCI_PYANNOTE_ID ? (
            <div className="notification is-info is-light mt-4">
              <span className="icon-text">
                <span className="icon">
                  <i className="fas fa-info-circle"></i>
                </span>
                <span>
                  <strong>Open-source Pyannote</strong> provides basic speaker diarization.
                  Advanced features like confidence scoring and voiceprints are not available in the open-source version.
                </span>
              </span>
            </div>
          ) : value.tool === OFFICIAL_PYANNOTE_ID ? (
            <>
              <div className="notification is-success is-light mt-4">
                <span className="icon-text">
                  <span className="icon">
                    <i className="fas fa-star"></i>
                  </span>
                  <span>
                    <strong>Official Pyannote API</strong> provides enhanced speaker diarization with confidence scoring and advanced features.
                  </span>
                </span>
              </div>

              {/* Confidence settings for Official Pyannote API */}
              <ConfidenceSettings
                value={value.confidence || { enabled: false, threshold: 0.7, includeInProcessing: true }}
                onChange={handleConfidenceChange}
              />
            </>
          ) : (
            <div className="notification is-light mt-4">
              <span className="icon-text">
                <span className="icon">
                  <i className="fas fa-info-circle"></i>
                </span>
                <span>
                  Advanced options available based on selected diarization service.
                </span>
              </span>
            </div>
          )}
        </div>
      )}
>>>>>>> WA-170_MCP
    </div>
  );
}
