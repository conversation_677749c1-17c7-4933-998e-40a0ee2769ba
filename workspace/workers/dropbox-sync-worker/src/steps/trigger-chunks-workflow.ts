/**
 * Trigger chunks workflow for RAG integration
 */

// WorkflowStep type (would be imported from cloudflare:workers in real deployment)
type WorkflowStep = any;
import { 
  StepContext, 
  TriggerChunksWorkflowResult,
  DropboxSyncProcessingConfig,
  AudioProcessingResult,
  FileDownloadResult,
  ChunksWorkflowResult,
  DropboxFileProcessingStatus
} from "./types";

export async function triggerChunksWorkflow(
  step: WorkflowStep,
  context: StepContext,
  processedFiles: (AudioProcessingResult & {
    dropboxFileId: string;
    fileName: string;
    downloadResult: FileDownloadResult;
  })[],
  processingConfig: DropboxSyncProcessingConfig,
  targetType: string
): Promise<TriggerChunksWorkflowResult> {
  return await step.do("triggerChunksWorkflow", async () => {
    console.log("🔄 Starting chunks workflow triggers", {
      totalFiles: processedFiles.length,
      targetType,
      ragId: processingConfig.ragId
    });

    const triggeredWorkflows: (ChunksWorkflowResult & {
      dropboxFileId: string;
      fileName: string;
    })[] = [];
    
    const failedTriggers: {
      dropboxFileId: string;
      fileName: string;
      error: string;
    }[] = [];

    // Only process for RAG target type
    if (targetType !== "rag") {
      console.log("⏭️ Skipping chunks workflow for non-RAG target type");
      return {
        triggeredWorkflows,
        failedTriggers
      };
    }

    if (!processingConfig.ragId) {
      throw new Error("RAG ID is required for RAG target type");
    }

    // Process files sequentially to avoid overwhelming the chunks workflow
    for (const file of processedFiles) {
      try {
        console.log(`🔄 Triggering chunks workflow for: ${file.fileName}`);

        // Update status to chunking
        await updateFileStatus(context, file.dropboxFileId, DropboxFileProcessingStatus.CHUNKING);

        // Create file record in the system first
        const fileRecord = await createFileRecord(context, file, processingConfig);

        // Trigger chunks workflow
        const workflowResult = await triggerChunksWorkflowForFile(
          context, 
          file, 
          fileRecord.fileId,
          processingConfig
        );

        triggeredWorkflows.push({
          ...workflowResult,
          dropboxFileId: file.dropboxFileId,
          fileName: file.fileName
        });

        // Update status to vectorizing
        await updateFileStatus(context, file.dropboxFileId, DropboxFileProcessingStatus.VECTORIZING);

        console.log(`✅ Successfully triggered chunks workflow for: ${file.fileName}`, {
          workflowId: workflowResult.workflowId,
          fileId: workflowResult.fileId
        });

      } catch (error) {
        console.error(`❌ Failed to trigger chunks workflow for: ${file.fileName}`, error);
        
        failedTriggers.push({
          dropboxFileId: file.dropboxFileId,
          fileName: file.fileName,
          error: error instanceof Error ? error.message : String(error)
        });

        // Update file status to failed
        await updateFileStatus(
          context, 
          file.dropboxFileId, 
          DropboxFileProcessingStatus.FAILED,
          error instanceof Error ? error.message : String(error)
        );
      }
    }

    console.log("🔄 Chunks workflow triggers completed", {
      successful: triggeredWorkflows.length,
      failed: failedTriggers.length
    });

    return {
      triggeredWorkflows,
      failedTriggers
    };
  });
}

/**
 * Create file record in the system
 */
async function createFileRecord(
  context: StepContext,
  file: AudioProcessingResult & {
    dropboxFileId: string;
    fileName: string;
    downloadResult: FileDownloadResult;
  },
  config: DropboxSyncProcessingConfig
): Promise<{ fileId: string }> {
  console.log(`📝 Creating file record for: ${file.fileName}`);

  const response = await fetch(`${context.env.API_HOST}/ux/whitelabel/${context.whitelabelId}/rag-vector/files`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${context.auth0Token}`,
      "cloudflare-worker-x-dev-auth": context.env.CLOUDFLARE_WORKER_X_AUTH_DEV || ""
    },
    body: JSON.stringify({
      fileName: file.fileName,
      fileSize: file.downloadResult.fileSize,
      mimeType: file.downloadResult.mimeType,
      bucket: "dropbox-sync-files", // R2 bucket name
      objectKey: file.downloadResult.objectKey,
      source: "dropbox",
      metadata: {
        dropboxFileId: file.dropboxFileId,
        transcriptionId: file.transcriptionId,
        diarizationId: file.diarizationId,
        processingDuration: file.processingDuration,
        jobId: context.jobId,
        syncedAt: new Date().toISOString()
      }
    })
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Failed to create file record: ${response.status} ${errorText}`);
  }

  const result = await response.json();
  
  if (!result.success) {
    throw new Error(`Failed to create file record: ${result.error || "Unknown error"}`);
  }

  console.log(`✅ File record created for: ${file.fileName}`, {
    fileId: result.data._id
  });

  return {
    fileId: result.data._id
  };
}

/**
 * Trigger chunks workflow for a single file
 */
async function triggerChunksWorkflowForFile(
  context: StepContext,
  file: AudioProcessingResult & {
    dropboxFileId: string;
    fileName: string;
    downloadResult: FileDownloadResult;
  },
  fileId: string,
  config: DropboxSyncProcessingConfig
): Promise<ChunksWorkflowResult> {
  console.log(`🔄 Triggering chunks workflow for file: ${file.fileName}`);

  // Prepare chunks workflow payload
  const payload = {
    files: [{
      fileId: fileId,
      target: "rag",
      fileName: file.fileName,
      bucket: "dropbox-sync-files",
      objectKey: file.downloadResult.objectKey,
      processor: config.chunkingTool || "unstructured",
      processorConfig: {
        ...config.chunkingConfig,
        // Add audio-specific processing config
        transcriptionId: file.transcriptionId,
        diarizationId: file.diarizationId,
        includeTranscript: true,
        includeSpeakerInfo: !!file.diarizationId
      }
    }],
    vectorizeConfig: {
      whitelabelId: context.whitelabelId,
      ragId: config.ragId,
      auth0Token: context.auth0Token
    },
    metadata: {
      source: "dropbox-sync",
      jobId: context.jobId,
      dropboxFileId: file.dropboxFileId
    }
  };

  const response = await fetch(`${context.env.CHUNKS_WORKFLOW_WORKER_URL}/trigger-workflow`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${context.auth0Token}`,
      "cloudflare-worker-x-dev-auth": context.env.CLOUDFLARE_WORKER_X_AUTH_DEV || ""
    },
    body: JSON.stringify(payload)
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Chunks workflow trigger failed: ${response.status} ${errorText}`);
  }

  const result = await response.json();
  
  if (!result.success) {
    throw new Error(`Chunks workflow trigger failed: ${result.error || "Unknown error"}`);
  }

  console.log(`✅ Chunks workflow triggered for: ${file.fileName}`, {
    workflowId: result.data.workflowId
  });

  return {
    workflowId: result.data.workflowId,
    fileId: fileId,
    chunkCount: 0, // Will be updated later by the chunks workflow
    vectorCount: 0, // Will be updated later by the chunks workflow
    status: "running"
  };
}

/**
 * Update file processing status via API
 */
async function updateFileStatus(
  context: StepContext,
  dropboxFileId: string,
  status: DropboxFileProcessingStatus,
  errorMessage?: string
): Promise<void> {
  try {
    const response = await fetch(`${context.env.API_HOST}/ux/whitelabel/${context.whitelabelId}/dropbox/sync/update-file-status`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${context.auth0Token}`,
        "cloudflare-worker-x-dev-auth": context.env.CLOUDFLARE_WORKER_X_AUTH_DEV || ""
      },
      body: JSON.stringify({
        jobId: context.jobId,
        dropboxFileId,
        status,
        errorMessage,
        timestamp: new Date().toISOString()
      })
    });

    if (!response.ok) {
      console.warn(`Failed to update file status: ${response.status}`);
    }
  } catch (error) {
    console.warn("Failed to update file status:", error);
    // Don't throw - this is a non-critical operation
  }
}
