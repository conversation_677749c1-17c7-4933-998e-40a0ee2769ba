#!/bin/bash
# Enhanced E2E test runner for cloud environments
# Usage: ./scripts/run-e2e-against-cloud.sh [environment] [services] [test_suite]

set -e

ENVIRONMENT=${1:-develop}
TARGET_SERVICES=${2:-all}
TEST_SUITE=${3:-smoke}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Running E2E tests against $ENVIRONMENT environment${NC}"
echo -e "${BLUE}📦 Target services: $TARGET_SERVICES${NC}"
echo -e "${BLUE}🧪 Test suite: $TEST_SUITE${NC}"

# Set environment-specific configuration
export TEST_ENV=$ENVIRONMENT
export TARGET_ENVIRONMENT=$ENVIRONMENT
export CHANGED_FOLDERS=$TARGET_SERVICES

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(dirname "$SCRIPT_DIR")"

# Wait for services to be ready
echo -e "${YELLOW}⏳ Waiting for services to be ready...${NC}"
if ! "$SCRIPT_DIR/wait-for-services.sh" "$ENVIRONMENT"; then
    echo -e "${RED}❌ Services failed to become ready${NC}"
    exit 1
fi

# Change to test directory
cd "$ROOT_DIR/workspace/clients/tests"

# Load environment-specific secrets if available
ENV_FILE="$ROOT_DIR/private-keys/$ENVIRONMENT/test.env"
if [ -f "$ENV_FILE" ]; then
    echo -e "${GREEN}📋 Loading environment variables from $ENV_FILE${NC}"
    set -a
    source "$ENV_FILE"
    set +a
fi

# Run the appropriate test suite
echo -e "${GREEN}🧪 Starting test execution...${NC}"

case $TEST_SUITE in
    "smoke")
        echo -e "${BLUE}Running smoke tests...${NC}"
        npx playwright test --project=e2e-tests --grep="@smoke" --reporter=html
        ;;
    "integration")
        echo -e "${BLUE}Running integration tests...${NC}"
        npx playwright test --project=e2e-tests --grep="@integration" --reporter=html
        ;;
    "performance")
        echo -e "${BLUE}Running performance tests...${NC}"
        npx playwright test --project=e2e-tests --grep="@performance" --reporter=html
        ;;
    "full")
        echo -e "${BLUE}Running full test suite...${NC}"
        npx playwright test --project=e2e-tests --reporter=html
        ;;
    *)
        echo -e "${BLUE}Running custom test pattern: $TEST_SUITE${NC}"
        npx playwright test --project=e2e-tests --grep="$TEST_SUITE" --reporter=html
        ;;
esac

TEST_EXIT_CODE=$?

if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}✅ All tests passed successfully!${NC}"
else
    echo -e "${RED}❌ Some tests failed (exit code: $TEST_EXIT_CODE)${NC}"
fi

# Generate test report summary
echo -e "${BLUE}📊 Test execution summary:${NC}"
echo -e "Environment: $ENVIRONMENT"
echo -e "Services: $TARGET_SERVICES"
echo -e "Test Suite: $TEST_SUITE"
echo -e "Exit Code: $TEST_EXIT_CODE"

exit $TEST_EXIT_CODE
