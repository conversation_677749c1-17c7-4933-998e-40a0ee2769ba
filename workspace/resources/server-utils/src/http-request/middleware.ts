import { IncomingMessage } from "http";
import { JSON_Unknown } from "@divinci-ai/utils";

import jsonBodyCallback from "body/json";

export function jsonBody(req: IncomingMessage): Promise<JSON_Unknown>{
<<<<<<< HEAD
=======
  console.log(`🔍 jsonBody called for ${req.method} ${req.url}`);
  console.log(`🔍 Request headers:`, JSON.stringify(req.headers, null, 2));

>>>>>>> WA-170_MCP
  return new Promise((res, rej)=>{
    console.log(`🔍 About to call jsonBodyCallback...`);

    jsonBodyCallback(req, (err: any, json: any)=>{
<<<<<<< HEAD
      console.error("🔂 jsonBodyCallback", err, json);
=======
>>>>>>> WA-170_MCP
      if(err) return rej(err);
      res(json as JSON_Unknown);
    });
  });
}
import formBodyCallback from "body/form";

export function formBody(req: IncomingMessage): Promise<JSON_Unknown>{
  return new Promise((res, rej)=>{
    formBodyCallback(req, (err: any, json: any)=>{
      if(err) return rej(err);
      res(json as JSON_Unknown);
    });
  });
}
