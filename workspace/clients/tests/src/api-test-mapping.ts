/**
 * API Test Mapping Module
 *
 * Maps source code folders to their corresponding API E2E test suites.
 * Used by the CI/CD pipeline to determine which API test suites to run
 * when specific folders change.
 *
 * NOTE: This file is for API testing only, not for unit tests in the
 * GitHub workflow's "Run Unit Tests for Changed Folders" step.
 */

interface ApiTestMapping {
  folder: string,
  testSuites: string[],
}

export const API_TEST_MAPPINGS: ApiTestMapping[] = [
  {
    folder: "workspace/servers/public-api/src/routes/ai-chat",
    testSuites: ["AI Chats"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/whitelabel",
    testSuites: ["White Label"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/finetune",
    testSuites: ["Fine Tune"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/moderation",
    testSuites: ["Prompt Moderation"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/thread",
    testSuites: ["Thread Prefix"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/message",
    testSuites: ["Message Prefix"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/rag",
    testSuites: ["RAG"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/workspace",
    testSuites: ["Workspace Release"]
  },
  {
    folder: "workspace/resources/actions/src/workspace/data-source/audio",
    testSuites: ["Audio Transcript"]
  },
  // Add mapping for web client audio transcript files
  {
    folder: "workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript",
    testSuites: ["Audio Transcript"]
  },
<<<<<<< HEAD
  // Add mapping for the tests folder itself - when test files are modified, run all tests
  {
    folder: "workspace/clients/tests",
    testSuites: ["AI Chats", "White Label", "Fine Tune", "Prompt Moderation", "Thread Prefix", "Message Prefix", "RAG", "Workspace Release", "Audio Transcript"]
=======
  // Voiceprint and Speaker Intelligence API mappings
  {
    folder: "workspace/servers/public-api/src/ux/whitelabel/router/voiceprints",
    testSuites: ["Voiceprint Management", "Voiceprint Creation Flow", "Voiceprint File Upload", "Voiceprint API Integration", "Voiceprint Auto-Trimming"]
  },
  {
    folder: "workspace/servers/public-api/src/ux/whitelabel/router/speaker-intelligence",
    testSuites: ["Speaker Intelligence API", "Voiceprint Management"]
  },
  {
    folder: "workspace/resources/server-tools/src/audio",
    testSuites: ["Voiceprint Creation Flow", "Voiceprint Management", "Speaker Intelligence API", "Voiceprint File Upload", "Voiceprint API Integration", "Voiceprint Auto-Trimming"]
  },
  {
    folder: "workspace/resources/server-models/src/white-label/data-source/audio-transcript",
    testSuites: ["Voiceprint Creation Flow", "Audio Transcript", "Voiceprint File Upload"]
  },
  // Voiceprint UI component mappings
  {
    folder: "workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Item",
    testSuites: ["Voiceprint UI Interactions", "Voiceprint Creation Flow", "Voiceprint File Upload"]
  },
  {
    folder: "workspace/clients/web/src/pages/WhiteLabel/Setup/Voiceprints",
    testSuites: ["Voiceprint UI Interactions", "Voiceprint Management", "Voiceprint File Upload"]
  },
  {
    folder: "workspace/clients/web/src/components/VoiceprintStatusIndicator",
    testSuites: ["Voiceprint UI Interactions", "Voiceprint File Upload", "Voiceprint Auto-Trimming"]
  },
  {
    folder: "workspace/clients/web/src/components/VoiceprintNotifications",
    testSuites: ["Voiceprint UI Interactions"]
  },
  // Add mapping for the tests folder itself - when test files are modified, run all tests
  {
    folder: "workspace/clients/tests",
    testSuites: ["AI Chats", "White Label", "Fine Tune", "Prompt Moderation", "Thread Prefix", "Message Prefix", "RAG", "Workspace Release", "Audio Transcript", "Voiceprint Creation Flow", "Voiceprint Management", "Speaker Intelligence API", "Voiceprint UI Interactions", "Voiceprint File Upload", "Voiceprint API Integration", "Voiceprint Auto-Trimming"]
>>>>>>> WA-170_MCP
  },
];

/**
 * Returns a list of API test suites that should be run based on changed folders
 *
 * @param changedFolders Array of folder paths that have changed
 * @returns Array of test suite names to run
 */
export function getApiTestSuitesForChangedFolders(changedFolders: string[]): string[] {
  const testSuites = new Set<string>();

  // Clean up folder paths by trimming whitespace and removing quotes
  const cleanedFolders = changedFolders.map(folder => {
    // Trim whitespace
    let cleaned = folder.trim();
    // Remove surrounding quotes if present
    if ((cleaned.startsWith("'") && cleaned.endsWith("'")) ||
        (cleaned.startsWith('"') && cleaned.endsWith('"'))) {
      cleaned = cleaned.substring(1, cleaned.length - 1);
    }
    return cleaned;
  }).filter(folder => folder.length > 0); // Filter out empty strings

  // Using console.error for debugging output to avoid interfering with GitHub Actions
  console.error('🔍 Analyzing changed folders for API tests:', cleanedFolders);

  cleanedFolders.forEach(folder => {
    API_TEST_MAPPINGS.forEach(mapping => {
<<<<<<< HEAD
      // More precise matching: either exact match or folder is a subfolder of mapping.folder
      // This prevents parent folders from matching all their children
      if (folder === mapping.folder ||
          folder.startsWith(mapping.folder + "/")) {
=======
      // More precise matching: either exact match or folder starts with mapping.folder + "/"
      // This prevents partial substring matches like "workspace/clients/web" matching "workspace/clients/tests"
      if (folder === mapping.folder ||
          folder.startsWith(mapping.folder + "/") ||
          mapping.folder.startsWith(folder + "/")) {
>>>>>>> WA-170_MCP
        console.error(`📍 Folder '${folder}' matches API test mapping '${mapping.folder}' -> Adding suites:`, mapping.testSuites);
        mapping.testSuites.forEach(suite => testSuites.add(suite));
      }
    });
  });

  const result = Array.from(testSuites);
  console.error('✅ API test suites to run:', result);
  return result;
}

// Keep the original function name for backward compatibility
export const getTestSuitesForChangedFolders = getApiTestSuitesForChangedFolders;
