import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { CastedBusBoy, HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";
import { R2BusBoyFileHandler } from "@divinci-ai/server-globals";
import { getAudioR2Instance } from "@divinci-ai/server-globals";

// Use the audio R2 instance which handles public bucket access
const r2 = getAudioR2Instance();
import { AUDIO_TOOLS } from "@divinci-ai/server-tools";
import crypto from "crypto";

/**
 * Media Upload for Voiceprints
 *
 * Handles uploading audio and video files for voiceprint creation.
 * Video files have audio extracted via FFMPEG.
 * Files are uploaded to R2 storage and a public URL is returned.
 */

// Use the same bucket as automatic voiceprints for consistency
// This should be the bucket that corresponds to the CLOUDFLARE_AUDIO_PUBLIC_URL
const BUCKET_NAME = "workspace-audio";
const fileHandler = new R2BusBoyFileHandler(
  r2,
  BUCKET_NAME,
  { purpose: "voiceprint-media" }, // Updated to reflect audio/video support
  new Set([
    // Audio formats
    'audio/wav', 'audio/mp3', 'audio/mpeg', 'audio/mp4', 'audio/aac',
    'audio/ogg', 'audio/webm', 'audio/flac', 'audio/x-wav',
    // Video formats (FFMPEG will extract audio)
    'video/mp4', 'video/mpeg', 'video/quicktime', 'video/x-msvideo',
    'video/webm', 'video/x-ms-wmv', 'video/3gpp', 'video/x-flv'
  ]) // Allow both audio and video files
);

// Create busboy handler for file uploads
const busboyHandler = CastedBusBoy.create(
  {
    file: "file" // Single file upload
  },
  fileHandler
);

/**
 * Check if a file with the same content already exists in R2
 * Uses file size and content hash to detect duplicates
 */
async function checkForDuplicateFile(
  file: { bucket: string; objectKey: string; info: { filename: string; mimeType: string } },
  fileBuffer?: Buffer
): Promise<{ isDuplicate: boolean; existingUrl?: string; existingKey?: string }> {
  try {
    console.log(`🔍 [DUPLICATE-CHECK] Checking for duplicate of ${file.info.filename}...`);

    // Get file metadata
    const headResult = await r2.headObject({
      Bucket: file.bucket,
      Key: file.objectKey
    });

    const fileSize = headResult.ContentLength;
    console.log(`📊 [DUPLICATE-CHECK] File size: ${fileSize} bytes`);

    // List all files in the bucket with similar names or sizes
    const listResult = await r2.listObjectsV2({
      Bucket: file.bucket,
      MaxKeys: 1000 // Reasonable limit for duplicate checking
    });

    if (!listResult.Contents) {
      console.log(`✅ [DUPLICATE-CHECK] No existing files found in bucket`);
      return { isDuplicate: false };
    }

    // Check for files with the same size
    const sameSizeFiles = listResult.Contents.filter(obj =>
      obj.Size === fileSize && obj.Key !== file.objectKey
    );

    if (sameSizeFiles.length === 0) {
      console.log(`✅ [DUPLICATE-CHECK] No files with same size found`);
      return { isDuplicate: false };
    }

    console.log(`🔍 [DUPLICATE-CHECK] Found ${sameSizeFiles.length} files with same size, checking content...`);

    // For files with the same size, compare content hash
    let currentFileHash: string;

    if (fileBuffer) {
      // Use provided buffer
      currentFileHash = crypto.createHash('md5').update(fileBuffer).digest('hex');
    } else {
      // Download and hash the current file
      const currentFileData = await r2.getObject({
        Bucket: file.bucket,
        Key: file.objectKey
      });

      if (!currentFileData.Body) {
        console.log(`⚠️ [DUPLICATE-CHECK] Could not read current file content`);
        return { isDuplicate: false };
      }

      const currentFileBuffer = await currentFileData.Body.transformToByteArray();
      currentFileHash = crypto.createHash('md5').update(currentFileBuffer).digest('hex');
    }

    console.log(`🔐 [DUPLICATE-CHECK] Current file hash: ${currentFileHash}`);

    // Check each file with the same size
    for (const existingFile of sameSizeFiles) {
      try {
        const existingFileData = await r2.getObject({
          Bucket: file.bucket,
          Key: existingFile.Key!
        });

        if (!existingFileData.Body) continue;

        const existingFileBuffer = await existingFileData.Body.transformToByteArray();
        const existingFileHash = crypto.createHash('md5').update(existingFileBuffer).digest('hex');

        console.log(`🔐 [DUPLICATE-CHECK] Existing file ${existingFile.Key} hash: ${existingFileHash}`);

        if (currentFileHash === existingFileHash) {
          console.log(`🎯 [DUPLICATE-CHECK] Duplicate found! ${existingFile.Key}`);

          // Check if the duplicate is a video file that might have a processed audio version
          const existingFileKey = existingFile.Key!;
          const isExistingVideoFile = existingFileKey.includes('.mp4') || existingFileKey.includes('.mov') ||
                                     existingFileKey.includes('.avi') || existingFileKey.includes('.mkv') ||
                                     existingFileKey.includes('.webm');

          if (isExistingVideoFile) {
            console.log(`🎬 [DUPLICATE-CHECK] Duplicate is a video file, looking for processed audio version...`);

            // Look for processed audio files with similar names
            const baseFileName = existingFileKey.replace(/\.[^/.]+$/, ''); // Remove extension
            const possibleAudioFiles = listResult.Contents?.filter(obj =>
              obj.Key &&
              obj.Key !== existingFileKey &&
              (obj.Key.startsWith(baseFileName) || obj.Key.includes(baseFileName)) &&
              (obj.Key.endsWith('.flac') || obj.Key.endsWith('.mp3'))
            ) || [];

            if (possibleAudioFiles.length > 0) {
              // Use the first processed audio file found
              const audioFileKey = possibleAudioFiles[0].Key!;

              // Check if this file has a leading slash (broken file from before the fix)
              if (audioFileKey.startsWith('/')) {
                console.log(`🚨 [DUPLICATE-CHECK] Found broken file with leading slash: ${audioFileKey}`);
                console.log(`🧹 [DUPLICATE-CHECK] Skipping broken file and allowing new upload to proceed`);
                // Don't return this as a duplicate - let the upload proceed with a clean file
                return { isDuplicate: false };
              }

              // Generate direct public URL for the existing audio file (same format as automatic voiceprints)
              const CLOUDFLARE_AUDIO_PUBLIC_URL = process.env.CLOUDFLARE_AUDIO_PUBLIC_URL || 'https://pub-2a04833210c8489198d3b76b8fb4f12d.r2.dev';
              const encodedKey = encodeURIComponent(audioFileKey).replace(/%2F/g, '/');
              const audioUrl = `${CLOUDFLARE_AUDIO_PUBLIC_URL}/${encodedKey}`;

              console.log(`🎵 [DUPLICATE-CHECK] Found processed audio version: ${audioFileKey}`);

              return {
                isDuplicate: true,
                existingUrl: audioUrl,
                existingKey: audioFileKey
              };
            } else {
              console.log(`⚠️ [DUPLICATE-CHECK] No processed audio version found for video duplicate`);
              // Fall through to return the video URL (will be caught by creation endpoint)
            }
          }

          // Generate direct public URL for the existing file (same format as automatic voiceprints)
          const CLOUDFLARE_AUDIO_PUBLIC_URL = process.env.CLOUDFLARE_AUDIO_PUBLIC_URL || 'https://pub-2a04833210c8489198d3b76b8fb4f12d.r2.dev';
          const encodedKey = encodeURIComponent(existingFileKey).replace(/%2F/g, '/');
          const existingUrl = `${CLOUDFLARE_AUDIO_PUBLIC_URL}/${encodedKey}`;

          return {
            isDuplicate: true,
            existingUrl,
            existingKey: existingFileKey
          };
        }
      } catch (error) {
        console.warn(`⚠️ [DUPLICATE-CHECK] Error checking file ${existingFile.Key}:`, error);
        continue;
      }
    }

    console.log(`✅ [DUPLICATE-CHECK] No duplicates found`);
    return { isDuplicate: false };

  } catch (error) {
    console.warn(`⚠️ [DUPLICATE-CHECK] Error during duplicate check:`, error);
    // Don't fail the upload if duplicate check fails
    return { isDuplicate: false };
  }
}

/**
 * Upload audio file for voiceprint creation
 * POST /white-label/:whitelabelId/voiceprints/upload-audio
 */
export const uploadVoiceprintAudio: RequestHandler = async (req, res, next) => {
  try {
    console.log("🎵 [VOICEPRINT-UPLOAD] Starting audio file upload...");

    // Parse the uploaded file
    const body = await busboyHandler.consumeRequest(req);

    if (!body.file) {
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("No audio file provided");
    }

    const file = body.file;
    console.log(`📁 [VOICEPRINT-UPLOAD] File uploaded: ${file.info.filename}`);
    console.log(`🎭 [VOICEPRINT-UPLOAD] MIME type: ${file.info.mimeType}`);

    // Check for duplicate files before processing
    const duplicateCheck = await checkForDuplicateFile(file);
    if (duplicateCheck.isDuplicate) {
      console.log(`🎯 [VOICEPRINT-UPLOAD] Duplicate file detected, using existing: ${duplicateCheck.existingKey}`);

      // Return the existing file URL instead of processing again
      return res.json({
        success: true,
        url: duplicateCheck.existingUrl,
        filename: file.info.filename,
        mimeType: file.info.mimeType,
        bucket: file.bucket,
        objectKey: duplicateCheck.existingKey,
        isDuplicate: true,
        message: "File already exists, using existing copy"
      });
    }

    // Check if this is a video file that needs audio extraction
    const isVideoFile = file.info.mimeType.startsWith('video/');
    let processedFile = file;
    let audioExtracted = false;

    if (isVideoFile) {
      console.log("🎬 [VOICEPRINT-UPLOAD] Video file detected, extracting audio...");

      try {
        // Use FFMPEG to extract audio from video
        const audioExtractionResult = await AUDIO_TOOLS.convertToMp3({
          Bucket: file.bucket,
          Key: file.objectKey
        });

        console.log(`🎵 [VOICEPRINT-UPLOAD] Audio extracted successfully: ${audioExtractionResult.Key}`);

        // Update file reference to the extracted audio
        // FFMPEG worker outputs FLAC for MP4 files for better quality
        const extractedMimeType = audioExtractionResult.Key.endsWith('.flac') ? 'audio/flac' : 'audio/mpeg';
        const extractedExtension = audioExtractionResult.Key.endsWith('.flac') ? '.flac' : '.mp3';

        processedFile = {
          ...file,
          bucket: audioExtractionResult.Bucket,
          objectKey: audioExtractionResult.Key,
          info: {
            ...file.info,
            mimeType: extractedMimeType,
            filename: file.info.filename.replace(/\.[^/.]+$/, extractedExtension)
          }
        };

        audioExtracted = true;
      } catch (audioExtractionError) {
        console.warn("⚠️ [VOICEPRINT-UPLOAD] Audio extraction failed:", audioExtractionError);
        // Continue with original video file - Pyannote API might handle it
        console.log("📝 [VOICEPRINT-UPLOAD] Proceeding with original video file");
      }
    }

    // Check audio duration and trim if necessary
    let finalFile = processedFile;
    let wasTrimmed = false;
    let originalDuration: number | undefined;

    try {
      console.log("⏱️ [VOICEPRINT-UPLOAD] Checking audio duration...");

      // Get audio duration using our FFMPEG worker (use processed file, not original)
      const durationResult = await AUDIO_TOOLS.getAudioDuration({
        Bucket: processedFile.bucket,
        Key: processedFile.objectKey
      });

      originalDuration = durationResult.duration;
      console.log(`📏 [VOICEPRINT-UPLOAD] Audio duration: ${originalDuration} seconds`);

      // If audio is longer than 27 seconds, trim it and convert to MP3
      if (originalDuration && originalDuration > 27) {
        console.log("✂️ [VOICEPRINT-UPLOAD] Audio exceeds 27 seconds, trimming and converting to MP3...");

        // Create a trimmed version (first 27 seconds) - use processed file, not original
        const trimmedResult = await AUDIO_TOOLS.createSlice(
          { Bucket: processedFile.bucket, Key: processedFile.objectKey },
          { start: 0, end: 27 }
        );

        console.log(`✅ [VOICEPRINT-UPLOAD] Audio trimmed to 27 seconds: ${trimmedResult.Key}`);

        // Update file reference to the trimmed version
        finalFile = {
          ...processedFile,  // Use processedFile as base, not original file
          bucket: trimmedResult.Bucket,
          objectKey: trimmedResult.Key
        };

        wasTrimmed = true;
      } else {
        console.log("✅ [VOICEPRINT-UPLOAD] Audio duration is within 27-second limit, converting to MP3...");

        // Convert to MP3 for consistency with Pyannote API preferences
        const mp3Result = await AUDIO_TOOLS.convertToMp3(
          { Bucket: processedFile.bucket, Key: processedFile.objectKey }
        );

        console.log(`🎵 [VOICEPRINT-UPLOAD] Converted audio to MP3: ${mp3Result.Key}`);

        finalFile = {
          ...processedFile,
          bucket: mp3Result.Bucket,
          objectKey: mp3Result.Key
        };
      }
    } catch (durationError) {
      console.warn("⚠️ [VOICEPRINT-UPLOAD] Could not check/trim audio duration:", durationError);
      // Continue with original file if duration check fails
      console.log("📝 [VOICEPRINT-UPLOAD] Proceeding with original file");
    }

    // Generate direct public URL for the final file (same format as automatic voiceprints)
    // Use the same public URL format that works for automatic voiceprints
    const CLOUDFLARE_AUDIO_PUBLIC_URL = process.env.CLOUDFLARE_AUDIO_PUBLIC_URL || 'https://pub-2a04833210c8489198d3b76b8fb4f12d.r2.dev';
    // URL-encode the key to handle filenames with spaces and special characters (same as automatic voiceprints)
    const encodedKey = encodeURIComponent(finalFile.objectKey).replace(/%2F/g, '/');
    const publicUrl = `${CLOUDFLARE_AUDIO_PUBLIC_URL}/${encodedKey}`;

    console.log(`🔗 [VOICEPRINT-UPLOAD] Generated direct public URL (same format as automatic voiceprints): ${publicUrl}`);
    console.log(`🔍 [VOICEPRINT-UPLOAD] Final file details:`, {
      bucket: finalFile.bucket,
      objectKey: finalFile.objectKey,
      expectedBucket: BUCKET_NAME,
      urlFormat: 'direct-public'
    });

    // Verify this matches the working automatic voiceprint URL format
    console.log(`✅ [VOICEPRINT-UPLOAD] URL format matches automatic voiceprints: ${publicUrl.includes('pub-2a04833210c8489198d3b76b8fb4f12d.r2.dev')}`);

    // Return the public URL that can be used with Pyannote API
    res.status(200).json({
      success: true,
      url: publicUrl,
      filename: file.info.filename, // Keep original filename for user reference
      mimeType: finalFile.info.mimeType, // Use processed file's MIME type
      bucket: finalFile.bucket,
      objectKey: finalFile.objectKey,
      // Include processing information
      processing: {
        originalDuration,
        wasTrimmed,
        finalDuration: wasTrimmed ? 27 : originalDuration,
        audioExtracted, // Indicate if audio was extracted from video
        originalMimeType: file.info.mimeType // Original file type for reference
      }
    });

  } catch (error) {
    console.error("❌ [VOICEPRINT-UPLOAD] Upload failed:", error);
    next(error);
  }
};
