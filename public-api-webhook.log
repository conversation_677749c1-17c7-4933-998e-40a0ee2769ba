
> @divinci-ai/public-api-webhook@0.3.0 start /workspaces/server/workspace/servers/public-api-webhook
> node dist/src/index.js

🐞LOG_DEBUG MODE:  1
🐞LOG_DEBUG MODE:  1
🔎 CURRENT ENVIRONMENT: dev
🔎 CORS FULL ORIGINS (raw): api.slack.com,dev-46tiys6hnb6vbg17-cd-hljzhimsn7o28fvq.edge.tenants.us.auth0.com,dev-46tiys6hnb6vbg17.us.auth0.com,local-chunks-vectorized,127.0.0.1:,effective-invention-xjwwp64g36r4j-8080.app.github.dev,effective-invention-xjwwp64g36r4j-8081.app.github.dev,effective-invention-xjwwp64g36r4j-8082.app.github.dev,effective-invention-xjwwp64g36r4j-8083.app.github.dev,effective-invention-xjwwp64g36r4j-9080.app.github.dev
🔎 CORS FULL ORIGINS (cleaned): [
  'api.slack.com',
  'dev-46tiys6hnb6vbg17-cd-hljzhimsn7o28fvq.edge.tenants.us.auth0.com',
  'dev-46tiys6hnb6vbg17.us.auth0.com',
  'local-chunks-vectorized',
  '127.0.0.1:',
  'effective-invention-xjwwp64g36r4j-8080.app.github.dev',
  'effective-invention-xjwwp64g36r4j-8081.app.github.dev',
  'effective-invention-xjwwp64g36r4j-8082.app.github.dev',
  'effective-invention-xjwwp64g36r4j-8083.app.github.dev',
  'effective-invention-xjwwp64g36r4j-9080.app.github.dev'
]
🐞LOG_DEBUG MODE:  1
🐞LOG_DEBUG MODE:  1
🐞LOG_DEBUG MODE:  1
🎈 Attempting to connect to Redis in dev environment: redis://default:<EMAIL>:15121
🔄 Creating simple R2 client for fine-tune in local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🔄 Creating simple R2 client for RAG vectors in local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🔄 Creating simple Audio R2 client for local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🪀 Can make external calls!
Attempting Mongoose Connection
🎈 MONGO_CONNECTION_URL: mongodb+srv://serverlessinstance0.c4pobzg.mongodb.net/divinci-dev
Attempting Redis Connection
🟢 Connected to Redis in dev environment at 2025-06-15T04:03:52.872Z
✅🌱 Successfully connected to Redis. 
✅🌱 Successfully connected to MongoDB. 
🔒 Creating HTTPS server on port 8081
✅ Server is running at on port:  { address: '::', family: 'IPv6', port: 8083 }
🌐 url:  /
🪪 remote address:  ::ffff:127.0.0.1
🔍 CORS debug: Checking origin: "undefined"
🔍 CORS debug: isCloudEnvironment: false
🔍 CORS debug: Environment: dev
