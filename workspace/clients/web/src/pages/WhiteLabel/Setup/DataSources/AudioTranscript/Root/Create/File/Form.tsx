import React, { useEffect, useState } from "react";

import { StateInputProps } from "../../../../../../../../util/react/input";
<<<<<<< HEAD
import { BodyType } from "./types";
=======
import { BodyType, DiarizeConfig, VoiceprintConfig } from "./types";
>>>>>>> WA-170_MCP
import { useAuth0Fetch } from "../../../../../../../../globals/auth0-user";

import { DiarizeTool } from "../../../Form/DiarizeTool";
import { TranscribeTool } from "../../../Form/TranscribeTool";
import { FileInput } from "../../../Form/MediaInput/FileInput";
<<<<<<< HEAD
=======
import { VoiceprintSimplePreview } from "../../../../../../../../components/VoiceprintLibraryPreview/VoiceprintSimplePreview";
>>>>>>> WA-170_MCP
import {
  loadAudioToolPreferences,
  saveAudioToolPreferences,
  StorageOption
} from "../../../utils/toolPreferences";

// Import the HealthCheckTester for debugging (only used in development)
import { HealthCheckTester } from "../../../../../../../../components/ServiceHealthCheck/HealthCheckTester";

<<<<<<< HEAD
=======
// Diarizer constants
const OFFICIAL_PYANNOTE_ID = '@pyannote/pyannote-segmentation';
const DIVINCI_PYANNOTE_ID = '@divinci-ai/pyannote-segmentation';

>>>>>>> WA-170_MCP
export function Form({ value, onChange }: StateInputProps<BodyType>) {
  const [preferencesLoaded, setPreferencesLoaded] = useState(false);
  const [savePreferences, setSavePreferences] = useState(true);
  const [storageOption, setStorageOption] = useState<StorageOption>('both');
  const [servicesAvailable, setServicesAvailable] = useState(true);
<<<<<<< HEAD
  const auth0Fetch = useAuth0Fetch();

=======

  const auth0Fetch = useAuth0Fetch();

  // Initialize configuration with defaults based on selected tool
  const initializeDiarizeConfig = (): DiarizeConfig => {
    const baseConfig = {
      tool: value.diarizerTool || '',
      numSpeakers: undefined,
      showAdvanced: false,
    };

    // Add confidence settings for Official Pyannote API
    if (value.diarizerTool === '@pyannote/pyannote-segmentation') {
      return {
        ...baseConfig,
        confidence: {
          enabled: false,
          threshold: 0.7,
          includeInProcessing: true,
        }
      };
    }

    return baseConfig;
  };

  const initializeVoiceprintConfig = (): VoiceprintConfig => ({
    enabled: false,
    existingVoiceprints: [],
    autoCreateFromHighConfidence: false,
    selectedVoiceprintIds: [],
  });

  // State for available voiceprints loaded from the API
  const [availableVoiceprints, setAvailableVoiceprints] = useState<any[]>([]);

  // Get current configurations or initialize defaults
  const diarizeConfig = value.diarizeConfig || initializeDiarizeConfig();
  const voiceprintConfig = value.voiceprintConfig || initializeVoiceprintConfig();

  // Check if voiceprints are supported with the current diarizer
  const isVoiceprintSupported = diarizeConfig.tool === OFFICIAL_PYANNOTE_ID;

  // Initialize voiceprint config in form state if not present
  useEffect(() => {
    if (!value.voiceprintConfig) {
      onChange(currentValue => ({
        ...currentValue,
        voiceprintConfig: initializeVoiceprintConfig()
      }));
    }
  }, [value.voiceprintConfig, onChange]);

>>>>>>> WA-170_MCP
  // Load saved preferences when component mounts
  useEffect(() => {
    const loadPreferences = async () => {
      try {
        // Try to load from both local and server storage
        const prefs = await loadAudioToolPreferences('both', auth0Fetch);

        // Only apply preferences if they exist and we haven't already set values
        if (!preferencesLoaded && (prefs.diarizerTool || prefs.transcriberTool)) {
          onChange(currentValue => ({
            ...currentValue,
            diarizerTool: prefs.diarizerTool || currentValue.diarizerTool,
            transcriberTool: prefs.transcriberTool || currentValue.transcriberTool
          }));
        }

        setPreferencesLoaded(true);
      } catch (error) {
        console.error("Error loading audio tool preferences:", error);
        setPreferencesLoaded(true);
      }
    };

    loadPreferences();
  }, [onChange, auth0Fetch]);

  // Save preferences when they change
  useEffect(() => {
    // Only save if preferences are loaded and the user has selected tools
    if (preferencesLoaded && savePreferences && value.diarizerTool && value.transcriberTool) {
      saveAudioToolPreferences(
        {
          diarizerTool: value.diarizerTool,
          transcriberTool: value.transcriberTool
        },
        storageOption,
        auth0Fetch
      ).catch(error => {
        console.error("Error saving audio tool preferences:", error);
      });
    }
  }, [value.diarizerTool, value.transcriberTool, preferencesLoaded, savePreferences, storageOption, auth0Fetch]);

<<<<<<< HEAD
  const handleDiarizerChange = (newValue: string) => {
    onChange(value => ({ ...value, diarizerTool: newValue }));
=======
  // Enhanced handlers for new configuration structure
  const handleDiarizeConfigChange = (newConfig: DiarizeConfig) => {
    // Create a compatible config for the backend
    const backendCompatibleConfig = {
      ...newConfig,
      confidence: newConfig.confidence || {
        enabled: false, // Default to false for open-source pyannote
        threshold: 0.7,
        includeInProcessing: false,
      }
    };

    onChange(value => ({
      ...value,
      diarizerTool: newConfig.tool, // Maintain backward compatibility
      diarizeConfig: backendCompatibleConfig
    }));
  };

  const handleVoiceprintConfigChange = (newConfig: VoiceprintConfig) => {
    // When voiceprints are enabled, populate with available voiceprints but don't auto-select
    if (newConfig.enabled && availableVoiceprints.length > 0) {
      const voiceprintData = availableVoiceprints.map(vp => ({
        voiceprintId: vp.voiceprintId,
        speakerLabel: vp.speakerLabel,
        voiceprintData: vp.voiceprintData?.voiceprint || vp.voiceprintData
      }));

      newConfig.existingVoiceprints = voiceprintData;
      console.log('🎯 [IDENTIFICATION] Available voiceprints for identification:', voiceprintData.length);
    } else if (!newConfig.enabled) {
      // Clear voiceprints and selections when disabled
      newConfig.existingVoiceprints = [];
      newConfig.selectedVoiceprintIds = [];
    }

    onChange(value => ({
      ...value,
      voiceprintConfig: newConfig
    }));
  };

  // Handle voiceprint selection changes
  const handleVoiceprintSelectionChange = (selectedIds: string[]) => {
    const updatedConfig = {
      ...voiceprintConfig,
      selectedVoiceprintIds: selectedIds
    };

    // Update the existing voiceprints to only include selected ones for backend processing
    if (selectedIds.length > 0) {
      const selectedVoiceprints = availableVoiceprints
        .filter(vp => selectedIds.includes(vp.voiceprintId))
        .map(vp => ({
          voiceprintId: vp.voiceprintId,
          speakerLabel: vp.speakerLabel,
          voiceprintData: vp.voiceprintData?.voiceprint || vp.voiceprintData
        }));

      updatedConfig.existingVoiceprints = selectedVoiceprints;
      console.log('🎯 [IDENTIFICATION] Selected voiceprints for identification:', selectedVoiceprints.length);
    } else {
      // Keep all available but mark none as selected
      updatedConfig.existingVoiceprints = [];
    }

    onChange(value => ({
      ...value,
      voiceprintConfig: updatedConfig
    }));
  };

  // Callback when voiceprints are loaded from the API
  const handleVoiceprintsLoaded = (voiceprints: any[]) => {
    console.log('🎤 [VOICEPRINTS] Loaded voiceprints:', voiceprints.length);
    setAvailableVoiceprints(voiceprints);

    // If voiceprints are already enabled, update the config with the loaded data
    if (voiceprintConfig.enabled && voiceprints.length > 0) {
      const voiceprintData = voiceprints.map(vp => ({
        voiceprintId: vp.voiceprintId,
        speakerLabel: vp.speakerLabel,
        voiceprintData: vp.voiceprintData?.voiceprint || vp.voiceprintData
      }));

      handleVoiceprintConfigChange({
        ...voiceprintConfig,
        existingVoiceprints: voiceprintData
      });
    }
  };

  // Legacy handlers for backward compatibility
  const handleDiarizerChange = (newValue: string) => {
    const updatedConfig = { ...diarizeConfig, tool: newValue };
    handleDiarizeConfigChange(updatedConfig);
>>>>>>> WA-170_MCP
  };

  const handleTranscriberChange = (newValue: string) => {
    onChange(value => ({ ...value, transcriberTool: newValue }));
  };

  const toggleSavePreferences = () => {
    setSavePreferences(!savePreferences);
  };

<<<<<<< HEAD
=======


>>>>>>> WA-170_MCP
  // We'll get service status from the DiarizeTool component
  useEffect(() => {
    // Listen for messages from DiarizeTool about service status
    const handleMessage = (event: MessageEvent) => {
      if (event.data && event.data.type === 'SERVICE_STATUS_CHANGE') {
        setServicesAvailable(event.data.available);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  return (
    <div className="container">
      <div className="card">
        <div className="card-content">
          <h1 className="title is-3 mb-5">Upload Audio or Video</h1>

          {/* Conditionally render the HealthCheckTester in development */}
          {process.env.NODE_ENV === 'development' && <HealthCheckTester />}

          <div className="content">
            <div className="mb-6">
              <FileInput
                value={value.mediaFile ? (Array.isArray(value.mediaFile) ? value.mediaFile : [value.mediaFile]) : null}
                onChange={(newValue) => onChange({ ...value, mediaFile: newValue })}
                // disabled={!servicesAvailable}
              />
              {/* {!servicesAvailable && (
                <p className="help is-danger mt-2">
                  File upload is disabled because one or more required services are unavailable.
                  Please try again later or contact support if the issue persists.
                </p>
              )} */}
            </div>

            <div className="columns">
              <div className="column">
                <div className="box">
                  <h3 className="subtitle is-4 mb-4">
                    <span className="icon-text">
                      <span className="icon">
                        <i className="fas fa-users"></i>
                      </span>
                      <span>Speaker Detection</span>
                    </span>
                  </h3>
                  <DiarizeTool
<<<<<<< HEAD
                    value={value.diarizerTool}
                    onChange={handleDiarizerChange}
=======
                    value={diarizeConfig}
                    onChange={handleDiarizeConfigChange}
>>>>>>> WA-170_MCP
                  />
                </div>
              </div>

              <div className="column">
                <div className="box">
                  <h3 className="subtitle is-4 mb-4">
                    <span className="icon-text">
                      <span className="icon">
                        <i className="fas fa-microphone"></i>
                      </span>
                      <span>Speech Recognition</span>
                    </span>
                  </h3>
                  <TranscribeTool
                    value={value.transcriberTool}
                    onChange={handleTranscriberChange}
                  />
                </div>
              </div>
            </div>

<<<<<<< HEAD
=======
            {/* Voiceprint Configuration */}
            <div className="box mt-5">
              <h3 className="subtitle is-4 mb-4">
                <span className="icon-text">
                  <span className="icon">
                    <i className="fas fa-microphone"></i>
                  </span>
                  <span>Voiceprint Settings</span>
                </span>
              </h3>

              <div className="columns">
                <div className="column">
                  {/* Voiceprint configuration */}
                  {isVoiceprintSupported ? (
                    <div className="field">
                      <div className="control">
                        <label className="checkbox">
                          <input
                            type="checkbox"
                            checked={voiceprintConfig.enabled}
                            onChange={(e) => handleVoiceprintConfigChange({
                              ...voiceprintConfig,
                              enabled: e.target.checked
                            })}
                            className="mr-2"
                          />
                          Use existing voiceprints for speaker identification
                        </label>
                      </div>
                      <p className="help">
                        When enabled, the system will use your voiceprint library to identify speakers more accurately
                      </p>
                    </div>
                  ) : (
                    <div className="field">
                      <div className="control">
                        <label className="checkbox">
                          <input
                            type="checkbox"
                            checked={false}
                            disabled={true}
                            className="mr-2"
                          />
                          <span style={{ color: '#999' }}>Use existing voiceprints for speaker identification</span>
                        </label>
                      </div>
                      <div className="notification is-info is-light mt-3">
                        <span className="icon-text">
                          <span className="icon">
                            <i className="fas fa-info-circle"></i>
                          </span>
                          <span>
                            <strong>Voiceprints are only available with Official Pyannote.</strong><br />
                            To use voiceprint features, please select "Official Pyannote" as your Speaker Diarizer above.
                          </span>
                        </span>
                      </div>
                    </div>
                  )}

                  {isVoiceprintSupported && voiceprintConfig.enabled && (
                    <div className="field">
                      <div className="control">
                        <label className="checkbox">
                          <input
                            type="checkbox"
                            checked={voiceprintConfig.autoCreateFromHighConfidence}
                            onChange={(e) => handleVoiceprintConfigChange({
                              ...voiceprintConfig,
                              autoCreateFromHighConfidence: e.target.checked
                            })}
                            className="mr-2"
                          />
                          Automatically create voiceprints from high-confidence segments
                        </label>
                      </div>
                      <p className="help">
                        The system will automatically create voiceprints from segments with high confidence scores
                      </p>
                    </div>
                  )}
                </div>

                {/* Available Speakers - Right side */}
                <div className="column">
                  {isVoiceprintSupported && voiceprintConfig.enabled && (
                    <VoiceprintSimplePreview
                      enabled={voiceprintConfig.enabled}
                      onVoiceprintsLoaded={handleVoiceprintsLoaded}
                      selectionMode={true}
                      selectedVoiceprints={voiceprintConfig.selectedVoiceprintIds}
                      onSelectionChange={handleVoiceprintSelectionChange}
                    />
                  )}
                </div>
              </div>
            </div>

>>>>>>> WA-170_MCP
            <div className="field mt-4">
              <div className="control">
                <label className="checkbox">
                  <input
                    type="checkbox"
                    checked={savePreferences}
                    onChange={toggleSavePreferences}
                    className="mr-2"
                  />
                  Remember my tool selections for next time
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}