#!/bin/bash

# MCP E2E Test Runner Script
# This script runs comprehensive E2E tests for the MCP (Model Context Protocol) implementation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
MCP_SERVER_URL="http://localhost:8793"
MCP_HEALTH_ENDPOINT="$MCP_SERVER_URL/health"
TEST_TIMEOUT=300 # 5 minutes

echo -e "${BLUE}🧪 MCP E2E Test Runner${NC}"
echo "=================================="

# Function to check if MCP server is running
check_mcp_server() {
    echo -e "${BLUE}🔍 Checking MCP Server status...${NC}"
    
    if curl -s -f "$MCP_HEALTH_ENDPOINT" > /dev/null; then
        echo -e "${GREEN}✅ MCP Server is running${NC}"
        
        # Get server info
        SERVER_INFO=$(curl -s "$MCP_HEALTH_ENDPOINT" | jq -r '.service + " v" + .version + " (" + .status + ")"' 2>/dev/null || echo "MCP Server")
        echo -e "${GREEN}   Server: $SERVER_INFO${NC}"
        return 0
    else
        echo -e "${RED}❌ MCP Server is not running${NC}"
        return 1
    fi
}

# Function to start MCP server if not running
start_mcp_server() {
    echo -e "${YELLOW}🚀 Starting MCP Server...${NC}"
    echo "   Location: workspace/workers/mcp-server"
    echo "   Command: npm run dev"
    echo ""
    echo -e "${YELLOW}Please start the MCP server manually:${NC}"
    echo "   cd workspace/workers/mcp-server"
    echo "   npm run dev"
    echo ""
    echo "Then run this script again."
    exit 1
}

# Function to run specific test suite
run_test_suite() {
    local test_file=$1
    local test_name=$2
    
    echo -e "${BLUE}🧪 Running $test_name...${NC}"
    
    if npx playwright test "$test_file" --timeout=$((TEST_TIMEOUT * 1000)) --reporter=line; then
        echo -e "${GREEN}✅ $test_name passed${NC}"
        return 0
    else
        echo -e "${RED}❌ $test_name failed${NC}"
        return 1
    fi
}

# Function to run quick MCP validation
quick_mcp_validation() {
    echo -e "${BLUE}⚡ Running Quick MCP Validation...${NC}"
    
    # Test 1: Health check
    echo "  Testing health endpoint..."
    if ! curl -s -f "$MCP_HEALTH_ENDPOINT" > /dev/null; then
        echo -e "${RED}❌ Health check failed${NC}"
        return 1
    fi
    
    # Test 2: Tools listing
    echo "  Testing tools listing..."
    TOOLS_RESPONSE=$(curl -s -X POST "$MCP_SERVER_URL/sse" \
        -H "Content-Type: application/json" \
        -d '{"jsonrpc":"2.0","id":1,"method":"tools/list","params":{}}')
    
    if echo "$TOOLS_RESPONSE" | jq -e '.result.tools | length > 0' > /dev/null 2>&1; then
        TOOL_COUNT=$(echo "$TOOLS_RESPONSE" | jq -r '.result.tools | length')
        echo -e "${GREEN}   Found $TOOL_COUNT tools${NC}"
    else
        echo -e "${RED}❌ Tools listing failed${NC}"
        return 1
    fi
    
    # Test 3: Tool execution
    echo "  Testing tool execution..."
    EXEC_RESPONSE=$(curl -s -X POST "$MCP_SERVER_URL/sse" \
        -H "Content-Type: application/json" \
        -d '{"jsonrpc":"2.0","id":2,"method":"tools/call","params":{"name":"get_user_profile","arguments":{}}}')
    
    if echo "$EXEC_RESPONSE" | jq -e '.result.content[0].text' > /dev/null 2>&1; then
        echo -e "${GREEN}   Tool execution successful${NC}"
    else
        echo -e "${RED}❌ Tool execution failed${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ Quick MCP validation passed${NC}"
    return 0
}

# Main execution
main() {
    echo "Starting MCP E2E test suite..."
    echo ""
    
    # Check if we're in the right directory
    if [ ! -f "package.json" ] || [ ! -d "src/e2e" ]; then
        echo -e "${RED}❌ Please run this script from the tests directory${NC}"
        echo "   Expected location: workspace/clients/tests/"
        exit 1
    fi
    
    # Check if MCP server is running
    if ! check_mcp_server; then
        start_mcp_server
    fi
    
    # Run quick validation first
    echo ""
    if ! quick_mcp_validation; then
        echo -e "${RED}❌ Quick validation failed. Please check MCP server.${NC}"
        exit 1
    fi
    
    echo ""
    echo -e "${BLUE}🚀 Starting comprehensive E2E tests...${NC}"
    echo ""
    
    # Track test results
    TOTAL_TESTS=0
    PASSED_TESTS=0
    FAILED_TESTS=0
    
    # Test Suite 1: MCP Server Integration
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if run_test_suite "src/e2e/mcp-server-integration.spec.ts" "MCP Server Integration Tests"; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    echo ""
    
    # Test Suite 2: MCP Inspector Integration
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if run_test_suite "src/e2e/mcp-inspector-integration.spec.ts" "MCP Inspector Integration Tests"; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    echo ""
    
    # Test Suite 3: Comprehensive Test Runner
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if run_test_suite "src/e2e/mcp-test-runner.spec.ts" "MCP Comprehensive Test Runner"; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    echo ""
    echo "=================================="
    echo -e "${BLUE}📊 Test Results Summary${NC}"
    echo "=================================="
    echo "Total Test Suites: $TOTAL_TESTS"
    echo -e "Passed: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "Failed: ${RED}$FAILED_TESTS${NC}"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo ""
        echo -e "${GREEN}🎉 All MCP E2E tests passed!${NC}"
        echo ""
        echo -e "${GREEN}✅ Your MCP implementation is working correctly${NC}"
        echo -e "${GREEN}✅ Ready for Claude Desktop integration${NC}"
        echo -e "${GREEN}✅ Ready for MCP Inspector testing${NC}"
        echo ""
        echo -e "${BLUE}🚀 Next Steps:${NC}"
        echo "1. Test with MCP Inspector: npx @modelcontextprotocol/inspector@latest"
        echo "2. Configure Claude Desktop with: npx mcp-remote http://localhost:8793/sse"
        echo "3. Deploy to production when ready"
        exit 0
    else
        echo ""
        echo -e "${RED}❌ Some tests failed. Please review the output above.${NC}"
        exit 1
    fi
}

# Check for required dependencies
if ! command -v curl &> /dev/null; then
    echo -e "${RED}❌ curl is required but not installed${NC}"
    exit 1
fi

if ! command -v jq &> /dev/null; then
    echo -e "${YELLOW}⚠️  jq is not installed. Some validations will be skipped.${NC}"
fi

if ! command -v npx &> /dev/null; then
    echo -e "${RED}❌ npx is required but not installed${NC}"
    exit 1
fi

# Run main function
main "$@"
