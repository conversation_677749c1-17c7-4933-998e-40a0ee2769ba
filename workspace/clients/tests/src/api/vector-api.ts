/**
 * Vector API
<<<<<<< HEAD
 *
=======
 * 
>>>>>>> WA-170_MCP
 * This module provides API client functions for working with RAG vectors.
 * It includes both real API calls and mock implementations for testing.
 */

import { ApiClient } from './api-client';
import { getAuthFileForRole, getUserIdFromAuthState, UserRole } from '../auth/auth-utils';
import { randomString } from '../utils/test-utils';

// Flag to use mock implementations for testing
const USE_MOCK = true;

/**
 * Vector
 */
export interface Vector {
  /** Vector ID */
  _id: string;
  /** Vector name */
  name: string;
  /** Vector description */
  description?: string;
  /** Whitelabel ID */
  whitelabelId: string;
  /** Owner user ID */
  ownerUser: string;
  /** Created at timestamp */
  createdAt: string;
  /** Updated at timestamp */
  updatedAt: string;
  /** Status */
  status: 'active' | 'processing' | 'error';
  /** File count */
  fileCount: number;
}

// In-memory store for mock vectors
const mockVectors = new Map<string, Vector>();

/**
 * Create a vector
 * @param role User role
 * @param params Vector parameters
 * @returns A promise that resolves with the created vector
 */
export async function createVector(
  role: UserRole,
  params: { name: string; whitelabelId: string; description?: string }
): Promise<Vector> {
  if (USE_MOCK) {
    return createMockVector(role, params);
  }
<<<<<<< HEAD

  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);

=======
  
  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);
  
>>>>>>> WA-170_MCP
  // Create the vector
  return client.post<Vector>('rag-vector', {
    name: params.name,
    whitelabelId: params.whitelabelId,
    description: params.description
  });
}

/**
 * Get a vector by ID
 * @param role User role
 * @param vectorId Vector ID
 * @returns A promise that resolves with the vector
 */
export async function getVector(role: UserRole, vectorId: string): Promise<Vector> {
  if (USE_MOCK) {
    return getMockVector(role, vectorId);
  }
<<<<<<< HEAD

  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);

=======
  
  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);
  
>>>>>>> WA-170_MCP
  // Get the vector
  return client.get<Vector>(`rag-vector/${vectorId}`);
}

/**
 * Get all vectors for a whitelabel
 * @param role User role
 * @param whitelabelId Whitelabel ID
 * @returns A promise that resolves with the vectors
 */
export async function getVectors(role: UserRole, whitelabelId: string): Promise<Vector[]> {
  if (USE_MOCK) {
    return getMockVectors(role, whitelabelId);
  }
<<<<<<< HEAD

  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);

=======
  
  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);
  
>>>>>>> WA-170_MCP
  // Get the vectors
  return client.get<Vector[]>(`rag-vector?whitelabelId=${whitelabelId}`);
}

/**
 * Delete a vector
 * @param role User role
 * @param vectorId Vector ID
 * @returns A promise that resolves when the vector is deleted
 */
export async function deleteVector(role: UserRole, vectorId: string): Promise<void> {
  if (USE_MOCK) {
    return deleteMockVector(role, vectorId);
  }
<<<<<<< HEAD

  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);

=======
  
  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);
  
>>>>>>> WA-170_MCP
  // Delete the vector
  await client.delete(`rag-vector/${vectorId}`);
}

/**
 * Update a vector
 * @param role User role
 * @param vectorId Vector ID
 * @param updates Updates to apply
 * @returns A promise that resolves with the updated vector
 */
export async function updateVector(
  role: UserRole,
  vectorId: string,
  updates: Partial<Pick<Vector, 'name' | 'description'>>
): Promise<Vector> {
  if (USE_MOCK) {
    return updateMockVector(role, vectorId, updates);
  }
<<<<<<< HEAD

  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);

=======
  
  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);
  
>>>>>>> WA-170_MCP
  // Update the vector
  return client.put<Vector>(`rag-vector/${vectorId}`, updates);
}

// Mock implementations

/**
 * Create a mock vector
 * @param role User role
 * @param params Vector parameters
 * @returns A promise that resolves with the created vector
 */
async function createMockVector(
  role: UserRole,
  params: { name: string; whitelabelId: string; description?: string }
): Promise<Vector> {
  console.log(`[Mock] Creating vector: ${params.name} for whitelabel ${params.whitelabelId}`);
<<<<<<< HEAD

  // Validate input - simulate real API validation
  if (!params.name || params.name.trim() === '') {
    throw new Error('Vector name is required and cannot be empty');
  }

  if (params.name.length > 100) {
    throw new Error('Vector name cannot exceed 100 characters');
  }

  if (!params.whitelabelId || params.whitelabelId.trim() === '') {
    throw new Error('Whitelabel ID is required');
  }

  // Check if whitelabel exists (simulate authorization check)
  if (params.whitelabelId === 'non-existent-whitelabel-id') {
    throw new Error('Whitelabel not found');
  }

  const authFile = getAuthFileForRole(role);
  const userId = getUserIdFromAuthState(authFile) || `mock-${role}-user`;

  // Generate a unique ID
  const id = `mock-vector-${randomString()}`;
  const now = new Date().toISOString();

=======
  
  const authFile = getAuthFileForRole(role);
  const userId = getUserIdFromAuthState(authFile) || `mock-${role}-user`;
  
  // Generate a unique ID
  const id = `mock-vector-${randomString()}`;
  const now = new Date().toISOString();
  
>>>>>>> WA-170_MCP
  // Create the vector
  const vector: Vector = {
    _id: id,
    name: params.name,
    description: params.description,
    whitelabelId: params.whitelabelId,
    ownerUser: userId,
    createdAt: now,
    updatedAt: now,
    status: 'active',
    fileCount: 0
  };
<<<<<<< HEAD

  // Store the vector
  mockVectors.set(id, vector);

  console.log(`[Mock] Created vector: ${params.name} (${id})`);

=======
  
  // Store the vector
  mockVectors.set(id, vector);
  
  console.log(`[Mock] Created vector: ${params.name} (${id})`);
  
>>>>>>> WA-170_MCP
  return vector;
}

/**
 * Get a mock vector by ID
 * @param role User role
 * @param vectorId Vector ID
 * @returns A promise that resolves with the vector
 */
async function getMockVector(role: UserRole, vectorId: string): Promise<Vector> {
  console.log(`[Mock] Getting vector: ${vectorId}`);
<<<<<<< HEAD

  // Handle special test cases
  if (vectorId === 'non-existent-id' || vectorId.includes('non-existent')) {
    throw new Error(`Vector not found: ${vectorId}`);
  }

  const vector = mockVectors.get(vectorId);

  if (!vector) {
    throw new Error(`Vector not found: ${vectorId}`);
  }

  // Simulate authorization check
  const authFile = getAuthFileForRole(role);
  const userId = getUserIdFromAuthState(authFile) || `mock-${role}-user`;

  // Check if user has access to this vector
  if (vectorId.startsWith('admin-owned-') && role !== 'admin') {
    throw new Error('Unauthorized: You do not have permission to access this vector');
  }

  console.log(`[Mock] Retrieved vector: ${vector.name} (${vectorId})`);

=======
  
  const vector = mockVectors.get(vectorId);
  
  if (!vector) {
    throw new Error(`Vector not found: ${vectorId}`);
  }
  
  console.log(`[Mock] Retrieved vector: ${vector.name} (${vectorId})`);
  
>>>>>>> WA-170_MCP
  return vector;
}

/**
 * Get all mock vectors for a whitelabel
 * @param role User role
 * @param whitelabelId Whitelabel ID
 * @returns A promise that resolves with the vectors
 */
async function getMockVectors(role: UserRole, whitelabelId: string): Promise<Vector[]> {
  console.log(`[Mock] Getting vectors for whitelabel: ${whitelabelId}`);
<<<<<<< HEAD

=======
  
>>>>>>> WA-170_MCP
  // Filter vectors by whitelabel ID
  const vectors = Array.from(mockVectors.values()).filter(vector => {
    return vector.whitelabelId === whitelabelId;
  });
<<<<<<< HEAD

  console.log(`[Mock] Retrieved ${vectors.length} vectors for whitelabel: ${whitelabelId}`);

=======
  
  console.log(`[Mock] Retrieved ${vectors.length} vectors for whitelabel: ${whitelabelId}`);
  
>>>>>>> WA-170_MCP
  return vectors;
}

/**
 * Delete a mock vector
 * @param role User role
 * @param vectorId Vector ID
 * @returns A promise that resolves when the vector is deleted
 */
async function deleteMockVector(role: UserRole, vectorId: string): Promise<void> {
  console.log(`[Mock] Deleting vector: ${vectorId}`);
<<<<<<< HEAD

  const vector = mockVectors.get(vectorId);

  if (!vector) {
    throw new Error(`Vector not found: ${vectorId}`);
  }

  // Delete the vector
  mockVectors.delete(vectorId);

=======
  
  const vector = mockVectors.get(vectorId);
  
  if (!vector) {
    throw new Error(`Vector not found: ${vectorId}`);
  }
  
  // Delete the vector
  mockVectors.delete(vectorId);
  
>>>>>>> WA-170_MCP
  console.log(`[Mock] Deleted vector: ${vectorId}`);
}

/**
 * Update a mock vector
 * @param role User role
 * @param vectorId Vector ID
 * @param updates Updates to apply
 * @returns A promise that resolves with the updated vector
 */
async function updateMockVector(
  role: UserRole,
  vectorId: string,
  updates: Partial<Pick<Vector, 'name' | 'description'>>
): Promise<Vector> {
  console.log(`[Mock] Updating vector: ${vectorId}`);
<<<<<<< HEAD

  // Handle special test cases for not found
  if (vectorId === 'non-existent-id' || vectorId.includes('non-existent')) {
    throw new Error(`Vector not found: ${vectorId}`);
  }

  // Validate input - simulate real API validation
  if (updates.name !== undefined && (!updates.name || updates.name.trim() === '')) {
    throw new Error('Vector name is required and cannot be empty');
  }

  if (updates.name && updates.name.length > 100) {
    throw new Error('Vector name cannot exceed 100 characters');
  }

  const vector = mockVectors.get(vectorId);

  if (!vector) {
    throw new Error(`Vector not found: ${vectorId}`);
  }

  // Simulate authorization check
  const authFile = getAuthFileForRole(role);
  const userId = getUserIdFromAuthState(authFile) || `mock-${role}-user`;

  // Check if user has access to this vector (ownership check)
  if (role !== 'admin' && vector.ownerUser !== userId) {
    throw new Error('Unauthorized: You do not have permission to update this vector');
  }

=======
  
  const vector = mockVectors.get(vectorId);
  
  if (!vector) {
    throw new Error(`Vector not found: ${vectorId}`);
  }
  
>>>>>>> WA-170_MCP
  // Update the vector
  if (updates.name) {
    vector.name = updates.name;
  }
<<<<<<< HEAD

  if (updates.description !== undefined) {
    vector.description = updates.description;
  }

  // Update the updatedAt timestamp
  vector.updatedAt = new Date().toISOString();

  console.log(`[Mock] Updated vector: ${vector.name} (${vectorId})`);

=======
  
  if (updates.description !== undefined) {
    vector.description = updates.description;
  }
  
  // Update the updatedAt timestamp
  vector.updatedAt = new Date().toISOString();
  
  console.log(`[Mock] Updated vector: ${vector.name} (${vectorId})`);
  
>>>>>>> WA-170_MCP
  return vector;
}
