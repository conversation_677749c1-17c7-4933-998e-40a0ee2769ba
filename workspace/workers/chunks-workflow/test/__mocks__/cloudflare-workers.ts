// Mock for cloudflare:workers module used in tests

export interface WorkflowEvent<T = any> {
  payload: T;
  timestamp: Date;
}

export interface WorkflowStep {
  do<T>(name: string, fn: () => Promise<T>): Promise<T>;
  do<T>(name: string, options: any, fn: () => Promise<T>): Promise<T>;
}

export class WorkflowEntrypoint<Env = any, EventType = any> {
  constructor(protected ctx: ExecutionContext, protected env: Env) {}
  
  async setup(): Promise<void> {
    // Mock implementation
  }
  
  async run(event: WorkflowEvent<EventType>, step: WorkflowStep): Promise<void> {
    // Mock implementation
  }
}
