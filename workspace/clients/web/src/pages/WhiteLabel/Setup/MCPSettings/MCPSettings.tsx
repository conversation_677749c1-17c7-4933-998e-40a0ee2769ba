import React, { useState, useEffect } from "react";
import { useWhiteLabel } from "../data/WhiteLabel";
import { useOwnPermission } from "../../../../components/Permission/data/OwnPermissionContext";

interface MCPConfig {
  enabled: boolean;
  serverUrl: string;
  authToken?: string;
  tools: {
    get_user_profile: boolean;
    create_chat: boolean;
    send_message: boolean;
    list_chats: boolean;
  };
  inspector: {
    enabled: boolean;
    allowedOrigins: string[];
  };
  claudeDesktop: {
    configGenerated: boolean;
    lastUpdated?: string;
  };
}

export function MCPSettings() {
  const { whitelabel } = useWhiteLabel();
  const { hasPermission } = useOwnPermission();
  const [config, setConfig] = useState<MCPConfig>({
    enabled: false,
    serverUrl: "http://localhost:8793",
    tools: {
      get_user_profile: true,
      create_chat: true,
      send_message: true,
      list_chats: true,
    },
    inspector: {
      enabled: true,
      allowedOrigins: ["*"],
    },
    claudeDesktop: {
      configGenerated: false,
    },
  });
  const [loading, setLoading] = useState(false);
  const [testStatus, setTestStatus] = useState<
    "idle" | "testing" | "success" | "error"
  >("idle");

  const canEdit = hasPermission("EDIT_SETTINGS");

  useEffect(() => {
    // Load existing MCP configuration
    loadMCPConfig();
  }, [whitelabel]);

  const loadMCPConfig = async () => {
    if (!whitelabel) return;

    try {
      // TODO: Implement API call to load MCP config
      // const response = await fetch(`/api/white-label/${whitelabel._id}/mcp-config`);
      // const data = await response.json();
      // setConfig(data);
    } catch (error) {
      console.error("Failed to load MCP config:", error);
    }
  };

  const saveMCPConfig = async () => {
    if (!whitelabel || !canEdit) return;

    setLoading(true);
    try {
      // TODO: Implement API call to save MCP config
      // await fetch(`/api/white-label/${whitelabel._id}/mcp-config`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(config)
      // });

      alert("MCP settings saved successfully!");
    } catch (error) {
      console.error("Failed to save MCP config:", error);
      alert("Failed to save MCP settings");
    } finally {
      setLoading(false);
    }
  };

  const testMCPConnection = async () => {
    setTestStatus("testing");
    try {
      const response = await fetch(`${config.serverUrl}/health`);
      if (response.ok) {
        const data = await response.json();
        console.log("MCP server health check:", data);
        setTestStatus("success");
        setTimeout(() => setTestStatus("idle"), 3000);
      } else {
        setTestStatus("error");
        setTimeout(() => setTestStatus("idle"), 3000);
      }
    } catch (error) {
      console.error("MCP connection test failed:", error);
      setTestStatus("error");
      setTimeout(() => setTestStatus("idle"), 3000);
    }
  };

  const generateClaudeDesktopConfig = () => {
    const claudeConfig = {
      mcpServers: {
        [whitelabel?.title?.toLowerCase().replace(/\s+/g, "-") || "divinci"]: {
          command: "npx",
          args: ["mcp-remote", `${config.serverUrl}/sse`],
        },
      },
    };

    const configText = JSON.stringify(claudeConfig, null, 2);
    navigator.clipboard.writeText(configText);

    setConfig((prev) => ({
      ...prev,
      claudeDesktop: {
        configGenerated: true,
        lastUpdated: new Date().toISOString(),
      },
    }));

    alert("Claude Desktop configuration copied to clipboard!");
  };

  if (!whitelabel) {
    return <div>Loading...</div>;
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          🔌 MCP Settings
        </h1>
        <p className="text-gray-600 mb-6">
          Configure Model Context Protocol integration for {whitelabel.title}
        </p>

        {/* Enable/Disable MCP */}
        <div className="mb-8">
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={config.enabled}
              onChange={(e) =>
                setConfig((prev) => ({ ...prev, enabled: e.target.checked }))
              }
              disabled={!canEdit}
              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
            <span className="text-lg font-medium text-gray-900">
              Enable MCP Integration
            </span>
          </label>
          <p className="text-sm text-gray-500 mt-1 ml-7">
            Allow external AI clients to access this workspace through MCP
          </p>
        </div>

        {config.enabled && (
          <>
            {/* Server Configuration */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Server Configuration
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    MCP Server URL
                  </label>
                  <div className="flex space-x-2">
                    <input
                      type="url"
                      value={config.serverUrl}
                      onChange={(e) =>
                        setConfig((prev) => ({
                          ...prev,
                          serverUrl: e.target.value,
                        }))
                      }
                      disabled={!canEdit}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="http://localhost:8793"
                    />
                    <button
                      onClick={testMCPConnection}
                      disabled={!config.serverUrl}
                      className={`px-4 py-2 rounded-md text-white font-medium ${
                        testStatus === "testing"
                          ? "bg-yellow-500"
                          : testStatus === "success"
                          ? "bg-green-500"
                          : testStatus === "error"
                          ? "bg-red-500"
                          : "bg-blue-500 hover:bg-blue-600"
                      }`}
                    >
                      {testStatus === "testing"
                        ? "Testing..."
                        : testStatus === "success"
                        ? "✓ Connected"
                        : testStatus === "error"
                        ? "✗ Failed"
                        : "Test"}
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Available Tools */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Available Tools
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(config.tools).map(([tool, enabled]) => (
                  <label
                    key={tool}
                    className="flex items-center space-x-3 p-3 border rounded-lg"
                  >
                    <input
                      type="checkbox"
                      checked={enabled}
                      onChange={(e) =>
                        setConfig((prev) => ({
                          ...prev,
                          tools: { ...prev.tools, [tool]: e.target.checked },
                        }))
                      }
                      disabled={!canEdit}
                      className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <div>
                      <span className="font-medium text-gray-900">{tool}</span>
                      <p className="text-sm text-gray-500">
                        {tool === "get_user_profile" &&
                          "Access user information"}
                        {tool === "create_chat" && "Create new conversations"}
                        {tool === "send_message" &&
                          "Send messages and get AI responses"}
                        {tool === "list_chats" && "List conversation history"}
                      </p>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Claude Desktop Integration */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Claude Desktop Integration
              </h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-sm text-gray-600 mb-4">
                  Generate configuration for Claude Desktop to connect to this
                  MCP server.
                </p>
                <button
                  onClick={generateClaudeDesktopConfig}
                  className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md font-medium"
                >
                  📋 Generate Claude Desktop Config
                </button>
                {config.claudeDesktop.configGenerated && (
                  <p className="text-sm text-green-600 mt-2">
                    ✓ Configuration copied to clipboard! Paste it into your
                    Claude Desktop settings.
                  </p>
                )}
              </div>
            </div>

            {/* MCP Inspector */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                MCP Inspector
              </h3>
              <div className="bg-blue-50 rounded-lg p-4">
                <p className="text-sm text-gray-600 mb-4">
                  Test your MCP server with the official MCP Inspector tool.
                </p>
                <div className="space-y-2">
                  <code className="block bg-gray-800 text-green-400 p-2 rounded text-sm">
                    npx @modelcontextprotocol/inspector@latest
                  </code>
                  <p className="text-xs text-gray-500">
                    Then connect with:{" "}
                    <code>npx mcp-remote {config.serverUrl}/sse</code>
                  </p>
                </div>
              </div>
            </div>

            {/* Save Button */}
            {canEdit && (
              <div className="flex justify-end">
                <button
                  onClick={saveMCPConfig}
                  disabled={loading}
                  className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-md font-medium"
                >
                  {loading ? "Saving..." : "Save MCP Settings"}
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
