/**
 * Test script for MCP Settings integration
 * This script verifies that the MCP settings page is properly integrated
 */

export interface MCPTestResult {
  success: boolean;
  message: string;
  details?: any;
}

export class MCPIntegrationTester {
  private baseUrl: string;

  constructor(baseUrl: string = "http://localhost:8793") {
    this.baseUrl = baseUrl;
  }

  /**
   * Test MCP server health endpoint
   */
  async testServerHealth(): Promise<MCPTestResult> {
    try {
      const response = await fetch(`${this.baseUrl}/health`);
      
      if (!response.ok) {
        return {
          success: false,
          message: `Health check failed with status: ${response.status}`,
        };
      }

      const data = await response.json();
      return {
        success: true,
        message: "MCP server health check passed",
        details: data,
      };
    } catch (error) {
      return {
        success: false,
        message: `Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  /**
   * Test MCP tools listing
   */
  async testToolsListing(): Promise<MCPTestResult> {
    try {
      const response = await fetch(`${this.baseUrl}/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: 1,
          method: 'tools/list',
        }),
      });

      if (!response.ok) {
        return {
          success: false,
          message: `Tools listing failed with status: ${response.status}`,
        };
      }

      const data = await response.json();
      
      if (data.error) {
        return {
          success: false,
          message: `Tools listing error: ${data.error.message}`,
          details: data.error,
        };
      }

      const expectedTools = ['get_user_profile', 'create_chat', 'send_message', 'list_chats'];
      const actualTools = data.result?.tools?.map((tool: any) => tool.name) || [];
      
      const missingTools = expectedTools.filter(tool => !actualTools.includes(tool));
      
      if (missingTools.length > 0) {
        return {
          success: false,
          message: `Missing expected tools: ${missingTools.join(', ')}`,
          details: { expected: expectedTools, actual: actualTools },
        };
      }

      return {
        success: true,
        message: `All ${expectedTools.length} expected tools are available`,
        details: { tools: actualTools },
      };
    } catch (error) {
      return {
        success: false,
        message: `Tools listing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  /**
   * Test SSE endpoint for MCP Inspector compatibility
   */
  async testSSEEndpoint(): Promise<MCPTestResult> {
    try {
      const response = await fetch(`${this.baseUrl}/sse`, {
        method: 'GET',
        headers: {
          'Accept': 'text/event-stream',
        },
      });

      if (!response.ok) {
        return {
          success: false,
          message: `SSE endpoint failed with status: ${response.status}`,
        };
      }

      const contentType = response.headers.get('content-type');
      if (!contentType?.includes('text/event-stream')) {
        return {
          success: false,
          message: `SSE endpoint returned wrong content type: ${contentType}`,
        };
      }

      return {
        success: true,
        message: "SSE endpoint is accessible and returns correct headers",
        details: { contentType },
      };
    } catch (error) {
      return {
        success: false,
        message: `SSE endpoint test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  /**
   * Run all MCP integration tests
   */
  async runAllTests(): Promise<{ 
    overall: boolean; 
    results: Record<string, MCPTestResult> 
  }> {
    const results: Record<string, MCPTestResult> = {};

    console.log("🔌 Running MCP Integration Tests...");

    // Test 1: Server Health
    console.log("1. Testing server health...");
    results.health = await this.testServerHealth();
    console.log(results.health.success ? "✅" : "❌", results.health.message);

    // Test 2: Tools Listing
    console.log("2. Testing tools listing...");
    results.tools = await this.testToolsListing();
    console.log(results.tools.success ? "✅" : "❌", results.tools.message);

    // Test 3: SSE Endpoint
    console.log("3. Testing SSE endpoint...");
    results.sse = await this.testSSEEndpoint();
    console.log(results.sse.success ? "✅" : "❌", results.sse.message);

    const overall = Object.values(results).every(result => result.success);
    
    console.log("\n📊 Test Summary:");
    console.log(`Overall: ${overall ? "✅ PASS" : "❌ FAIL"}`);
    console.log(`Health: ${results.health.success ? "✅" : "❌"}`);
    console.log(`Tools: ${results.tools.success ? "✅" : "❌"}`);
    console.log(`SSE: ${results.sse.success ? "✅" : "❌"}`);

    return { overall, results };
  }
}

/**
 * Utility function to test MCP integration from the browser console
 */
export async function testMCPIntegration(serverUrl?: string) {
  const tester = new MCPIntegrationTester(serverUrl);
  return await tester.runAllTests();
}

// Make it available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testMCPIntegration = testMCPIntegration;
}
