import { ProcessedChunk, WorkflowMetadata } from "./types/workflow";
import { StorageClient } from "./utils/storage-client";

// Re-export for convenience
export { ProcessedChunk, WorkflowMetadata };

export interface FileRecordPayload {
  fileId?: string,
  bucket: string,
  objectKey: string,
  originalName: string,
  chunkingTool: string,
  title?: string,
  description?: string,
}

interface AiModels {
  "@cf/huggingface/distilbert-sst-2-int8": {
    text: string,
  },
  "@cf/baai/bge-base-en-v1.5": {
    text: string,
  },
}

export interface AiResponse {
  values?: number[],
  embedding?: number[],
}

export interface ChunkingJob {
  fileData: number[] | string, // Array for binary or string for base64
  config: any,
  fileName: string,
  fileId: string,
  whitelabelId: string,
  processor: "unstructured" | "openparse",
  partInfo: {
    partNumber: number,
    totalParts: number,
    size: number,
    encoding?: "base64",
  },
}

export interface VectorizeJob {
  chunks: ProcessedChunk[],
  vectorIndex: string,
  fileId: string,
}

export interface VideoToAudioEvent {
  videoFile: {
    fileId: string,
    objectKey: string,
    fileName: string,
    title?: string,
    description?: string,
  },
  instanceId: string,
  whitelabelId: string,
  auth0Token: string,
}


export interface Env {
  DB: D1Database,
  LOGS_DB: D1Database,
  R2: R2Bucket,
  // Storage client is initialized at runtime based on environment
  storageClient?: StorageClient,
  AI: {
    run<K extends keyof AiModels>(
      model: K,
      inputs: AiModels[K]
    ): Promise<number[] | AiResponse>,
  },
  UNSTRUCTURED_WORKER_URL: string,
  OPENPARSE_API_KEY: string,
  FFMPEG_WORKER_URL: string,
  OPENPARSE_API_URL: string,
  ENVIRONMENT: string,
  CLOUDFLARE_API_URL: string,
  CLOUDFLARE_ACCOUNT_ID: string,
  CLOUDFLARE_API_TOKEN: string,
  CHUNKS_VECTORIZED: Workflow<ChunksVectorizedEvent>,
  VIDEO_TO_AUDIO: Workflow<VideoToAudioEvent>,
  API_HOST: string,
  R2_BUCKET_URL: string,
  R2_ACCESS_KEY_ID?: string,
  R2_SECRET_ACCESS_KEY?: string,
<<<<<<< HEAD
=======
  CLOUDFLARE_WHITELABEL_VECTOR_ACCESS_KEY_ID?: string,
  CLOUDFLARE_WHITELABEL_VECTOR_ACCESS_KEY_SECRET?: string,
  CLOUDFLARE_WORKER_X_AUTH_DEV?: string,
>>>>>>> WA-170_MCP
}

export interface ChunksVectorizedEvent {
  files: Array<{
    fileId: string,
    originalFileId?: string, // Store original ID for reference and fallback
    target: string,
    fileName: string,
    bucket: string,
    objectKey: string,
    processor: "unstructured" | "openparse",
    processorConfig?: {
      semantic_chunking?: boolean,
      embeddings_provider?: "cloudflare" | "ollama" | "none",
      minTokens?: number,
      maxTokens?: number,
      relevanceThreshold?: number,
      skipDeJunk?: boolean,
    },
    // Remove fileData from the workflow event to avoid RPC size limits
    formData?: FormData,
    title?: string,
    description?: string,
  }>,
  vectorizeConfig: {
    accountId: string,
    apiToken: string,
    auth0Token: string,
    ragName: string,
    ragVectors: Array<string>,
    ragId?: string, // Optional for backward compatibility
    whitelabelId: string,
  },
  timestamp: Date,
  instanceId: string,
}

export interface R2ValidationResult {
  objectKey: string,
  fileName: string,
  hasExistingFile: boolean,
  pendingDirectUpload?: boolean,
  size: number,
  mimeType: string,
  customMetadata: {
    fileName?: string,
    uploadedAt?: string,
    size?: string,
    [key: string]: string | undefined,
  },
}

export interface FileRecordData {
  _id: string,
  target: string,
  title: string,
  chunkingTool: string,
  originalFilename: string,
  uploadTimestamp: number,
  updateTimestamp: number,
  rawFileKey: string,
  fileKey: string,
  status: string,
  vectorIndexProcessing: any[],
  chunks: any[],
  __v: number,
}

export interface FileRecord {
  status: "success" | string,
  data: FileRecordData,
}

export enum RagVectorTextChunksStatus {
  CHUNKING = "file to chunks",
  FAILED_TO_CHUNK = "failed to chunk",
  EDITING = "editing chunks",
  STORING = "chunks to storage",
  VECTORIZING = "chunks to vectors",
  FAILED_TO_VECTORIZE = "failed to vectorize",
  COMPLETED = "completed",
}

export interface FileRecordResponse {
  status: "success" | "error",
  data?: {
    _id: string,
    bucket: string,
    objectKey: string,
    originalName: string,
    [key: string]: any,
  },
  message?: string,
}

// ProcessedChunk is now imported from ./types/workflow
