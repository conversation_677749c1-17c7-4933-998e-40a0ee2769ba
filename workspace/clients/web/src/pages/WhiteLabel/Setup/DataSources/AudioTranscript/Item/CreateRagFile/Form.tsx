import React, { useMemo } from "react";

import { useAudioTranscriptItem } from "../../data/AudioTranscriptItem";
import { InputProps } from "../../../../../../../util/react/input";
import { BodyType } from "./types";
import styles from "./form.module.css";

import { ChunkerTool } from "../../Form/ChunkerTool";
import { RagVectorSelector } from "./RagVectorSelector";

export function AudioTranscriptRagFileForm(
  { value, onChange }: InputProps<BodyType>
){

  const { value: item } = useAudioTranscriptItem();
  const speakers = useMemo(()=>{
    if(!item) return [];
    return getSpeakers(item.samples);
  }, [item]);

  // Show loading state if item or speakers are not available yet
<<<<<<< HEAD
  if (!item) {
=======
  if(!item) {
>>>>>>> WA-170_MCP
    return (
      <div className={`audio-rag-file ${styles.container}`}>
        <div className={styles.formSection}>
          <h1 className={`title is-4 ${styles.title}`}>Audio Transcript RAG File Configuration</h1>
          <div className="has-text-centered my-5">
            <p className={`mb-3 ${styles.formContent}`}>Loading transcript data...</p>
            <progress className="progress is-primary" max="100"></progress>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`audio-rag-file ${styles.container}`}>
      <div className={styles.formSection}>
        <h1 className={`title is-4 ${styles.title}`}>Audio Transcript RAG File Configuration</h1>
        <p className={`subtitle is-6 ${styles.subtitle}`}>
          Configure how the audio transcript will be processed for RAG (Retrieval Augmented Generation).
        </p>

        <div className={`content ${styles.formContent}`}>
          <p>
            This configuration allows you to select which speakers to include in the RAG file and
            choose the chunking tool that will be used to process the transcript.
          </p>
        </div>

        <div className={`${styles.columns} columns`}>
          <div className={`${styles.column} column`}>
            <h2 className={`${styles.subtitle} subtitle is-5`}>
              Speakers List
              <span className="tag is-info is-light ml-2">{value.speakers.length} selected</span>
            </h2>
            <p className={styles.helpText}>Select speakers to include in the RAG file</p>
            <div className={styles.selectContainer}>
              <select
                className={`${styles.selectBox} select is-multiple`}
                multiple={true}
                value={value.speakers}
                onChange={(e)=>{
                  onChange({
                    ...value,
                    speakers: Array.from(
                      e.target.selectedOptions, (option)=>(option.value)
                    )
                  });
                }}
              >
                {speakers.map((speaker)=>(
                  <option className="speaker-checkbox" key={speaker} value={speaker}>{speaker}</option>
                ))}
              </select>
            </div>
          </div>

          <div className={`${styles.column} column`}>
            <ChunkerTool
              value={value.chunkingTool}
              onChange={(newValue)=>(
                onChange({ ...value, chunkingTool: newValue })
              )}
            />
          </div>
        </div>
<<<<<<< HEAD
=======

        <div className={`${styles.columns} columns`}>
          <div className={`${styles.column} column`}>
            <RagVectorSelector
              value={value.ragVectors}
              onChange={(newValue)=>(
                onChange({ ...value, ragVectors: newValue })
              )}
            />
          </div>
        </div>
>>>>>>> WA-170_MCP
      </div>
    </div>
  );
}

function getSpeakers(samples: Array<{ speaker: string }>){
  const unique = new Set<string>();
  for(const { speaker } of samples) unique.add(speaker);
  return Array.from(unique);
}
