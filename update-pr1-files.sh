#!/bin/bash

# <PERSON><PERSON>t to update files in PR-Split-1-GitHub-Actions with their latest versions from AS-211_AS-176-Workflow-Polish_2

# Ensure we're on the temporary branch
git checkout temp-PR-Split-1-GitHub-Actions

# Get the list of files from the analysis file, skipping the header lines
FILES=$(tail -n +4 pr-analysis/pr1-files.txt)

# Create a temporary directory to store the files
mkdir -p temp_files

# For each file in the list
for FILE in $FILES; do
  # Skip empty lines
  if [ -z "$FILE" ]; then
    continue
  fi
  
  # Skip lines that start with dashes (separator lines)
  if [[ $FILE == -* ]]; then
    continue
  fi
  
  echo "Processing file: $FILE"
  
  # Check if the file exists in the current branch
  if git show origin/AS-211_AS-176-Workflow-Polish_2:$FILE > /dev/null 2>&1; then
    # Create the directory structure if it doesn't exist
    mkdir -p $(dirname "temp_files/$FILE")
    
    # Copy the file from the current branch to the temporary directory
    git show origin/AS-211_AS-176-Workflow-Polish_2:$FILE > "temp_files/$FILE"
    
    # Create the directory structure in the target branch if it doesn't exist
    mkdir -p $(dirname "$FILE")
    
    # Copy the file to the target branch
    cp "temp_files/$FILE" "$FILE"
    
    # Add the file to the staging area
    git add "$FILE"
  else
    echo "File not found in AS-211_AS-176-Workflow-Polish_2: $FILE"
  fi
done

# Commit the changes
git commit -m "Update files from AS-211_AS-176-Workflow-Polish_2"

# Clean up
rm -rf temp_files

echo "Files updated successfully!"
