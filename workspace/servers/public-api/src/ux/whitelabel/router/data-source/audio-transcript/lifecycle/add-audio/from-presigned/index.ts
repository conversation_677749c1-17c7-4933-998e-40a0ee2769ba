import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getWhitelabelTarget } from "../../../util/whitelabel";
import { HTTP_ERRORS_WITH_CONTEXT, jsonBody } from "@divinci-ai/server-utils";
import { castShallowObject } from "@divinci-ai/utils";
import { createPresignedURL, castPresignedConfig, handlePresignedURL } from "../../../../../../../../util/presigned-r2";
import { getUserId } from "@divinci-ai/server-globals";
// import { R2_INSTANCE } from "../../../../../fine-tuning/csv-editor/util";
import { r2 } from "../../../util/r2-constants";

import { transcribeMedia } from "../resuable/transcribe-media";
import { ensureValidMediaName } from "../resuable/validate-media-name";

// Constants
const PRESIGNED_PURPOSE = "AudioTranscript Video Upload";

// Presigned URL preparation endpoint
export const presignedPrepare: RequestHandler = async function(req, res, next){
  console.log("🖋️ presignedPrepare hit!");
  try {
    const userId = getUserId(req);
    const [unCastedBody] = await Promise.all([
      jsonBody(req),
      getWhitelabelTarget(req),
    ]);

    // Handle both single file and multiple files
    const fileInfo = castShallowObject(unCastedBody, {
      filename: "string",
      byteSize: "number",
    });

    ensureValidMediaName(
      fileInfo.filename, HTTP_ERRORS_WITH_CONTEXT.BAD_FORM
    );

    const presignedUrl = await createPresignedURL(
      r2,
      { userId, purpose: PRESIGNED_PURPOSE },
      fileInfo
    );

    res.statusCode = 200;
    res.json(presignedUrl);
  }catch(e) {
    next(e);
  }
};

// Presigned URL finalization endpoint
export const presignedFinalized: RequestHandler = async function(req, res, next){
  try {
    const userId = getUserId(req);
    const [unCastedBody, { target, whitelabel }] = await Promise.all([
      jsonBody(req),
      getWhitelabelTarget(req),
    ]);

    const bodyData = castShallowObject(unCastedBody, {
      diarizerTool: "string",
      transcriberTool: "string",
      mediaFile: "string",
      // Confidence settings
      confidenceEnabled: "string?",
      confidenceThreshold: "string?",
      confidenceIncludeInProcessing: "string?",
      // Voiceprint settings
      voiceprintsEnabled: "string?",
      voiceprintsAutoCreate: "string?",
      voiceprintsData: "string?", // 🎯 Add voiceprintsData field for identification
    });

    const {
      diarizerTool,
      transcriberTool,
      mediaFile: mediaFileRaw,
    } = bodyData;

    // Parse confidence settings from request data
    const confidenceSettings = bodyData.confidenceEnabled === "true" ? {
      enabled: true,
      threshold: parseFloat(bodyData.confidenceThreshold || "0.7"),
      includeInProcessing: bodyData.confidenceIncludeInProcessing !== "false",
    } : undefined;

    // Parse voiceprint settings from request data
    let voiceprintSettings = bodyData.voiceprintsEnabled === "true" ? {
      enabled: true,
      autoCreate: bodyData.voiceprintsAutoCreate === "true",
    } : undefined;

    // Parse existing voiceprints for identification if provided
    if (voiceprintSettings && bodyData.voiceprintsData) {
      try {
        const voiceprintsData = JSON.parse(bodyData.voiceprintsData);
        if (Array.isArray(voiceprintsData) && voiceprintsData.length > 0) {
          (voiceprintSettings as any).existingVoiceprints = voiceprintsData;
          console.log("🎯 [IDENTIFICATION] Parsed voiceprints for identification (presigned):", voiceprintsData.length);
        }
      } catch (error) {
        console.warn("⚠️ [IDENTIFICATION] Failed to parse voiceprints data (presigned):", error);
      }
    }

    const presignedConfig = castPresignedConfig(mediaFileRaw);

    const presigned = await handlePresignedURL(
      r2,
      { userId, purpose: PRESIGNED_PURPOSE },
      presignedConfig
    );

<<<<<<< HEAD
    const doc = await transcribeMedia(
=======
    const { doc, work } = await transcribeMedia(
>>>>>>> WA-170_MCP
      target, whitelabel._id.toString(),
      { diarizer: diarizerTool, transcriber: transcriberTool },
      presigned,
      {
        filename: presignedConfig.filename,
        sourceType: "file",
        rawValue: presignedConfig.filename,
      },
      undefined, // copiedLocation
      undefined, // localModeOptions
      confidenceSettings,  // Pass confidence settings
      voiceprintSettings   // Pass voiceprint settings
    );

<<<<<<< HEAD
=======
    // Handle the background work promise to prevent unhandled rejections
    work.catch((error: any) => {
      console.error("❌ Background transcription work failed:", error);
      // The error is logged but doesn't affect the HTTP response
    });

>>>>>>> WA-170_MCP
    // Return immediately with the document ID and status URL
    res.statusCode = 202; // Accepted
    res.json({
      id: doc._id,
      status: doc.processStatus,
      statusUrl: `/white-label/${whitelabel._id}/data-source/audio-transcript/${doc._id}/status`,
      message: "Audio transcription job started. Poll the statusUrl to check progress."
    });
  }catch(e) {
    next(e);
  }
};
