import { setupEnv } from "@divinci-ai/server-utils";
import { resolve as pathResolve } from "path";

const addedVars = setupEnv({ envPath: pathResolve(__dirname, "../../../../private-keys/local-fast"), quiet: true });

if(isMain()){
  console.log(
    Object.entries(addedVars)
    // IMPORTANT!! Spaces in values dont work
    //.concat([["TESTING_SPACE", "hope it works"]])
    // get rid of undefined values
    .filter(([, value])=>(typeof value !== "undefined"))
    // turn the entry into a KEY="VALUE" string
    .map(([key, value])=>{
      let escapedValue = value
      .replace(/'/g, "\\'")   // Escape single quotes
      .replace(/"/g, '\\"')   // Escape double quotes
      .replace(/\n/g, '\\n'); // Escape newlines

      // If the value contains spaces, wrap it in double quotes
      if(/\s/.test(escapedValue)){
        escapedValue = `'${escapedValue}'`;
        throw new Error("Spaces in values don't currently work");
      }
      return `${key}=${escapedValue}`;
    })
    // join the strings with ; and space
    .join(" ")
  );
}

export { addedVars };

function isMain(){
  // If running in a CommonJS environment
  if(typeof require !== 'undefined' && typeof module !== 'undefined'){
    return require.main === module;
  }

  /*
  // If running in an ES Module environment
  // @ts-expect-error 1470
  if(typeof import.meta !== 'undefined'){
    // @ts-expect-error 1470
    return import.meta.url === new URL(import.meta.url, import.meta.url).href;
  }
  */

  // Default to false if we can't determine the environment
  return false;
}
