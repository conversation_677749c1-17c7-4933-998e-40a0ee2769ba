/**
 * Types for Dropbox Sync Worker
 */

import { WorkflowStep } from "cloudflare:workers";

// Environment bindings
export interface Env {
  // Cloudflare bindings
  R2: R2Bucket;
  DROPBOX_SYNC_WORKFLOW: Workflow;
  
  // Environment variables
  ENVIRONMENT: string;
  API_HOST: string;
  CLOUDFLARE_ACCOUNT_ID: string;
  CLOUDFLARE_API_TOKEN?: string;
  ALLOWED_ORIGINS: string;
  CLOUDFLARE_WORKER_X_AUTH_DEV?: string;
  
  // Audio processing worker URLs
  AUDIO_TRANSCRIPT_WORKER_URL: string;
  AUDIO_DIARIZATION_WORKER_URL: string;
  CHUNKS_WORKFLOW_WORKER_URL: string;
  
  // R2 configuration
  R2_BUCKET_URL: string;
  R2_ACCESS_KEY_ID?: string;
  R2_SECRET_ACCESS_KEY?: string;
}

// Dropbox sync job status
export enum DropboxSyncJobStatus {
  PENDING = "pending",
  RUNNING = "running",
  COMPLETED = "completed", 
  FAILED = "failed",
  CANCELLED = "cancelled"
}

// Dropbox sync target type
export enum DropboxSyncTargetType {
  RAG = "rag",
  FINE_TUNING = "fine-tuning"
}

// File processing status for individual files
export enum DropboxFileProcessingStatus {
  PENDING = "pending",
  DOWNLOADING = "downloading",
  TRANSCRIBING = "transcribing",
  DIARIZING = "diarizing", 
  CHUNKING = "chunking",
  VECTORIZING = "vectorizing",
  COMPLETED = "completed",
  FAILED = "failed"
}

// Dropbox file information
export interface DropboxSyncFile {
  dropboxPath: string;
  dropboxFileId: string;
  fileName: string;
  fileSize: number;
  isAudio: boolean;
  isVideo: boolean;
  extension: string;
  mimeType?: string;
  
  // Processing status for this specific file
  status: DropboxFileProcessingStatus;
  internalFileId?: string; // ID after successful processing
  errorMessage?: string;
  transferredBytes?: number;
  startedAt?: Date;
  completedAt?: Date;
}

// Processing configuration
export interface DropboxSyncProcessingConfig {
  // RAG-specific config
  chunkingTool?: string;
  chunkingConfig?: any;
  ragId?: string;
  
  // Fine-tuning specific config
  fineTuningDatasetId?: string;
  
  // Audio processing config
  transcriptionTool?: string;
  diarizationTool?: string;
  
  // General config
  skipExistingFiles?: boolean;
  maxFileSize?: number;
}

// Progress tracking
export interface DropboxSyncProgress {
  totalFiles: number;
  processedFiles: number;
  failedFiles: number;
  totalBytes: number;
  transferredBytes: number;
  currentFile?: string;
  estimatedTimeRemaining?: number; // seconds
  averageSpeed?: number; // bytes per second
}

// Main workflow event payload
export interface DropboxSyncWorkflowEvent {
  // Job identification
  jobId: string;
  whitelabelId: string;
  connectionId: string;
  userId: string;
  
  // Job configuration
  targetType: DropboxSyncTargetType;
  sourceFiles: DropboxSyncFile[];
  processingConfig: DropboxSyncProcessingConfig;
  
  // Authentication
  accessToken: string; // Dropbox access token
  auth0Token: string; // API authentication
  
  // Metadata
  timestamp: string | Date;
  instanceId: string;
  metadata?: {
    jobName?: string;
    description?: string;
    priority?: number;
    tags?: string[];
  };
}

// Step context for workflow steps
export interface StepContext {
  env: Env;
  jobId: string;
  whitelabelId: string;
  auth0Token: string;
}

// File download result
export interface FileDownloadResult {
  objectKey: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
  downloadedAt: Date;
  r2Url: string;
}

// Audio processing result
export interface AudioProcessingResult {
  transcriptionId?: string;
  transcriptionText?: string;
  diarizationId?: string;
  speakerSegments?: any[];
  processingDuration: number; // seconds
  audioMetadata?: {
    duration?: number;
    sampleRate?: number;
    channels?: number;
  };
}

// Chunks workflow result
export interface ChunksWorkflowResult {
  workflowId: string;
  fileId: string;
  chunkCount: number;
  vectorCount: number;
  status: string;
}

// API response types
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Sync job update payload
export interface SyncJobUpdatePayload {
  status?: DropboxSyncJobStatus;
  progress?: Partial<DropboxSyncProgress>;
  errors?: string[];
  fileUpdates?: {
    dropboxFileId: string;
    status: DropboxFileProcessingStatus;
    errorMessage?: string;
    internalFileId?: string;
    transferredBytes?: number;
  }[];
}

// File mapping update payload
export interface FileMappingUpdatePayload {
  dropboxFileId: string;
  internalFileId?: string;
  syncStatus: string;
  processingMetadata?: {
    transcriptionId?: string;
    chunkCount?: number;
    vectorCount?: number;
    processingDuration?: number;
    processingErrors?: string[];
  };
}

// Workflow metadata for tracking
export interface WorkflowMetadata {
  workflowId: string;
  jobId: string;
  targetType: DropboxSyncTargetType;
  totalFiles: number;
  steps: {
    downloadFiles: {
      startTime: string;
      endTime?: string;
      filesDownloaded: number;
      totalBytes: number;
    };
    processAudio: {
      startTime?: string;
      endTime?: string;
      filesProcessed: number;
      transcriptionResults: any[];
      diarizationResults: any[];
    };
    triggerChunksWorkflow: {
      startTime?: string;
      endTime?: string;
      workflowIds: string[];
    };
    updateDatabase: {
      startTime?: string;
      endTime?: string;
      updatedRecords: number;
    };
    [key: string]: any;
  };
  status: DropboxSyncJobStatus;
  error?: string;
  startTime: string;
  endTime?: string;
}
