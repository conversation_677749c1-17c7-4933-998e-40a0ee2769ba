name = "dropbox-sync-worker"
main = "src/index.ts"
compatibility_flags = ["nodejs_compat"]
compatibility_date = "2024-09-23"
logpush = true

[observability]
enabled = true

[placement]
mode = "smart"

[dev]
port = 8790
ip = "0.0.0.0"
local_protocol = "http"

[vars]
ENVIRONMENT = "development"
API_HOST = "http://local-api:8080"
CLOUDFLARE_ACCOUNT_ID = "14a6fa23390363382f378b5bd4a0f849"

# Local environment
[env.local]
[env.local.vars]
ENVIRONMENT = "local"
API_HOST = "http://local-api:8080"
CLOUDFLARE_ACCOUNT_ID = "14a6fa23390363382f378b5bd4a0f849"
CLOUDFLARE_API_TOKEN = "****************************************"
ALLOWED_ORIGINS = "http://localhost:8080,http://localhost:8789,http://localhost:8790,http://localhost:8791,http://127.0.0.1:8080,http://127.0.0.1:8789,http://127.0.0.1:8790,http://127.0.0.1:8791"
CLOUDFLARE_WORKER_X_AUTH_DEV = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.4ygtkAA4RmHtGAT5jHKzpr_3HgpaVSVKQ-IqzFJA7TI2"

# Audio processing worker URLs
AUDIO_TRANSCRIPT_WORKER_URL = "http://local-audio-transcript:8080"
AUDIO_DIARIZATION_WORKER_URL = "http://local-audio-diarization:8080"
CHUNKS_WORKFLOW_WORKER_URL = "http://local-chunks-workflow:8789"

# R2 bucket configuration
R2_BUCKET_URL = "http://local-minio:9000"
R2_ACCESS_KEY_ID = "EKzhr0JMbJwLFVlE08Om"
R2_SECRET_ACCESS_KEY = "ropI7tOATZSjVpdRnZz59A25xpzGe4yxAWoINO0M"

[[env.local.workflows]]
name = "dropbox-sync-local"
binding = "DROPBOX_SYNC_WORKFLOW"
class_name = "DropboxSyncWorkflow"

[[env.local.r2_buckets]]
binding = "R2"
bucket_name = "dropbox-sync-files-local"
preview_bucket_name = "dropbox-sync-files-preview"

# Development environment
[env.dev]
routes = [
  { pattern = "dropbox-sync.dev.divinci.app", custom_domain = true }
]

[env.dev.vars]
ENVIRONMENT = "dev"
API_HOST = "https://api.dev.divinci.app"
CLOUDFLARE_ACCOUNT_ID = "14a6fa23390363382f378b5bd4a0f849"
CLOUDFLARE_API_TOKEN = "****************************************"
ALLOWED_ORIGINS = "https://*.dev.divinci.app"

# Audio processing worker URLs
AUDIO_TRANSCRIPT_WORKER_URL = "https://audio-transcript.dev.divinci.app"
AUDIO_DIARIZATION_WORKER_URL = "https://audio-diarization.dev.divinci.app"
CHUNKS_WORKFLOW_WORKER_URL = "https://rag-workflow.dev.divinci.app"

R2_BUCKET_URL = "https://dropbox-files.dev.divinci.app"

[[env.dev.workflows]]
name = "dropbox-sync-dev"
binding = "DROPBOX_SYNC_WORKFLOW"
class_name = "DropboxSyncWorkflow"

[[env.dev.r2_buckets]]
binding = "R2"
bucket_name = "dropbox-sync-files-dev"

# Staging environment
[env.stage]
routes = [
  { pattern = "dropbox-sync.stage.divinci.app", custom_domain = true }
]

[env.stage.vars]
ENVIRONMENT = "stage"
API_HOST = "https://api.stage.divinci.app"
CLOUDFLARE_ACCOUNT_ID = "14a6fa23390363382f378b5bd4a0f849"
CLOUDFLARE_API_TOKEN = "****************************************"
ALLOWED_ORIGINS = "https://*.stage.divinci.app"

# Audio processing worker URLs
AUDIO_TRANSCRIPT_WORKER_URL = "https://audio-transcript.stage.divinci.app"
AUDIO_DIARIZATION_WORKER_URL = "https://audio-diarization.stage.divinci.app"
CHUNKS_WORKFLOW_WORKER_URL = "https://rag-workflow.stage.divinci.app"

R2_BUCKET_URL = "https://dropbox-files.stage.divinci.app"

[[env.stage.workflows]]
name = "dropbox-sync-stage"
binding = "DROPBOX_SYNC_WORKFLOW"
class_name = "DropboxSyncWorkflow"

[[env.stage.r2_buckets]]
binding = "R2"
bucket_name = "dropbox-sync-files-stage"

# Production environment
[env.production]
routes = [
  { pattern = "dropbox-sync.divinci.app", custom_domain = true }
]

[env.production.vars]
ENVIRONMENT = "production"
API_HOST = "https://api.divinci.app"
CLOUDFLARE_ACCOUNT_ID = "14a6fa23390363382f378b5bd4a0f849"
ALLOWED_ORIGINS = "https://*.divinci.app"

# Audio processing worker URLs
AUDIO_TRANSCRIPT_WORKER_URL = "https://audio-transcript.divinci.app"
AUDIO_DIARIZATION_WORKER_URL = "https://audio-diarization.divinci.app"
CHUNKS_WORKFLOW_WORKER_URL = "https://rag-workflow.divinci.app"

R2_BUCKET_URL = "https://dropbox-files.divinci.app"

[[env.production.workflows]]
name = "dropbox-sync-production"
binding = "DROPBOX_SYNC_WORKFLOW"
class_name = "DropboxSyncWorkflow"

[[env.production.r2_buckets]]
binding = "R2"
bucket_name = "dropbox-sync-files-production"
