/**
 * Vector Pool
<<<<<<< HEAD
 *
=======
 * 
>>>>>>> WA-170_MCP
 * This module provides a resource pool for RAG vectors.
 */

import { ResourcePool } from './resource-pool';
import { Vector, createVector, deleteVector } from '../api/vector-api';
import { UserRole } from '../auth/auth-utils';
import { config } from '../config/config';
import { randomName } from '../utils/test-utils';
import { getWhitelabel, releaseWhitelabel } from './whitelabel-pool';

/**
 * Vector pool
 */
export const vectorPool = new ResourcePool<Vector>(
  async (key: string) => {
    // Parse the key to get the role, whitelabel ID, and name
    const [role, whitelabelId, name] = key.split('::');
<<<<<<< HEAD

    // If no whitelabel ID is provided, get a whitelabel from the pool
    let actualWhitelabelId = whitelabelId;
    let usePooledWhitelabel = false;

=======
    
    // If no whitelabel ID is provided, get a whitelabel from the pool
    let actualWhitelabelId = whitelabelId;
    let usePooledWhitelabel = false;
    
>>>>>>> WA-170_MCP
    if (!actualWhitelabelId || actualWhitelabelId === 'auto') {
      usePooledWhitelabel = true;
      const whitelabel = await getWhitelabel(role as UserRole);
      actualWhitelabelId = whitelabel._id;
    }
<<<<<<< HEAD

=======
    
>>>>>>> WA-170_MCP
    try {
      // Create a vector with the specified role, whitelabel ID, and name
      const vector = await createVector(
        role as UserRole,
        {
<<<<<<< HEAD
          name: name !== undefined ? name : randomName('test-vector'),
=======
          name: name || randomName('test-vector'),
>>>>>>> WA-170_MCP
          whitelabelId: actualWhitelabelId,
          description: `Test vector created by the vector pool for testing purposes.`
        }
      );
<<<<<<< HEAD

=======
      
>>>>>>> WA-170_MCP
      return vector;
    } finally {
      // Release the whitelabel back to the pool if we got it from there
      if (usePooledWhitelabel) {
        releaseWhitelabel(role as UserRole);
      }
    }
  },
  async (vector: Vector) => {
    // Delete the vector
    try {
      // Use the admin role to ensure we have permission to delete
      await deleteVector('admin', vector._id);
    } catch (error) {
      console.error(`Error deleting vector ${vector._id}: ${error}`);
    }
  },
  {
    maxSize: config.resourcePool.maxSize,
    maxAge: config.resourcePool.maxAge,
    cleanupIntervalMs: config.resourcePool.cleanupIntervalMs,
    debug: config.resourcePool.debug
  }
);

/**
 * Get a vector from the pool
 * @param role User role
 * @param whitelabelId Whitelabel ID (optional, if not provided a whitelabel will be created)
 * @param name Vector name (optional)
 * @returns A promise that resolves with a vector
 */
export async function getVector(
  role: UserRole,
  whitelabelId?: string,
  name?: string
): Promise<Vector> {
  const key = `${role}::${whitelabelId || 'auto'}::${name || ''}`;
  return vectorPool.get(key);
}

/**
 * Release a vector back to the pool
 * @param role User role
 * @param whitelabelId Whitelabel ID (optional)
 * @param name Vector name (optional)
 */
export function releaseVector(
  role: UserRole,
  whitelabelId?: string,
  name?: string
): void {
  const key = `${role}::${whitelabelId || 'auto'}::${name || ''}`;
  vectorPool.release(key);
}

/**
 * Remove a vector from the pool
 * @param role User role
 * @param whitelabelId Whitelabel ID (optional)
 * @param name Vector name (optional)
 * @returns A promise that resolves when the vector is removed
 */
export async function removeVector(
  role: UserRole,
  whitelabelId?: string,
  name?: string
): Promise<void> {
  const key = `${role}::${whitelabelId || 'auto'}::${name || ''}`;
  await vectorPool.remove(key);
}

/**
 * Clear all vectors from the pool
 * @returns A promise that resolves when all vectors are cleared
 */
export async function clearVectors(): Promise<void> {
  await vectorPool.clear();
}
