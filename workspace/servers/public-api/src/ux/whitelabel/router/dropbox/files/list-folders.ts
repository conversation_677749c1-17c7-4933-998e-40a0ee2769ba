/**
 * List folders in Dropbox
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { ensureDropboxConnection, handleDropboxError } from "../util/ensure-whitelabel";

interface ListFoldersQuery {
  path?: string;
  recursive?: string;
  limit?: string;
}

export const listDropboxFolders: RequestHandler = async function(req, res, next) {
  try {
    const { connection, dropboxClient } = await ensureDropboxConnection(req);
    const { path = "/", recursive = "false", limit = "1000" }: ListFoldersQuery = req.query;

    // List folder contents
    const response = await dropboxClient.listFolder({
      path: path === "/" ? "" : path,
      recursive: recursive === "true",
      limit: parseInt(limit, 10)
    });

    // Filter to only include folders
    const folders = response.entries
      .filter(entry => entry[".tag"] === "folder")
      .map(folder => ({
        id: folder.id,
        name: folder.name,
        path: folder.path_display,
        pathLower: folder.path_lower,
        sharedFolderId: (folder as any).shared_folder_id
      }));

    res.status(200).json({
      success: true,
      data: {
        folders,
        hasMore: response.has_more,
        cursor: response.cursor,
        totalCount: folders.length
      }
    });

  } catch (error) {
    try {
      await handleDropboxError(error, req.connection);
    } catch (handledError) {
      return next(handledError);
    }
    next(error);
  }
};
