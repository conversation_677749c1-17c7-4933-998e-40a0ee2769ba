#!/bin/bash
# <PERSON><PERSON>t to change Cloudflare SSL mode

set -e

# Get the desired SSL mode
SSL_MODE=${1:-"full"}
if [ "$SSL_MODE" != "full" ] && [ "$SSL_MODE" != "strict" ] && [ "$SSL_MODE" != "flexible" ]; then
  echo "❌ Invalid SSL mode: $SSL_MODE"
  echo "Valid modes: full, strict, flexible"
  exit 1
fi

echo "🔍 Changing Cloudflare SSL mode to $SSL_MODE..."

# Get Cloudflare API token
<<<<<<< HEAD
CF_API_TOKEN=${2:-"xQI66zSWAOjLnWlyoZne8_CjVgStYXYbH26f3p2c"}
=======
CF_API_TOKEN=${2:-"aR8Ud0AVhRI59O69XX815JmYuPalWdPG1S_nZGf2"}
>>>>>>> WA-170_MCP

# Get zone ID for divinci.app
ZONE_ID=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones?name=divinci.app" \
  -H "Authorization: Bearer $CF_API_TOKEN" \
  -H "Content-Type: application/json" | jq -r '.result[0].id')

if [ -z "$ZONE_ID" ] || [ "$ZONE_ID" == "null" ]; then
  echo "❌ Failed to get zone ID for divinci.app"
  exit 1
fi

echo "✅ Found zone ID: $ZONE_ID"

# Change SSL mode
RESPONSE=$(curl -s -X PATCH "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/settings/ssl" \
  -H "Authorization: Bearer $CF_API_TOKEN" \
  -H "Content-Type: application/json" \
  --data "{\"value\":\"$SSL_MODE\"}")

# Check if the request was successful
SUCCESS=$(echo "$RESPONSE" | jq -r '.success')
if [ "$SUCCESS" != "true" ]; then
  echo "❌ Failed to change SSL mode"
  echo "$RESPONSE" | jq
  exit 1
fi

echo "✅ Changed SSL mode to $SSL_MODE"
echo "📋 If you're testing, remember to change it back to 'strict' when you're done"
