/* eslint-disable @typescript-eslint/no-var-requires */

const path = require("node:path");
const fs = require("node:fs");
const { setupEnv } = require("@divinci-ai/server-utils");

setupEnv({ envPath: path.resolve(__dirname, "./env"), client: true });

const webpack = require("webpack");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const NodePolyfillPlugin = require("node-polyfill-webpack-plugin");

// Codespace-specific configuration
const isCodespace = process.env.CODESPACES === "true";
const codespaceUrl = process.env.CODESPACE_NAME
  ? `https://${process.env.CODESPACE_NAME}-8080.app.github.dev`
  : "https://sturdy-space-broccoli-g4xpjgv6376q-8080.app.github.dev";

module.exports = {
  entry: "./src/index.tsx",
  output: {
    filename: "hidden.build.js",
    path: path.resolve(__dirname, "public"),
    publicPath: "/",
  },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: "ts-loader",
        exclude: /node_modules/,
      },
      {
        test: /\.css$/,
        oneOf: [
          {
            // Enable CSS Modules for files ending in `.module.css`
            test: /\.module\.css$/,
            use: [
              "style-loader",
              {
                loader: "css-loader",
                options: {
                  modules: {
                    localIdentName: "[name]__[local]___[hash:base64:5]",
                  },
                },
              },
            ],
          },
          {
            // Regular global CSS files
            use: ["style-loader", "css-loader"],
          },
        ],
      },
      {
        test: /\.svg$/i,
        issuer: /\.[jt]sx?$/,
        use: ["@svgr/webpack"],
      },
      {
        test: /\.txt$/i,
        use: "raw-loader",
      },
      {
        test: /\.md$/i,
        use: "raw-loader",
      },
    ],
  },
  resolve: {
    extensions: [".tsx", ".ts", ".js", ".cjs", ".mjs"],
    alias: {
      "@divinci-ai/utils": path.resolve(__dirname, "../../resources/utils"),
      "@divinci-ai/models": path.resolve(__dirname, "../../resources/models"),
      "@styles": path.resolve(__dirname, "./public"),
      process: "process/browser.js",
    },
    fallback: {
      stream: require.resolve("stream-browserify"),
      buffer: require.resolve("buffer/"),
    },
  },
  plugins: [
    new webpack.ProvidePlugin({
      process: "process/browser.js",
      Buffer: ["buffer", "Buffer"],
    }),
    new webpack.DefinePlugin({
      "process.env": JSON.stringify({
        ...process.env,
        // Codespace-specific environment variables
        IS_CODESPACE: isCodespace,
        CODESPACE_URL: codespaceUrl,
        // Override API endpoints for Codespace - Use HTTPS for production
        API_IS_SECURE: "1",
        API_HOST: "sturdy-space-broccoli-g4xpjgv6376q-9080.app.github.dev",
        API_LIVE_IS_SECURE: "1",
        API_LIVE_HOST: "sturdy-space-broccoli-g4xpjgv6376q-8081.app.github.dev",
      }),
    }),
    new HtmlWebpackPlugin({
      template: "./public/template.html",
    }),
    new NodePolyfillPlugin({
      includeAliases: ["events"],
    }),
  ],
  mode: process.env.NODE_ENV || "development",
  devtool: "inline-source-map",
  optimization: {
    usedExports: true,
  },
  devServer: {
    hot: true,
    port: process.env.HTTP_PORT || 8080,
    host: "0.0.0.0",
    server: {
      type: "https",
      options: {
        key: fs.readFileSync(
          "/workspaces/server/private-keys/local-fast/server.key"
        ),
        cert: fs.readFileSync(
          "/workspaces/server/private-keys/local-fast/server.crt"
        ),
      },
    },
    historyApiFallback: true,
    static: {
      directory: path.join(__dirname, "public"),
    },
    allowedHosts: "all",
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, PATCH, OPTIONS",
      "Access-Control-Allow-Headers":
        "X-Requested-With, content-type, Authorization",
    },
    client: {
      logging: "verbose",
      // Codespace-specific WebSocket configuration for HTTPS
      webSocketURL: isCodespace
        ? {
            hostname: "sturdy-space-broccoli-g4xpjgv6376q-8080.app.github.dev",
            pathname: "/ws",
            port: 443,
            protocol: "wss", // Use wss:// protocol for HTTPS compatibility
          }
        : "auto",
    },
    compress: false,
    webSocketServer: "ws",
  },
  // 📓 For much webpack more logs, turn this puppy on!
  // stats: {
  //   errorDetails: true,
  // },
};
