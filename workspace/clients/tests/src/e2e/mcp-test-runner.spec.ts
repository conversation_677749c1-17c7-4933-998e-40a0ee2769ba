import { test, expect, Page } from '@playwright/test';
import { verifyLoggedIn } from '../utils/auth';
import { getWhitelabelId } from '../utils/whitelabel';

/**
 * MCP Test Runner - Comprehensive End-to-End Testing
 * 
 * This test suite runs a comprehensive set of tests to verify that
 * the MCP (Model Context Protocol) implementation is working correctly
 * across all integration points.
 */

/**
 * Helper function to check if MCP server is running
 */
async function checkMCPServerStatus(page: Page): Promise<{ running: boolean; version?: string; error?: string }> {
  try {
    const response = await page.evaluate(async () => {
      try {
        const response = await fetch('http://localhost:8793/health', {
          method: 'GET',
          headers: { 'Accept': 'application/json' }
        });
        
        if (response.ok) {
          const data = await response.json();
          return { ok: true, data };
        }
        return { ok: false, status: response.status };
      } catch (error) {
        return { ok: false, error: error.message };
      }
    });

    if (response.ok) {
      return {
        running: true,
        version: response.data.version
      };
    } else {
      return {
        running: false,
        error: `Server not responding (status: ${response.status})`
      };
    }
  } catch (error) {
    return {
      running: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Helper function to run a complete MCP workflow test
 */
async function runMCPWorkflowTest(page: Page, whitelabelId: string): Promise<boolean> {
  try {
    console.log('🔄 Running complete MCP workflow test...');
    
    // Step 1: Get user profile
    const profileResponse = await page.evaluate(async () => {
      const response = await fetch('http://localhost:8793/sse', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: 'workflow-profile',
          method: 'tools/call',
          params: { name: 'get_user_profile', arguments: {} }
        })
      });
      return { ok: response.ok, data: await response.json() };
    });
    
    if (!profileResponse.ok) {
      console.error('❌ Profile retrieval failed');
      return false;
    }
    console.log('✅ Step 1: User profile retrieved');
    
    // Step 2: Create a chat
    const chatResponse = await page.evaluate(async (wlId) => {
      const response = await fetch('http://localhost:8793/sse', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: 'workflow-chat',
          method: 'tools/call',
          params: {
            name: 'create_chat',
            arguments: {
              title: `E2E Workflow Test - ${wlId}`,
              model: 'gpt-4'
            }
          }
        })
      });
      return { ok: response.ok, data: await response.json() };
    }, whitelabelId);
    
    if (!chatResponse.ok) {
      console.error('❌ Chat creation failed');
      return false;
    }
    console.log('✅ Step 2: Chat created');
    
    // Extract chat ID from response
    const chatContent = chatResponse.data.result.content[0].text;
    const chatIdMatch = chatContent.match(/chat_(\w+)/);
    const chatId = chatIdMatch ? `chat_${chatIdMatch[1]}` : 'test_chat_workflow';
    
    // Step 3: Send a message
    const messageResponse = await page.evaluate(async (cId) => {
      const response = await fetch('http://localhost:8793/sse', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: 'workflow-message',
          method: 'tools/call',
          params: {
            name: 'send_message',
            arguments: {
              chatId: cId,
              message: 'This is a test message from the E2E workflow test.'
            }
          }
        })
      });
      return { ok: response.ok, data: await response.json() };
    }, chatId);
    
    if (!messageResponse.ok) {
      console.error('❌ Message sending failed');
      return false;
    }
    console.log('✅ Step 3: Message sent');
    
    // Step 4: List chats
    const listResponse = await page.evaluate(async () => {
      const response = await fetch('http://localhost:8793/sse', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: 'workflow-list',
          method: 'tools/call',
          params: { name: 'list_chats', arguments: { limit: 10 } }
        })
      });
      return { ok: response.ok, data: await response.json() };
    });
    
    if (!listResponse.ok) {
      console.error('❌ Chat listing failed');
      return false;
    }
    console.log('✅ Step 4: Chats listed');
    
    console.log('✅ Complete MCP workflow test passed');
    return true;
    
  } catch (error) {
    console.error('❌ MCP workflow test failed:', error);
    return false;
  }
}

test.describe('MCP Test Runner - Comprehensive Testing', () => {
  test.beforeEach(async ({ page }) => {
    test.setTimeout(180000); // 3 minutes for comprehensive tests
    await verifyLoggedIn(page);
  });

  test('MCP Server Prerequisites Check', async ({ page }) => {
    console.log('🔍 Checking MCP Server Prerequisites...');
    
    // Check if MCP server is running
    const serverStatus = await checkMCPServerStatus(page);
    
    if (!serverStatus.running) {
      console.error('❌ MCP Server is not running!');
      console.error(`Error: ${serverStatus.error}`);
      console.log('');
      console.log('🚀 To start the MCP server:');
      console.log('   cd workspace/workers/mcp-server');
      console.log('   npm run dev');
      console.log('');
      test.skip();
      return;
    }
    
    console.log(`✅ MCP Server is running (version: ${serverStatus.version})`);
    
    // Verify all required endpoints are accessible
    const endpoints = ['/health', '/sse'];
    
    for (const endpoint of endpoints) {
      const endpointTest = await page.evaluate(async (ep) => {
        try {
          const response = await fetch(`http://localhost:8793${ep}`);
          return { ok: response.ok, status: response.status };
        } catch (error) {
          return { ok: false, error: error.message };
        }
      }, endpoint);
      
      expect(endpointTest.ok).toBe(true);
      console.log(`✅ Endpoint ${endpoint} is accessible`);
    }
    
    console.log('✅ All MCP Server prerequisites are met');
  });

  test('Complete MCP Integration Workflow', async ({ page }) => {
    console.log('🔄 Running Complete MCP Integration Workflow...');
    
    // Get whitelabel context
    const whitelabelId = await getWhitelabelId(page);
    console.log(`Using whitelabel: ${whitelabelId}`);
    
    // Run the complete workflow
    const workflowSuccess = await runMCPWorkflowTest(page, whitelabelId);
    expect(workflowSuccess).toBe(true);
    
    console.log('✅ Complete MCP integration workflow passed');
  });

  test('MCP Server Stress Test', async ({ page }) => {
    console.log('💪 Running MCP Server Stress Test...');
    
    const iterations = 20;
    const concurrentRequests = 5;
    let successCount = 0;
    let errorCount = 0;
    
    console.log(`Executing ${iterations} iterations with ${concurrentRequests} concurrent requests each`);
    
    for (let i = 0; i < iterations; i++) {
      const promises = [];
      
      // Create concurrent requests
      for (let j = 0; j < concurrentRequests; j++) {
        const promise = page.evaluate(async (requestId) => {
          try {
            const response = await fetch('http://localhost:8793/sse', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                jsonrpc: '2.0',
                id: `stress-${requestId}`,
                method: 'tools/call',
                params: {
                  name: 'get_user_profile',
                  arguments: {}
                }
              })
            });
            
            if (response.ok) {
              const data = await response.json();
              return { success: true, id: requestId, data };
            } else {
              return { success: false, id: requestId, status: response.status };
            }
          } catch (error) {
            return { success: false, id: requestId, error: error.message };
          }
        }, `${i}-${j}`);
        
        promises.push(promise);
      }
      
      // Wait for all concurrent requests to complete
      const results = await Promise.all(promises);
      
      // Count successes and errors
      results.forEach(result => {
        if (result.success) {
          successCount++;
        } else {
          errorCount++;
          console.log(`❌ Request ${result.id} failed:`, result.error || result.status);
        }
      });
      
      // Brief pause between iterations
      await page.waitForTimeout(100);
      
      if ((i + 1) % 5 === 0) {
        console.log(`Progress: ${i + 1}/${iterations} iterations completed`);
      }
    }
    
    const totalRequests = iterations * concurrentRequests;
    const successRate = (successCount / totalRequests) * 100;
    
    console.log(`📊 Stress Test Results:`);
    console.log(`   Total Requests: ${totalRequests}`);
    console.log(`   Successful: ${successCount}`);
    console.log(`   Failed: ${errorCount}`);
    console.log(`   Success Rate: ${successRate.toFixed(2)}%`);
    
    // Expect at least 95% success rate
    expect(successRate).toBeGreaterThanOrEqual(95);
    console.log('✅ MCP Server stress test passed');
  });

  test('MCP Error Recovery Test', async ({ page }) => {
    console.log('🔧 Testing MCP Error Recovery...');
    
    // Test 1: Invalid JSON recovery
    const invalidJsonTest = await page.evaluate(async () => {
      try {
        const response = await fetch('http://localhost:8793/sse', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: 'invalid json here'
        });
        return { status: response.status, ok: response.ok };
      } catch (error) {
        return { error: error.message };
      }
    });
    
    expect(invalidJsonTest.status).toBe(400);
    console.log('✅ Invalid JSON handled correctly');
    
    // Test 2: Server still responds after error
    const recoveryTest = await page.evaluate(async () => {
      try {
        const response = await fetch('http://localhost:8793/sse', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            jsonrpc: '2.0',
            id: 'recovery-test',
            method: 'tools/call',
            params: { name: 'get_user_profile', arguments: {} }
          })
        });
        return { ok: response.ok, data: await response.json() };
      } catch (error) {
        return { ok: false, error: error.message };
      }
    });
    
    expect(recoveryTest.ok).toBe(true);
    console.log('✅ Server recovered and responds normally after error');
    
    console.log('✅ MCP error recovery test passed');
  });
});
