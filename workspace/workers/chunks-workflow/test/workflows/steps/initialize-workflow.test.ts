import { describe, it, expect, vi, beforeEach } from "vitest";
import { initializeWorkflow } from "../../../src/workflows/steps/initialize-workflow";
import { WorkflowStep } from "cloudflare:workers";
import { StepContext, WorkflowFile } from "../../../src/workflows/steps/types";

describe("initialize-workflow", ()=>{
  // Mock console.log to avoid cluttering test output
  beforeEach(()=>{
    vi.spyOn(console, "log").mockImplementation(()=>{});
  });

  describe("initializeWorkflow", ()=>{
    it("should initialize workflow with unstructured processor", async ()=>{
      // Mock step
      const step = {
        do: vi.fn().mockImplementation((name, fn)=>fn())
      } as unknown as WorkflowStep;

      // Mock context
      const context = {
        env: {
          ENVIRONMENT: "production"
        }
      } as unknown as StepContext;

      // Mock file
      const file: WorkflowFile = {
        fileId: "test-file-id",
        processor: "unstructured",
        processorConfig: {
          skipDeJunk: false,
          maxCharacters: 2048
        },
        objectKey: "test-object-key",
        fileName: "test-file.pdf",
        title: "Test File",
        description: "Test description",
        target: "test-target",
        bucket: "test-bucket"
      };

      const result = await initializeWorkflow(step, context, file);

      expect(step.do).toHaveBeenCalledWith("initializeWorkflow", expect.any(Function));
      expect(result).toEqual({
        normalizedConfig: {
          skipDeJunk: false,
          chunkingStrategy: "by_title",
          maxCharacters: 2048,
          splitPdfPage: true,
          splitPdfConcurrencyLevel: 5,
          splitPdfAllowFailed: true,
          minTokens: undefined,
          maxTokens: undefined
        },
        metadata: {
          workflowId: `wf-test-target-test-file-id`,
          fileId: "test-file-id",
          target: "test-target",
          processor: "unstructured",
          processorConfig: expect.any(Object),
          steps: {
            initializeWorkflow: {
              startTime: expect.any(String),
              endTime: expect.any(String)
            },
            deJunk: {}
          },
          status: "processing"
        },
        fileInfo: {
          fileId: "test-file-id",
<<<<<<< HEAD
=======
          originalFileId: "test-file-id",
>>>>>>> WA-170_MCP
          processor: "unstructured",
          objectKey: "test-object-key",
          fileName: "test-file.pdf",
          title: "Test File",
          description: "Test description",
          target: "test-target",
          bucket: "test-bucket",
          hasFormData: false
        }
      });
    });

    it("should initialize workflow with openparse processor", async ()=>{
      // Mock step
      const step = {
        do: vi.fn().mockImplementation((name, fn)=>fn())
      } as unknown as WorkflowStep;

      // Mock context
      const context = {
        env: {
          ENVIRONMENT: "production"
        }
      } as unknown as StepContext;

      // Mock file
      const file: WorkflowFile = {
        fileId: "test-file-id",
        processor: "openparse",
        processorConfig: {
          semantic_chunking: true,
          embeddings_provider: "openai",
          minTokens: 512,
          maxTokens: 2048
        },
        objectKey: "test-object-key",
        fileName: "test-file.pdf",
        title: "Test File",
        description: "Test description",
        target: "test-target",
        bucket: "test-bucket"
      };

      const result = await initializeWorkflow(step, context, file);

      expect(step.do).toHaveBeenCalledWith("initializeWorkflow", expect.any(Function));
      expect(result).toEqual({
        normalizedConfig: {
          semantic: true,
          embeddingsProvider: "openai",
          useTokens: true,
          minTokens: 512,
          maxTokens: 2048,
          chunkOverlap: 200,
          relevanceThreshold: 0.3,
          skipDeJunk: true
        },
        metadata: {
          workflowId: `wf-test-target-test-file-id`,
          fileId: "test-file-id",
          target: "test-target",
          processor: "openparse",
          processorConfig: expect.any(Object),
          steps: {
            initializeWorkflow: {
              startTime: expect.any(String),
              endTime: expect.any(String)
            },
            deJunk: {}
          },
          status: "processing"
        },
        fileInfo: {
          fileId: "test-file-id",
<<<<<<< HEAD
=======
          originalFileId: "test-file-id",
>>>>>>> WA-170_MCP
          processor: "openparse",
          objectKey: "test-object-key",
          fileName: "test-file.pdf",
          title: "Test File",
          description: "Test description",
          target: "test-target",
          bucket: "test-bucket",
          hasFormData: false
        }
      });
    });

    it("should normalize objectKey in local development mode", async ()=>{
      // Mock step
      const step = {
        do: vi.fn().mockImplementation((name, fn)=>fn())
      } as unknown as WorkflowStep;

      // Mock context
      const context = {
        env: {
          ENVIRONMENT: "local"
        }
      } as unknown as StepContext;

<<<<<<< HEAD
      // Mock file with timestamp prefix in objectKey
=======
      // Mock file with timestamp prefix in objectKey (using 10+ digit timestamp)
>>>>>>> WA-170_MCP
      const file: WorkflowFile = {
        fileId: "test-file-id",
        processor: "unstructured",
        processorConfig: {},
        objectKey: "folder/1234567890-test-file.pdf",
        fileName: "test-file.pdf",
        title: "Test File",
        description: "Test description",
        target: "test-target",
        bucket: "test-bucket"
      };

      const result = await initializeWorkflow(step, context, file);

      expect(result.fileInfo.objectKey).toBe("folder/test-file.pdf");
    });
<<<<<<< HEAD
=======

    it("should NOT normalize ISO timestamp objectKey in local development mode", async ()=>{
      // Mock step
      const step = {
        do: vi.fn().mockImplementation((name, fn)=>fn())
      } as unknown as WorkflowStep;

      // Mock context
      const context = {
        env: {
          ENVIRONMENT: "local"
        }
      } as unknown as StepContext;

      // Mock file with ISO timestamp prefix in objectKey (should NOT be removed)
      const file: WorkflowFile = {
        fileId: "test-file-id",
        processor: "unstructured",
        processorConfig: {},
        objectKey: "folder/2025-06-13T07-59-46-857Z_test-file.pdf",
        fileName: "test-file.pdf",
        title: "Test File",
        description: "Test description",
        target: "test-target",
        bucket: "test-bucket"
      };

      const result = await initializeWorkflow(step, context, file);

      // ISO timestamp should NOT be removed (only 4 digits before hyphen)
      expect(result.fileInfo.objectKey).toBe("folder/2025-06-13T07-59-46-857Z_test-file.pdf");
    });
>>>>>>> WA-170_MCP
  });
});
