import { ChunkD<PERSON>, InitialSetup, StepContext, VectorizeConfig } from "./types";
import { WorkflowStep } from "cloudflare:workers";

/**
 * Stores chunks in R2 as a JSON file
 * This allows for easier retrieval of chunks for debugging or other purposes
 */
export async function storeChunksInR2(
  step: WorkflowStep,
  context: StepContext,
  filteredChunks: ChunkData[],
  vectorizeConfig: VectorizeConfig,
  initialSetup: InitialSetup
): Promise<void> {
<<<<<<< HEAD
  console.log(`🔄 Storing ${filteredChunks.length} chunks in R2`);

  // Create the object key for the chunks JSON file
  const chunksObjectKey = `${vectorizeConfig.ragId}/${initialSetup.fileInfo.fileId}/chunks.json`;
  
  try {
    // Store the chunks in R2
    await context.env.R2.put(chunksObjectKey, JSON.stringify(filteredChunks));
    console.log(`✅ Successfully stored chunks in R2: ${chunksObjectKey}`);
=======
  console.log(`🚀🚀🚀 [STORE-CHUNKS-R2] Function called with ${filteredChunks.length} chunks`);
  console.log(`🚀🚀🚀 [STORE-CHUNKS-R2] fileId: ${initialSetup.fileInfo.fileId}`);
  console.log(`🚀🚀🚀 [STORE-CHUNKS-R2] whitelabelId: ${vectorizeConfig.whitelabelId}`);
  console.log(`🔄 Storing ${filteredChunks.length} chunks in R2`);

  // Create the object key for the chunks JSON file
  // Use the fileId to ensure consistency with the API expectations
  // This matches the pattern used in store-embeddings-in-r2.ts and the API
  const chunksObjectKey = `${vectorizeConfig.whitelabelId}/${initialSetup.fileInfo.fileId}-chunks.json`;

  // 🔍 COMPREHENSIVE CHUNKS STORAGE URL LOGGING
  console.log(`🔍🔍🔍 [CHUNKS STORAGE DEBUG] ==========================================`);
  console.log(`🔍 WORKFLOW STORING CHUNKS WITH:`);
  console.log(`🔍 Whitelabel ID: ${vectorizeConfig.whitelabelId}`);
  console.log(`🔍 File ID: ${initialSetup.fileInfo.fileId}`);
  console.log(`🔍 Constructed object key: ${chunksObjectKey}`);
  console.log(`🔍 Full storage URL: ${context.storageClient.buckets?.[0] || 'unknown-bucket'}/${chunksObjectKey}`);
  console.log(`🔍 This URL will be used by addRagFile API to retrieve chunks`);
  console.log(`🔍🔍🔍 ================================================================`);

  try {
    // Debug: Log the actual chunks content
    console.log(`🔍 [DEBUG] Chunks array length: ${filteredChunks.length}`);
    console.log(`🔍 [DEBUG] First chunk sample:`, filteredChunks[0] ? JSON.stringify(filteredChunks[0]).substring(0, 200) + '...' : 'No chunks');

    // Convert chunks to JSON string
    const chunksJson = JSON.stringify(filteredChunks, null, 2);
    console.log(`📤 Uploading chunks to R2: ${chunksObjectKey}`);
    console.log(`📊 Chunks JSON length: ${chunksJson.length} characters`);
    console.log(`📊 Chunks file size: ${(chunksJson.length / 1024 / 1024).toFixed(2)} MB`);

    // Store the chunks in R2 using the same storage client as embeddings
    // This ensures consistency with the working embeddings storage approach
    await context.storageClient.put(chunksObjectKey, chunksJson, {
      httpMetadata: {
        contentType: "application/json",
      },
      customMetadata: {
        fileId: initialSetup.fileInfo.fileId,
        fileName: initialSetup.fileInfo.fileName,
        whitelabelId: vectorizeConfig.whitelabelId,
        ragId: vectorizeConfig.ragId,
        type: "chunks",
        createdAt: new Date().toISOString(),
      },
    });

    console.log(`✅ Successfully stored chunks in R2: ${chunksObjectKey}`);
    console.log(`📊 Chunks file size: ${(chunksJson.length / 1024 / 1024).toFixed(2)} MB`);

    // Verify the file was written successfully with retry mechanism
    // This ensures the file is available before the workflow completes
    const maxAttempts = 3;
    let verificationSuccess = false;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        console.log(`🔍 Verifying chunks file exists (attempt ${attempt}/${maxAttempts}): ${chunksObjectKey}`);
        const verifyObject = await context.storageClient.get(chunksObjectKey);

        if (verifyObject) {
          console.log(`✅ Chunks file verification successful: ${chunksObjectKey}`);
          verificationSuccess = true;
          break;
        } else {
          throw new Error("File not found during verification");
        }
      } catch (verifyError) {
        console.warn(`⚠️ Chunks file verification attempt ${attempt} failed:`, verifyError);

        if (attempt < maxAttempts) {
          // Wait with exponential backoff before retrying
          const delay = 1000 * Math.pow(2, attempt - 1);
          console.log(`🔄 Waiting ${delay}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    if (!verificationSuccess) {
      throw new Error(`Failed to verify chunks file after ${maxAttempts} attempts: ${chunksObjectKey}`);
    }

>>>>>>> WA-170_MCP
  } catch (error) {
    console.error(`❌ Error storing chunks in R2:`, error);
    throw error;
  }
}
