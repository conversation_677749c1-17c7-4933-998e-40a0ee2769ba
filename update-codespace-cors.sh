#!/bin/bash
# Standalone script to update CORS configuration for GitHub Codespaces
# This can be run independently to update CORS settings without restarting services

set -e

CORS_ENV_PATH="/workspaces/server/private-keys/local-fast/cors.env"

update_codespace_cors() {
  if [ -n "$CODESPACE_NAME" ]; then
    echo "🌐 Updating CORS configuration for Codespace: $CODESPACE_NAME"
    
    if [ ! -f "$CORS_ENV_PATH" ]; then
      echo "❌ CORS config file not found at: $CORS_ENV_PATH"
      echo "Please ensure private-keys submodule is properly initialized:"
      echo "   git submodule sync && git submodule update --init --recursive --remote"
      exit 1
    fi
    
    # Get current Codespace hostname
    CODESPACE_HOST="$CODESPACE_NAME.app.github.dev"
    
    # Define the ports that need CORS access
    CORS_PORTS="8080,8081,8082,8083,9080"
    
    # Build the new CORS origins for this Codespace
    CODESPACE_ORIGINS=""
    for port in ${CORS_PORTS//,/ }; do
      if [ -n "$CODESPACE_ORIGINS" ]; then
        CODESPACE_ORIGINS="$CODESPACE_ORIGINS,"
      fi
      CODESPACE_ORIGINS="$CODESPACE_ORIGINS$CODESPACE_HOST-$port.app.github.dev,https://$CODESPACE_HOST-$port.app.github.dev"
    done
    
    # Read current CORS_FULL_ORIGINS
    CURRENT_ORIGINS=$(grep "^CORS_FULL_ORIGINS=" "$CORS_ENV_PATH" | cut -d'=' -f2- | tr -d '"')
    
    # Remove any existing entries for other Codespaces (to avoid duplicates)
    CLEANED_ORIGINS=$(echo "$CURRENT_ORIGINS" | sed 's/,[^,]*\.app\.github\.dev[^,]*//g' | sed 's/^[^,]*\.app\.github\.dev[^,]*,//g' | sed 's/^[^,]*\.app\.github\.dev[^,]*$//g')
    
    # Add the new Codespace origins
    if [ -n "$CLEANED_ORIGINS" ]; then
      NEW_ORIGINS="$CLEANED_ORIGINS,$CODESPACE_ORIGINS"
    else
      NEW_ORIGINS="$CODESPACE_ORIGINS"
    fi
    
    # Update the cors.env file
    sed -i "s|^CORS_FULL_ORIGINS=.*|CORS_FULL_ORIGINS=\"$NEW_ORIGINS\"|" "$CORS_ENV_PATH"
    
    echo "✅ Updated CORS configuration successfully!"
    echo "📋 Current Codespace origins added:"
    echo "   $CODESPACE_ORIGINS"
    echo ""
    echo "💡 To apply changes, restart your services:"
    echo "   ./restart-services.sh"
    
  else
    echo "❌ CODESPACE_NAME environment variable not found"
    echo "This script is designed to run in GitHub Codespaces"
    exit 1
  fi
}

echo "🔧 GitHub Codespaces CORS Configuration Updater"
echo "================================================"
update_codespace_cors
