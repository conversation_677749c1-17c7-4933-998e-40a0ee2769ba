/**
 * Environment Configuration
<<<<<<< HEAD
 *
=======
 * 
>>>>>>> WA-170_MCP
 * This module provides utilities for loading environment variables.
 */

import * as fs from 'fs';
import * as path from 'path';
import * as dotenv from 'dotenv';

/**
 * Load environment variables from .env files
 */
export function loadEnv(): void {
  // Determine the environment
  const env = process.env.NODE_ENV || 'development';
<<<<<<< HEAD

=======
  
>>>>>>> WA-170_MCP
  // Load .env files
  const envFiles = [
    '.env',
    `.env.${env}`,
    '.env.local',
    `.env.${env}.local`
  ];
<<<<<<< HEAD

=======
  
>>>>>>> WA-170_MCP
  // Load each .env file if it exists
  for (const file of envFiles) {
    const filePath = path.resolve(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      dotenv.config({ path: filePath });
    }
  }
<<<<<<< HEAD

  // Load test API environment variables for real Auth0 testing
  loadTestApiEnv();
}

/**
 * Load test API environment variables from private-keys/test-api-local
 */
function loadTestApiEnv(): void {
  try {
    // Path to the test API environment files
    const testApiEnvDir = path.join(__dirname, '..', '..', '..', '..', '..', 'private-keys', 'test-api-local');

    // List of environment files to load
    const envFiles = [
      'api-dev.env',
      'test.env'
    ];

    for (const envFile of envFiles) {
      const envFilePath = path.join(testApiEnvDir, envFile);

      if (fs.existsSync(envFilePath)) {
        console.log(`[Env] Loading test API environment from: ${envFile}`);
        dotenv.config({ path: envFilePath });
      } else {
        console.log(`[Env] Test API environment file not found: ${envFilePath}`);
      }
    }

    // Log loaded Auth0 configuration for debugging
    console.log(`[Env] AUTH0_BASE_URL: ${process.env.AUTH0_BASE_URL}`);
    console.log(`[Env] AUTH0_CLIENT_ID: ${process.env.AUTH0_CLIENT_ID}`);
    console.log(`[Env] AUTH0_AUDIENCE: ${process.env.AUTH0_AUDIENCE}`);
    console.log(`[Env] AUTH0_S2S_CLIENT_ID: ${process.env.AUTH0_S2S_CLIENT_ID}`);
    console.log(`[Env] AUTH0_TEST_USER_EMAIL: ${process.env.AUTH0_TEST_USER_EMAIL}`);
  } catch (error) {
    console.error(`[Env] Failed to load test API environment:`, error);
  }
=======
>>>>>>> WA-170_MCP
}

/**
 * Get an environment variable
 * @param name The name of the environment variable
 * @param defaultValue The default value to use if the environment variable is not set
 * @returns The value of the environment variable or the default value
 */
export function getEnv(name: string, defaultValue?: string): string {
  const value = process.env[name];
  if (value === undefined) {
    if (defaultValue === undefined) {
      throw new Error(`Environment variable ${name} is not set`);
    }
    return defaultValue;
  }
  return value;
}

/**
 * Get an environment variable as a number
 * @param name The name of the environment variable
 * @param defaultValue The default value to use if the environment variable is not set
 * @returns The value of the environment variable as a number or the default value
 */
export function getEnvNumber(name: string, defaultValue?: number): number {
  const value = process.env[name];
  if (value === undefined) {
    if (defaultValue === undefined) {
      throw new Error(`Environment variable ${name} is not set`);
    }
    return defaultValue;
  }
  const num = Number(value);
  if (isNaN(num)) {
    throw new Error(`Environment variable ${name} is not a number: ${value}`);
  }
  return num;
}

/**
 * Get an environment variable as a boolean
 * @param name The name of the environment variable
 * @param defaultValue The default value to use if the environment variable is not set
 * @returns The value of the environment variable as a boolean or the default value
 */
export function getEnvBoolean(name: string, defaultValue?: boolean): boolean {
  const value = process.env[name];
  if (value === undefined) {
    if (defaultValue === undefined) {
      throw new Error(`Environment variable ${name} is not set`);
    }
    return defaultValue;
  }
  return value.toLowerCase() === 'true';
}

/**
 * Get an environment variable as a JSON object
 * @param name The name of the environment variable
 * @param defaultValue The default value to use if the environment variable is not set
 * @returns The value of the environment variable as a JSON object or the default value
 */
export function getEnvJson<T>(name: string, defaultValue?: T): T {
  const value = process.env[name];
  if (value === undefined) {
    if (defaultValue === undefined) {
      throw new Error(`Environment variable ${name} is not set`);
    }
    return defaultValue;
  }
  try {
    return JSON.parse(value) as T;
  } catch (error) {
    throw new Error(`Environment variable ${name} is not valid JSON: ${value}`);
  }
}

/**
 * Require an environment variable
 * @param name The name of the environment variable
 * @returns The value of the environment variable
 * @throws An error if the environment variable is not set
 */
export function requireEnv(name: string): string {
  const value = process.env[name];
  if (value === undefined) {
    throw new Error(`Required environment variable ${name} is not set`);
  }
  return value;
}
