const http = require("http");
const https = require("https");
const fs = require("fs");

// Configuration for different API ports and their mock responses
const API_CONFIGS = {
  9080: {
    name: "Public API",
    response: {
      message: "Public API is running",
      version: "1.0.0",
      endpoints: ["/health", "/api/v1/status"],
      timestamp: new Date().toISOString(),
    },
  },
  8082: {
    name: "Public API Live",
    response: {
      message: "Public API Live is running",
      version: "1.0.0",
      endpoints: ["/health", "/live/status"],
      timestamp: new Date().toISOString(),
    },
  },
  8083: {
    name: "Public API Webhook",
    response: {
      message: "Public API Webhook is running",
      version: "1.0.0",
      endpoints: ["/health", "/webhook/status"],
      timestamp: new Date().toISOString(),
    },
  },
};

// CORS headers for all responses
const CORS_HEADERS = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
  "Access-Control-Allow-Headers":
    "Content-Type, Authorization, X-Requested-With",
  "Access-Control-Max-Age": "3600",
};

function createMockServer(port) {
  const config = API_CONFIGS[port];

  const requestHandler = (req, res) => {
    console.log(
      `[${config.name}:${port}] ${req.method} ${
        req.url
      } - ${new Date().toISOString()}`
    );

    // Handle CORS preflight
    if (req.method === "OPTIONS") {
      res.writeHead(200, CORS_HEADERS);
      res.end();
      return;
    }

    // Set CORS headers for all responses
    Object.keys(CORS_HEADERS).forEach((header) => {
      res.setHeader(header, CORS_HEADERS[header]);
    });

    // Health check endpoint
    if (req.url === "/health" || req.url === "/" || req.url === "/status") {
      res.writeHead(200, { "Content-Type": "application/json" });
      res.end(
        JSON.stringify(
          {
            ...config.response,
            endpoint: req.url,
            healthy: true,
          },
          null,
          2
        )
      );
      return;
    }

    // Default response for other endpoints
    res.writeHead(200, { "Content-Type": "application/json" });
    res.end(
      JSON.stringify(
        {
          ...config.response,
          endpoint: req.url,
          note: "This is a mock API response for development testing",
        },
        null,
        2
      )
    );
  };

  // Try to use HTTPS if certificates exist, otherwise use HTTP
  const certPath =
    "/workspaces/server/private-keys/local-fast/certs/server.crt";
  const keyPath = "/workspaces/server/private-keys/local-fast/certs/server.key";

  let server;

  if (fs.existsSync(certPath) && fs.existsSync(keyPath)) {
    console.log(`🔒 Starting ${config.name} HTTPS server on port ${port}...`);
    const options = {
      cert: fs.readFileSync(certPath),
      key: fs.readFileSync(keyPath),
    };
    server = https.createServer(options, requestHandler);
  } else {
    console.log(`🌐 Starting ${config.name} HTTP server on port ${port}...`);
    server = http.createServer(requestHandler);
  }

  server.listen(port, "0.0.0.0", () => {
    console.log(`✅ ${config.name} mock server running on port ${port}`);
    console.log(
      `🌐 Codespace URL: https://sturdy-space-broccoli-g4xpjgv6376q-${port}.app.github.dev`
    );
  });

  return server;
}

// Start all mock API servers
console.log("🚀 Starting Divinci Mock API servers...\n");

const servers = Object.keys(API_CONFIGS).map((port) => {
  return createMockServer(parseInt(port));
});

console.log("\n📊 All mock API servers started!");
console.log("📋 Available endpoints:");
Object.keys(API_CONFIGS).forEach((port) => {
  const config = API_CONFIGS[port];
  console.log(
    `  🔗 ${config.name}: https://sturdy-space-broccoli-g4xpjgv6376q-${port}.app.github.dev`
  );
});

// Graceful shutdown
process.on("SIGINT", () => {
  console.log("\n🛑 Shutting down mock API servers...");
  servers.forEach((server) => server.close());
  process.exit(0);
});
