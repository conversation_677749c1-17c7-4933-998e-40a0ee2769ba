<<<<<<< HEAD
import { IncomingMessage, ServerResponse, Server as HttpServer } from "http";
import { Server as HttpsServer } from "https";
import { readFileSync } from "fs";
=======

import { IncomingMessage, Server, ServerResponse } from "http";
>>>>>>> WA-170_MCP
import { WebSocketServer } from "ws";

import { setupDBs } from "./setup/database";
import { setupHttpApp } from "./setup/http";
import { setupWsApp, wsHandleError } from "./setup/websocket";

import { httpUpgradeToWSRequest } from "@divinci-ai/server-utils";

<<<<<<< HEAD
function createServer(): HttpServer | HttpsServer {
  const httpsEnabled = process.env.HTTPS === "true";
=======
Promise.resolve().then(async ()=>{
  await setupDBs();
  const httpApp = await setupHttpApp();
  const wsApp = await setupWsApp();
>>>>>>> WA-170_MCP

  if (httpsEnabled) {
    const sslCertPath = process.env.SSL_CERT_PATH;
    const sslKeyPath = process.env.SSL_KEY_PATH;

<<<<<<< HEAD
    if (!sslCertPath || !sslKeyPath) {
      console.warn(
        "⚠️  HTTPS enabled but SSL_CERT_PATH or SSL_KEY_PATH not provided, falling back to HTTP"
      );
      return new HttpServer();
=======
  const wsServer = new WebSocketServer({ noServer: true });

  server.on("request", function(req: IncomingMessage, res: ServerResponse){
    console.log("🌐 url: ", req.url);
    // https://stackoverflow.com/questions/8107856/how-to-determine-a-users-ip-address-in-node
    // issue is that it seems docker always returns a docker address
    console.log("🪪 remote address:  ", req.socket.remoteAddress);
    httpApp(req, res);
  });

  server.on("upgrade", function(req: IncomingMessage, socket, head){
    try {
      const wsReq = httpUpgradeToWSRequest(wsServer, req, socket, head);
      wsApp.handleRequest(wsReq, (reason)=>{
        wsHandleError(wsReq, reason);
      });
    }catch(error) {
      console.error("Error handling WebSocket upgrade:", error);
>>>>>>> WA-170_MCP
    }

    try {
      const cert = readFileSync(sslCertPath, "utf8");
      const key = readFileSync(sslKeyPath, "utf8");

<<<<<<< HEAD
      console.log("🔒 Creating HTTPS server on port 8080");
      return new HttpsServer({ cert, key });
    } catch (error) {
      console.error(
        "❌ Failed to load SSL certificates, falling back to HTTP:",
        error
      );
      return new HttpServer();
    }
  }

  console.log("🌐 Creating HTTP server on port 8080");
  return new HttpServer();
}

function getServerPort(): number {
  return process.env.HTTP_PORT ? parseInt(process.env.HTTP_PORT) : 8080;
}

Promise.resolve()
  .then(async () => {
    await setupDBs();
    const httpApp = await setupHttpApp();
    const wsApp = await setupWsApp();

    const server = createServer();

    const wsServer = new WebSocketServer({ noServer: true });

    server.on("request", function (req: IncomingMessage, res: ServerResponse) {
      console.log("🌐 url: ", req.url);
      // https://stackoverflow.com/questions/8107856/how-to-determine-a-users-ip-address-in-node
      // issue is that it seems docker always returns a docker address
      console.log("🪪 remote address:  ", req.socket.remoteAddress);
      httpApp(req, res);
    });

    server.on("upgrade", function (req: IncomingMessage, socket, head) {
      try {
        const wsReq = httpUpgradeToWSRequest(wsServer, req, socket, head);
        wsApp.handleRequest(wsReq, (reason) => {
          wsHandleError(wsReq, reason);
        });
      } catch (error) {
        console.error("Error handling WebSocket upgrade:", error);
      }
    });

    const PORT = getServerPort();
    server.listen(PORT, () => {
      console.log("🚀 Server listening on:  ", server.address());
    });
  })
  .catch((error) => {
    console.error("🔥 Server initialization error: ", error);
  });
=======
}).catch(error=>{
  console.error("🔥 Server initialization error: ", error);
});
>>>>>>> WA-170_MCP
