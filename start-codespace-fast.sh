#!/bin/bash
# Quick start script for GitHub Codespaces - skips pnpm install

set -e

# Function to update CORS configuration for current Codespace
update_codespace_cors() {
  if [ -n "$CODESPACE_NAME" ]; then
    echo "🌐 Updating CORS configuration for Codespace: $CODESPACE_NAME"
    
    CORS_ENV_PATH="/workspaces/server/private-keys/local/cors.env"
    
    if [ -f "$CORS_ENV_PATH" ]; then
      # Define the ports that need CORS access
      CORS_PORTS="8080,8081,8082,8083,9080"
      
      # Build the new CORS origins for this Codespace
      CODESPACE_ORIGINS=""
      for port in ${CORS_PORTS//,/ }; do
        if [ -n "$CODESPACE_ORIGINS" ]; then
          CODESPACE_ORIGINS="$CODESPACE_ORIGINS,"
        fi
        CODESPACE_ORIGINS="$CODESPACE_ORIGINS$CODESPACE_NAME-$port.app.github.dev,https://$CODESPACE_NAME-$port.app.github.dev"
      done
      
      # Read current CORS_FULL_ORIGINS
      CURRENT_ORIGINS=$(grep "^CORS_FULL_ORIGINS=" "$CORS_ENV_PATH" | cut -d'=' -f2- | tr -d '"')
      
      # Remove any existing entries for other Codespaces (to avoid duplicates)
      CLEANED_ORIGINS=$(echo "$CURRENT_ORIGINS" | sed 's/,[^,]*\.app\.github\.dev[^,]*//g' | sed 's/^[^,]*\.app\.github\.dev[^,]*,//g' | sed 's/^[^,]*\.app\.github\.dev[^,]*$//g')
      
      # Add the new Codespace origins
      if [ -n "$CLEANED_ORIGINS" ]; then
        NEW_ORIGINS="$CLEANED_ORIGINS,$CODESPACE_ORIGINS"
      else
        NEW_ORIGINS="$CODESPACE_ORIGINS"
      fi
      
      # Update the cors.env file
      sed -i "s|^CORS_FULL_ORIGINS=.*|CORS_FULL_ORIGINS=\"$NEW_ORIGINS\"|" "$CORS_ENV_PATH"
      
      echo "✅ Updated CORS configuration with current Codespace origins"
    else
      echo "⚠️  CORS config file not found, skipping CORS update"
    fi
  fi
}

# Update CORS configuration before starting services
update_codespace_cors

# Load all env files from private-keys/local
set -a
for envfile in /workspaces/server/private-keys/local/*.env; do
  if [ -f "$envfile" ]; then
    # shellcheck disable=SC1090
    . "$envfile"
  fi
done
set +a

# Kill any existing processes
pkill -f "pnpm start" || true
pkill -f "node.*dist" || true
sleep 2

# Start public-api
cd /workspaces/server/workspace/servers/public-api
echo "[public-api] Starting..."
export HTTP_PORT=9080
export ENABLE_MTLS=true
export MTLS_CERT_DIR="/workspaces/server/private-keys/local"
export HTTPS=true
export SSL_CERT_PATH="/workspaces/server/private-keys/local/server.crt"
export SSL_KEY_PATH="/workspaces/server/private-keys/local/server.key"
pnpm start > /workspaces/server/public-api.log 2>&1 &
PUBLIC_API_PID=$!

# Start public-api-live
cd /workspaces/server/workspace/servers/public-api-live
echo "[public-api-live] Starting..."
export HTTP_PORT=8081
export HTTPS=true
export SSL_CERT_PATH="/workspaces/server/private-keys/local/server.crt"
export SSL_KEY_PATH="/workspaces/server/private-keys/local/server.key"
pnpm start > /workspaces/server/public-api-live.log 2>&1 &
PUBLIC_API_LIVE_PID=$!

# Start public-api-webhook
cd /workspaces/server/workspace/servers/public-api-webhook
echo "[public-api-webhook] Starting..."
export HTTP_PORT=8083
export HTTPS=true
export SSL_CERT_PATH="/workspaces/server/private-keys/local/server.crt"
export SSL_KEY_PATH="/workspaces/server/private-keys/local/server.key"
pnpm start > /workspaces/server/public-api-webhook.log 2>&1 &
PUBLIC_API_WEBHOOK_PID=$!

# Ensure Google service key is available at legacy path for requireJSON fallback
SRC_KEY="/workspaces/server/private-keys/local/credentials/google-service-key.json"
DEST_KEY="/workspaces/server/private-keys/local/credentials/google-service-key.json"
mkdir -p "$(dirname \"$DEST_KEY\")"
if [ "$(realpath "$SRC_KEY" 2>/dev/null)" != "$(realpath "$DEST_KEY" 2>/dev/null)" ]; then
  cp "$SRC_KEY" "$DEST_KEY" || echo "⚠️  Could not copy Google service key"
fi

# Start web-client
cd /workspaces/server/workspace/clients/web
export ENV_FOLDER="/workspaces/server/workspace/clients/web/env"
echo "[web-client] Starting..."
export HTTP_PORT=8080
export HTTPS=true
export SSL_CERT_PATH="/workspaces/server/private-keys/local/server.crt"
export SSL_KEY_PATH="/workspaces/server/private-keys/local/server.key"
pnpm start > /workspaces/server/web-client.log 2>&1 &
WEB_CLIENT_PID=$!

cd /workspaces/server

echo "All services started. Logs:"
echo "  public-api:         tail -f /workspaces/server/public-api.log"
echo "  public-api-live:    tail -f /workspaces/server/public-api-live.log"
echo "  public-api-webhook: tail -f /workspaces/server/public-api-webhook.log"
echo "  web-client:         tail -f /workspaces/server/web-client.log"
echo "To stop all: kill $PUBLIC_API_PID $PUBLIC_API_LIVE_PID $PUBLIC_API_WEBHOOK_PID $WEB_CLIENT_PID"

# Health check for all services - wait longer for services to start
echo "⏳ Waiting 10 seconds for services to start..."
sleep 10

SERVICES=(8080 9080 8081 8083)
ALL_HEALTHY=true
for PORT in "${SERVICES[@]}"; do
  STATUS=$(curl -k -s -o /dev/null -w "%{http_code}" https://localhost:$PORT/ || echo "000")
  if [ "$STATUS" != "200" ] && [ "$STATUS" != "404" ]; then
    echo "❌ Service on port $PORT is not healthy (HTTP $STATUS)"
    ALL_HEALTHY=false
  else
    echo "✅ Service on port $PORT responded (HTTP $STATUS)"
  fi
done

if [ "$ALL_HEALTHY" = true ]; then
  echo ""
  echo "🎉 All services are healthy!"
  echo ""
  echo "🌐 Your Codespace URLs:"
  echo "   • Web Client: https://$CODESPACE_NAME-8080.app.github.dev"
  echo "   • Public API: https://$CODESPACE_NAME-9080.app.github.dev"
  echo "   • API Live: https://$CODESPACE_NAME-8081.app.github.dev"
  echo "   • API Webhook: https://$CODESPACE_NAME-8083.app.github.dev"
else
  echo ""
  echo "⚠️  Some services failed health checks. Check logs:"
  echo "  tail -f /workspaces/server/*.log"
fi
