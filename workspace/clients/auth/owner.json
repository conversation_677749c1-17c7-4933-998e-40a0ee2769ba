{
  "cookies": [],
  "origins": [
    {
      "origin": "http://localhost:8080",
      "localStorage": [
        {
<<<<<<< HEAD
          "name": "@@auth0spajs@@::waO2KMbVwo3xXu1wqZyUeLA1cTinVpXx::chat.divinci.app:8081::openid profile email",
          "value": "{\"body\":{\"access_token\":\"eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjF5Z1ZfVmZ4a1p3cHU3TUY4ejRpbSJ9.eyJpc3MiOiJodHRwczovL2Rldi00NnRpeXM2aG5iNnZiZzE3LnVzLmF1dGgwLmNvbS8iLCJzdWIiOiJrcmxWQmV0MGxzbTc1VVdmb08yTUlmbWdYa0JhRnVqTUBjbGllbnRzIiwiYXVkIjoiY2hhdC5kaXZpbmNpLmFwcDo4MDgxIiwiaWF0IjoxNzQ4MDA2NDk2LCJleHAiOjE3NDgwOTI4OTYsImd0eSI6ImNsaWVudC1jcmVkZW50aWFscyIsImF6cCI6ImtybFZCZXQwbHNtNzVVV2ZvTzJNSWZtZ1hrQmFGdWpNIn0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************\",\"id_token\":\"real-id-token\",\"scope\":\"openid profile email\",\"expires_in\":86400,\"token_type\":\"Bearer\"},\"expiresAt\":1748092896458}"
=======
          "name": "@@auth0spajs@@::mock-client-id::https://api.divinci.app::openid profile email",
          "value": "{\"body\":{\"access_token\":\"mock-owner-token\",\"id_token\":\"mock-id-token\",\"scope\":\"openid profile email\",\"expires_in\":86400,\"token_type\":\"Bearer\"},\"expiresAt\":9999999999}"
>>>>>>> WA-170_MCP
        },
        {
          "name": "auth0.user",
          "value": "{\"sub\":\"auth0|owner\",\"email\":\"<EMAIL>\",\"email_verified\":true}"
        }
      ]
    }
  ]
}