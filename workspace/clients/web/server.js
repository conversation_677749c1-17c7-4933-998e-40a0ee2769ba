const express = require("express");
const morgan = require("morgan");
const path = require("path");
const cors = require("cors");
const {
  DIVINCI_DOMAINS,
  CORS_PORTS,
  AUTH0_BASE_URL,
} = require("@divinci-ai/server-globals");
const app = express();
const PORT = process.env.HTTP_PORT || 8080;

const isCloudEnvironment = process.env.ENVIRONMENT === "cloud";
const standardPorts = [80, 8080, 443];

console.log("🌐 Web CORS_PORTS: ", CORS_PORTS);

// Parse CORS_PORTS from environment variable or use fallback
const additionalPorts = CORS_PORTS && typeof CORS_PORTS === "string"
  ? CORS_PORTS.split(",").map(port=>parseInt(port.trim()))
  : [8080, 8081, 8082, 8083, 8084, 9080, 9081, 8787, 8788, 8789, 8790]; // fallback values matching cors.env

// Combine standard ports with additional ports
const allPorts = [...new Set([...standardPorts, ...additionalPorts])];

const generateOrigins = (domains, ports)=>{
  return domains.flatMap((domain)=>ports.map((port)=>port === 8080 ||
      port === 80 ||
      port === 443 ||
      port === 9080 ||
      port === 8789 ||
      port === 9081
        ? `https://${domain}`
        : `http://${domain}:${port}`
    )
  );
};

// Generate allowed origins and convert to a Set for faster lookups
const allowedOriginsSet = new Set([
  ...generateOrigins(DIVINCI_DOMAINS, allPorts),
  ...(isCloudEnvironment
    ? []
    : generateOrigins(["localhost"], additionalPorts)),
]);

const corsOptions = {
  origin: function(origin, callback){
    // Allow undefined origins (e.g., for tools like Postman) or matching subdomains
    if (
      origin === undefined ||
      /^https:\/\/(.*\.)?divinci\.app$/.test(origin) || // Subdomain wildcard check
      (isCloudEnvironment ? /^http:\/\/localhost:\d+$/.test(origin) : false) || // Localhost check for dev environments
      allowedOriginsSet.has(origin) // Optimized lookup using Set
    ) {
      callback(null, true);
    } else {
      const errorMessage = `🚨 Origin: ${origin} is not allowed by CORS.`;
      console.error(errorMessage);
      callback(new Error(errorMessage));
    }
  },
  methods: "GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS", // Support standard HTTP methods
  credentials: true, // Allow cookies and credentials
  allowedHeaders: [
    "Content-Type",
    "Authorization",
    "Origin",
    "x-access-token",
    "X-File-Name",
    "X-File-Id",
    "X-Target",
    "X-Processor",
    "X-Vectorize-Config",
    "X-Processor-Config",
    "divinci-organization",
  ],
  preflightContinue: false,
  optionsSuccessStatus: 204,
  exposedHeaders: [
    "X-File-Name",
    "X-File-Id",
    "X-Target",
    "X-Processor",
    "X-Vectorize-Config",
    "X-Processor-Config",
  ],
};

morgan.token("req-headers", (req)=>JSON.stringify(req.headers));
app.use(
  morgan(
    ":method :url :status :res[content-length] - :response-time ms :req-headers"
  )
);
app.use(cors(corsOptions));

// Set Content-Security-Policy header
app.use((req, res, next)=>{
  // Start with base connect-src domains
  const baseConnectSrc = [
    "'self'",
    "wss:",
    "https://api.speechly.com",
    "https://browser-intake-us5-datadoghq.com",
  ];

  // Add DIVINCI_DOMAINS with https protocol
  const divinciConnectSrc = DIVINCI_DOMAINS.map(domain=>`https://${domain}`);

  // Combine all connect-src domains
  let connectSrc = `connect-src ${[...baseConnectSrc, ...divinciConnectSrc].join(" ")}`;

  // Add AUTH0_BASE_URL for non-cloud environments
  if (!isCloudEnvironment && AUTH0_BASE_URL) {
    connectSrc += ` ${AUTH0_BASE_URL}`;
  }

  const cspString =
    "default-src 'self'; " +
    "script-src 'self' 'unsafe-inline' blob: https://static.cloudflareinsights.com; " +
    "worker-src 'self' blob:; " +
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
    "img-src 'self' data:; " +
    "font-src 'self' https://fonts.googleapis.com https://fonts.gstatic.com; " +
    connectSrc;

  res.setHeader("Content-Security-Policy", cspString);
  next();
});

// Serve static files
app.use(express.static(path.join(__dirname, "public")));

// Inject API_BASE_URL into index.html for dynamic Codespaces/localhost support
app.get("/", (req, res, next) => {
  const indexPath = path.join(__dirname, "public", "index.html");
  let apiBaseUrl = "";
  const forwardedHost = req.headers["x-forwarded-host"];
  if (forwardedHost && typeof forwardedHost === "string") {
    // Codespaces: replace -8080 with -9080 for API
    apiBaseUrl = `https://${forwardedHost.replace(/-8080/, "-9080")}`;
  } else {
    // Local: use http://localhost:9080
    apiBaseUrl = "http://localhost:9080";
  }
  // Read and inject the API_BASE_URL script
  require("fs").readFile(indexPath, "utf8", (err, html) => {
    if (err) return next(err);
    const injected = html.replace(
      /<head>/i,
      `<head>\n<script>window.API_BASE_URL='${apiBaseUrl}';</script>`
    );
    res.send(injected);
  });
});

// SPA Fallback
app.get("*", (req, res)=>{
  res.sendFile(path.join(__dirname, "public", "index.html"));
});

app.listen(PORT, ()=>{
  console.log(`Server is running on port ${PORT}`);
});
