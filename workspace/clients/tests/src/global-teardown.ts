/**
 * Global Teardown
 *
 * This file contains the global teardown logic that runs after all tests.
 * It ensures that all resources created during tests are properly cleaned up.
 */

import { FullConfig } from '@playwright/test';
import { clearUserGroups } from './resources/user-group-pool';
import { clearWhitelabels } from './resources/whitelabel-pool';
import { clearVectors } from './resources/vector-pool';
import { recordTracker } from './utils/record-tracker';

/**
 * Global teardown function that runs after all tests
 * @param config Playwright configuration
 */
async function globalTeardown(config: FullConfig): Promise<void> {
  console.log('🧹 Running global teardown...');

  try {
    // Clean up all resource pools
    await Promise.all([
      clearUserGroups(),
      clearWhitelabels(),
      clearVectors()
    ]);

    // Check for any undeleted records
    const undeletedRecords = recordTracker.getUndeletedRecords();
    if (undeletedRecords.length > 0) {
      console.warn(`⚠️ Found ${undeletedRecords.length} undeleted records after tests:`);

      // Group undeleted records by type for better reporting
      const recordsByType = undeletedRecords.reduce((acc, record) => {
        if (!acc[record.type]) {
          acc[record.type] = [];
        }
        acc[record.type].push(record);
        return acc;
      }, {} as Record<string, typeof undeletedRecords>);

      // Log undeleted records by type
      for (const [type, records] of Object.entries(recordsByType)) {
        console.warn(`  - ${type}: ${records.length} records`);
        for (const record of records) {
          console.warn(`    - ID: ${record.id}, Created: ${new Date(record.createdAt).toISOString()}`);
        }
      }

      // Get a summary of all tracked records
      const summary = recordTracker.getSummary();
      console.log('📊 Record tracking summary:');
      for (const [type, counts] of Object.entries(summary)) {
        console.log(`  - ${type}: ${counts.total} total, ${counts.deleted} deleted, ${counts.undeleted} undeleted`);
      }
    } else {
      console.log('✅ All tracked records were properly deleted');
    }

    // Clear the record tracker
    recordTracker.clearRecords();

    console.log('✅ Global teardown completed successfully');
  } catch (error) {
    console.error('❌ Error during global teardown:', error);
  }
}

export default globalTeardown;
