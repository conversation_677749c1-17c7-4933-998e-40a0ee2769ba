
import { DataSourceAudioTranscriptModel } from "@divinci-ai/server-models";
import { getAudioTools } from "../../../util/audio-tools";
import { getAudioR2Instance } from "@divinci-ai/server-globals";
import { CLOUDFLARE_AUDIO_PUBLIC_URL, LOCAL_AUDIO_PUBLIC_URL, IS_LOCAL_MODE, r2 } from "../../../util/r2-constants";
import { AUDIO_TOOLS } from "@divinci-ai/server-tools";
import { HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";

import { getS3Info, copyOriginalToS3 } from "./s3-copy-original";
import { ensureValidFlac } from "./ensure-valid-flac";

// Initialize a backup r2 client for fallbacks
const fallbackR2 = getAudioR2Instance();

// Add this type for local mode options
interface LocalModeOptions {
  useLocalMinio: boolean;
  skipCloudServices: boolean;
  environment: string;
  forceLocalMode?: boolean;
}

export async function transcribeMedia(
  target: string, whitelabelId: string,
  toolIds: { diarizer: string, transcriber: string },
  originalS3Location: { Bucket: string, Key: string },
  sourceInfo: { filename: string, sourceType: "url" | "file", rawValue: string },
  copiedLocation?: { Bucket: string, Key: string },
<<<<<<< HEAD
  localModeOptions?: LocalModeOptions
=======
  localModeOptions?: LocalModeOptions,
  confidenceSettings?: {
    enabled: boolean,
    threshold: number,
    includeInProcessing: boolean,
  },
  voiceprintSettings?: {
    enabled: boolean,
    autoCreate: boolean,
    existingVoiceprints?: Array<{
      voiceprintId: string,
      speakerLabel: string,
      voiceprintData: string
    }>
  }
>>>>>>> WA-170_MCP
){
  const isLocalMode = localModeOptions?.forceLocalMode ||
                      localModeOptions?.useLocalMinio ||
                      IS_LOCAL_MODE ||
                      process.env.ENVIRONMENT === "local" ||
                      process.env.NODE_ENV === "development" ||
                      process.env.NODE_ENV === "local";

<<<<<<< HEAD
=======
  // Check if we should force R2 storage even in local mode
  const forceR2Storage = process.env.FORCE_R2_STORAGE === "true";

>>>>>>> WA-170_MCP
  console.log("🔄 Transcribe media called with options:", {
    target,
    whitelabelId,
    toolIds,
    originalS3Location,
    sourceType: sourceInfo.sourceType,
    filename: sourceInfo.filename,
    isLocalMode,
<<<<<<< HEAD
=======
    forceR2Storage,
>>>>>>> WA-170_MCP
    localModeOptions
  });

  const convertArgs = await AUDIO_TOOLS.canConvertToFlac(sourceInfo.filename);
  if(!convertArgs.support){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Unsupported file type");
  }

  // Implement the logic to transcribe the video
  const tools = getAudioTools({
    diarizer: toolIds.diarizer,
    transcriber: toolIds.transcriber,
  });

  // In local mode, we might not be able to get S3 info, so we handle failures differently
  let etag = "";
  let contentLength = 0;

  try {
    const s3Info = await getS3Info(originalS3Location, isLocalMode);
    etag = s3Info.etag;
    contentLength = s3Info.contentLength;
  } catch (error) {
    if (isLocalMode) {
      console.warn("⚠️ Failed to get S3 info in local mode, using dummy values", error);
      etag = `dummy-etag-${Date.now()}`;
      contentLength = 12345;
    } else {
      throw error;
    }
  }

  // Process the audio file using MP3 conversion and S3 copy
  let audioS3Location;
  let rawS3Location;

  try {
    [audioS3Location, rawS3Location] = await Promise.all([
      ensureValidFlac(originalS3Location, whitelabelId, isLocalMode),
      copiedLocation ? copiedLocation : copyOriginalToS3(originalS3Location, whitelabelId, isLocalMode),
    ]);
  } catch (error) {
    if (isLocalMode) {
      console.warn("⚠️ Failed to process files in local mode, using dummy values", error);
      const timestamp = Date.now();
      const filenameWithoutExt = sourceInfo.filename.replace(/\.[^.]+$/, '');
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');

      audioS3Location = {
        Bucket: "workspace-audio",
        Key: `${whitelabelId}/audio/${year}/${month}/${day}/${timestamp}_${filenameWithoutExt}.flac`
      };

      rawS3Location = {
        Bucket: "workspace-audio",
        Key: `${whitelabelId}/original/${year}/${month}/${day}/${timestamp}_${filenameWithoutExt}${sourceInfo.filename.includes('.') ? '.' + sourceInfo.filename.split('.').pop() : ''}`
      };
    } else {
      throw error;
    }
  }

<<<<<<< HEAD
  // Choose the appropriate public URL based on environment
  // Note: We're now using FLAC files instead of MP3
  const publicUrl = isLocalMode
    ? (LOCAL_AUDIO_PUBLIC_URL || "http://localhost:9000") + "/" + audioS3Location.Key
    : CLOUDFLARE_AUDIO_PUBLIC_URL + "/" + audioS3Location.Key;

  console.log("📄 Using public URL:", publicUrl);
=======
  // Choose the appropriate public URL based on environment and force flag
  // Note: We're now using FLAC files instead of MP3
  const shouldUseR2Url = !isLocalMode || forceR2Storage;

  let publicUrl: string;
  if (shouldUseR2Url) {
    if (forceR2Storage && isLocalMode) {
      // Force R2 storage in local mode - use real Cloudflare R2 public URL
      // URL-encode the key to handle filenames with spaces and special characters
      const encodedKey = encodeURIComponent(audioS3Location.Key).replace(/%2F/g, '/');
      publicUrl = "https://pub-2a04833210c8489198d3b76b8fb4f12d.r2.dev/" + encodedKey;
      console.log("🌐 Using real Cloudflare R2 public URL (forced in local mode)");
    } else {
      // Production mode - use configured R2 public URL
      // URL-encode the key to handle filenames with spaces and special characters
      const encodedKey = encodeURIComponent(audioS3Location.Key).replace(/%2F/g, '/');
      publicUrl = CLOUDFLARE_AUDIO_PUBLIC_URL + "/" + encodedKey;
      console.log("🌐 Using configured R2 public URL");
    }
  } else {
    // Local mode without force - use MinIO URL
    // URL-encode the key to handle filenames with spaces and special characters
    const encodedKey = encodeURIComponent(audioS3Location.Key).replace(/%2F/g, '/');
    publicUrl = (LOCAL_AUDIO_PUBLIC_URL || "http://localhost:9000") + "/" + encodedKey;
    console.log("🌐 Using local MinIO URL");
  }

  console.log("📄 Using public URL:", publicUrl);
  console.log("🔍 URL Selection Logic:", {
    isLocalMode,
    forceR2Storage,
    shouldUseR2Url,
    urlType: shouldUseR2Url ? 'Cloudflare R2' : 'Local MinIO'
  });

  // Debug voiceprint settings before document creation
  console.log("🔍 [TRANSCRIBE-DEBUG] voiceprintSettings:", voiceprintSettings);
  console.log("🔍 [TRANSCRIBE-DEBUG] voiceprintSettings.existingVoiceprints:", voiceprintSettings?.existingVoiceprints);
  console.log("🔍 [TRANSCRIBE-DEBUG] existingVoiceprints length:", voiceprintSettings?.existingVoiceprints?.length);

  const shouldAddVoiceprintConfig = voiceprintSettings?.existingVoiceprints && voiceprintSettings.existingVoiceprints.length > 0;
  console.log("🔍 [TRANSCRIBE-DEBUG] shouldAddVoiceprintConfig:", shouldAddVoiceprintConfig);
>>>>>>> WA-170_MCP

  // Create a pending transcript record
  const { doc, work } = await DataSourceAudioTranscriptModel.createTranscript(
    target, tools, {
      sourceType: sourceInfo.sourceType,
      mediaType: convertArgs.type,
      sourceId: `${etag}-${contentLength}`,
      info: {
        rawValue: sourceInfo.rawValue,
        rawKey: rawS3Location.Key,
        rawBucket: rawS3Location.Bucket,
        fileType: convertArgs.type,
        filename: sourceInfo.filename,
        hash: etag,
        filesize: contentLength,
        isLocalMode // Add flag for local mode
      }
    },
    {
      Bucket: audioS3Location.Bucket,
      Key: audioS3Location.Key,
      filename: sourceInfo.filename,
      publicUrl
<<<<<<< HEAD
    }
  );

  // For local mode, just log additional information but process normally
  if (isLocalMode) {
    console.log("📄 Using local mode, real audio file will be processed");
    console.log(`📄 Audio file (FLAC) located at: ${audioS3Location.Bucket}/${audioS3Location.Key}`);

    // Define alternative URLs for debugging
    const minioHttpUrls = [
      `http://minio.divinci.local:9000/${audioS3Location.Key}`, // DNS alias - reliable option
      `http://localhost:9000/${audioS3Location.Key}`,
      `http://127.0.0.1:9000/${audioS3Location.Key}`,
      `http://host.docker.internal:9000/${audioS3Location.Key}`
    ];

    console.log("🔍 MinIO HTTP access URLs (for debugging):");
    minioHttpUrls.forEach(url => console.log(`   - ${url}`));

    // Verify if the file exists in MinIO using the reliable endpoint
    let fileVerified = false;
    const minioEndpoint = "http://minio.divinci.local:9000"; // Reliable DNS alias

    try {
      // Create a client with the reliable endpoint
      const client = {
        ...fallbackR2,
        config: { ...fallbackR2.config, endpoint: minioEndpoint }
      };

      console.log(`🔍 Verifying file existence with endpoint: ${minioEndpoint}`);

      const headResult = await client.headObject({
        Bucket: audioS3Location.Bucket,
        Key: audioS3Location.Key
      });

      console.log(`✅ Verified audio file exists in MinIO with endpoint ${minioEndpoint}:`, {
        Bucket: audioS3Location.Bucket,
        Key: audioS3Location.Key,
        ContentLength: headResult.ContentLength,
        ContentType: headResult.ContentType
      });

      fileVerified = true;
    } catch (verifyError) {
      console.warn(`⚠️ Failed to verify file with endpoint ${minioEndpoint}`);
    }

    if (!fileVerified) {
      console.error("❌ Could not verify audio file existence in MinIO with any endpoint.");
      console.log("🔍 This may be OK if the file is accessible via HTTP. Audio processing will continue.");
    }

    // Add additional logging but don't modify the normal process
    console.log("📝 In local mode, will now continue with normal processing");
  }
=======
    },
    {
      // Pass confidence settings if provided
      ...(confidenceSettings && {
        confidenceSettings: {
          enabled: confidenceSettings.enabled,
          threshold: confidenceSettings.threshold,
          includeInProcessing: confidenceSettings.includeInProcessing,
        }
      }),
      // Initialize empty voiceprints array if voiceprint auto-creation is enabled
      ...(voiceprintSettings?.enabled && voiceprintSettings?.autoCreate && { voiceprints: [] }),
      // Pass voiceprint configuration for identification
      ...(shouldAddVoiceprintConfig && {
        voiceprintConfig: {
          enabled: voiceprintSettings.enabled,
          existingVoiceprints: voiceprintSettings.existingVoiceprints,
          autoCreate: voiceprintSettings.autoCreate // Store autoCreate setting
        }
      }),
    }
  );

  console.log("🔍 [TRANSCRIBE-DEBUG] Document created with keys:", Object.keys(doc.toObject()));
>>>>>>> WA-170_MCP

  // For local mode, just log additional information but process normally
  if (isLocalMode) {
    console.log("📄 Using local mode, real audio file will be processed");
    console.log(`📄 Audio file (FLAC) located at: ${audioS3Location.Bucket}/${audioS3Location.Key}`);

    if (forceR2Storage) {
      // When forcing R2 storage, skip MinIO verification
      console.log("🔍 FORCE_R2_STORAGE=true: Skipping MinIO verification, using R2 storage");
      console.log("🌐 File should be accessible via R2 public URL");
    } else {
      // Define alternative URLs for debugging
      const minioHttpUrls = [
        `http://minio.divinci.local:9000/${audioS3Location.Key}`, // DNS alias - reliable option
        `http://localhost:9000/${audioS3Location.Key}`,
        `http://127.0.0.1:9000/${audioS3Location.Key}`,
        `http://host.docker.internal:9000/${audioS3Location.Key}`
      ];

      console.log("🔍 MinIO HTTP access URLs (for debugging):");
      minioHttpUrls.forEach(url => console.log(`   - ${url}`));

      // Verify if the file exists in MinIO using the reliable endpoint
      let fileVerified = false;
      const minioEndpoint = "http://host.docker.internal:9000"; // Use container-to-host endpoint

      try {
        // Import S3Client for proper MinIO configuration
        const { S3Client } = await import("@aws-sdk/client-s3");

        // Create a properly configured MinIO client
        const client = new S3Client({
          endpoint: minioEndpoint,
          region: "us-east-1",
          credentials: {
            accessKeyId: process.env.MINIO_ACCESS_KEY || "minioadmin",
            secretAccessKey: process.env.MINIO_SECRET_KEY || "minioadmin"
          },
          forcePathStyle: true, // Required for MinIO
          tls: false
        });

        console.log(`🔍 Verifying file existence with endpoint: ${minioEndpoint}`);

        const { HeadObjectCommand } = await import("@aws-sdk/client-s3");
        const headResult = await client.send(new HeadObjectCommand({
          Bucket: audioS3Location.Bucket,
          Key: audioS3Location.Key
        }));

        console.log(`✅ Verified audio file exists in MinIO with endpoint ${minioEndpoint}:`, {
          Bucket: audioS3Location.Bucket,
          Key: audioS3Location.Key,
          ContentLength: headResult.ContentLength,
          ContentType: headResult.ContentType
        });

        fileVerified = true;
      } catch (verifyError) {
        console.warn(`⚠️ Failed to verify file with endpoint ${minioEndpoint}`);
      }

      if (!fileVerified) {
        console.error("❌ Could not verify audio file existence in MinIO with any endpoint.");
        console.log("🔍 This may be OK if the file is accessible via HTTP. Audio processing will continue.");
      }
    }

    // Add additional logging but don't modify the normal process
    console.log("📝 In local mode, will now continue with normal processing");
  }

  return { doc, work };
}


