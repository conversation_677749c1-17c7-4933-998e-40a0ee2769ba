# Divinci MCP Server Testing Guide

This guide provides comprehensive instructions for testing the Divinci AI MCP (Model Context Protocol) server implementation.

## Prerequisites

- Node.js 20+
- npm or pnpm
- curl (for HTTP testing)
- jq (for JSON formatting, optional)

## Quick Start Testing

### 1. Start the MCP Server

```bash
cd workspace/workers/mcp-server
npm install
npm run dev
```

The server will start on `http://localhost:8793`

### 2. Run Automated Tests

```bash
./test-mcp-server.sh
```

This script tests:
- ✅ Health endpoint (`/health`)
- ✅ Root endpoint (`/`)
- ✅ 404 error handling
- ✅ CORS preflight requests
- ✅ MCP SSE endpoint (`/sse`)

## Manual Testing Methods

### Method 1: MCP Inspector (Recommended)

The MCP Inspector is the official testing tool for MCP servers.

1. **Install and run MCP Inspector:**
```bash
npx @modelcontextprotocol/inspector@latest
```

2. **Open the inspector in your browser:**
   - The inspector will show a URL like: `http://localhost:6274/?MCP_PROXY_AUTH_TOKEN=...`
   - Open this URL in your browser

3. **Connect to your MCP server:**
   - In the inspector, enter: `http://localhost:8793/sse`
   - Click "Connect"

4. **Test available tools:**
   - Click "List Tools" to see available tools
   - Test each tool:
     - `create_chat` - Creates a new chat conversation
     - `send_message` - Sends a message to a chat
     - `list_chats` - Lists user's chat conversations
     - `get_user_profile` - Gets user profile information

### Method 2: curl Testing

Test individual endpoints with curl:

```bash
# Test health endpoint
curl -s http://localhost:8793/health | jq .

# Test root endpoint
curl -s http://localhost:8793/ | jq .

# Test SSE endpoint (will stream events)
curl -s -H "Accept: text/event-stream" http://localhost:8793/sse

# Test CORS
curl -s -X OPTIONS -H "Origin: http://localhost:5173" http://localhost:8793/sse -v
```

### Method 3: Claude Desktop Integration

1. **Install mcp-remote:**
```bash
npm install -g mcp-remote
```

2. **Configure Claude Desktop:**
   
   Edit your Claude Desktop configuration file:
   - **macOS:** `~/Library/Application Support/Claude/claude_desktop_config.json`
   - **Windows:** `%APPDATA%\Claude\claude_desktop_config.json`

   Add this configuration:
   ```json
   {
     "mcpServers": {
       "divinci": {
         "command": "npx",
         "args": ["mcp-remote", "http://localhost:8793/sse"]
       }
     }
   }
   ```

3. **Restart Claude Desktop** and test the tools

## Expected Test Results

### Health Endpoint Response
```json
{
  "success": true,
  "service": "divinci-mcp-server",
  "environment": "development",
  "version": "1.0.0",
  "timestamp": "2025-06-17T21:38:12.631Z",
  "status": "healthy"
}
```

### Root Endpoint Response
```json
{
  "name": "Divinci AI MCP Server",
  "version": "1.0.0",
  "description": "Model Context Protocol server for Divinci AI services",
  "endpoints": {
    "sse": "/sse",
    "health": "/health"
  }
}
```

### SSE Endpoint Response
```
event: message
data: {"jsonrpc":"2.0","method":"notifications/initialized"}

event: message
data: {"jsonrpc":"2.0","method":"notifications/initialized","params":{"protocolVersion":"2024-11-05","capabilities":{"tools":{}},"serverInfo":{"name":"divinci-ai-mcp-server","version":"1.0.0"}}}
```

## Tool Testing Examples

### create_chat Tool
```json
{
  "name": "create_chat",
  "arguments": {
    "title": "Test Chat",
    "model": "gpt-4"
  }
}
```

Expected response:
```
Created new chat conversation with ID: chat_1734567890123 and title: "Test Chat"
```

### send_message Tool
```json
{
  "name": "send_message",
  "arguments": {
    "chatId": "chat_1734567890123",
    "message": "Hello, how are you?"
  }
}
```

Expected response:
```
Message sent to chat chat_1734567890123: "Hello, how are you?"

Mock AI Response: I received your message "Hello, how are you?" and I'm ready to help!
```

### list_chats Tool
```json
{
  "name": "list_chats",
  "arguments": {
    "limit": 5
  }
}
```

Expected response:
```
Found 3 chat conversations:
- General Discussion (chat_1) - 2024-01-15T10:30:00Z
- Project Planning (chat_2) - 2024-01-14T15:45:00Z
- Technical Questions (chat_3) - 2024-01-13T09:20:00Z
```

### get_user_profile Tool
```json
{
  "name": "get_user_profile",
  "arguments": {}
}
```

Expected response:
```
User Profile:
- Email: <EMAIL>
- Name: Test User
- ID: user_123
- Environment: development
```

## Troubleshooting

### Common Issues

1. **Server not starting:**
   - Check if port 8793 is available
   - Verify Node.js version (requires 20+)
   - Check for TypeScript compilation errors

2. **SSE connection fails:**
   - Verify CORS headers are set correctly
   - Check browser console for errors
   - Ensure Accept header includes `text/event-stream`

3. **Tools not working:**
   - Check server logs for errors
   - Verify tool arguments match the schema
   - Ensure proper JSON formatting

### Debug Mode

Enable debug logging by setting environment variables:
```bash
DEBUG=mcp:* npm run dev
```

### Server Logs

Monitor server logs in the terminal where you ran `npm run dev`. Look for:
- Connection attempts
- Tool invocations
- Error messages

## Next Steps

Once basic testing is complete:

1. **Integrate with real Divinci APIs** (replace mock responses)
2. **Add authentication** (Auth0 integration)
3. **Deploy to production** (Cloudflare Workers)
4. **Add more tools** (document processing, audio handling)
5. **Implement proper error handling**

## Support

If you encounter issues:
1. Check the server logs
2. Verify your configuration
3. Test with the automated test script
4. Use the MCP Inspector for detailed debugging
