import { WorkflowStep } from "cloudflare:workers";
import { StepContext } from "./types";

/**
 * Links file to RAG with robust error handling
 */
export async function linkFileToRag(
  step: WorkflowStep,
  context: StepContext,
  ragId: string,
  whitelabelId: string,
  fileId: string,
  auth0Token: string
): Promise<void>{
  await step.do("linkFileToRag", async ()=>{
<<<<<<< HEAD
    console.log(`📝 Linking file ${fileId} to custom RAG ${ragId}`);

    // First, add to pending (using updatePendingAtomic)
    await addFileToPending(context, ragId, whitelabelId, fileId, auth0Token);

    // Then, move to success (using updateSuccessAtomic)
    await moveFileToSuccess(context, ragId, whitelabelId, fileId, auth0Token);

    console.log(`✅ Successfully linked file ${fileId} to RAG ${ragId}`);
=======
    console.log(`🚀🚀🚀 [LINK-FILE-TO-RAG] Starting linkFileToRag step`);
    console.log(`🚀🚀🚀 [LINK-FILE-TO-RAG] Parameters:`, {
      ragId,
      whitelabelId,
      fileId,
      hasAuth0Token: !!auth0Token,
      auth0TokenLength: auth0Token?.length || 0
    });

    try {
      // APPROACH 1: Try the current approach first (calls /file endpoint)
      console.log(`🚀🚀🚀 [LINK-FILE-TO-RAG] APPROACH 1: Trying current addFileToRag approach`);
      await addFileToRag(context, ragId, whitelabelId, fileId, auth0Token);
      console.log(`🚀🚀🚀 [LINK-FILE-TO-RAG] APPROACH 1: addFileToRag completed successfully`);
      console.log(`✅ Successfully linked file ${fileId} to RAG ${ragId} using current approach`);
    }catch(currentApproachError) {
      console.error(`❌ [LINK-FILE-TO-RAG] APPROACH 1 FAILED:`, currentApproachError);
      console.error(`❌ [LINK-FILE-TO-RAG] APPROACH 1 Error details:`, {
        message: currentApproachError.message,
        stack: currentApproachError.stack,
        ragId,
        whitelabelId,
        fileId
      });

      try {
        // APPROACH 2: Fall back to the old manual pending/success approach
        console.log(`🚀🚀🚀 [LINK-FILE-TO-RAG] APPROACH 2: Trying manual pending/success approach as fallback`);
        await addFileToRagManual(context, ragId, whitelabelId, fileId, auth0Token);
        console.log(`🚀🚀🚀 [LINK-FILE-TO-RAG] APPROACH 2: Manual approach completed successfully`);
        console.log(`✅ Successfully linked file ${fileId} to RAG ${ragId} using manual fallback approach`);
      }catch(manualApproachError) {
        console.error(`❌ [LINK-FILE-TO-RAG] APPROACH 2 ALSO FAILED:`, manualApproachError);
        console.error(`❌ [LINK-FILE-TO-RAG] Both approaches failed. Current approach error:`, currentApproachError);
        console.error(`❌ [LINK-FILE-TO-RAG] Manual approach error:`, manualApproachError);

        // Throw the original error from the current approach
        throw new Error(`Both linking approaches failed. Current: ${currentApproachError.message}. Manual: ${manualApproachError.message}`);
      }
    }
>>>>>>> WA-170_MCP
  });
}

/**
<<<<<<< HEAD
 * Adds file to pending with robust error handling
 */
async function addFileToPending(
=======
 * Adds file to RAG using the addRagFile endpoint with robust error handling
 */
async function addFileToRag(
>>>>>>> WA-170_MCP
  context: StepContext,
  ragId: string,
  whitelabelId: string,
  fileId: string,
  auth0Token: string
): Promise<void>{
  const maxAttempts = 3;
  const baseDelay = 2000; // 2 seconds

<<<<<<< HEAD
  for(let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      console.log(`🔄 Adding file to pending (attempt ${attempt}/${maxAttempts})`);

      const pendingResponse = await fetch(
        `${context.env.API_HOST}/white-label/${whitelabelId}/rag-vector/${ragId}/pending-file`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${auth0Token}`
          },
          body: JSON.stringify({ fileId })
        }
      );

      if(pendingResponse.ok) {
        console.log(`✅ Successfully added file ${fileId} to pending`);
=======
  console.log(`🚀🚀🚀 [ADD-FILE-TO-RAG] Starting addFileToRag function`);
  console.log(`🚀🚀🚀 [ADD-FILE-TO-RAG] Environment: ${context.env.ENVIRONMENT}`);
  console.log(`🚀🚀🚀 [ADD-FILE-TO-RAG] API_HOST: ${context.env.API_HOST}`);

  for(let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      console.log(`🔄 [ADD-FILE-TO-RAG] Adding file to RAG (attempt ${attempt}/${maxAttempts})`);

      // Prepare headers for addRagFile request
      const headers: Record<string, string> = {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${auth0Token}`
      };

      // In BARE METAL MODE, we need to add special headers for local/dev/stage environments
      if(context.env.ENVIRONMENT === "local" || context.env.ENVIRONMENT === "development" || context.env.ENVIRONMENT === "stage") {
        if(context.env.CLOUDFLARE_WORKER_X_AUTH_DEV) {
          headers["cloudflare-worker-x-dev-auth"] = context.env.CLOUDFLARE_WORKER_X_AUTH_DEV;
          headers["x-worker-local-dev"] = "true";
          console.log(`🔑 [ADD-FILE-TO-RAG] Added Cloudflare Worker dev auth headers for addRagFile request`);
        } else {
          console.log(`⚠️ [ADD-FILE-TO-RAG] No CLOUDFLARE_WORKER_X_AUTH_DEV found for environment: ${context.env.ENVIRONMENT}`);
        }
      }

      const url = `${context.env.API_HOST}/white-label/${whitelabelId}/rag-vector/${ragId}/file`;
      const requestBody = { fileId };

      console.log(`🚀🚀🚀 [ADD-FILE-TO-RAG] Making request to: ${url}`);
      console.log(`🚀🚀🚀 [ADD-FILE-TO-RAG] Request headers:`, headers);
      console.log(`🚀🚀🚀 [ADD-FILE-TO-RAG] Request body:`, requestBody);

      // Call the /file endpoint which calls addRagFile internally
      const response = await fetch(url, {
        method: "POST",
        headers: headers,
        body: JSON.stringify(requestBody)
      });

      console.log(`🚀🚀🚀 [ADD-FILE-TO-RAG] Response status: ${response.status}`);
      console.log(`🚀🚀🚀 [ADD-FILE-TO-RAG] Response ok: ${response.ok}`);
      console.log(`🚀🚀🚀 [ADD-FILE-TO-RAG] Response headers:`, Object.fromEntries(response.headers.entries()));

      if(response.ok) {
        const result = await response.json();
        console.log(`🚀🚀🚀 [ADD-FILE-TO-RAG] Response body:`, result);
        console.log(`✅ Successfully added file ${fileId} to RAG:`, result);
>>>>>>> WA-170_MCP
        return;
      }

      let error: any;
      try {
<<<<<<< HEAD
        error = await pendingResponse.json();
      }catch(e) {
        error = await pendingResponse.text();
      }

      console.error(`❌ Failed to add file to pending (attempt ${attempt}/${maxAttempts}):`, error);
=======
        error = await response.json();
        console.log(`🚀🚀🚀 [ADD-FILE-TO-RAG] Error response (JSON):`, error);
      }catch(e) {
        error = await response.text();
        console.log(`🚀🚀🚀 [ADD-FILE-TO-RAG] Error response (text):`, error);
      }

      console.error(`❌ [ADD-FILE-TO-RAG] Failed to add file to RAG (attempt ${attempt}/${maxAttempts}):`, error);
>>>>>>> WA-170_MCP

      if(attempt < maxAttempts) {
        // Add jitter to prevent thundering herd
        const jitter = Math.random() * 500;
        const delay = baseDelay + jitter;
        console.log(`🔄 Retrying in ${Math.round(delay/1000)} seconds...`);
        await new Promise(resolve=>setTimeout(resolve, delay));
      } else {
<<<<<<< HEAD
        console.warn(`⚠️ All attempts to add file to pending failed, continuing anyway`);
        return; // Continue with the workflow even if this step fails
      }
    }catch(error) {
      console.error(`❌ Error adding file to pending (attempt ${attempt}/${maxAttempts}):`, error);
=======
        console.error(`❌ All attempts to add file to RAG failed`);
        throw new Error(`Failed to add file ${fileId} to RAG ${ragId} after ${maxAttempts} attempts: ${JSON.stringify(error)}`);
      }
    }catch(error) {
      console.error(`❌ Error adding file to RAG (attempt ${attempt}/${maxAttempts}):`, error);
>>>>>>> WA-170_MCP

      if(attempt < maxAttempts) {
        // Add jitter to prevent thundering herd
        const jitter = Math.random() * 500;
        const delay = baseDelay + jitter;
        console.log(`🔄 Retrying in ${Math.round(delay/1000)} seconds...`);
        await new Promise(resolve=>setTimeout(resolve, delay));
      } else {
<<<<<<< HEAD
        console.warn(`⚠️ All attempts to add file to pending failed, continuing anyway`);
        return; // Continue with the workflow even if this step fails
=======
        console.error(`❌ All attempts to add file to RAG failed`);
        throw error;
>>>>>>> WA-170_MCP
      }
    }
  }
}

/**
<<<<<<< HEAD
 * Moves file to success with robust error handling
 */
async function moveFileToSuccess(
=======
 * OLD WORKING APPROACH: Manually handle pending/success flow
 * This is the approach from the old working workflow
 */
async function addFileToRagManual(
>>>>>>> WA-170_MCP
  context: StepContext,
  ragId: string,
  whitelabelId: string,
  fileId: string,
  auth0Token: string
): Promise<void>{
<<<<<<< HEAD
  const maxAttempts = 3;
  const baseDelay = 2000; // 2 seconds

  for(let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      console.log(`🔄 Moving file to success (attempt ${attempt}/${maxAttempts})`);

      const successResponse = await fetch(
        `${context.env.API_HOST}/white-label/${whitelabelId}/rag-vector/${ragId}/success-file`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${auth0Token}`
          },
          body: JSON.stringify({ fileId })
        }
      );

      if(successResponse.ok) {
        console.log(`✅ Successfully moved file ${fileId} to success`);
        return;
      }

      let error: any;
      try {
        error = await successResponse.json();
      }catch(e) {
        error = await successResponse.text();
      }

      console.error(`❌ Failed to move file to success (attempt ${attempt}/${maxAttempts}):`, error);

      if(attempt < maxAttempts) {
        // Add jitter to prevent thundering herd
        const jitter = Math.random() * 500;
        const delay = baseDelay + jitter;
        console.log(`🔄 Retrying in ${Math.round(delay/1000)} seconds...`);
        await new Promise(resolve=>setTimeout(resolve, delay));
      } else {
        console.warn(`⚠️ All attempts to move file to success failed, continuing anyway`);
        return; // Continue with the workflow even if this step fails
      }
    }catch(error) {
      console.error(`❌ Error moving file to success (attempt ${attempt}/${maxAttempts}):`, error);

      if(attempt < maxAttempts) {
        // Add jitter to prevent thundering herd
        const jitter = Math.random() * 500;
        const delay = baseDelay + jitter;
        console.log(`🔄 Retrying in ${Math.round(delay/1000)} seconds...`);
        await new Promise(resolve=>setTimeout(resolve, delay));
      } else {
        console.warn(`⚠️ All attempts to move file to success failed, continuing anyway`);
        return; // Continue with the workflow even if this step fails
      }
    }
  }
}
=======
  console.log(`🚀🚀🚀 [ADD-FILE-TO-RAG-MANUAL] Starting manual pending/success approach`);
  console.log(`📝 Linking file ${fileId} to custom RAG ${ragId}`);

  // First, add to pending (using updatePendingAtomic)
  console.log(`🚀🚀🚀 [ADD-FILE-TO-RAG-MANUAL] Step 1: Adding to pending`);
  const pendingUrl = `${context.env.API_HOST}/white-label/${whitelabelId}/rag-vector/${ragId}/pending-file`;
  console.log(`🚀🚀🚀 [ADD-FILE-TO-RAG-MANUAL] Pending URL: ${pendingUrl}`);

  const pendingResponse = await fetch(pendingUrl, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${auth0Token}`,
    },
    body: JSON.stringify({ fileId })
  });

  console.log(`🚀🚀🚀 [ADD-FILE-TO-RAG-MANUAL] Pending response status: ${pendingResponse.status}`);
  console.log(`🚀🚀🚀 [ADD-FILE-TO-RAG-MANUAL] Pending response ok: ${pendingResponse.ok}`);

  if(!pendingResponse.ok) {
    const error = await pendingResponse.json();
    console.error("❌ [ADD-FILE-TO-RAG-MANUAL] Failed to add file to pending:", error);
    throw new Error(`Failed to add file to pending: ${JSON.stringify(error)}`);
  }

  console.log(`✅ [ADD-FILE-TO-RAG-MANUAL] Successfully added file to pending`);

  // After vectors are stored, move from pending to success
  console.log(`🚀🚀🚀 [ADD-FILE-TO-RAG-MANUAL] Step 2: Moving to success`);
  const successUrl = `${context.env.API_HOST}/white-label/${whitelabelId}/rag-vector/${ragId}/success-file`;
  console.log(`🚀🚀🚀 [ADD-FILE-TO-RAG-MANUAL] Success URL: ${successUrl}`);

  const successResponse = await fetch(successUrl, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${auth0Token}`,
    },
    body: JSON.stringify({ fileId })
  });

  console.log(`🚀🚀🚀 [ADD-FILE-TO-RAG-MANUAL] Success response status: ${successResponse.status}`);
  console.log(`🚀🚀🚀 [ADD-FILE-TO-RAG-MANUAL] Success response ok: ${successResponse.ok}`);

  if(!successResponse.ok) {
    const error = await successResponse.json();
    console.error("❌ [ADD-FILE-TO-RAG-MANUAL] Failed to move file to success:", error);
    throw new Error(`Failed to move file to success: ${JSON.stringify(error)}`);
  }

  console.log(`✅ [ADD-FILE-TO-RAG-MANUAL] Successfully moved file to success`);
  console.log(`✅ Successfully linked file ${fileId} to RAG ${ragId} using manual approach`);
}


>>>>>>> WA-170_MCP
