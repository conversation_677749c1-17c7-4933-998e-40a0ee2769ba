<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
    </style>
</head>
<body>
    <h1>CORS Test</h1>
    
    <div>
        <h2>Test API Endpoints</h2>
        <button id="testTrending">Test /ai-chat/trending</button>
        <button id="testHealth">Test /health</button>
    </div>
    
    <div>
        <h2>Results</h2>
        <pre id="results">Click a button to test an endpoint...</pre>
    </div>
    
    <script>
        document.getElementById('testTrending').addEventListener('click', () => {
            testEndpoint('https://fictional-spork-v6p59xxjhwxg-8082.app.github.dev/ai-chat/trending');
        });
        
        document.getElementById('testHealth').addEventListener('click', () => {
            testEndpoint('https://fictional-spork-v6p59xxjhwxg-8082.app.github.dev/health');
        });
        
        async function testEndpoint(url) {
            const resultsElement = document.getElementById('results');
            resultsElement.innerHTML = `Testing ${url}...\n`;
            
            try {
                // First, make a preflight OPTIONS request
                resultsElement.innerHTML += `\nSending OPTIONS request...\n`;
                
                try {
                    const optionsResponse = await fetch(url, {
                        method: 'OPTIONS',
                        headers: {
                            'Origin': window.location.origin,
                            'Access-Control-Request-Method': 'GET',
                            'Access-Control-Request-Headers': 'Content-Type, Authorization'
                        }
                    });
                    
                    resultsElement.innerHTML += `OPTIONS Status: ${optionsResponse.status} ${optionsResponse.statusText}\n`;
                    resultsElement.innerHTML += `OPTIONS Headers:\n`;
                    
                    for (const [key, value] of optionsResponse.headers.entries()) {
                        resultsElement.innerHTML += `  ${key}: ${value}\n`;
                    }
                    
                    resultsElement.innerHTML += `\n<span class="success">OPTIONS request successful!</span>\n`;
                } catch (error) {
                    resultsElement.innerHTML += `\n<span class="error">OPTIONS request failed: ${error.message}</span>\n`;
                }
                
                // Now make the actual GET request
                resultsElement.innerHTML += `\nSending GET request...\n`;
                
                try {
                    const response = await fetch(url, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': 'Bearer test'
                        }
                    });
                    
                    resultsElement.innerHTML += `GET Status: ${response.status} ${response.statusText}\n`;
                    resultsElement.innerHTML += `GET Headers:\n`;
                    
                    for (const [key, value] of response.headers.entries()) {
                        resultsElement.innerHTML += `  ${key}: ${value}\n`;
                    }
                    
                    if (response.ok) {
                        const data = await response.json();
                        resultsElement.innerHTML += `\nResponse Data:\n${JSON.stringify(data, null, 2)}\n`;
                        resultsElement.innerHTML += `\n<span class="success">GET request successful!</span>\n`;
                    } else {
                        resultsElement.innerHTML += `\n<span class="error">GET request failed with status ${response.status}</span>\n`;
                    }
                } catch (error) {
                    resultsElement.innerHTML += `\n<span class="error">GET request failed: ${error.message}</span>\n`;
                }
            } catch (error) {
                resultsElement.innerHTML += `\n<span class="error">Test failed: ${error.message}</span>\n`;
            }
        }
    </script>
</body>
</html>
