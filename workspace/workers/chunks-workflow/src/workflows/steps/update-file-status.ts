import { WorkflowStep } from "cloudflare:workers";
import { StepContext, VectorizeConfig } from "./types";

/**
 * Updates file status - simplified version
 */
export async function updateFileStatus(
  step: WorkflowStep,
  context: StepContext,
  fileId: string,
  status: string,
  vectorizeConfig: VectorizeConfig
): Promise<void>{
  // Sanitize fileId to ensure it's a valid MongoDB ObjectId
  let sanitizedFileId = fileId;

  // Log the original fileId for debugging
  console.log(`🔍 [UPDATE STATUS] Original fileId: ${fileId}`);

  // Check if the fileId is in a valid MongoDB ObjectId format (24 hex chars)
  const isValidObjectId = typeof fileId === 'string' && /^[0-9a-fA-F]{24}$/.test(fileId);

  if (!isValidObjectId) {
    console.warn(`⚠️ [UPDATE STATUS] Invalid ObjectId format detected: ${fileId}`);

    // If fileId contains slashes, try to extract a valid ObjectId
    if (typeof fileId === 'string' && fileId.includes('/')) {
      const parts = fileId.split('/');
      // Look for a valid ObjectId in each part
      for (const part of parts) {
        if (/^[0-9a-fA-F]{24}$/.test(part)) {
          sanitizedFileId = part;
          console.log(`✅ [UPDATE STATUS] Extracted valid ObjectId from path: ${sanitizedFileId}`);
          break;
        }
      }
    }
    // If fileId contains a valid ObjectId pattern anywhere, extract it
    else if (typeof fileId === 'string' && fileId.match(/[0-9a-fA-F]{24}/)) {
      const match = fileId.match(/([0-9a-fA-F]{24})/);
      if (match && match[1]) {
        sanitizedFileId = match[1];
        console.log(`✅ [UPDATE STATUS] Extracted valid ObjectId from string: ${sanitizedFileId}`);
      }
    }

    // If we still don't have a valid ObjectId, generate a valid one
    if (!(/^[0-9a-fA-F]{24}$/.test(sanitizedFileId))) {
      // Check if there's an originalFileId property in the context that might contain a valid id
      let originalFileId;

      // Try to extract originalFileId from context - could be in different places
      try {
        if (context.originalFileId) {
          originalFileId = context.originalFileId;
        } else if (context.params?.originalFileId) {
          originalFileId = context.params.originalFileId;
        } else if (context.params?.files && Array.isArray(context.params.files) && context.params.files.length > 0) {
          // Try to get from files array if it exists
          originalFileId = context.params.files[0].originalFileId;
        }
        console.log(`🔍 [UPDATE STATUS] Found potential originalFileId: ${originalFileId}`);
      } catch (error) {
        console.warn(`⚠️ [UPDATE STATUS] Error accessing context for originalFileId: ${error.message}`);
      }

      if (originalFileId && typeof originalFileId === 'string' && /^[0-9a-fA-F]{24}$/.test(originalFileId)) {
        // Use the original file ID if it's valid
        sanitizedFileId = originalFileId;
        console.log(`✅ [UPDATE STATUS] Using valid originalFileId: ${sanitizedFileId}`);
      } else {
        // As a last resort, check if the first 24 characters of sanitizedFileId are mostly hex
        // This is a heuristic - if most characters are hex, we'll try using it
        const firstPart = sanitizedFileId.substring(0, 24);
        const hexCharCount = (firstPart.match(/[0-9a-fA-F]/g) || []).length;

        if (hexCharCount >= 20) { // If at least 20/24 chars are hex, it might be usable
          sanitizedFileId = firstPart;
          console.log(`⚠️ [UPDATE STATUS] Using first 24 chars as ObjectId: ${sanitizedFileId}`);
        } else {
          // Generate a new valid ObjectId as last resort
          sanitizedFileId = Math.random().toString(16).substring(2, 10) +
                           Date.now().toString(16) +
                           Math.random().toString(16).substring(2, 8);

          console.warn(`⚠️ [UPDATE STATUS] Generated new ObjectId as fallback: ${sanitizedFileId}`);
        }
      }
    }
  }

  // Use the sanitized fileId for the API call
  fileId = sanitizedFileId;
  await step.do(`updateFileStatus_${status}`, async ()=>{
    console.log(`🔄 Updating file status: ${fileId} -> ${status}`);

    // Early return if fileId is empty to prevent fetch calls
    if(!fileId) {
      console.error("❌ Cannot update file status: Invalid file ID (empty)");
      return;
    }

    // Ensure we have the required config values
    if(!vectorizeConfig || !vectorizeConfig.whitelabelId || !vectorizeConfig.auth0Token) {
      console.error("❌ Cannot update file status: Missing required configuration");
      return;
    }

    // Enhanced implementation with multiple retries
    const maxAttempts = 5;
    const baseDelay = 2000; // 2 seconds

    for(let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        console.log(`🔄 Attempt ${attempt}/${maxAttempts} to update file status to ${status}`);

        // Assemble the API endpoint URL
        const url = `${context.env.API_HOST}/white-label/${vectorizeConfig.whitelabelId}/rag-vector/files/${fileId}/status`;
        console.log(`🔄 Using API endpoint: ${url}`);

        // Prepare headers
        const headers: Record<string, string> = {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${vectorizeConfig.auth0Token}`,
          "X-Retry-Attempt": `${attempt}/${maxAttempts}`,
          "X-Workflow-ID": fileId
        };

<<<<<<< HEAD
        // In BARE METAL MODE, we need to add special headers for local development
        if(context.env.ENVIRONMENT === 'local' || context.env.ENVIRONMENT === 'development') {
=======
        // In BARE METAL MODE, we need to add special headers for local/dev/stage environments
        if(context.env.ENVIRONMENT === 'local' || context.env.ENVIRONMENT === 'development' || context.env.ENVIRONMENT === 'stage') {
>>>>>>> WA-170_MCP
          if (context.env.CLOUDFLARE_WORKER_X_AUTH_DEV) {
            headers["cloudflare-worker-x-dev-auth"] = context.env.CLOUDFLARE_WORKER_X_AUTH_DEV;
            headers["x-worker-local-dev"] = "true";
            console.log(`🔑 Added Cloudflare Worker dev auth headers for status update`);
          }
        }

        // Make the API request
        const response = await fetch(url, {
          method: "POST",
          headers,
          body: JSON.stringify({ status })
        });

        // Handle the response
        if(response.ok) {
          console.log(`✅ File status updated successfully: ${fileId} -> ${status}`);
          return; // Exit on success
        } else {
          const errorData = await response.json().catch(() => null);
          const errorStatus = response.status;

          console.error(`❌ Failed to update file status to ${status} (attempt ${attempt}/${maxAttempts}):`,
            { status: errorStatus, data: errorData });

          // Determine if we should retry
          const isServerError = errorStatus >= 500;
          const isRateLimited = errorStatus === 429;
          // Consider 404 errors as retryable because we might be having a race condition
          // where the file record is created but not yet available in the database
          const isNotFound = errorStatus === 404;
          const isRetryable = isServerError || isRateLimited || isNotFound;

          if(attempt < maxAttempts && isRetryable) {
            // Add jitter to prevent thundering herd
            const jitter = Math.random() * 1000;
            // Use longer backoff for 404 errors to allow time for DB propagation
            const backoffMultiplier = isNotFound ? 3 : 2;
            const delay = baseDelay * Math.pow(backoffMultiplier, attempt - 1) + jitter;
            console.log(`🔄 Retrying in ${Math.round(delay/1000)} seconds${isNotFound ? " (not found error)" : ""}...`);
            await new Promise(resolve => setTimeout(resolve, delay));
          } else if(!isRetryable) {
            console.log(`⚠️ Non-retryable error (${errorStatus}), stopping retry attempts`);
            break;
          } else {
            // This is the last attempt and it failed
            console.warn(`⚠️ All ${maxAttempts} attempts to update file status failed, continuing workflow anyway`);
          }
        }
      } catch(error) {
        console.error(`❌ Error updating file status to ${status} (attempt ${attempt}/${maxAttempts}):`, error);

        // Check if it's a network error
        const isNetworkError = error.message && (
          error.message.includes("Network connection lost") ||
          error.message.includes("network") ||
          error.message.includes("connection") ||
          error.message.includes("ECONNREFUSED") ||
          error.message.includes("ETIMEDOUT")
        );

        if(attempt < maxAttempts) {
          // Add jitter to prevent thundering herd
          const jitter = Math.random() * 1000;
          const delay = isNetworkError
            ? baseDelay * Math.pow(3, attempt - 1) + jitter // More aggressive backoff for network errors
            : baseDelay * Math.pow(2, attempt - 1) + jitter;

          console.log(`🔄 Retrying in ${Math.round(delay/1000)} seconds${isNetworkError ? " (network error)" : ""}...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        } else {
          // This is the last attempt and it failed
          console.warn(`⚠️ All ${maxAttempts} attempts to update file status failed, continuing workflow anyway`);
        }
      }
    }
  });
}
