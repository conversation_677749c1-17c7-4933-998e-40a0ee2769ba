import { IncomingMessage, ServerResponse, Server as HttpServer } from "http";
import { Server as HttpsServer } from "https";
import { readFileSync } from "fs";

import { setupDBs } from "./setup/database";
import { setupHttpApp } from "./setup/http";

function createServer(): HttpServer | HttpsServer {
  const httpsEnabled = process.env.HTTPS === "true";

  if (httpsEnabled) {
    const sslCertPath = process.env.SSL_CERT_PATH;
    const sslKeyPath = process.env.SSL_KEY_PATH;

    if (!sslCertPath || !sslKeyPath) {
      console.warn(
        "⚠️  HTTPS enabled but SSL_CERT_PATH or SSL_KEY_PATH not provided, falling back to HTTP"
      );
      return new HttpServer();
    }

    try {
      const cert = readFileSync(sslCertPath, "utf8");
      const key = readFileSync(sslKeyPath, "utf8");

      console.log("🔒 Creating HTTPS server on port 8081");
      return new HttpsServer({ cert, key });
    } catch (error) {
      console.error(
        "❌ Failed to load SSL certificates, falling back to HTTP:",
        error
      );
      return new HttpServer();
    }
  }

  console.log("🌐 Creating HTTP server on port 8081");
  return new HttpServer();
}

function getServerPort(): number {
  return process.env.HTTP_PORT ? parseInt(process.env.HTTP_PORT) : 8081;
}

Promise.resolve()
  .then(async () => {
    await attemptExternal();
    await setupDBs();
    const httpApp = await setupHttpApp();

    const server = createServer();

    server.on("request", function (req: IncomingMessage, res: ServerResponse) {
      console.log("🌐 url: ", req.url);
      // https://stackoverflow.com/questions/8107856/how-to-determine-a-users-ip-address-in-node
      // issue is that it seems docker always returns a docker address
      console.log("🪪 remote address: ", req.socket.remoteAddress);
      httpApp(req, res);
    });

    const PORT = getServerPort();
    server.listen(PORT, () => {
      console.log(`✅ Server is running at on port: `, server.address());
    });
  })
  .catch((error) => {
    console.error("❌ Error while starting the server: ", error);
  });

import { delay } from "@divinci-ai/utils";
function attemptExternal() {
  return Promise.race([
    Promise.resolve().then(async () => {
      await delay(5 * 1000);
      throw new Error("⌛️ Timed out making external call.");
    }),
    Promise.resolve().then(async () => {
      const resp = await fetch("https://pokeapi.co/api/v2/pokemon/ditto");
      if (!resp.ok) throw new Error("😵 Bad external call. ");
      console.log("🪀 Can make external calls!");
    }),
  ]);
}
