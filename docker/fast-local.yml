name: divinci-local-fast

networks:
  local-network:
    driver: bridge

services:
<<<<<<< HEAD
  local-web-client:
    extends:
      file: base-fast.yml
      service: base-client
    working_dir: "/home/<USER>/app/workspace/clients/web"
    command: bash -c "cd /home/<USER>/app/workspace/clients/web && npm run start:dev"
    environment:
      HTTP_PORT: 8080
      HTTPS: "true"
      SSL_CERT_PATH: "/workspaces/server/private-keys/local-fast/server.crt"
      SSL_KEY_PATH: "/workspaces/server/private-keys/local-fast/server.key"
      ENVIRONMENT: "dev"
      PATH: "/home/<USER>/app/node_modules/.bin:$PATH"
=======
  # ========================================================================
  # CLIENT Servers (Essential for web access)
  # ========================================================================
  local-web-client:
    extends:
      file: base.yml
      service: base-client
    working_dir: "/home/<USER>/app/clients/web"
    environment:
      ENV_FOLDER: "/home/<USER>/app/clients/web/env"
      HTTP_PORT: 8080
    volumes:
      - ../private-keys/local:/home/<USER>/app/clients/web/env
>>>>>>> WA-170_MCP
    networks:
      - local-network
    ports:
      - 8080:8080
    healthcheck:
<<<<<<< HEAD
      test: ["CMD", "curl", "-kf", "https://localhost:8080/"]
      interval: 10s
      timeout: 5s
      retries: 3
    volumes:
      - /workspaces:/workspaces

  public-api-live:
    extends:
      file: base-fast.yml
      service: base-api
    working_dir: "/home/<USER>/app/workspace/servers/public-api-live"
    command: bash -c "cd /home/<USER>/app/workspace/servers/public-api-live && npm run start:dev"
    environment:
      LOG_DEBUG: "1"
      USE_CHUNK_WORKFLOW: "true"
      PATH: "/home/<USER>/app/node_modules/.bin:$PATH"
      ENVIRONMENT: "dev"
      REDIS_DOMAIN_HOSTNAME: "redis.dev.divinci.app"
      REDIS_PORT: "6379"
      REDIS_USERNAME: ""
      REDIS_PASSWORD: ""
      MONGO_INITDB_ROOT_USERNAME: "divinciUser1"
      MONGO_INITDB_ROOT_PASSWORD: "divinciPassword1"
      MONGO_DOMAIN_HOSTNAME: "serverlessinstance0.c4pobzg.mongodb.net"
      MONGO_IS_SRV: "1"
      MONGO_DATABASE_NAME: "divinci-development"
      CLOUDFLARE_WORKER_X_AUTH_DEV: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.4ygtkAA4RmHtGAT5jHKzpr_3HgpaVSVKQ-IqzFJA7TI2"
      HTTPS: "true"
      SSL_CERT_PATH: "/workspaces/server/private-keys/local-fast/server.crt"
      SSL_KEY_PATH: "/workspaces/server/private-keys/local-fast/server.key"
      ENABLE_MTLS: "true"
      MTLS_CERT_DIR: "/workspaces/server/private-keys/local-fast"
    networks:
      local-network:
        aliases:
          - public-api-live.divinci.local
    ports:
      - 9080:8080
    healthcheck:
      test: ["CMD", "curl", "-kf", "https://localhost:8080/health"]
      interval: 10s
      timeout: 5s
      retries: 3
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "localhost:127.0.0.1"
    volumes:
      - /workspaces:/workspaces

  public-api-webhook:
    extends:
      file: base-fast.yml
      service: base-api
    working_dir: "/home/<USER>/app/workspace/servers/public-api-webhook"
    command: bash -c "cd /home/<USER>/app/workspace/servers/public-api-webhook && npm run start:dev"
    environment:
      LOG_DEBUG: "1"
      USE_CHUNK_WORKFLOW: "true"
      PATH: "/home/<USER>/app/node_modules/.bin:$PATH"
      ENVIRONMENT: "dev"
      REDIS_DOMAIN_HOSTNAME: "redis.dev.divinci.app"
      REDIS_PORT: "6379"
      REDIS_USERNAME: ""
      REDIS_PASSWORD: ""
      MONGO_INITDB_ROOT_USERNAME: "divinciUser1"
      MONGO_INITDB_ROOT_PASSWORD: "divinciPassword1"
      MONGO_DOMAIN_HOSTNAME: "serverlessinstance0.c4pobzg.mongodb.net"
      MONGO_IS_SRV: "1"
      MONGO_DATABASE_NAME: "divinci-development"
      CLOUDFLARE_WORKER_X_AUTH_DEV: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.4ygtkAA4RmHtGAT5jHKzpr_3HgpaVSVKQ-IqzFJA7TI2"
      HTTPS: "true"
      SSL_CERT_PATH: "/workspaces/server/private-keys/local-fast/server.crt"
      SSL_KEY_PATH: "/workspaces/server/private-keys/local-fast/server.key"
      ENABLE_MTLS: "true"
      MTLS_CERT_DIR: "/workspaces/server/private-keys/local-fast"
    networks:
      local-network:
        aliases:
          - public-api-webhook.divinci.local
    ports:
      - 8082:8080
    healthcheck:
      test: ["CMD", "curl", "-kf", "https://localhost:8080/health"]
      interval: 10s  
      timeout: 5s
=======
      test: ["CMD", "curl", "-f", "http://localhost:8080/"]

  # ========================================================================
  # API Servers (Using existing base services for now)
  # ========================================================================
  local-api:
    extends:
      file: base.yml
      service: base-api
    working_dir: "/home/<USER>/app/servers/public-api"
    environment:
      ENV_FOLDER: "/home/<USER>/app/servers/public-api/env"
      HTTP_PORT: 8080
      LOG_DEBUG: "1"
      USE_CHUNK_WORKFLOW: "true"
      # MinIO credentials for local development
      CLOUDFLARE_AUDIO_S3: "http://minio.divinci.local:9000"
      CLOUDFLARE_AUDIO_ACCESS_KEY: "minioadmin"
      CLOUDFLARE_AUDIO_SECRET_ACCESS_KEY: "minioadmin"
      CLOUDFLARE_AUDIO_PUBLIC_URL: "http://minio.divinci.local:9000"
      LOCAL_AUDIO_PUBLIC_URL: "http://minio.divinci.local:9000"
      AWS_S3_FORCE_PATH_STYLE: "true"
      MINIO_ENDPOINT: "http://minio.divinci.local:9000"
      MINIO_ROOT_USER: "minioadmin"
      MINIO_ROOT_PASSWORD: "minioadmin"
      CLOUDFLARE_WORKER_X_AUTH_DEV: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.4ygtkAA4RmHtGAT5jHKzpr_3HgpaVSVKQ-IqzFJA7TI2"
    volumes:
      - ../private-keys/local:/home/<USER>/app/servers/public-api/env
    networks:
      local-network:
        aliases:
          - local-api.divinci.local
    ports:
      - 9080:8080
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/"]
    depends_on:
      local-mongodb:
        condition: service_started
      local-redis:
        condition: service_started
      local-minio:
        condition: service_healthy
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "localhost:127.0.0.1"
      - "minio.divinci.local:host-gateway"

  # ========================================================================
  # DATABASE (Essential services)
  # ========================================================================
  local-mongodb:
    extends:
      file: base.yml
      service: base-mongodb
    env_file:
      - ../private-keys/local/mongodb.env
    volumes:
      - ../hidden/mongodb:/data/db
    networks:
      - local-network
    ports:
      - "27017:27017"

  local-redis:
    extends:
      file: base.yml
      service: base-redis
    env_file:
      - ../private-keys/local/redis.env
    volumes:
      - ../hidden/redis:/data
    networks:
      - local-network

  # ========================================================================
  # LOCAL S3 - MINIO (Essential for file storage)
  # ========================================================================
  local-minio:
    image: minio/minio
    hostname: minio
    domainname: divinci.local
    ports:
      - "9000:9000"
      - "9001:9001"
    networks:
      local-network:
        aliases:
          - minio.divinci.local
          - local-minio
    volumes:
      - minio_data:/data
      - minio_config:/root/.minio
      - ../deploy/util/minio-entrypoint-updated.sh:/minio-entrypoint.sh
      - ../deploy/util:/mnt/util
    env_file:
      - ../private-keys/local/minio.env
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    entrypoint: ["/bin/sh"]
    command: ["/minio-entrypoint.sh"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
>>>>>>> WA-170_MCP
      retries: 3
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "localhost:127.0.0.1"
<<<<<<< HEAD
    volumes:
      - /workspaces:/workspaces
=======

volumes:
  minio_data:
  minio_config:
>>>>>>> WA-170_MCP
