name: divinci-local

networks:
  local-network:
    driver: bridge

services:
  # ========================================================================
  # CLIENT Servers (1, web)
  # ========================================================================
  local-web-client:
    extends:
      file: base.yml
      service: base-client
    working_dir: "/home/<USER>/app/clients/web"
    environment:
      ENV_FOLDER: "/home/<USER>/app/clients/web/env"
      HTTP_PORT: 8080
    volumes:
      - ../private-keys/local:/home/<USER>/app/clients/web/env
    networks:
      - local-network
    ports:
      - 8080:8080
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/"]

  local-embed-client:
    extends:
      file: base.yml
      service: base-client
    working_dir: "/home/<USER>/app/clients/embed"
    environment:
      ENV_FOLDER: "/home/<USER>/app/clients/embed/env"
      HTTP_PORT: 8081
      STATIC_PORT: 8082
    volumes:
      - ../private-keys/local:/home/<USER>/app/clients/embed/env
    networks:
      - local-network
    ports:
      - 8081:8081
      - 8082:8082
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/"]

  # ========================================================================
  # API Servers (3, api, live, webhook)
  # ========================================================================

  local-api:
    extends:
      file: base.yml
      service: base-api
    working_dir: "/home/<USER>/app/servers/public-api"
    environment:
      ENV_FOLDER: "/home/<USER>/app/servers/public-api/env"
      HTTP_PORT: 8080
      LOG_DEBUG: "1"
      USE_CHUNK_WORKFLOW: "true"
      # Force R2 storage for E2E testing with external APIs (set to "false" to use local MinIO for development)
      FORCE_R2_STORAGE: "false"
      # Cloudflare R2 credentials for forced R2 storage
      CLOUDFLARE_AUDIO_S3: "https://14a6fa23390363382f378b5bd4a0f849.r2.cloudflarestorage.com"
      CLOUDFLARE_AUDIO_ACCESS_KEY: "8cadbca7d973a3442581c875c6725c2c"
      CLOUDFLARE_AUDIO_SECRET_ACCESS_KEY: "df8d58584f5373255cba114d2c5fd603f86dd7fdbcf15b292a3d347ff62e3ebc"
      CLOUDFLARE_AUDIO_PUBLIC_URL: "https://pub-2a04833210c8489198d3b76b8fb4f12d.r2.dev"
      LOCAL_AUDIO_PUBLIC_URL: "http://minio.divinci.local:9000"
      # Fix for S3 endpoint format
      AWS_S3_FORCE_PATH_STYLE: "true"
      # Audio service URLs
      DIVINCI_AUDIO_DIARIZER_PYANNOTE_URL: "http://audio-speak-dia-pyannote:5001"
      DIVINCI_AUDIO_SPLITTER_FFMPEG_URL: "http://audio-splitter-ffmpeg:5002"
      # Force the correct MinIO endpoint
      MINIO_ENDPOINT: "http://minio.divinci.local:9000"
      # Add MinIO root credentials
      MINIO_ROOT_USER: "minioadmin"
      MINIO_ROOT_PASSWORD: "minioadmin"
      # Add Cloudflare Worker dev auth
      CLOUDFLARE_WORKER_X_AUTH_DEV: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.4ygtkAA4RmHtGAT5jHKzpr_3HgpaVSVKQ-IqzFJA7TI2"
      # Official Pyannote API credentials
      PYANNOTE_APIKEY: "sk_f9c6c510b4ee45099506d7c615689fbb"
    volumes:
      - ../private-keys/local:/home/<USER>/app/servers/public-api/env
    networks:
      local-network:
        aliases:
          - local-api.divinci.local
    ports:
      - 9080:8080
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/"]
    links:
      - local-d1-rag
      - audio-speak-dia-pyannote
      - audio-splitter-ffmpeg
      - local-mongodb
      - local-redis
      - local-minio
    depends_on:
      local-d1-rag:
        condition: service_started
      audio-speak-dia-pyannote:
        condition: service_started
      audio-splitter-ffmpeg:
        condition: service_started
      local-mongodb:
        condition: service_started
      local-redis:
        condition: service_started
      local-minio:
        condition: service_healthy
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "localhost:127.0.0.1"
      - "minio.divinci.local:host-gateway"

  local-api-live:
    extends:
      file: base.yml
      service: base-api
    working_dir: "/home/<USER>/app/servers/public-api-live"
    environment:
      ENV_FOLDER: "/home/<USER>/app/servers/public-api-live/env"
    volumes:
      - ../private-keys/local:/home/<USER>/app/servers/public-api-live/env
    networks:
      - local-network
    ports:
      - 9081:8080
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/"]
    links:
      - local-mongodb
      - local-redis
    depends_on:
      - local-mongodb
      - local-redis

  local-api-webhook:
    extends:
      file: base.yml
      service: base-api
    working_dir: "/home/<USER>/app/servers/public-api-webhook"
    environment:
      ENV_FOLDER: "/home/<USER>/app/servers/public-api-webhook/env"
    volumes:
      - ../private-keys/local:/home/<USER>/app/servers/public-api-webhook/env
    networks:
      - local-network
    ports:
      - 9082:8080
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/"]
    links:
      - local-mongodb
      - local-redis
    depends_on:
      - local-mongodb
      - local-redis

  # ========================================================================
  # WORKER (d1-rag, open-parse, chunks-vectorized, mcp-server)
  # ========================================================================

  local-mcp-server:
    extends:
      file: base.yml
      service: base-cloudflare-worker
    volumes:
      - ../workspace/workers/mcp-server:/app
      - ../private-keys/local/cloudflare.env:/app/.dev.vars
    command: |
      /bin/bash -c 'npm install --no-package-lock &&
      npx wrangler@4.20.0 dev --env local --ip 0.0.0.0 --port 8793 --inspector-port 8794'
    networks:
      local-network:
        aliases:
          - mcp-server.divinci.local
    environment:
      NODE_ENV: development
      WRANGLER_SEND_METRICS: "false"
      DIVINCI_API_BASE_URL: "http://local-api:8080"
    ports:
      - 8793:8793
      - 8794:8794 # for inspector
    depends_on:
      - local-api

  local-d1-rag:
    extends:
      file: base.yml
      service: base-cloudflare-worker
    volumes:
      - ../workspace/workers/d1-doc-elements:/app
      - ../private-keys/local/cloudflare.env:/app/.dev.vars
    command: |
      /bin/bash -c 'npm install --no-package-lock &&
      npx wrangler@4.20.0 dev --env local --ip 0.0.0.0 --port 8787 --inspector-port 8788'
    networks:
      - local-network
    environment:
      NODE_ENV: development
      WRANGLER_SEND_METRICS: "false"
    ports:
      - 8787:8787
      - 8788:8788 # for inspector

  # ========================================================================
  # ====== UNSTRUCTURED WORKER =============================================
  # ====== WORK IN PROGRESS ================================================
  # ========================================================================
  # local-rag-chunker-unstructured:
  #   extends:
  #     file: base.yml
  #     service: base-cloudflare-worker
  #   volumes:
  #     - ../workspace/workers/rag-chunker-unstructured:/app
  #     - ../private-keys/local/cloudflare.env:/app/.dev.vars
  #   command: |
  #     /bin/bash -c 'npm install --no-package-lock &&
  #     npx wrangler@4.20.0 dev --env local --ip 0.0.0.0 --port 8789 --inspector-port 8790'
  #   networks:
  #     - local-network
  #   environment:
  #     PORT: "8789"
  #     NODE_ENV: development
  #     WRANGLER_SEND_METRICS: "false"
  #   ports:
  #     - "8789:8789"
  #     - "8790:8790"  # for inspector

  # ========================================================================
  # OPEN-PARSE
  # RAG DOCUMENT CHUNKER
  # ========================================================================
  local-open-parse:
    build:
      context: ../workspace/workers/open-parse
      dockerfile: open-parse.Dockerfile
    volumes:
      # Temp File Storage
      - ../hidden/servers/open-parse:/openparse-tmp
      # Private Keys
      - ../private-keys/local/cloudflare.env:/home/<USER>/app/env/cloudflare.env
      - ../private-keys/local/open-parse.env:/home/<USER>/app/env/open-parse.env
      - ../private-keys/local/minio.env:/home/<USER>/app/env/minio.env
      # Mount local open-parse for development
      - ../../open-parse:/home/<USER>/open-parse

    environment:
      OPEN_PARSE_HTTP_PORT: 8084
      ENV_FOLDER: "/home/<USER>/app/env"
      PYTHONUNBUFFERED: 1
      PYTHONPATH: "/home/<USER>/app:/home/<USER>/open-parse/src"
      LOG_LEVEL: DEBUG
      OPENPARSE_LOG_LEVEL: DEBUG
      ENVIRONMENT: "development"
      # Force R2 storage for E2E testing with external APIs (set to "false" to use local MinIO for development)
      FORCE_R2_STORAGE: "false"
      # Configure explicit MinIO access inside the container
      # Make MinIO credentials available to the container - use the same credentials as MinIO service
      MINIO_ROOT_USER: "minioadmin"
      MINIO_ROOT_PASSWORD: "minioadmin"
      # Use the same credentials as the MinIO service for reliable connectivity
      # MinIO connection settings - using host.docker.internal for container-to-host communication
      # Override env file setting to use host.docker.internal for accessing host MinIO from container
      MINIO_HOST: "host.docker.internal"
      MINIO_PORT: "9000"
      MINIO_ENDPOINT: "http://host.docker.internal:9000"
      # Set an environment variable to force the use of the Docker network
      USE_LOCAL_MINIO: "true"
      # Force Python requests to use the Docker network
      REQUESTS_CA_BUNDLE: "/etc/ssl/certs/ca-certificates.crt"
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "localhost:127.0.0.1"
      - "minio.divinci.local:host-gateway"
    networks:
      local-network:
        aliases:
          - open-parse.divinci.local
    ports:
      - 19002:8084
    # Add health check to verify MinIO connectivity
    healthcheck:
      test:
        [
          "CMD",
          "python3",
          "-c",
          "import boto3; boto3.client('s3', endpoint_url='http://minio.divinci.local:9000', aws_access_key_id='minioadmin', aws_secret_access_key='minioadmin').list_buckets()",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    depends_on:
      local-minio:
        condition: service_healthy

  # ========================================================================
  # RAG WORKFLOW WORKER
  # ========================================================================
  local-chunks-vectorized:
    extends:
      file: base.yml
      service: base-cloudflare-worker
    volumes:
      - ../workspace/workers/chunks-workflow:/app
      - ../private-keys/local/cloudflare.worker.env:/app/.dev.vars
    command: |
      /bin/bash -c 'echo "Running initial commands as root to install certificates..." &&
      apt-get update && apt-get install -y ca-certificates && update-ca-certificates &&
      echo "Switching to node user for remaining operations..." &&
      su node -c "cd /app &&
        mkdir -p /home/<USER>/.wrangler/config &&
        set -a && source /app/.dev.vars && set +a &&
        echo \"api_token = \\\"$$CLOUDFLARE_API_TOKEN\\\"\" > /home/<USER>/.wrangler/config/default.toml &&
        echo \"account_id = \\\"$$CLOUDFLARE_ACCOUNT_ID\\\"\" >> /home/<USER>/.wrangler/config/default.toml &&
        # Install npm dependencies
        echo \"Installing npm dependencies...\" &&
        npm install --no-package-lock --legacy-peer-deps &&
        # Run wrangler dev
        npx wrangler@4.20.0 dev --env local --ip 0.0.0.0 --port 8791 --inspector-port 8792 --local"'
    hostname: workflow-chunks
    domainname: divinci.local
    environment:
      NODE_ENV: development
      NODE_EXTRA_CA_CERTS: /etc/ssl/certs/ca-certificates.crt
      WRANGLER_SEND_METRICS: "false"
      HOST: 0.0.0.0
      PORT: "8791"
      OPENPARSE_API_URL: http://local-open-parse:8084
      API_HOST: http://local-api:8080
      CLOUDFLARE_ACCOUNT_ID: 4a6fa23390363382f378b5bd4a0f849
      CLOUDFLARE_D1_API_URL: http://local-d1-rag:8787
      # Force R2 storage for E2E testing with external APIs (set to "false" to use local MinIO for development)
      FORCE_R2_STORAGE: "false"
      # Cloudflare R2 credentials for forced R2 storage
      CLOUDFLARE_AUDIO_S3: "https://14a6fa23390363382f378b5bd4a0f849.r2.cloudflarestorage.com"
      CLOUDFLARE_AUDIO_ACCESS_KEY: "8cadbca7d973a3442581c875c6725c2c"
      CLOUDFLARE_AUDIO_SECRET_ACCESS_KEY: "df8d58584f5373255cba114d2c5fd603f86dd7fdbcf15b292a3d347ff62e3ebc"
      # MinIO credentials - using root credentials (fallback)
      R2_BUCKET_URL: http://minio.divinci.local:9000
      R2_ACCESS_KEY_ID: minioadmin
      R2_SECRET_ACCESS_KEY: minioadmin
    user: root
    ports:
      - 8791:8791
      - 8792:8792
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "localhost:127.0.0.1"
      - "minio.divinci.local:host-gateway"
    depends_on:
      local-minio:
        condition: service_healthy
      local-d1-rag:
        condition: service_started
      local-open-parse:
        condition: service_started
    networks:
      local-network:
        aliases:
          - workflow-chunks.divinci.local

  # pdf-to-chunks:
  #   build:
  #     context: ../workspace/workers/open-parse
  #     dockerfile: open-parse.Dockerfile
  #   volumes:
  #     - ../hidden/workers/open-parse:/openparse-tmp
  #   environment:
  #     HTTP_PORT: 5000
  #   networks:
  #     - local-network
  #   ports:
  #     - 10080:5000

  # ========================================================================
  # Ollama
  # OFFLINE DIVINCI
  # LARGE MEMORY FOOTPRINT
  # ========================================================================

  # local-ollama:
  #   environment:
  #     OLLAMA_HOST: 0.0.0.0
  #     OLLAMA_DEBUG: 0
  #     OLLAMA_NOHISTORY: 1
  #     OLLAMA_FLASH_ATTENTION: 1
  #   build:
  #     context: ../deploy/docker/ollama
  #     dockerfile: ollama.Dockerfile
  #   volumes:
  #     - ../hidden/ollama_data:/root/.ollama  # Changed from ./hidden to ../hidden for consistency
  #   networks:
  #     - local-network
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:11434/"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #   ports:
  #     - "11434:11434"

  audio-speak-dia-pyannote:
    build:
      context: ../workspace/workers/audio-speaker-diarization@pyannote
      dockerfile: python.Dockerfile
    volumes:
      # Temp File Storage
      - ../hidden/workers/audio-speaker-diarization@pyannote:/file-tmp
      # Private Keys
      - ../private-keys/local:/home/<USER>/app/env
    environment:
      HTTP_PORT: 5001
      ENV_FOLDER: "/home/<USER>/app/env"
      # MinIO credentials - using root credentials
      MINIO_ENDPOINT: "http://minio.divinci.local:9000"
      MINIO_ROOT_USER: "minioadmin"
      MINIO_ROOT_PASSWORD: "minioadmin"
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "localhost:127.0.0.1"
      - "minio.divinci.local:host-gateway"
    networks:
      local-network:
        aliases:
          - audio-speak-dia-pyannote.divinci.local
    ports:
      - 19000:5001
    depends_on:
      local-minio:
        condition: service_healthy

  audio-splitter-ffmpeg:
    build:
      context: ../workspace/workers/audio-splitter@ffmpeg
      dockerfile: typescript.Dockerfile
    volumes:
      # Private Keys
      - ../private-keys/local:/home/<USER>/env
      # Source code for development
      - ../workspace/workers/audio-splitter@ffmpeg:/home/<USER>/app
    environment:
      HTTP_PORT: 5002
      ENV_FOLDER: "/home/<USER>/env"
      NODE_ENV: "development"
      # Force R2 storage for E2E testing with external APIs (set to "false" to use local MinIO for development)
      FORCE_R2_STORAGE: "false"
      # Cloudflare R2 credentials for forced R2 storage (use current credentials from env file)
      CLOUDFLARE_AUDIO_S3: "https://14a6fa23390363382f378b5bd4a0f849.r2.cloudflarestorage.com"
      CLOUDFLARE_AUDIO_ACCESS_KEY: "8cadbca7d973a3442581c875c6725c2c"
      CLOUDFLARE_AUDIO_SECRET_ACCESS_KEY: "df8d58584f5373255cba114d2c5fd603f86dd7fdbcf15b292a3d347ff62e3ebc"
      # MinIO credentials - using root credentials (fallback)
      MINIO_ENDPOINT: "http://minio.divinci.local:9000"
      MINIO_ROOT_USER: "minioadmin"
      MINIO_ROOT_PASSWORD: "minioadmin"
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "localhost:127.0.0.1"
      - "minio.divinci.local:host-gateway"
    networks:
      local-network:
        aliases:
          - audio-splitter.divinci.local
    ports:
      - 19001:5002
    depends_on:
      local-minio:
        condition: service_healthy

  # =======================================================================
  # ==== AUDIO TRANSCRIPTION (1, whisper-large-v3) ========================
  # ==== LARGE MEMORY FOOTPRINT ===========================================
  # =======================================================================
  # audio-transcript-whisper-large-v3:
  #   build:
  #     context: ../workspace/workers/audio-transcript@whisper-large-v3
  #     dockerfile: python.Dockerfile
  #   volumes:
  #     # Temp File Storage
  #     - ../hidden/workers/audio-transcript@whisper-large-v3:/file-tmp
  #     # Private Keys
  #     - ../private-keys/local:/home/<USER>/app/env
  #   environment:
  #     HTTP_PORT: 5003
  #     ENV_FOLDER: "/home/<USER>/app/env"
  #   networks:
  #     - local-network
  #   ports:
  #     - 19003:5003

  # ========================================================================
  # VECTOR DATABASE (1, qdrant)
  # ========================================================================
  local-qdrant:
    image: qdrant/qdrant
    volumes:
      - ../hidden/qdrant:/qdrant/storage
    networks:
      - local-network
    ports:
      - 6333:6333
      - 6334:6334

  # =======================================================================
  # ==== SQLITE WEB ========================
  # =======================================================================
  # sqlite-web:
  #   image: coleifer/sqlite-web
  #   container_name: sqlite-web
  #   environment:
  #     - SQLITE_DATABASE=dfo-chunks-1.db
  #   volumes:
  #     - ../workspace/workers/d1-doc-elements/dfo-chunks-1.db:/data/dfo-chunks-1.db
  #   ports:
  #     - 8888:8080
  #   restart: unless-stopped

  # ========================================================================
  # DATABASE (2, mongodb, redis)
  # ========================================================================
  local-mongodb:
    extends:
      file: base.yml
      service: base-mongodb
    env_file:
      - ../private-keys/local/mongodb.env
    volumes:
      - ../hidden/mongodb:/data/db
    networks:
      - local-network
    ports:
      - "27017:27017" # This line maps port 27017 in the container to port 27017 on your host machine

  mongo-express:
    image: mongo-express
    networks:
      - local-network
    links:
      - local-mongodb
    env_file:
      - ../private-keys/local/mongodb.env
    ports:
      - 8091:8081

  local-redis:
    extends:
      file: base.yml
      service: base-redis
    env_file:
      - ../private-keys/local/redis.env
    volumes:
      - ../hidden/redis:/data
    networks:
      - local-network

  # local-postgres:
  #   extends:
  #     file: base.yml
  #     service: base-postgres
  #   env_file:
  #     - ../private-keys/local/postgres.env
  #   volumes:
  #     - ../hidden/postgres:/data
  #   networks:
  #     - local-network

  # =======================================================================
  # ==== REDIS VIEWER ========================
  # =======================================================================
  # redis-viewer:
  #   image: marian/rebrow
  #   networks:
  #     - local-network
  #   links:
  #     - local-redis
  #   ports:
  #     - 8092:5001

  # =======================================================================
  # ==== NGROK TUNNEL ========================
  # ==== LOCAL DIVINCI ========================
  # =======================================================================
  # tunnel:
  #   image: ngrok/ngrok:latest
  #   restart: unless-stopped
  #   command: start webhook --config /etc/ngrok.yml
  #   volumes:
  #     - ../private-keys/local/ngrok.yml:/etc/ngrok.yml
  #   links:
  #     - api-webhook
  #   ports:
  #     - 4040:4040

  # =======================================================================
  # ==== LOCAL S3 - MINIO ========================
  # ==== OFFLINE DIVINCI ========================
  # =======================================================================
  local-minio:
    image: minio/minio
    hostname: minio
    domainname: divinci.local
    ports:
      - "9000:9000"
      - "9001:9001"
    networks:
      local-network:
        aliases:
          - minio.divinci.local
          - local-minio
    volumes:
      - minio_data:/data
      - minio_config:/root/.minio # Add this volume to persist MinIO config including users
      - ../deploy/util/minio-entrypoint-updated.sh:/minio-entrypoint.sh
      - ../deploy/util:/mnt/util
    env_file:
      - ../private-keys/local/minio.env
    environment:
      # Set fixed credentials that will persist
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
      # MINIO_ACCESS_KEY and MINIO_SECRET_KEY are deprecated, use MINIO_ROOT_USER and MINIO_ROOT_PASSWORD
    entrypoint: ["/bin/sh"]
    command: ["/minio-entrypoint.sh"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "localhost:127.0.0.1"

volumes:
  minio_data:
  minio_config:
