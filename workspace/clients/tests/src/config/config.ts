/**
 * Test Configuration
<<<<<<< HEAD
 *
=======
 * 
>>>>>>> WA-170_MCP
 * This module provides configuration for tests.
 */

import { loadEnv } from './env';

// Load environment variables
loadEnv();

/**
 * Configuration interface
 */
export interface Config {
  /** Base URL for the web application */
  baseUrl: string;
  /** Base URL for the API */
  apiBaseUrl: string;
  /** Timeout for API requests in milliseconds */
  apiTimeout: number;
  /** Maximum number of retries for API requests */
  apiRetryCount: number;
  /** Delay between retries for API requests in milliseconds */
  apiRetryDelay: number;
  /** Whether to log API requests and responses */
  logApiCalls: boolean;
  /** Auth0 configuration */
  auth0: {
    /** Auth0 domain */
    domain: string;
    /** Auth0 client ID */
    clientId: string;
    /** Auth0 audience */
    audience: string;
  };
  /** Resource pool configuration */
  resourcePool: {
    /** Maximum number of resources to keep in the pool */
    maxSize: number;
    /** Maximum age of resources in milliseconds before they are cleaned up */
    maxAge: number;
    /** Interval in milliseconds for cleaning up expired resources */
    cleanupIntervalMs: number;
    /** Whether to log debug information */
    debug: boolean;
  };
}

/**
 * Default configuration
 */
const defaultConfig: Config = {
  baseUrl: 'http://localhost:8080',
<<<<<<< HEAD
  apiBaseUrl: 'http://localhost:18081',
  apiTimeout: 30000,
  apiRetryCount: 3,
  apiRetryDelay: 1000,
  logApiCalls: true,
  auth0: {
    domain: 'divinci-dev.us.auth0.com',
    clientId: process.env.AUTH0_CLIENT_ID || 'your-client-id',
    audience: 'chat.divinci.app:8081' // Match the API server's expected audience
=======
  apiBaseUrl: 'http://localhost:9080',
  apiTimeout: 30000,
  apiRetryCount: 3,
  apiRetryDelay: 1000,
  logApiCalls: false,
  auth0: {
    domain: 'divinci-dev.us.auth0.com',
    clientId: process.env.AUTH0_CLIENT_ID || 'your-client-id',
    audience: 'https://api.divinci.app'
>>>>>>> WA-170_MCP
  },
  resourcePool: {
    maxSize: 10,
    maxAge: 30 * 60 * 1000, // 30 minutes
    cleanupIntervalMs: 5 * 60 * 1000, // 5 minutes
    debug: false
  }
};

/**
 * Environment-specific configuration overrides
 */
const envConfig: Partial<Config> = {
  baseUrl: process.env.BASE_URL || defaultConfig.baseUrl,
  apiBaseUrl: process.env.API_BASE_URL || defaultConfig.apiBaseUrl,
  apiTimeout: process.env.API_TIMEOUT ? parseInt(process.env.API_TIMEOUT) : defaultConfig.apiTimeout,
  apiRetryCount: process.env.API_RETRY_COUNT ? parseInt(process.env.API_RETRY_COUNT) : defaultConfig.apiRetryCount,
  apiRetryDelay: process.env.API_RETRY_DELAY ? parseInt(process.env.API_RETRY_DELAY) : defaultConfig.apiRetryDelay,
  logApiCalls: process.env.LOG_API_CALLS === 'true' || defaultConfig.logApiCalls,
  auth0: {
    domain: process.env.AUTH0_DOMAIN || defaultConfig.auth0.domain,
    clientId: process.env.AUTH0_CLIENT_ID || defaultConfig.auth0.clientId,
    audience: process.env.AUTH0_AUDIENCE || defaultConfig.auth0.audience
  },
  resourcePool: {
    maxSize: process.env.RESOURCE_POOL_MAX_SIZE ? parseInt(process.env.RESOURCE_POOL_MAX_SIZE) : defaultConfig.resourcePool.maxSize,
    maxAge: process.env.RESOURCE_POOL_MAX_AGE ? parseInt(process.env.RESOURCE_POOL_MAX_AGE) : defaultConfig.resourcePool.maxAge,
    cleanupIntervalMs: process.env.RESOURCE_POOL_CLEANUP_INTERVAL ? parseInt(process.env.RESOURCE_POOL_CLEANUP_INTERVAL) : defaultConfig.resourcePool.cleanupIntervalMs,
    debug: process.env.RESOURCE_POOL_DEBUG === 'true' || defaultConfig.resourcePool.debug
  }
};

/**
 * Merged configuration
 */
export const config: Config = {
  ...defaultConfig,
  ...envConfig,
  auth0: {
    ...defaultConfig.auth0,
    ...envConfig.auth0
  },
  resourcePool: {
    ...defaultConfig.resourcePool,
    ...envConfig.resourcePool
  }
};

/**
 * Override configuration for tests
 * @param overrides Configuration overrides
 */
export function overrideConfig(overrides: Partial<Config>): void {
  Object.assign(config, overrides);
}

/**
 * Reset configuration to default values
 */
export function resetConfig(): void {
  Object.assign(config, defaultConfig, envConfig);
}
