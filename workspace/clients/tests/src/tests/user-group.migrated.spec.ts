/**
 * User Group Tests (Migrated)
<<<<<<< HEAD
 *
=======
 * 
>>>>>>> WA-170_MCP
 * This file contains tests for user groups using the hybrid StorageState + Resource Pooling architecture.
 * It has been migrated from the original user-group.spec.ts file.
 */

import { test, expect } from '@playwright/test';
import { getUserGroup, releaseUserGroup } from '../resources/user-group-pool';
<<<<<<< HEAD
import { addUserInvitationToGroup, getUserGroup as getMockUserGroup } from '../api/user-group-api';
=======
import { addUserInvitationToGroup } from '../api/user-group-api';
>>>>>>> WA-170_MCP
import { randomEmail } from '../utils/test-utils';

// Define a test suite for user group management
test.describe('User Group Management', () => {
  // Test creating a user group with admin role
  test('should create a user group with admin role', async () => {
    // Get a user group from the pool
    const userGroup = await getUserGroup('admin');
<<<<<<< HEAD

=======
    
>>>>>>> WA-170_MCP
    try {
      // Verify the user group properties
      expect(userGroup._id).toBeDefined();
      expect(userGroup.name).toBeDefined();
      expect(userGroup.slug).toBeDefined();
      expect(userGroup.ownerUser).toBeDefined();
      expect(userGroup.users).toBeInstanceOf(Array);
      expect(userGroup.invitations).toBeInstanceOf(Array);
<<<<<<< HEAD

=======
      
>>>>>>> WA-170_MCP
      // Log the user group for debugging
      console.log('User Group:', userGroup);
    } finally {
      // Release the user group back to the pool
      releaseUserGroup('admin');
    }
  });
<<<<<<< HEAD

=======
  
>>>>>>> WA-170_MCP
  // Test inviting a user to a group
  test('should invite a user to a group', async () => {
    // Get a user group from the pool
    const userGroup = await getUserGroup('admin');
<<<<<<< HEAD

    try {
      // Generate a random email for the invitation
      const email = randomEmail();

      // Invite the user to the group (returns updated group)
      const updatedGroup = await addUserInvitationToGroup('admin', userGroup._id, email);

      // Verify that the invitation was added
      expect(updatedGroup.invitations).toContain(email);

=======
    
    try {
      // Generate a random email for the invitation
      const email = randomEmail();
      
      // Invite the user to the group
      await addUserInvitationToGroup('admin', userGroup._id, email);
      
      // Get the updated user group
      const updatedGroup = await getUserGroup('admin', userGroup.name);
      
      // Verify that the invitation was added
      expect(updatedGroup.invitations).toContain(email);
      
>>>>>>> WA-170_MCP
      // Log the updated user group for debugging
      console.log('Updated User Group:', updatedGroup);
    } finally {
      // Release the user group back to the pool
      releaseUserGroup('admin');
    }
  });
<<<<<<< HEAD

=======
  
>>>>>>> WA-170_MCP
  // Test that a user cannot access a group they are not a member of
  test('should not allow a user to access a group they are not a member of', async () => {
    // Get a user group from the pool with admin role
    const adminGroup = await getUserGroup('admin', 'admin-only-group');
<<<<<<< HEAD

    try {
      // Try to access the SAME group (by ID) with a regular user role
      // This should throw an error because the user role doesn't have access to the admin's group
      let error: Error | null = null;
      try {
        // Use the actual group ID instead of the name to test real authorization
        await getMockUserGroup('user', adminGroup._id);
      } catch (e) {
        error = e as Error;
      }

      // Verify that an error was thrown
      expect(error).not.toBeNull();
      expect((error as any).status).toBe(403);

=======
    
    try {
      // Try to access the group with a regular user role
      // This should throw an error
      let error: Error | null = null;
      try {
        await getUserGroup('user', 'admin-only-group');
      } catch (e) {
        error = e as Error;
      }
      
      // Verify that an error was thrown
      expect(error).not.toBeNull();
      expect((error as any).status).toBe(403);
      
>>>>>>> WA-170_MCP
      // Log the error for debugging
      console.log('Error:', error);
    } finally {
      // Release the user group back to the pool
      releaseUserGroup('admin', 'admin-only-group');
    }
  });
<<<<<<< HEAD

=======
  
>>>>>>> WA-170_MCP
  // Test creating multiple user groups
  test('should create multiple user groups', async () => {
    // Get two user groups from the pool
    const group1 = await getUserGroup('admin', 'group1');
    const group2 = await getUserGroup('admin', 'group2');
<<<<<<< HEAD

=======
    
>>>>>>> WA-170_MCP
    try {
      // Verify that the groups are different
      expect(group1._id).not.toBe(group2._id);
      expect(group1.name).toBe('group1');
      expect(group2.name).toBe('group2');
<<<<<<< HEAD

=======
      
>>>>>>> WA-170_MCP
      // Log the groups for debugging
      console.log('Group 1:', group1);
      console.log('Group 2:', group2);
    } finally {
      // Release the user groups back to the pool
      releaseUserGroup('admin', 'group1');
      releaseUserGroup('admin', 'group2');
    }
  });
});
