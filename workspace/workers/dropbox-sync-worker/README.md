# Dropbox Sync Worker

A Cloudflare Worker with Workflows that orchestrates the complete pipeline for syncing Dropbox files (especially <PERSON><PERSON>'s podcast files) into the Divinci AI RAG system.

## Overview

This worker handles the end-to-end process of:
1. **Downloading** files from Dropbox to R2 storage
2. **Processing** audio files (transcription + speaker diarization)
3. **Chunking & Vectorizing** content for RAG integration
4. **Updating** database records and file mappings

## Architecture

### Main Components

- **Main Worker** (`src/index.ts`) - HTTP endpoints for triggering and managing sync jobs
- **DropboxSyncWorkflow** (`src/workflows/dropbox-sync-workflow.ts`) - Main workflow orchestrator
- **Workflow Steps** (`src/steps/`) - Individual processing steps
- **Types** (`src/types.ts`) - TypeScript definitions

### Workflow Steps

1. **Initialize Workflow** - Validate inputs and prepare metadata
2. **Download Files** - Download from Dropbox to R2 storage
3. **Process Audio** - Transcription (Whisper) + Diarization (Pyannote)
4. **Trigger Chunks Workflow** - RAG integration via existing chunks-workflow
5. **Update Database** - Final status and file mapping updates

## API Endpoints

### `POST /trigger-sync-job`
Starts a new Dropbox sync workflow.

**Request Body:**
```json
{
  "jobId": "string",
  "whitelabelId": "string", 
  "connectionId": "string",
  "userId": "string",
  "targetType": "rag" | "fine-tuning",
  "sourceFiles": [
    {
      "dropboxPath": "/path/to/file.mp3",
      "dropboxFileId": "id:abc123",
      "fileName": "episode-001.mp3",
      "fileSize": 52428800,
      "isAudio": true,
      "isVideo": false,
      "extension": ".mp3",
      "mimeType": "audio/mpeg"
    }
  ],
  "processingConfig": {
    "ragId": "rag_id_here",
    "transcriptionTool": "whisper-large-v3",
    "diarizationTool": "pyannote",
    "chunkingTool": "unstructured"
  },
  "accessToken": "dropbox_access_token",
  "auth0Token": "api_auth_token",
  "metadata": {
    "jobName": "Dr. Fuhrman Podcast Sync",
    "description": "Sync Season 1 episodes"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "workflowId": "dropbox-sync-job123-**********",
    "jobId": "job123",
    "status": "running",
    "totalFiles": 5,
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### `GET /workflow-status/:workflowId`
Get the status of a running workflow.

### `POST /cancel-workflow/:workflowId`
Cancel a running workflow.

### `GET /workflows`
List active workflows (basic implementation).

## Environment Variables

### Required
- `ENVIRONMENT` - Environment name (local, dev, stage, production)
- `API_HOST` - Main API host URL
- `CLOUDFLARE_ACCOUNT_ID` - Cloudflare account ID

### Audio Processing
- `AUDIO_TRANSCRIPT_WORKER_URL` - Whisper transcription worker URL
- `AUDIO_DIARIZATION_WORKER_URL` - Pyannote diarization worker URL
- `CHUNKS_WORKFLOW_WORKER_URL` - Chunks workflow worker URL

### Storage
- `R2_BUCKET_URL` - R2 bucket URL for file storage

### Development
- `CLOUDFLARE_WORKER_X_AUTH_DEV` - Development authentication token
- `ALLOWED_ORIGINS` - CORS allowed origins

## Deployment

### Local Development
```bash
pnpm dev
```

### Deploy to Environments
```bash
# Development
pnpm deploy:dev

# Staging  
pnpm deploy:stage

# Production
pnpm deploy:production
```

## Integration Points

### With Main API
- Triggered by `/ux/whitelabel/:id/dropbox/sync/create` endpoint
- Calls back to update job status and file mappings
- Uses Auth0 tokens for API authentication

### With Audio Workers
- **Transcription**: Calls `audio-transcript@whisper-large-v3` worker
- **Diarization**: Calls `audio-speaker-diarization@pyannote` worker

### With Chunks Workflow
- Triggers existing `chunks-workflow` for RAG integration
- Passes transcription and diarization results for enhanced chunking

### With Storage
- Downloads files from Dropbox API
- Uploads to Cloudflare R2 buckets
- Maintains file metadata and mappings

## Error Handling

- **Retry Logic**: Built-in retries for network operations
- **Graceful Degradation**: Continues processing other files if one fails
- **Status Tracking**: Detailed status updates for each file and overall job
- **Error Reporting**: Comprehensive error messages and stack traces

## Monitoring

- **Cloudflare Analytics**: Built-in request and performance monitoring
- **Custom Logging**: Detailed console logs for debugging
- **Status Endpoints**: Real-time workflow status checking

## Example Use Case: Dr. Fuhrman Podcast

1. User connects Dropbox account via OAuth
2. User selects podcast episodes from Dropbox folders
3. Creates sync job targeting specific RAG dataset
4. Worker downloads episodes to R2 storage
5. Transcribes audio using Whisper Large V3
6. Performs speaker diarization with Pyannote
7. Triggers chunks workflow for RAG integration
8. Updates database with final results
9. Podcast content is now searchable in RAG system

## Development Notes

- Built with Hono framework for fast HTTP handling
- Uses Zod for request validation
- Follows existing worker patterns from chunks-workflow
- TypeScript throughout for type safety
- Modular step-based architecture for maintainability

## Testing

```bash
# Type checking
pnpm prepare

# Run tests (when implemented)
pnpm test

# Watch mode
pnpm test:watch
```
