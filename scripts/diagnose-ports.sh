#!/bin/bash
# Diagnostic script to check if the web client and API ports are returning valid responses

set -e

WEB_PORT=8080
API_PORT=9080

# Check web client
WEB_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:$WEB_PORT/)
echo "Web client (http://localhost:$WEB_PORT/) HTTP status: $WEB_RESPONSE"

# Check API
API_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:$API_PORT/)
echo "API (http://localhost:$API_PORT/) HTTP status: $API_RESPONSE"

# Optionally, print a snippet of the HTML for manual inspection
if [ "$WEB_RESPONSE" = "200" ]; then
  echo "--- Web client HTML preview ---"
  curl -s http://localhost:$WEB_PORT/ | head -20
fi

if [ "$API_RESPONSE" = "200" ]; then
  echo "--- API root response preview ---"
  curl -s http://localhost:$API_PORT/ | head -20
fi
