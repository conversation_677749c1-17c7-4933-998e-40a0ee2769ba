/**
 * API Endpoint Coverage Analyzer
 * 
 * This tool analyzes which API endpoints are being tested and which ones are not.
 * It scans the test files and the API implementation to identify coverage gaps.
 */

const fs = require('fs');
const path = require('path');

// API test mappings from the original file
const API_TEST_MAPPINGS = [
  {
    folder: "workspace/servers/public-api/src/routes/ai-chat",
    testSuites: ["AI Chats"],
    testFiles: ["ai-chat.spec.ts"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/whitelabel",
    testSuites: ["White Label"],
    testFiles: ["whitelabel.spec.ts", "whitelabel-api.spec.ts"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/finetune",
    testSuites: ["Fine Tune"],
    testFiles: ["finetune.spec.ts"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/moderation",
    testSuites: ["Prompt Moderation"],
    testFiles: ["moderation.spec.ts"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/thread",
    testSuites: ["Thread Prefix"],
    testFiles: ["thread.spec.ts"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/message",
    testSuites: ["Message Prefix"],
    testFiles: ["message.spec.ts"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/rag",
    testSuites: ["RAG"],
    testFiles: ["vector.spec.ts", "rag.spec.ts"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/workspace",
    testSuites: ["Workspace Release"],
    testFiles: ["workspace.spec.ts"]
  }
];

// HTTP methods to look for
const HTTP_METHODS = ['get', 'post', 'put', 'delete', 'patch'];

/**
 * Find all API tests in the codebase
 * @returns {Object} Map of API tests by category
 */
function findApiTests() {
  const tests = {};
  
  // Find all test files
  const testFiles = findFiles(path.join(process.cwd(), 'src', 'tests'), '.spec.ts');
  
  // Extract test suites from the mapping
  for (const mapping of API_TEST_MAPPINGS) {
    const category = mapping.folder.split('/').pop();
    tests[category] = {
      testSuites: mapping.testSuites,
      testFiles: [],
      apiCalls: []
    };
    
    // Find test files that match the test suites or file names
    for (const testFile of testFiles) {
      const fileName = path.basename(testFile);
      const content = fs.readFileSync(testFile, 'utf8');
      
      // Check if the test file contains any of the test suites or matches the file name
      const matchesTestSuite = mapping.testSuites.some(suite => content.includes(suite));
      const matchesFileName = mapping.testFiles && mapping.testFiles.some(file => fileName === file);
      
      if (matchesTestSuite || matchesFileName) {
        tests[category].testFiles.push(testFile);
        
        // Extract API calls from the test file
        const apiCalls = extractApiCalls(content);
        tests[category].apiCalls.push(...apiCalls);
      }
    }
  }
  
  return tests;
}

/**
 * Extract API calls from a test file
 * @param {string} content File content
 * @returns {Object[]} API calls
 */
function extractApiCalls(content) {
  const apiCalls = [];
  
  // Look for API client calls
  for (const method of HTTP_METHODS) {
    const regex = new RegExp(`client\\.${method}\\(['"]([^'"]+)['"]`, 'g');
    let match;
    
    while ((match = regex.exec(content)) !== null) {
      apiCalls.push({
        method: method.toUpperCase(),
        path: match[1]
      });
    }
  }
  
  // Look for API function calls
  const functionCallRegex = /\bawait\s+(\w+)\(/g;
  let functionMatch;
  
  while ((functionMatch = functionCallRegex.exec(content)) !== null) {
    const functionName = functionMatch[1];
    
    // Check if this is an API function
    if (functionName.includes('create') || 
        functionName.includes('get') || 
        functionName.includes('update') || 
        functionName.includes('delete') ||
        functionName.includes('add')) {
      
      apiCalls.push({
        method: 'UNKNOWN',
        path: functionName
      });
    }
  }
  
  return apiCalls;
}

/**
 * Find files recursively
 * @param {string} dir Directory to search
 * @param {string} extension File extension to filter by
 * @returns {string[]} Array of file paths
 */
function findFiles(dir, extension) {
  if (!fs.existsSync(dir)) {
    return [];
  }
  
  let files = [];
  const entries = fs.readdirSync(dir, { withFileTypes: true });
  
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    
    if (entry.isDirectory()) {
      files = files.concat(findFiles(fullPath, extension));
    } else if (entry.name.endsWith(extension)) {
      files.push(fullPath);
    }
  }
  
  return files;
}

/**
 * Find all API client files
 * @returns {Object} Map of API client files by category
 */
function findApiClientFiles() {
  const apiClients = {};
  
  // Find all API client files
  const apiClientFiles = findFiles(path.join(process.cwd(), 'src', 'api'), '.ts');
  
  for (const mapping of API_TEST_MAPPINGS) {
    const category = mapping.folder.split('/').pop();
    apiClients[category] = [];
    
    // Find API client files that match the category
    for (const apiClientFile of apiClientFiles) {
      const fileName = path.basename(apiClientFile);
      
      if (fileName.includes(category) || 
          (category === 'ai-chat' && fileName.includes('chat')) ||
          (category === 'whitelabel' && fileName.includes('whitelabel')) ||
          (category === 'rag' && fileName.includes('vector'))) {
        
        apiClients[category].push(apiClientFile);
      }
    }
  }
  
  return apiClients;
}

/**
 * Analyze API test coverage
 */
function analyzeApiTestCoverage() {
  console.log('📊 API Test Coverage Analysis');
  console.log('============================');
  
  // Find all API tests
  const tests = findApiTests();
  
  // Find all API client files
  const apiClients = findApiClientFiles();
  
  // Print test coverage by category
  for (const category in tests) {
    console.log(`\n📁 ${category}`);
    console.log('------------------');
    
    const categoryTests = tests[category];
    const categoryApiClients = apiClients[category] || [];
    
    console.log(`Test Suites: ${categoryTests.testSuites.join(', ')}`);
    console.log(`Test Files: ${categoryTests.testFiles.length}`);
    console.log(`API Client Files: ${categoryApiClients.length}`);
    
    if (categoryTests.testFiles.length === 0) {
      console.log('❌ No tests found for this category');
      continue;
    }
    
    console.log('\nTest Files:');
    for (const testFile of categoryTests.testFiles) {
      console.log(`  - ${path.basename(testFile)}`);
    }
    
    console.log('\nAPI Client Files:');
    if (categoryApiClients.length === 0) {
      console.log('  ⚠️ No API client files found for this category');
    } else {
      for (const apiClientFile of categoryApiClients) {
        console.log(`  - ${path.basename(apiClientFile)}`);
      }
    }
    
    console.log('\nAPI Calls:');
    if (categoryTests.apiCalls.length === 0) {
      console.log('  ❌ No API calls found in tests');
    } else {
      for (const apiCall of categoryTests.apiCalls) {
        console.log(`  - ${apiCall.method} ${apiCall.path}`);
      }
    }
  }
  
  // Check for categories without tests
  const categoriesWithoutTests = API_TEST_MAPPINGS
    .map(mapping => mapping.folder.split('/').pop())
    .filter(category => !tests[category] || tests[category].testFiles.length === 0);
  
  if (categoriesWithoutTests.length > 0) {
    console.log('\n❌ Categories Without Tests:');
    for (const category of categoriesWithoutTests) {
      console.log(`  - ${category}`);
    }
  }
  
  // Suggest next steps
  console.log('\n📋 Suggested Next Steps:');
  
  if (categoriesWithoutTests.length > 0) {
    console.log('1. Create tests for categories without any tests:');
    for (const category of categoriesWithoutTests) {
      console.log(`   - ${category}`);
    }
  }
  
  console.log('2. Enhance existing tests to cover more API endpoints');
  console.log('3. Add tests for error conditions and edge cases');
}

// Run the analyzer
analyzeApiTestCoverage();
