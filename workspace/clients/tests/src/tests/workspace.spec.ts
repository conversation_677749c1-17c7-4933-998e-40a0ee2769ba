/**
 * Workspace API Tests
 *
 * This file contains tests for the Workspace API.
 */

import { test, expect } from '@playwright/test';
import { ApiClient } from '../api/api-client';
import { getAuthFileForRole } from '../auth/auth-utils';
import { config } from '../config/config';

// Mock Workspace API responses
const mockWorkspaceResponse = {
  _id: 'mock-workspace-123',
  name: 'Test Workspace',
  description: 'This is a test workspace',
  ownerId: 'user-123',
  members: [
    {
      userId: 'user-123',
      role: 'owner',
      email: '<EMAIL>'
    },
    {
      userId: 'user-456',
      role: 'editor',
      email: '<EMAIL>'
    }
  ],
  settings: {
    theme: 'light',
    defaultView: 'list'
  },
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
};

// Mock API client for Workspace
class MockWorkspaceClient extends ApiClient {
  constructor() {
    super(config.apiBaseUrl, getAuthFileForRole('admin'));
  }

  async get(path: string) {
    if (path === 'workspace') {
      return [mockWorkspaceResponse];
    } else if (path.startsWith('workspace/')) {
      return mockWorkspaceResponse;
    }
    return Promise.resolve({});
  }

  async post(path: string, data: any) {
    if (path === 'workspace') {
      return {
        ...mockWorkspaceResponse,
        name: data.name || 'New Workspace',
        description: data.description || 'New workspace description'
      };
    } else if (path.includes('workspace') && path.includes('member')) {
      return {
        ...mockWorkspaceResponse,
        members: [
          ...mockWorkspaceResponse.members,
          {
            userId: data.userId,
            role: data.role || 'viewer',
            email: data.email || '<EMAIL>'
          }
        ]
      };
    }
    return Promise.resolve({});
  }

  async delete(path: string) {
    if (path.startsWith('workspace/')) {
      return { success: true };
    } else if (path.includes('workspace') && path.includes('member')) {
      return {
        success: true,
        members: mockWorkspaceResponse.members.filter(m => m.userId !== path.split('/').pop())
      };
    }
    return Promise.resolve({ success: true, members: [] });
  }

  async put(path: string, data: any) {
    if (path.startsWith('workspace/')) {
      return {
        ...mockWorkspaceResponse,
        name: data.name || mockWorkspaceResponse.name,
        description: data.description || mockWorkspaceResponse.description,
        settings: data.settings || mockWorkspaceResponse.settings,
        updatedAt: new Date().toISOString()
      };
    }
    return Promise.resolve({});
  }
}

test.describe('Workspace API', () => {
  let client: MockWorkspaceClient;

  test.beforeEach(() => {
    client = new MockWorkspaceClient();
  });

  test('should create a new workspace', async () => {
    const workspace = await client.post('workspace', {
      name: 'Test Workspace',
      description: 'This is a test workspace'
    });

    expect(workspace).toBeDefined();
    expect(workspace._id).toBeDefined();
    expect(workspace.name).toBe('Test Workspace');
    expect(workspace.description).toBe('This is a test workspace');
    expect(workspace.members).toBeInstanceOf(Array);
  });

  test('should get all workspaces', async () => {
    const workspaces = await client.get('workspace');

    expect(workspaces).toBeInstanceOf(Array);
    expect(workspaces.length).toBeGreaterThan(0);
    expect(workspaces[0]._id).toBeDefined();
    expect(workspaces[0].name).toBeDefined();
  });

  test('should get a workspace by ID', async () => {
    const workspace = await client.get('workspace/mock-workspace-123');

    expect(workspace).toBeDefined();
    expect(workspace._id).toBe('mock-workspace-123');
    expect(workspace.name).toBe('Test Workspace');
    expect(workspace.members).toBeInstanceOf(Array);
  });

  test('should update a workspace', async () => {
    const updatedWorkspace = await client.put('workspace/mock-workspace-123', {
      name: 'Updated Workspace',
      description: 'This workspace has been updated',
      settings: {
        theme: 'dark',
        defaultView: 'grid'
      }
    });

    expect(updatedWorkspace).toBeDefined();
    expect(updatedWorkspace.name).toBe('Updated Workspace');
    expect(updatedWorkspace.description).toBe('This workspace has been updated');
    expect(updatedWorkspace.settings.theme).toBe('dark');
    expect(updatedWorkspace.settings.defaultView).toBe('grid');
  });

  test('should add a member to a workspace', async () => {
    const updatedWorkspace = await client.post('workspace/mock-workspace-123/member', {
      userId: 'user-789',
      role: 'viewer',
      email: '<EMAIL>'
    });

    expect(updatedWorkspace).toBeDefined();
    expect(updatedWorkspace.members).toBeInstanceOf(Array);

    // Find the new member
    const newMember = updatedWorkspace.members.find(m => m.userId === 'user-789');

    expect(newMember).toBeDefined();
    expect(newMember.role).toBe('viewer');
    expect(newMember.email).toBe('<EMAIL>');
  });

  test('should remove a member from a workspace', async () => {
    // Override the delete method for this test
    const originalDelete = client.delete;
    client.delete = async (path) => {
      if (path.includes('workspace') && path.includes('member')) {
        return {
          success: true,
          members: mockWorkspaceResponse.members.filter(m => m.userId !== 'user-456')
        };
      }
      return Promise.resolve({ success: true });
    };

    const result = await client.delete('workspace/mock-workspace-123/member/user-456');

    expect(result).toBeDefined();
    expect(result.success).toBe(true);
    expect(result.members).toBeInstanceOf(Array);

    // Check that the member was removed
    const removedMember = result.members.find(m => m.userId === 'user-456');

    expect(removedMember).toBeUndefined();

    // Restore the original delete method
    client.delete = originalDelete;
  });

  test('should delete a workspace', async () => {
    const result = await client.delete('workspace/mock-workspace-123');

    expect(result).toBeDefined();
    expect(result.success).toBe(true);
  });
});
