#!/bin/bash
# Start all main services for GitHub Codespaces (web-client, public-api, public-api-live, public-api-webhook)
# Each service runs in the background and logs to its own file.

set -e

echo "🚀 Starting Codespace services..."
echo "🔍 Environment check:"
echo "   CODESPACE_NAME: ${CODESPACE_NAME:-'not set'}"
echo "   Current directory: $(pwd)"
echo ""

# Function to update endpoint hostnames for current Codespace
update_codespace_endpoints() {
  if [ -n "$CODESPACE_NAME" ]; then
    echo "🌐 Updating endpoint hostnames for Codespace: $CODESPACE_NAME"
    
    # Define the files that need hostname updates
    ENV_FILES=(
      "/workspaces/server/private-keys/local-fast/endpoints.shared.env"
      "/workspaces/server/private-keys/local-fast/web-client-dev.env"
    )
    
    # Update each environment file
    for env_file in "${ENV_FILES[@]}"; do
      if [ -f "$env_file" ]; then
        echo "  📝 Updating $(basename "$env_file")"
        
        # Show current values before updating
        CURRENT_API_HOST=$(grep "^API_HOST=" "$env_file" | cut -d'=' -f2 || echo "not found")
        CURRENT_API_LIVE_HOST=$(grep "^API_LIVE_HOST=" "$env_file" | cut -d'=' -f2 || echo "not found") 
        echo "    Before: API_HOST=$CURRENT_API_HOST"
        echo "    Before: API_LIVE_HOST=$CURRENT_API_LIVE_HOST"
        
        # Replace API_HOST (port 9080)
        sed -i "s|API_HOST=.*\.app\.github\.dev|API_HOST=$CODESPACE_NAME-9080.app.github.dev|g" "$env_file"
        
        # Replace API_LIVE_HOST (port 8082)  
        sed -i "s|API_LIVE_HOST=.*\.app\.github\.dev|API_LIVE_HOST=$CODESPACE_NAME-8082.app.github.dev|g" "$env_file"
        
        # Show updated values
        NEW_API_HOST=$(grep "^API_HOST=" "$env_file" | cut -d'=' -f2 || echo "not found")
        NEW_API_LIVE_HOST=$(grep "^API_LIVE_HOST=" "$env_file" | cut -d'=' -f2 || echo "not found")
        echo "    After:  API_HOST=$NEW_API_HOST"
        echo "    After:  API_LIVE_HOST=$NEW_API_LIVE_HOST"
        echo "    ✅ Updated hostnames in $(basename "$env_file")"
      else
        echo "    ⚠️  File not found: $(basename "$env_file")"
      fi
    done
  else
    echo "⚠️  CODESPACE_NAME environment variable not set, skipping endpoint hostname updates"
  fi
}

# Function to update CORS configuration for current Codespace
update_codespace_cors() {
  if [ -n "$CODESPACE_NAME" ]; then
    echo "🌐 Updating CORS configuration for Codespace: $CODESPACE_NAME"
    
    CORS_ENV_PATH="/workspaces/server/private-keys/local-fast/cors.env"
    
    if [ -f "$CORS_ENV_PATH" ]; then
      # Get current Codespace hostname
      CODESPACE_HOST="$CODESPACE_NAME.app.github.dev"
      
      # Define the ports that need CORS access
      CORS_PORTS="8080,8081,8082,8083,9080"
      
      # Build the new CORS origins for this Codespace
      CODESPACE_ORIGINS=""
      for port in ${CORS_PORTS//,/ }; do
        if [ -n "$CODESPACE_ORIGINS" ]; then
          CODESPACE_ORIGINS="$CODESPACE_ORIGINS,"
        fi
        CODESPACE_ORIGINS="$CODESPACE_ORIGINS$CODESPACE_NAME-$port.app.github.dev"
      done
      
      # Read current CORS_FULL_ORIGINS
      CURRENT_ORIGINS=$(grep "^CORS_FULL_ORIGINS=" "$CORS_ENV_PATH" | cut -d'=' -f2- | tr -d '"')
      
      # Remove any existing entries for other Codespaces (to avoid duplicates)
      CLEANED_ORIGINS=$(echo "$CURRENT_ORIGINS" | sed 's/,[^,]*\.app\.github\.dev[^,]*//g' | sed 's/^[^,]*\.app\.github\.dev[^,]*,//g' | sed 's/^[^,]*\.app\.github\.dev[^,]*$//g')
      
      # Add the new Codespace origins
      if [ -n "$CLEANED_ORIGINS" ]; then
        NEW_ORIGINS="$CLEANED_ORIGINS,$CODESPACE_ORIGINS"
      else
        NEW_ORIGINS="$CODESPACE_ORIGINS"
      fi
      
      # Update the cors.env file
      sed -i "s|^CORS_FULL_ORIGINS=.*|CORS_FULL_ORIGINS=\"$NEW_ORIGINS\"|" "$CORS_ENV_PATH"
      
      echo "✅ Updated CORS configuration with current Codespace origins"
    else
      echo "⚠️  CORS config file not found, skipping CORS update"
    fi
  else
    echo "⚠️  CODESPACE_NAME environment variable not set, skipping CORS updates"
  fi
}

# Update endpoint hostnames and CORS configuration before starting services
update_codespace_endpoints
update_codespace_cors

# Load all env files from private-keys/local-fast
set -a
for envfile in /workspaces/server/private-keys/local-fast/*.env; do
  if [ -f "$envfile" ]; then
    # shellcheck disable=SC1090
    . "$envfile"
  fi
done
set +a

# Install all dependencies at the workspace root (pnpm workspace install)
cd /workspaces/server
pnpm install

# Start public-api
cd /workspaces/server/workspace/servers/public-api
echo "[public-api] Building..."
pnpm run build
echo "[public-api] Starting..."
export HTTP_PORT=9080
export ENABLE_MTLS=true
export MTLS_CERT_DIR="/workspaces/server/private-keys/local-fast"
export HTTPS=true
export SSL_CERT_PATH="/workspaces/server/private-keys/local-fast/server.crt"
export SSL_KEY_PATH="/workspaces/server/private-keys/local-fast/server.key"
pnpm start > /workspaces/server/public-api.log 2>&1 &
PUBLIC_API_PID=$!

# Start public-api-live
cd /workspaces/server/workspace/servers/public-api-live
echo "[public-api-live] Building..."
pnpm run build
echo "[public-api-live] Starting..."
export HTTP_PORT=8081
export HTTPS=true
export SSL_CERT_PATH="/workspaces/server/private-keys/local-fast/server.crt"
export SSL_KEY_PATH="/workspaces/server/private-keys/local-fast/server.key"
pnpm start > /workspaces/server/public-api-live.log 2>&1 &
PUBLIC_API_LIVE_PID=$!

# Start public-api-webhook
cd /workspaces/server/workspace/servers/public-api-webhook
echo "[public-api-webhook] Building..."
pnpm run build
echo "[public-api-webhook] Starting..."
export HTTP_PORT=8083
export HTTPS=true
export SSL_CERT_PATH="/workspaces/server/private-keys/local-fast/server.crt"
export SSL_KEY_PATH="/workspaces/server/private-keys/local-fast/server.key"
pnpm start > /workspaces/server/public-api-webhook.log 2>&1 &
PUBLIC_API_WEBHOOK_PID=$!

# Ensure Google service key is available at legacy path for requireJSON fallback
SRC_KEY="/workspaces/server/private-keys/local-fast/credentials/google-service-key.json"
DEST_KEY="/workspaces/server/private-keys/local/credentials/google-service-key.json"
mkdir -p "$(dirname \"$DEST_KEY\")"
if [ "$(realpath "$SRC_KEY" 2>/dev/null)" != "$(realpath "$DEST_KEY" 2>/dev/null)" ]; then
  cp "$SRC_KEY" "$DEST_KEY"
fi

# Start web-client
cd /workspaces/server/workspace/clients/web
export ENV_FOLDER="/workspaces/server/workspace/clients/web/env"
echo "[web-client] Building..."
pnpm run build
echo "[web-client] Starting..."
export HTTP_PORT=8080
export HTTPS=true
export SSL_CERT_PATH="/workspaces/server/private-keys/local-fast/server.crt"
export SSL_KEY_PATH="/workspaces/server/private-keys/local-fast/server.key"
pnpm start > /workspaces/server/web-client.log 2>&1 &
WEB_CLIENT_PID=$!

cd /workspaces/server

echo "All services started. Logs:"
echo "  public-api:         tail -f /workspaces/server/public-api.log"
echo "  public-api-live:    tail -f /workspaces/server/public-api-live.log"
echo "  public-api-webhook: tail -f /workspaces/server/public-api-webhook.log"
echo "  web-client:         tail -f /workspaces/server/web-client.log"
echo "To stop all: kill $PUBLIC_API_PID $PUBLIC_API_LIVE_PID $PUBLIC_API_WEBHOOK_PID $WEB_CLIENT_PID"

# Health check for all services
sleep 5
SERVICES=(8080 9080 8081 8083)
ALL_HEALTHY=true
for PORT in "${SERVICES[@]}"; do
  STATUS=$(curl -k -s -o /dev/null -w "%{http_code}" https://localhost:$PORT/ || echo "000")
  if [ "$STATUS" != "200" ] && [ "$STATUS" != "404" ]; then
    echo "❌ Service on port $PORT is not healthy (HTTP $STATUS)"
    ALL_HEALTHY=false
  else
    echo "✅ Service on port $PORT responded (HTTP $STATUS)"
  fi
done
if [ "$ALL_HEALTHY" = true ]; then
  echo "All services are healthy!"
else
  echo "Some services failed health checks. See logs above."
fi
