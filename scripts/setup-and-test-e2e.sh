#!/bin/bash
# Comprehensive E2E Setup and Testing Script
# This script guides you through the complete E2E testing setup and execution

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 E2E Testing Setup and Execution${NC}"
echo -e "${BLUE}===================================${NC}"
echo ""

# Function to run a step with error handling
run_step() {
    local step_name="$1"
    local step_command="$2"
    local optional="${3:-false}"
    
    echo -e "${BLUE}📋 Step: $step_name${NC}"
    echo -e "${YELLOW}Command: $step_command${NC}"
    
    if eval "$step_command"; then
        echo -e "${GREEN}✅ $step_name completed successfully${NC}"
        echo ""
        return 0
    else
        if [ "$optional" = "true" ]; then
            echo -e "${YELLOW}⚠️ $step_name failed (optional step)${NC}"
            echo ""
            return 0
        else
            echo -e "${RED}❌ $step_name failed${NC}"
            echo ""
            return 1
        fi
    fi
}

# Function to prompt user for next step
prompt_continue() {
    local message="$1"
    echo -e "${YELLOW}$message${NC}"
    read -p "Continue? (y/n): " continue_choice
    if [ "$continue_choice" != "y" ] && [ "$continue_choice" != "Y" ]; then
        echo -e "${YELLOW}Stopping at user request.${NC}"
        exit 0
    fi
    echo ""
}

# Main execution
main() {
    echo -e "${GREEN}This script will help you set up and test the E2E framework.${NC}"
    echo ""
    
    echo -e "${BLUE}📋 What this script will do:${NC}"
    echo -e "1. 🔐 Configure real credentials for E2E testing"
    echo -e "2. 🔍 Diagnose deployment issues"
    echo -e "3. 🧪 Test staging environment"
    echo -e "4. 🏭 Test production environment (read-only)"
    echo -e "5. 📊 Generate comprehensive reports"
    echo ""
    
    prompt_continue "Ready to start the E2E setup and testing process?"
    
    # Step 1: Configure credentials
    echo -e "${BLUE}🔐 Step 1: Configure Real Credentials${NC}"
    echo -e "${BLUE}====================================${NC}"
    
    if [ -f "scripts/configure-e2e-credentials.sh" ]; then
        prompt_continue "Do you want to configure real credentials now?"
        run_step "Configure E2E Credentials" "./scripts/configure-e2e-credentials.sh" true
    else
        echo -e "${YELLOW}⚠️ Credential configuration script not found${NC}"
        echo -e "${BLUE}💡 You can configure credentials manually in private-keys/[env]/test.env${NC}"
        echo ""
    fi
    
    # Step 2: Diagnose deployment issues
    echo -e "${BLUE}🔍 Step 2: Diagnose Deployment Issues${NC}"
    echo -e "${BLUE}====================================${NC}"
    
    prompt_continue "Do you want to run deployment diagnostics?"
    run_step "Deployment Diagnostics" "./scripts/diagnose-deployment-issues.sh" true
    
    # Step 3: Test environments
    echo -e "${BLUE}🧪 Step 3: Test Environments${NC}"
    echo -e "${BLUE}===========================${NC}"
    
    # Test staging environment
    echo -e "${YELLOW}🎭 Testing Staging Environment${NC}"
    prompt_continue "Do you want to test the staging environment?"
    
    run_step "Staging Health Check" "./scripts/wait-for-services.sh staging" true
    
    if [ -d "workspace/clients/tests" ]; then
        echo -e "${YELLOW}Running E2E tests against staging...${NC}"
        cd workspace/clients/tests
        
        # Load staging environment if available
        if [ -f "../../../private-keys/staging/test.env" ]; then
            echo -e "${GREEN}📋 Loading staging environment variables${NC}"
            set -a
            source "../../../private-keys/staging/test.env"
            set +a
        else
            echo -e "${YELLOW}⚠️ Using demo environment variables${NC}"
            source demo-test-env.sh
            export TARGET_ENVIRONMENT=staging
        fi
        
        run_step "Staging E2E Tests" "TARGET_ENVIRONMENT=staging npx playwright test --config=playwright.cloud.config.ts --reporter=list" true
        
        cd ../../..
    else
        echo -e "${YELLOW}⚠️ E2E test directory not found${NC}"
    fi
    
    # Test develop environment
    echo -e "${YELLOW}🛠️ Testing Develop Environment${NC}"
    prompt_continue "Do you want to test the develop environment?"
    
    run_step "Develop Health Check" "./scripts/wait-for-services.sh develop" true
    
    # Step 4: Production testing (if requested)
    echo -e "${BLUE}🏭 Step 4: Production Testing (Read-Only)${NC}"
    echo -e "${BLUE}=========================================${NC}"
    
    echo -e "${RED}⚠️ WARNING: This will run tests against PRODUCTION${NC}"
    echo -e "${GREEN}✅ All tests are READ-ONLY - no data will be modified${NC}"
    echo ""
    
    read -p "Do you want to run production tests? (y/n): " prod_test
    if [ "$prod_test" = "y" ] || [ "$prod_test" = "Y" ]; then
        
        run_step "Production Health Check" "./scripts/wait-for-services.sh production" true
        
        if [ -d "workspace/clients/tests" ]; then
            echo -e "${YELLOW}Running READ-ONLY E2E tests against production...${NC}"
            cd workspace/clients/tests
            
            # Load production environment if available
            if [ -f "../../../private-keys/production/test.env" ]; then
                echo -e "${GREEN}📋 Loading production environment variables${NC}"
                set -a
                source "../../../private-keys/production/test.env"
                set +a
            else
                echo -e "${RED}❌ Production environment variables not found${NC}"
                echo -e "${YELLOW}Please configure production credentials first${NC}"
                cd ../../..
                return 1
            fi
            
            run_step "Production E2E Tests (Read-Only)" "TARGET_ENVIRONMENT=production npx playwright test --config=playwright.production.config.ts --reporter=list" true
            
            cd ../../..
        fi
    else
        echo -e "${YELLOW}Skipping production tests${NC}"
    fi
    
    # Step 5: Generate reports
    echo -e "${BLUE}📊 Step 5: Generate Reports${NC}"
    echo -e "${BLUE}==========================${NC}"
    
    echo -e "${GREEN}🎉 E2E Testing Setup and Execution Complete!${NC}"
    echo ""
    
    echo -e "${BLUE}📋 Summary of what was accomplished:${NC}"
    echo -e "✅ Credential configuration (if requested)"
    echo -e "✅ Deployment diagnostics"
    echo -e "✅ Environment health checks"
    echo -e "✅ E2E test execution"
    echo -e "✅ Production testing (if requested)"
    echo ""
    
    echo -e "${BLUE}📁 Test artifacts can be found in:${NC}"
    echo -e "   📊 Staging: workspace/clients/tests/test-results-cloud/"
    echo -e "   🏭 Production: workspace/clients/tests/test-results-production/"
    echo -e "   📦 Archives: workspace/clients/tests/archived-test-results/"
    echo ""
    
    echo -e "${BLUE}🚀 Next steps:${NC}"
    echo -e "1. Review test results and fix any issues"
    echo -e "2. Set up CI/CD integration with GitHub Actions"
    echo -e "3. Configure monitoring and alerting"
    echo -e "4. Train team on E2E testing procedures"
    echo ""
    
    echo -e "${GREEN}The E2E testing framework is now ready for production use! 🎉${NC}"
}

# Run main function
main "$@"
