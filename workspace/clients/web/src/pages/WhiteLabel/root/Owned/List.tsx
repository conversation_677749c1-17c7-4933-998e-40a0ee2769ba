<<<<<<< HEAD
import React from "react";
=======
import React, { useState } from "react";
>>>>>>> WA-170_MCP
import { Link } from "react-router-dom";
import { useAuth0FetchJSON } from "../../../../globals/auth0-user";
import { PATH_WHITELABEL_INDEX, PATH_WHITELABEL_ITEM } from "../../paths";
import { ROBOT_AVATAR_PATH } from "../../../../globals/constants/paths";

import { OwnedWhiteLabel } from "../../types";
import { replaceParams } from "../../../../util/router";
import { WhitelabelTable } from "./WhitelabelTable";

import "./whitelabel.css";
import styles from "./WhitelabelList.module.css";

type WhiteLabelProps = {
  title: string,
  whitelabels: Array<OwnedWhiteLabel>,
  update: ()=>any,
};


export const WhiteLabelList: React.FC<WhiteLabelProps> = ({ title, whitelabels, update })=>{
  const auth0FetchJSON = useAuth0FetchJSON();
  const [viewMode, setViewMode] = useState<'cards' | 'table'>('cards');

  return (
    <>
<<<<<<< HEAD
    <h3 className='subtitle'>{title}</h3>
    <div className='whitelabel-list container'>
      {(whitelabels || []).map((whitelabel)=>(
=======
    <div className={styles.header}>
      <h3 className='subtitle'>{title}</h3>

      {/* View Mode Toggle */}
      <div className={styles.viewToggle}>
        <button
          type="button"
          onClick={() => setViewMode('cards')}
          className={`${styles.viewButton} ${viewMode === 'cards' ? styles.active : ''}`}
          title="Card view"
        >
          <i className="fas fa-th-large"></i>
        </button>
        <button
          type="button"
          onClick={() => setViewMode('table')}
          className={`${styles.viewButton} ${viewMode === 'table' ? styles.active : ''}`}
          title="Table view"
        >
          <i className="fas fa-table"></i>
        </button>
      </div>
    </div>

    {viewMode === 'table' ? (
      <WhitelabelTable whitelabels={whitelabels || []} onUpdate={update} />
    ) : (
      <div className='whitelabel-list container'>
        {(whitelabels || []).map((whitelabel)=>(
>>>>>>> WA-170_MCP
        <div className="card whitelabel-ai-card" key={whitelabel.doc._id}>
          <div className="card-image">
            <figure className="image">
              <img src={whitelabel.doc.picture || ROBOT_AVATAR_PATH} alt="Placeholder image"/>
            </figure>
          </div>
          <div className="card-content">
            <div className="media">
              <div className="media-left">
                {/* <figure className="image is-48x48">
                  <img src={PLACEHOLDER_IMG_PATH} alt="Placeholder image"/>
                </figure> */}
                </div>
                <div className="media-content">
                  <p className="title">
                    <Link className="clickable-text" to={replaceParams(PATH_WHITELABEL_ITEM, { whitelabelId: whitelabel.doc._id })} >
                      {whitelabel.doc.title}
                    </Link>
                  </p>
                  {/* <p className="subtitle is-6">@johnsmith</p> */}
                </div>
              </div>
            </div>

            <div className="content">
              <p>
              <span className='has-text-weight-semibold'>
                Description: </span>
              {whitelabel.doc.description}
              </p>
              <p>
              <time dateTime="2016-1-1">
                <span className='has-text-weight-semibold'>
                  Created: </span> <span>
                  11:09 PM - 1 Jan 2016
                </span>
              </time>
              </p>
              <p>
                <span className='has-text-weight-semibold'>
                  Whitelabel ID: </span>
                <code className="is-family-monospace has-text-grey" title="Click to copy">
                  {whitelabel.doc._id}
                </code>
              </p>
              <p>
                <span className='has-text-weight-semibold'>
                  Number of Releases: </span>
                {whitelabel.releaseCount}
              </p>
            { whitelabel.releaseCount === 0 ? null : (
              <>
                <p>
                  <span className='has-text-weight-semibold'>Number of Users: </span>
                  {whitelabel.userCount}
                </p>
                <p>
                  <span className='has-text-weight-semibold'>Total Threads: </span>
                  {whitelabel.chatCount}
                </p>
                <p>
                  <span className='has-text-weight-semibold'>Notifications: </span>
                  {whitelabel.notifications || 0}
                </p>
              </>
            ) }
            <div className='div deprecate-whitelabel'>
              <button className='deprecate-whitelabel-button button is-small is-danger is-outlined'
                onClick={async (e)=>{
                  e.preventDefault();
                  await auth0FetchJSON(
                    `${PATH_WHITELABEL_INDEX}/${whitelabel.doc._id}`,
                    { method: "DELETE" }
                  );
                  update();
                }}
              >Delete</button>
              {/* <button className='deprecate-whitelabel-button button is-small is-danger is-outlined'>🌅 Deprecate</button> */}
            </div>
          </div>
        </div>
      ))}
      </div>
    )}
    </>
  );
};
