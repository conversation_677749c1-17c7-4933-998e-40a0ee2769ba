import { WorkflowStep } from "cloudflare:workers";
import { Step<PERSON>ontext, ChunkData, VectorizeConfig, InitialSetup } from "./types";

/**
 * Adds chunks to file record
 */
export async function addChunksToFileRecord(
  step: WorkflowStep,
  context: StepContext,
  filteredChunks: ChunkData[],
  vectorizeConfig: VectorizeConfig,
  initialSetup: InitialSetup
): Promise<void>{
  await step.do("addChunksToFileRecord", async ()=>{

<<<<<<< HEAD
    // Add chunks
=======
    // Make sure the fileId is a valid MongoDB ObjectId format (24 hex characters)
    let fileId = initialSetup.fileInfo.fileId;
    // Log the current fileId for debugging
    console.log(`🔍 [ADD CHUNKS] Original fileId: ${fileId}, type: ${typeof fileId}`);

    // Handle undefined/null case
    if (!fileId) {
      console.error(`❌ [ADD CHUNKS] fileId is ${fileId}, cannot proceed`);
      throw new Error(`Invalid fileId: ${fileId}`);
    }

    // Convert to string if it's not already
    fileId = String(fileId);

    // Check if the fileId is in a valid MongoDB ObjectId format
    const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(fileId);
    if (!isValidObjectId) {
      console.warn(`⚠️ [ADD CHUNKS] Invalid ObjectId format detected: ${fileId}`);

      // If it contains special characters like '/', attempt to extract a valid ID portion
      if (fileId.includes('/')) {
        const parts = fileId.split('/');
        // Check each part for a valid ObjectId
        for (const part of parts) {
          if (/^[0-9a-fA-F]{24}$/.test(part)) {
            fileId = part;
            console.log(`✅ [ADD CHUNKS] Extracted valid ObjectId from path: ${fileId}`);
            break;
          }
        }
      }
      // Also try to extract a valid ObjectId pattern from any string
      else if (fileId.match(/[0-9a-fA-F]{24}/)) {
        const match = fileId.match(/([0-9a-fA-F]{24})/);
        if (match && match[1]) {
          fileId = match[1];
          console.log(`✅ [ADD CHUNKS] Extracted valid ObjectId from string: ${fileId}`);
        }
      }

      // If fileId is still not valid, try to use the originalFileId if it exists
      if (!/^[0-9a-fA-F]{24}$/.test(fileId) && initialSetup.fileInfo.originalFileId) {
        console.log(`⚠️ [ADD CHUNKS] Attempting to use originalFileId as fallback: ${initialSetup.fileInfo.originalFileId}`);
        // Check if originalFileId is valid
        if (/^[0-9a-fA-F]{24}$/.test(initialSetup.fileInfo.originalFileId)) {
          fileId = initialSetup.fileInfo.originalFileId;
          console.log(`✅ [ADD CHUNKS] Using originalFileId as fallback: ${fileId}`);
        }
      }

      // Next attempt fallback: try to use the whitelabelId if it's valid
      if (!/^[0-9a-fA-F]{24}$/.test(fileId) && vectorizeConfig.whitelabelId && /^[0-9a-fA-F]{24}$/.test(vectorizeConfig.whitelabelId)) {
        console.log(`⚠️ [ADD CHUNKS] Using whitelabelId as fallback: ${vectorizeConfig.whitelabelId}`);
        fileId = vectorizeConfig.whitelabelId;
      }
    }

    // File record validation/creation is now handled earlier in the workflow
    // We can proceed directly with the fileId from initialSetup
    console.log(`🔍 [ADD CHUNKS] Using validated fileId: ${fileId}`);

    try {
      // CRITICAL: Update the file status to EDITING_CHUNKS before adding chunks
      console.log(`🔍 [ADD CHUNKS] Setting file status to "editing chunks" to allow chunk addition`);

      const statusUrl = `${context.env.API_HOST}/white-label/${vectorizeConfig.whitelabelId}/rag-vector/files/${fileId}/status`;
      // Prepare headers for status update
      const statusHeaders: Record<string, string> = {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${vectorizeConfig.auth0Token}`
      };

      // In BARE METAL MODE, we need to add special headers for local/dev/stage environments
      if(context.env.ENVIRONMENT === 'local' || context.env.ENVIRONMENT === 'development' || context.env.ENVIRONMENT === 'stage') {
        if (context.env.CLOUDFLARE_WORKER_X_AUTH_DEV) {
          statusHeaders["cloudflare-worker-x-dev-auth"] = context.env.CLOUDFLARE_WORKER_X_AUTH_DEV;
          statusHeaders["x-worker-local-dev"] = "true";
          console.log(`🔑 Added Cloudflare Worker dev auth headers for status update`);
        }
      }

      const statusResponse = await fetch(statusUrl, {
        method: "POST",
        headers: statusHeaders,
        body: JSON.stringify({ status: "editing chunks" })
      });

      if (statusResponse.ok) {
        console.log(`✅ [ADD CHUNKS] File status updated to "editing chunks" successfully`);
      } else {
        console.error(`❌ [ADD CHUNKS] Failed to update file status: ${statusResponse.status}`);
        console.error(`❌ [ADD CHUNKS] Response text: ${await statusResponse.text()}`);
        // We'll continue anyway and hope for the best
      }

    } catch (validationError) {
      console.warn(`⚠️ [ADD CHUNKS] Error during file validation: ${validationError}`);
      // Continue anyway
    }

    console.log(`🔄 [ADD CHUNKS] Using fileId: ${fileId}`);

    // Update the initialSetup with the sanitized fileId
    initialSetup.fileInfo.fileId = fileId;

    // Prepare headers for adding chunks
    const addChunksHeaders: Record<string, string> = {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${vectorizeConfig.auth0Token}`
    };

    // In BARE METAL MODE, we need to add special headers for local/dev/stage environments
    if(context.env.ENVIRONMENT === 'local' || context.env.ENVIRONMENT === 'development' || context.env.ENVIRONMENT === 'stage') {
      if (context.env.CLOUDFLARE_WORKER_X_AUTH_DEV) {
        addChunksHeaders["cloudflare-worker-x-dev-auth"] = context.env.CLOUDFLARE_WORKER_X_AUTH_DEV;
        addChunksHeaders["x-worker-local-dev"] = "true";
        console.log(`🔑 Added Cloudflare Worker dev auth headers for adding chunks`);
      }
    }

    // Process chunks in batches
>>>>>>> WA-170_MCP
    const CHUNK_BATCH_SIZE = 5; // Reduced batch size to avoid timeouts
    console.log(`🔄 Adding chunks in batches of ${CHUNK_BATCH_SIZE}`);
    console.log(`🔄 Total chunks to add: ${filteredChunks.length}`);

<<<<<<< HEAD
    // Process chunks in smaller batches to avoid "request entity too large" errors and timeouts
    for(let i = 0; i < filteredChunks.length; i += CHUNK_BATCH_SIZE) {
=======
    for (let i = 0; i < filteredChunks.length; i += CHUNK_BATCH_SIZE) {
>>>>>>> WA-170_MCP
      const chunkBatch = filteredChunks.slice(i, i + CHUNK_BATCH_SIZE);
      console.log(`🔄 Adding chunks batch ${Math.floor(i/CHUNK_BATCH_SIZE) + 1}/${Math.ceil(filteredChunks.length/CHUNK_BATCH_SIZE)} (${chunkBatch.length} chunks)`);

      try {
        // Prepare the request payload
        const requestPayload = {
          chunks: chunkBatch.map(chunk=>({
            // Don't include id or _id - let the server generate it
            text: chunk.text,
            tags: Array.isArray(chunk.metadata?.tags) ? chunk.metadata.tags : []
          }))
        };

        console.log(`🔍 [ADD CHUNKS] Request payload structure:`, {
          chunkCount: requestPayload.chunks.length,
          firstChunkKeys: Object.keys(requestPayload.chunks[0] || {}),
          firstChunkTextLength: requestPayload.chunks[0]?.text?.length || 0,
          firstChunkTagsLength: requestPayload.chunks[0]?.tags?.length || 0
        });

<<<<<<< HEAD
        // Log the first chunk to help with debugging
        console.log(`🔄 First chunk in batch:`, {
          textLength: requestPayload.chunks[0]?.text?.length,
          tagsLength: requestPayload.chunks[0]?.tags?.length || 0,
          hasTags: Array.isArray(requestPayload.chunks[0]?.tags)
        });

        // Make sure the fileId is a valid MongoDB ObjectId format (24 hex characters)
  let fileId = initialSetup.fileInfo.fileId;
  // Log the current fileId for debugging
  console.log(`🔍 [ADD CHUNKS] Original fileId: ${fileId}, type: ${typeof fileId}`);

  // Handle undefined/null case
  if (!fileId) {
    console.error(`❌ [ADD CHUNKS] fileId is ${fileId}, cannot proceed`);
    throw new Error(`Invalid fileId: ${fileId}`);
  }

  // Convert to string if it's not already
  fileId = String(fileId);

  // Check if the fileId is in a valid MongoDB ObjectId format
  const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(fileId);
  if (!isValidObjectId) {
    console.warn(`⚠️ [ADD CHUNKS] Invalid ObjectId format detected: ${fileId}`);

    // If it contains special characters like '/', attempt to extract a valid ID portion
    if (fileId.includes('/')) {
      const parts = fileId.split('/');
      // Check each part for a valid ObjectId
      for (const part of parts) {
        if (/^[0-9a-fA-F]{24}$/.test(part)) {
          fileId = part;
          console.log(`✅ [ADD CHUNKS] Extracted valid ObjectId from path: ${fileId}`);
          break;
        }
      }
    }
    // Also try to extract a valid ObjectId pattern from any string
    else if (fileId.match(/[0-9a-fA-F]{24}/)) {
      const match = fileId.match(/([0-9a-fA-F]{24})/);
      if (match && match[1]) {
        fileId = match[1];
        console.log(`✅ [ADD CHUNKS] Extracted valid ObjectId from string: ${fileId}`);
      }
    }

    // If fileId is still not valid, try to use the originalFileId if it exists
    if (!/^[0-9a-fA-F]{24}$/.test(fileId) && initialSetup.fileInfo.originalFileId) {
      console.log(`⚠️ [ADD CHUNKS] Attempting to use originalFileId as fallback: ${initialSetup.fileInfo.originalFileId}`);
      // Check if originalFileId is valid
      if (/^[0-9a-fA-F]{24}$/.test(initialSetup.fileInfo.originalFileId)) {
        fileId = initialSetup.fileInfo.originalFileId;
        console.log(`✅ [ADD CHUNKS] Using originalFileId as fallback: ${fileId}`);
      }
    }

    // Next attempt fallback: try to use the whitelabelId if it's valid
    if (!/^[0-9a-fA-F]{24}$/.test(fileId) && vectorizeConfig.whitelabelId && /^[0-9a-fA-F]{24}$/.test(vectorizeConfig.whitelabelId)) {
      console.log(`⚠️ [ADD CHUNKS] Using whitelabelId as fallback: ${vectorizeConfig.whitelabelId}`);
      fileId = vectorizeConfig.whitelabelId;
    }
  }

  // Verify there's actually a file record with this ID before proceeding
  try {
    // Perform a validation HEAD request to check if the file exists
    console.log(`🔍 [ADD CHUNKS] Verifying file exists with ID: ${fileId}`);
    const validationUrl = `${context.env.API_HOST}/white-label/${vectorizeConfig.whitelabelId}/rag-vector/files/${fileId}`;

    // Prepare headers
    const headers: Record<string, string> = {
      "Authorization": `Bearer ${vectorizeConfig.auth0Token}`
    };

    // In BARE METAL MODE, we need to add special headers for local development
    if(context.env.ENVIRONMENT === 'local' || context.env.ENVIRONMENT === 'development') {
      if (context.env.CLOUDFLARE_WORKER_X_AUTH_DEV) {
        headers["cloudflare-worker-x-dev-auth"] = context.env.CLOUDFLARE_WORKER_X_AUTH_DEV;
        headers["x-worker-local-dev"] = "true";
        console.log(`🔑 Added Cloudflare Worker dev auth headers for validation request`);
      }
    }

    const validationResponse = await fetch(validationUrl, {
      method: "HEAD",
      headers
    });

    if (!validationResponse.ok) {
      console.warn(`⚠️ [ADD CHUNKS] File validation failed: ${validationResponse.status}. Attempting to create file record...`);

      // As a fallback, try to create a new file record
      const createPayload = {
        bucket: initialSetup.fileInfo.bucket || "default",
        objectKey: initialSetup.fileInfo.objectKey,
        originalName: initialSetup.fileInfo.fileName,
        chunkingTool: initialSetup.fileInfo.processor,
        title: initialSetup.fileInfo.title || initialSetup.fileInfo.fileName,
        description: initialSetup.fileInfo.description
      };

      const createUrl = `${context.env.API_HOST}/white-label/${vectorizeConfig.whitelabelId}/rag-vector/files/create-record`;
      // Prepare headers for create request
      const createHeaders: Record<string, string> = {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${vectorizeConfig.auth0Token}`
      };

      // In BARE METAL MODE, we need to add special headers for local development
      if(context.env.ENVIRONMENT === 'local' || context.env.ENVIRONMENT === 'development') {
        if (context.env.CLOUDFLARE_WORKER_X_AUTH_DEV) {
          createHeaders["cloudflare-worker-x-dev-auth"] = context.env.CLOUDFLARE_WORKER_X_AUTH_DEV;
          createHeaders["x-worker-local-dev"] = "true";
          console.log(`🔑 Added Cloudflare Worker dev auth headers for create request`);
        }
      }

      const createResponse = await fetch(createUrl, {
        method: "POST",
        headers: createHeaders,
        body: JSON.stringify(createPayload)
      });

      if (createResponse.ok) {
        const createResult = await createResponse.json();
        if (createResult?.data?._id) {
          fileId = createResult.data._id;
          console.log(`✅ [ADD CHUNKS] Created new file record with ID: ${fileId}`);
          // Update initialSetup with the new fileId
          initialSetup.fileInfo.fileId = fileId;
        }
      } else {
        console.error(`❌ [ADD CHUNKS] Failed to create file record: ${await createResponse.text()}`);
      }
    } else {
      console.log(`✅ [ADD CHUNKS] File validation successful: ${fileId}`);
    }

    // CRITICAL: Update the file status to EDITING_CHUNKS before adding chunks
    console.log(`🔍 [ADD CHUNKS] Setting file status to "editing chunks" to allow chunk addition`);

    const statusUrl = `${context.env.API_HOST}/white-label/${vectorizeConfig.whitelabelId}/rag-vector/files/${fileId}/status`;
    // Prepare headers for status update
    const statusHeaders: Record<string, string> = {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${vectorizeConfig.auth0Token}`
    };

    // In BARE METAL MODE, we need to add special headers for local development
    if(context.env.ENVIRONMENT === 'local' || context.env.ENVIRONMENT === 'development') {
      if (context.env.CLOUDFLARE_WORKER_X_AUTH_DEV) {
        statusHeaders["cloudflare-worker-x-dev-auth"] = context.env.CLOUDFLARE_WORKER_X_AUTH_DEV;
        statusHeaders["x-worker-local-dev"] = "true";
        console.log(`🔑 Added Cloudflare Worker dev auth headers for status update`);
      }
    }

    const statusResponse = await fetch(statusUrl, {
      method: "POST",
      headers: statusHeaders,
      body: JSON.stringify({ status: "editing chunks" })
    });

    if (statusResponse.ok) {
      console.log(`✅ [ADD CHUNKS] File status updated to "editing chunks" successfully`);
    } else {
      console.error(`❌ [ADD CHUNKS] Failed to update file status: ${statusResponse.status}`);
      console.error(`❌ [ADD CHUNKS] Response text: ${await statusResponse.text()}`);
      // We'll continue anyway and hope for the best
    }

  } catch (validationError) {
    console.warn(`⚠️ [ADD CHUNKS] Error during file validation: ${validationError.message}`);
    // Continue anyway
  }

  console.log(`🔄 [ADD CHUNKS] Using fileId: ${fileId}`);

  // Update the initialSetup with the sanitized fileId
  initialSetup.fileInfo.fileId = fileId;

  // Prepare headers for adding chunks
  const addChunksHeaders: Record<string, string> = {
    "Content-Type": "application/json",
    "Authorization": `Bearer ${vectorizeConfig.auth0Token}`
  };

  // In BARE METAL MODE, we need to add special headers for local development
  if(context.env.ENVIRONMENT === 'local' || context.env.ENVIRONMENT === 'development') {
    if (context.env.CLOUDFLARE_WORKER_X_AUTH_DEV) {
      addChunksHeaders["cloudflare-worker-x-dev-auth"] = context.env.CLOUDFLARE_WORKER_X_AUTH_DEV;
      addChunksHeaders["x-worker-local-dev"] = "true";
      console.log(`🔑 Added Cloudflare Worker dev auth headers for adding chunks`);
    }
  }

  const addChunksResponse = await fetch(
=======
        const addChunksResponse = await fetch(
>>>>>>> WA-170_MCP
          `${context.env.API_HOST}/white-label/${vectorizeConfig.whitelabelId}/rag-vector/files/${fileId}/chunks/bulk`,
          {
            method: "POST",
            headers: addChunksHeaders,
            body: JSON.stringify(requestPayload)
          }
        );

        if(!addChunksResponse.ok) {
          let error: { status?: string, message?: string, context?: string } | string = {};
          try {
            error = await addChunksResponse.json();
          }catch(e) {
            error = await addChunksResponse.text();
          }

          throw new Error(`Failed to add chunks batch: ${JSON.stringify(error)}`);
        }

        console.log(`✅ Successfully added chunks batch ${Math.floor(i/CHUNK_BATCH_SIZE) + 1}/${Math.ceil(filteredChunks.length/CHUNK_BATCH_SIZE)}`);
<<<<<<< HEAD
      }catch(error) {
=======
      } catch(error) {
>>>>>>> WA-170_MCP
        console.error(`❌ Error adding chunks batch:`, error);
        throw error;
      }
    }
<<<<<<< HEAD
=======

    console.log(`✅ [ADD CHUNKS] Successfully added all ${filteredChunks.length} chunks to file ${fileId}`);
>>>>>>> WA-170_MCP
  });
}