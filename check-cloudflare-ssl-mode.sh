#!/bin/bash
# <PERSON>ript to check Cloudflare SSL mode

set -e

echo "🔍 Checking Cloudflare SSL mode..."

# Get Cloudflare API token
<<<<<<< HEAD
CF_API_TOKEN=${1:-"xQI66zSWAOjLnWlyoZne8_CjVgStYXYbH26f3p2c"}
=======
CF_API_TOKEN=${1:-"aR8Ud0AVhRI59O69XX815JmYuPalWdPG1S_nZGf2"}
>>>>>>> WA-170_MCP

# Get zone ID for divinci.app
ZONE_ID=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones?name=divinci.app" \
  -H "Authorization: Bearer $CF_API_TOKEN" \
  -H "Content-Type: application/json" | jq -r '.result[0].id')

if [ -z "$ZONE_ID" ] || [ "$ZONE_ID" == "null" ]; then
  echo "❌ Failed to get zone ID for divinci.app"
  exit 1
fi

echo "✅ Found zone ID: $ZONE_ID"

# Get SSL mode
SSL_MODE=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/settings/ssl" \
  -H "Authorization: Bearer $CF_API_TOKEN" \
  -H "Content-Type: application/json" | jq -r '.result.value')

if [ -z "$SSL_MODE" ] || [ "$SSL_MODE" == "null" ]; then
  echo "❌ Failed to get SSL mode"
  exit 1
fi

echo "📋 Cloudflare SSL mode: $SSL_MODE"

# Check if SSL mode is Full (Strict)
if [ "$SSL_MODE" == "strict" ]; then
  echo "✅ SSL mode is Full (Strict)"
  echo "ℹ️ This mode requires a valid certificate on your origin server"
elif [ "$SSL_MODE" == "full" ]; then
  echo "ℹ️ SSL mode is Full"
  echo "ℹ️ This mode accepts self-signed certificates on your origin server"
else
  echo "ℹ️ SSL mode is $SSL_MODE"
fi

echo "✅ SSL mode check completed"
