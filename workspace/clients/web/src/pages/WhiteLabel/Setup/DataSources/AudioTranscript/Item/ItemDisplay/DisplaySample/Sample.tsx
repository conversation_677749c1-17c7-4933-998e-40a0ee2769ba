import React, { FC, useCallback, useMemo, useState, useEffect } from "react";

import { secondsToReadable } from "../seconds-to-readable";
import { DisplaySpeaker } from "../display-speaker";
import { AudioTranscriptDoc } from "@divinci-ai/models";
<<<<<<< HEAD
=======
import { ConfidenceBadge, ConfidenceBar } from "../../../../../../../../components/ConfidenceIndicator";
>>>>>>> WA-170_MCP
import styles from "../styles.module.css";

type SampleType = AudioTranscriptDoc["samples"][0];

const SAMPLE_PAGES: Record<string, {
  name: string,
  Component: FC<{ sample: SampleType, setPage: (pagename: string)=>any }>,
}> = {
  details: { name: "details", Component: SampleDetails },
  edit: { name: "edit", Component: SampleEdit },
};
export function DisplaySample({ sample }: { sample: SampleType }){
  const [pageName, setPage] = useState(SAMPLE_PAGES.details.name);
  const { Component } = SAMPLE_PAGES[pageName];
  return <Component sample={sample} setPage={setPage} />;
}

function SampleDetails({ sample, setPage }: { sample: SampleType, setPage: (pageName: string)=>any }){
  const [isDarkMode, setIsDarkMode] = useState(false);

<<<<<<< HEAD
=======
  // Check if this sample has confidence data and if it's below threshold
  const hasConfidence = sample.confidence && typeof sample.confidence.average === 'number';
  const confidenceThreshold = 0.75; // Default threshold
  const isLowConfidence = hasConfidence && sample.confidence!.average < confidenceThreshold;

>>>>>>> WA-170_MCP
  // Check if dark mode is enabled
  useEffect(() => {
    const checkDarkMode = () => {
      const isDark = document.documentElement.classList.contains('dark-mode');
      setIsDarkMode(isDark);
    };

    // Check initially
    checkDarkMode();

    // Set up a MutationObserver to detect changes to the class list
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'class') {
          checkDarkMode();
        }
      });
    });

    observer.observe(document.documentElement, { attributes: true });

    // Clean up
    return () => observer.disconnect();
  }, []);

  return (
    <div className={`sample-container ${isDarkMode ? styles.darkModeContainer : ''}`}>
      <div className="columns">
        <div className="column is-3">
          <div className="field">
            <label className="label has-text-white-in-dark">Speaker</label>
            <div className="control">
              <div className="tags">
                <span className={`tag is-info is-medium speaker-label ${isDarkMode ? styles.speakerLabel : ''}`}>
                  <DisplaySpeaker speaker={sample.speaker} />
                </span>
              </div>
            </div>
          </div>
        </div>
        <div className="column is-3">
          <div className="field">
            <label className="label has-text-white-in-dark">Start Time</label>
            <div className="control">
              <div className="tags">
                <span className={`tag is-light is-medium ${isDarkMode ? styles.timeTag : ''}`}>
                  {secondsToReadable(sample.start)}
                </span>
              </div>
            </div>
          </div>
        </div>
        <div className="column is-3">
          <div className="field">
            <label className="label has-text-white-in-dark">End Time</label>
            <div className="control">
              <div className="tags">
                <span className={`tag is-light is-medium ${isDarkMode ? styles.timeTag : ''}`}>
                  {secondsToReadable(sample.end)}
                </span>
              </div>
            </div>
          </div>
        </div>
        <div className="column is-3">
          <div className="field">
            <label className="label has-text-white-in-dark">Actions</label>
            <div className="control">
              <button
                className="button is-primary"
                onClick={(e)=>{ e.preventDefault(); setPage("edit"); }}
              >
                <span className="icon">
                  <i className="fas fa-edit"></i>
                </span>
                <span>Edit</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="field">
        <label className="label has-text-white-in-dark">Audio Clip</label>
        <div className={`control has-text-centered audio-player ${isDarkMode ? styles.audioPlayer : ''}`}>
          <audio controls className="is-fullwidth">
            <source src={sample.audio.publicUrl} />
            Your browser does not support the audio element.
          </audio>
        </div>
      </div>

      <div className="field">
        <label className="label has-text-white-in-dark">Transcript</label>
        <div className="control">
<<<<<<< HEAD
          <div className={`box has-background-light-in-light has-background-dark-in-dark has-text-dark-in-light has-text-white-in-dark transcript-text ${isDarkMode ? styles.transcriptText : ''}`}>
=======
          <div className={`box has-background-light-in-light has-background-dark-in-dark has-text-dark-in-light has-text-white-in-dark transcript-text ${isDarkMode ? styles.transcriptText : ''} ${isLowConfidence ? styles.lowConfidenceTranscript : ''}`}>
>>>>>>> WA-170_MCP
            {sample.transcript}
          </div>
        </div>
      </div>
<<<<<<< HEAD
=======

      {/* Confidence Score Display */}
      {hasConfidence && (
        <div className="field">
          <label className="label has-text-white-in-dark">Confidence Score</label>
          <div className="control">
            <div className={`box has-background-light-in-light has-background-dark-in-dark ${isLowConfidence ? styles.lowConfidenceBox : ''}`}>
              <div className="level">
                <div className="level-left">
                  <div className="level-item">
                    <ConfidenceBadge
                      confidence={sample.confidence!.average}
                    />
                  </div>
                  <div className="level-item">
                    <span className={`has-text-dark-in-light has-text-white-in-dark ${isLowConfidence ? styles.lowConfidenceText : ''}`}>
                      Average: {(sample.confidence!.average * 100).toFixed(1)}%
                    </span>
                  </div>
                  <div className="level-item">
                    <span className="has-text-grey">
                      Range: {(sample.confidence!.min * 100).toFixed(1)}% - {(sample.confidence!.max * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
                {isLowConfidence && (
                  <div className="level-right">
                    <div className="level-item">
                      <span className={`tag is-warning ${styles.reviewTag}`}>
                        <span className="icon">
                          <i className="fas fa-exclamation-triangle"></i>
                        </span>
                        <span>Needs Review</span>
                      </span>
                    </div>
                  </div>
                )}
              </div>

              {/* Confidence visualization bar */}
              <div className="mt-3">
                <ConfidenceBar
                  confidence={sample.confidence!}
                  height={8}
                  showLabels={true}
                  className={styles.confidenceBar}
                />
              </div>

              {isLowConfidence && (
                <div className={`notification is-warning is-light mt-3 ${styles.lowConfidenceWarning}`}>
                  <span className="icon">
                    <i className="fas fa-info-circle"></i>
                  </span>
                  <span>
                    This segment has low confidence and may need manual review for accuracy.
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
>>>>>>> WA-170_MCP
    </div>
  );
}

import { dataSourceAudioItemSampleUpdate } from "@divinci-ai/actions";
import { SelectWithCustomValue } from "../../../../../../../../components/inputs/SelectWithCustomValue";
import { useAudioTranscriptItem } from "../../../data/AudioTranscriptItem";
import { useRunGetter, GetterState } from "../../../../../../../../util/fetch";
import { useParams } from "react-router";
import { useAuth0Fetch } from "../../../../../../../../globals/auth0-user";
type SampleBody = Parameters<typeof dataSourceAudioItemSampleUpdate>[2];
function SampleEdit({ sample, setPage }: { sample: SampleType, setPage: (pageName: string)=>any }){
  const { value: item, update: updateAudioTranscript } = useAudioTranscriptItem();
  const [body, setBody] = useState<SampleBody>({
    speaker: sample.speaker,
    transcript: sample.transcript,
  });
  const speakers = useMemo(()=>{
    if(!item) return [];
    const list = new Set<string>();
    for(const { speaker } of item.samples) list.add(speaker);
    for(const { speaker } of item.failedSamples) list.add(speaker);
    for(const { speaker } of item.ignoredSamples) list.add(speaker);
    return Array.from(list).map((speaker)=>({
      value: speaker,
      title: (
        !item.speakerNames ? speaker :
        !(speaker in item.speakerNames) ? speaker :
        `${item.speakerNames[speaker]} (${speaker})`
      )
    }));
  }, [item]);
  const { run, state } = useUpdateSample({ sample, body });

  return (
    <form onSubmit={async (e)=>{
      e.preventDefault();
      await run();
      await updateAudioTranscript();
      setPage("details");
    }}>
      <div className="field">
        <label className="label">Speaker</label>
        <div className="control">
          <SelectWithCustomValue
            options={speakers}
            value={body.speaker}
            onChange={(speaker)=>(setBody({ ...body, speaker }))}
          />
        </div>
      </div>
      <div className="field">
        <label className="label">Transcript</label>
        <div className="control">
          <textarea
            className="textarea" placeholder="Transcript..."
            value={body.transcript}
            onChange={(e)=>(setBody({ ...body, transcript: e.target.value }))}
          />
        </div>
      </div>
      <div className="field is-grouped">
        <div className="control">
          <button
            className="button is-link"
            type="submit"
            disabled={state === GetterState.Loading}
          >Submit</button>
        </div>
        <div className="control">
          <button
            className="button is-link is-light"
            onClick={(e)=>{
              e.preventDefault();
              setBody({
                speaker: sample.speaker,
                transcript: sample.transcript,
              });
            }}
          >Reset</button>
        </div>
        <div className="control">
          <button
            className="button is-link is-light"
            onClick={(e)=>{ e.preventDefault(); setPage("details"); }}
          >Cancel</button>
        </div>
      </div>
    </form>
  );
}

function useUpdateSample({ sample, body }: { sample: SampleType, body: SampleBody }){
  const params = useParams();
  const auth0Fetch = useAuth0Fetch();
  const sampleId = sample._id;
  const loadedGetter = useCallback(async ()=>{
    const { whitelabelId, audioId } = params;
    if(typeof whitelabelId !== "string") throw new Error("No Whitelabel Available");
    if(typeof audioId !== "string") throw new Error("No Audio Available");
    return dataSourceAudioItemSampleUpdate(
      auth0Fetch, { whitelabelId, audioId, sampleId }, body
    );
  }, [auth0Fetch, params.whitelabelId, params.audioId, sampleId, body]);
  return useRunGetter(loadedGetter);
}
