
> @divinci-ai/web-client@0.3.0 webpack:codespace /workspaces/server/workspace/clients/web
> webpack serve --config webpack.codespace.config.js --host 0.0.0.0 --port 8080

🐞LOG_DEBUG MODE:  1
set __env: /workspaces/server/workspace/clients/web/env
<i> [webpack-dev-server] Project is running at:
<i> [webpack-dev-server] Loopback: https://localhost:8080/, https://[::1]:8080/
<i> [webpack-dev-server] On Your Network (IPv4): https://**********:8080/
<i> [webpack-dev-server] Content not from webpack is served from '/workspaces/server/workspace/clients/web/public' directory
<i> [webpack-dev-server] 404s will fallback to '/index.html'
<i> [webpack-dev-middleware] wait until bundle finished: /
assets by chunk 16.5 MiB (id hint: vendors)
  asset vendors-node_modules_mermaid_dist_flowchart-elk-definition-4a651766_js.hidden.build.js 4.25 MiB [emitted] (id hint: vendors)
  asset vendors-node_modules_mermaid_dist_mindmap-definition-fc14e90a_js.hidden.build.js 3.3 MiB [emitted] (id hint: vendors)
  asset vendors-node_modules_crypto-browserify_index_js.hidden.build.js 1.84 MiB [emitted] (id hint: vendors)
  + 29 assets
assets by info 30.9 KiB [immutable]
  asset a77de540a38981833f9e.eot 11.9 KiB [emitted] [immutable] [from: ../../../node_modules/react-multi-carousel/lib/revicons.eot] (auxiliary name: main)
  asset 57fd05d4ae650374c8de.ttf 11.7 KiB [emitted] [immutable] [from: ../../../node_modules/react-multi-carousel/lib/revicons.ttf] (auxiliary name: main)
  asset e8746a624ed098489406.woff 7.36 KiB [emitted] [immutable] [from: ../../../node_modules/react-multi-carousel/lib/revicons.woff] (auxiliary name: main)
+ 7 assets
orphan modules 1.2 MiB [orphan] 1100 modules
runtime modules 31 KiB 17 modules
modules by path ../../ 22 MiB (javascript) 30.9 KiB (asset)
  modules by path ../../../node_modules/ 21.7 MiB (javascript) 30.9 KiB (asset)
    javascript modules 21.6 MiB 2010 modules
    json modules 99.9 KiB 8 modules
    asset modules 30.9 KiB (asset) 126 bytes (javascript) 3 modules
  modules by path ../../resources/ 272 KiB 230 modules
modules by path ./ 1.99 MiB 735 modules
modules by mime type image/svg+xml 6.61 KiB
  data:image/svg+xml;base64,PHN2ZyB3aWR0aD0i.. 6.36 KiB [built] [code generated]
  data:image/svg+xml;utf8,<svg xmlns="http.. 252 bytes [built] [code generated]
+ 10 modules
webpack 5.99.9 compiled successfully in 35418 ms
 ELIFECYCLE  Command failed.
