/**
 * Update file mapping with processing results (called by worker)
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { DropboxFileMappingModel } from "@divinci-ai/server-models";
import { ensureWhitelabel } from "../util/ensure-whitelabel";

interface UpdateFileMappingRequest {
  dropboxFileId: string,
  internalFileId?: string,
  syncStatus: string,
  processingMetadata?: {
    transcriptionId?: string,
    diarizationId?: string,
    workflowId?: string,
    chunkCount?: number,
    vectorCount?: number,
    processingDuration?: number,
    r2ObjectKey?: string,
    r2Url?: string,
    processedAt?: string,
    [key: string]: any,
  },
}

export const updateDropboxFileMapping: RequestHandler = async function(req, res, next){
  try {
    const { whitelabel } = await ensureWhitelabel(req);
    const {
      dropboxFileId,
      internalFileId,
      syncStatus,
      processingMetadata
    }: UpdateFileMappingRequest = req.body;

    if(!dropboxFileId || !syncStatus) {
      return res.status(400).json({
        success: false,
        error: "missing_required_fields",
        message: "Dropbox file ID and sync status are required"
      });
    }

    // Find the file mapping
    const fileMapping = await DropboxFileMappingModel.findByDropboxFileId(dropboxFileId);

    if(!fileMapping) {
      return res.status(404).json({
        success: false,
        error: "file_mapping_not_found",
        message: "File mapping not found"
      });
    }

    // Verify file mapping belongs to this whitelabel
    if(fileMapping.whitelabelId !== whitelabel._id.toString()) {
      return res.status(403).json({
        success: false,
        error: "access_denied",
        message: "Access denied to this file mapping"
      });
    }

    // Update sync status
    await fileMapping.updateSyncStatus(
      syncStatus as any,
      undefined, // syncJobId - could be added if needed
      internalFileId
    );

    // Add processing metadata if provided
    if(processingMetadata) {
      await fileMapping.addProcessingMetadata(processingMetadata);
    }

    res.status(200).json({
      success: true,
      data: {
        dropboxFileId,
        syncStatus: fileMapping.syncStatus,
        internalFileId: fileMapping.internalFileId,
        lastSyncAt: fileMapping.lastSyncAt,
        processingMetadata: fileMapping.processingMetadata
      },
      message: "File mapping updated successfully"
    });

  }catch(error) {
    console.error("Update file mapping error:", error);
    next(error);
  }
};
