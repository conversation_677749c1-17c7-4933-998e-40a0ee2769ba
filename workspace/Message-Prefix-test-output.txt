
> @divinci-ai/test-client@0.3.0 start:dev /Users/<USER>/Documents/server3/server/workspace/clients/tests
> ts-node --transpileOnly src/index.ts

node:internal/modules/cjs/loader:1404
  throw err;
  ^

Error: Cannot find module '/Users/<USER>/Documents/server3/server/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1.11.13_@types+node@22.13.13_typescript@5.8.3/node_modules/ts-node/dist/bin.js'
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49 {
  code: 'MODULE_NOT_FOUND',
  requireStack: []
}

Node.js v22.15.0
 ELIFECYCLE  Command failed with exit code 1.
