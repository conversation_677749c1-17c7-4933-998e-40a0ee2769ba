/**
 * Fine Tune API Tests
 *
 * This file contains tests for the Fine Tune API.
 */

import { test, expect } from '@playwright/test';
import { ApiClient } from '../api/api-client';
import { getAuthFileForRole } from '../auth/auth-utils';
import { config } from '../config/config';

// Mock Fine Tune API responses
const mockFineTuneResponse = {
  _id: 'mock-finetune-123',
  name: 'Test Fine Tune',
  model: 'gpt-3.5-turbo',
  status: 'completed',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  trainingFiles: [
    {
      filename: 'training-data.jsonl',
      size: 1024,
      uploadedAt: new Date().toISOString()
    }
  ]
};

// Mock API client for Fine Tune
class MockFineTuneClient extends ApiClient {
  constructor() {
    super(config.apiBaseUrl, getAuthFileForRole('admin'));
  }

  async get(path: string) {
    if (path === 'finetune') {
      return [mockFineTuneResponse];
    } else if (path.startsWith('finetune/')) {
      return mockFineTuneResponse;
    }
    return Promise.resolve({});
  }

  async post(path: string, data: any) {
    if (path === 'finetune') {
      return {
        ...mockFineTuneResponse,
        name: data.name || 'New Fine Tune',
        model: data.model || 'gpt-3.5-turbo'
      };
    } else if (path.includes('finetune') && path.includes('upload')) {
      return {
        success: true,
        filename: 'training-data.jsonl',
        size: 1024
      };
    }
    return Promise.resolve({});
  }

  async delete(path: string) {
    if (path.startsWith('finetune/')) {
      return { success: true };
    }
    return Promise.resolve({});
  }
}

test.describe('Fine Tune API', () => {
  let client: MockFineTuneClient;

  test.beforeEach(() => {
    client = new MockFineTuneClient();
  });

  test('should create a new fine tune', async () => {
    const fineTune = await client.post('finetune', {
      name: 'Test Fine Tune',
      model: 'gpt-3.5-turbo'
    });

    expect(fineTune).toBeDefined();
    expect(fineTune._id).toBeDefined();
    expect(fineTune.name).toBe('Test Fine Tune');
    expect(fineTune.model).toBe('gpt-3.5-turbo');
    expect(fineTune.status).toBeDefined();
  });

  test('should get all fine tunes', async () => {
    const fineTunes = await client.get('finetune');

    expect(fineTunes).toBeInstanceOf(Array);
    expect(fineTunes.length).toBeGreaterThan(0);
    expect(fineTunes[0]._id).toBeDefined();
    expect(fineTunes[0].name).toBeDefined();
  });

  test('should get a fine tune by ID', async () => {
    const fineTune = await client.get('finetune/mock-finetune-123');

    expect(fineTune).toBeDefined();
    expect(fineTune._id).toBe('mock-finetune-123');
    expect(fineTune.name).toBe('Test Fine Tune');
    expect(fineTune.model).toBe('gpt-3.5-turbo');
  });

  test('should upload a training file', async () => {
    const result = await client.post('finetune/mock-finetune-123/upload', {
      file: 'mock-file-data'
    });

    expect(result).toBeDefined();
    expect(result.success).toBe(true);
    expect(result.filename).toBeDefined();
    expect(result.size).toBeGreaterThan(0);
  });

  test('should delete a fine tune', async () => {
    const result = await client.delete('finetune/mock-finetune-123');

    expect(result).toBeDefined();
    expect(result.success).toBe(true);
  });
});
