import React, { useCallback, useState, useEffect } from "react";

import { useNavigate, usePara<PERSON>, Link } from "react-router-dom";
import { useAuth0Fetch } from "../../../../../../../../globals/auth0-user";
import { ENVIRONMENT } from "../../../../../../../../globals/constants/clients";

import { BodyType, runner, SuccessResultSingle } from "./types";
import { Form } from "./Form";
import { replaceParams } from "@divinci-ai/utils";
import { PATH_WHITELABEL_DATASOURCE_AUDIO_ROOT } from "../../../paths";
import { BatchViewer } from "../../../../../../../../components/displays/BatchStatus";

export function AudioCreateFile() {
  const params = useParams();
  const auth0Fetch = useAuth0Fetch();
  const [body, setBody] = useState<BodyType>({
    mediaFile: null, diarizerTool: "", transcriberTool: ""
  });
  const [activeBulk, setActiveBulk] = useState<Promise<SuccessResultSingle>[]>([]);
  const [state, setState] = useState<"empty" | "loading" | "success" | "fail">(() => "empty");
  const [error, setError] = useState<any>(null);
  const [lastUploadedId, setLastUploadedId] = useState<string>("");
  const navigate = useNavigate();

  // Create a memoized function to generate the promises for parallel processing
  const memoRunner = useCallback(() => {
    const whitelabelId = params.whitelabelId;
    if (typeof whitelabelId !== "string") throw new Error("No Whitelabel Available");

    const mediaFiles = body.mediaFile;
    if (!mediaFiles) throw new Error("Please select at least one file to upload");
    
    // Ensure mediaFiles is an array
    const mediaFilesArray = Array.isArray(mediaFiles) ? mediaFiles : [mediaFiles];
    
    if (mediaFilesArray.length === 0) throw new Error("Please select at least one file to upload");


    // Create a copy of the body without the files
    const bodyWithoutFiles = { ...body, mediaFile: undefined };

    // Create a promise for each file
    return mediaFilesArray.map((file) => {
      console.log("🔍 Creating promise for file:", file.name);
      return runner(
        auth0Fetch,
        { whitelabelId },
        { ...bodyWithoutFiles, mediaFile: file } // Pass the file directly as mediaFile
      );
    });
  }, [auth0Fetch, params.whitelabelId, body]);

  // This function is no longer used since we're handling the upload directly in handleSubmit
  // But we'll keep it for reference in case we need to revert
  /*
  const run = useCallback(async () => {
    try {
      setError(null);
      setState("loading");

      // Generate the promises for parallel processing
      const bulk = memoRunner();
      setActiveBulk(bulk);

      // Wait for all promises to settle (complete or fail)
      const results = await Promise.allSettled(bulk);

      // Check if any uploads failed
      const failedUploads = results.filter(result => result.status === 'rejected');
      if (failedUploads.length > 0) {
        console.error("Failed uploads:", failedUploads);
        throw new Error(`${failedUploads.length} file(s) failed to upload`);
      }

      // Get the last successful result to navigate to
      const lastSuccessfulResult = results
        .filter((result): result is PromiseFulfilledResult<SuccessResultSingle> =>
          result.status === 'fulfilled')
        .pop();

      if (!lastSuccessfulResult) {
        throw new Error("No files were successfully uploaded");
      }

      console.log("All uploads completed successfully:", results);
      setState("success");
      return lastSuccessfulResult.value;
    } catch (e) {
      console.error("Error during upload:", e);
      setState("fail");
      setError(e);
      throw e;
    }
  }, [memoRunner]);
  */

  const isFormValid = body.mediaFile && 
    (Array.isArray(body.mediaFile) ? body.mediaFile.length > 0 : true) &&
    body.diarizerTool && body.transcriberTool;

  // Function to poll the status of an audio file
  const pollAudioStatus = async (audioId: string, maxAttempts = 60, interval = 5000) => {
    console.log(`Starting to poll status for audioId: ${audioId}`);
    let attempts = 0;

    while (attempts < maxAttempts) {
      try {
        const statusUrl = `/white-label/${params.whitelabelId}/data-source/audio-transcript/${audioId}/status`;
        console.log(`Polling status endpoint: ${statusUrl}`);

        const response = await auth0Fetch(statusUrl);
        const data = await response.json();

        console.log(`Status response:`, data);

        if (data.status === 'completed') {
          console.log('Processing completed successfully');
          return true;
        } else if (data.status === 'failed') {
          console.error('Processing failed:', data.error);
          return false;
        }

        // Wait before polling again
        await new Promise(resolve => setTimeout(resolve, interval));
        attempts++;
      } catch (error) {
        console.error('Error polling status:', error);
        return false;
      }
    }

    console.warn('Polling timeout exceeded');
    return false;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      // Detect local mode and log environment info
      const isLocalMode = ENVIRONMENT === 'local' || ENVIRONMENT === 'development';
      console.log(`🔍 Environment detected: ${ENVIRONMENT}, using ${isLocalMode ? 'local' : 'standard'} mode`);
      console.log("🚀 Starting file upload...");
<<<<<<< HEAD
      
=======

      console.log("🔍 Form submission with voiceprint config:", {
        voiceprintEnabled: body.voiceprintConfig?.enabled,
        autoCreate: body.voiceprintConfig?.autoCreateFromHighConfidence
      });

>>>>>>> WA-170_MCP
      setError(null);
      setState("loading");

      // Start the upload process - the runner will automatically use local mode if needed
      const bulk = memoRunner();
      setActiveBulk(bulk);

      // Wait for the first file to complete
      const firstFilePromise = bulk[0];
      const firstFileResult = await firstFilePromise;
      console.log("✅ First file uploaded successfully:", firstFileResult);

      // Store the audioId in localStorage for the E2E test to find
      if (firstFileResult && firstFileResult._id) {
        const audioId = firstFileResult._id;
        localStorage.setItem('lastUploadedAudioId', audioId);
        console.log(`📝 Stored audioId in localStorage: ${audioId}`);

        // Set the last uploaded ID for highlighting in the list
        setLastUploadedId(audioId);

        // Navigate to the Audio File List page
        const audioListUrl = replaceParams(PATH_WHITELABEL_DATASOURCE_AUDIO_ROOT, params);
        console.log(`🔄 Navigating to Audio File List: ${audioListUrl}`);
        navigate(audioListUrl);

        // Poll the status in the background (this will continue even after navigation)
        pollAudioStatus(audioId).then(success => {
          console.log(`🔄 Polling completed with success: ${success}`);
          if (success) {
            setState("success");
          } else {
            setState("fail");
          }
        });
      }

      // Continue processing the rest of the files in the background
      if (bulk.length > 1) {
        console.log(`🔄 Processing remaining ${bulk.length - 1} files in the background...`);
        Promise.allSettled(bulk.slice(1)).then(results => {
          console.log("✅ All remaining files processed:", results);
        }).catch(error => {
          console.error("❌ Error processing remaining files:", error);
        });
      }
    } catch (e) {
      console.error("❌ Error during form submission:", e);
      setState("fail");
      setError(e);
    }
  };

  return (
    <div className="section">
      <div className="container">
        <nav className="breadcrumb mb-5" aria-label="breadcrumbs">
          <ul>
            <li>
              <Link to={replaceParams(PATH_WHITELABEL_DATASOURCE_AUDIO_ROOT, params)}>
                <span className="icon is-small">
                  <i className="fas fa-headphones" aria-hidden="true"></i>
                </span>
                <span>Audio Transcripts</span>
              </Link>
            </li>
            <li className="is-active">
              <a href="#" aria-current="page">
                <span className="icon is-small">
                  <i className="fas fa-upload" aria-hidden="true"></i>
                </span>
                <span>Upload File</span>
              </a>
            </li>
          </ul>
        </nav>

        <form onSubmit={handleSubmit}>
          <Form value={body} onChange={setBody} />

          {error && (
            <div className="notification is-danger mt-5">
              <button className="delete" onClick={() => window.location.reload()}></button>
              <p className="has-text-weight-bold">Error</p>
              <p>{error.toString()}</p>
            </div>
          )}

          <div className="field is-grouped mt-5">
            <div className="control">
              <button
                type="submit"
                className="button is-primary is-large"
                disabled={!isFormValid}
              >
                <span className="icon">
                  <i className="fas fa-upload"></i>
                </span>
                <span>Upload and Process</span>
              </button>
            </div>
            <div className="control">
              <Link
                to={replaceParams(PATH_WHITELABEL_DATASOURCE_AUDIO_ROOT, params)}
                className="button is-light is-large"
              >
                Cancel
              </Link>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}

