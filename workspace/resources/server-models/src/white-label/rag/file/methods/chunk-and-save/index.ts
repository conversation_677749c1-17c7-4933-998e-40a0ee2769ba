// Legacy implementation
import { pipeline } from "node:stream/promises";
import {
  IRagVectorFileModelType,
  RagVectorTextChunk,
} from "../../types";
import { RawFileToChunks } from "@divinci-ai/server-tools";
import { delay } from "@divinci-ai/utils";
import { RagVectorTextChunksStatus } from "@divinci-ai/models";
import { DivinciTestProcessConfig, DIVINCI_TEST_PROCESS_CONFIG as DIVINCI_PROCESS  } from "@divinci-ai/server-globals";

import { WriteToMongo } from "./WriteToMongo";
import { increaseDelayWhenData } from "./increaseDelayWhenData";
import { chunkAndSaveWorkflow } from "./chunk-and-save-workflow";

const FIVE_MINUTES = 1000 * 60 * 5;
const TWO_MINUTES = 1000 * 60 * 2;

// Environment flag to control which implementation to use
// Default to true in local development for better debugging
const USE_WORKFLOW = process.env.USE_CHUNK_WORKFLOW !== "false";

export const chunkAndSave = async function(
  doc: InstanceType<IRagVectorFileModelType>,
  tool: RawFileToChunks,
  processConfig: null | DivinciTestProcessConfig,
  ragVectors?: Array<string>
){
  // Use the workflow implementation if enabled
<<<<<<< HEAD
  if (USE_WORKFLOW) {
    return chunkAndSaveWorkflow(doc, tool, processConfig);
=======
  if(USE_WORKFLOW) {
    return chunkAndSaveWorkflow(doc, tool, processConfig, ragVectors);
>>>>>>> WA-170_MCP
  }

  // Legacy implementation
  const model = doc.constructor as IRagVectorFileModelType;
  try {
    await DIVINCI_PROCESS.runConfig(processConfig);


    const { readable: chunker, cancel } = await tool.transformFile(doc.rawR2Pointer());
    const mongoWriter = new WriteToMongo<
      { text: string },
      Omit<RagVectorTextChunk, "_id">
    >(model, doc._id.toString(), "chunks", ({ text })=>({
      text: text,
      tags: [],
    }));


    await Promise.race([
      pipeline(
        chunker,
        mongoWriter
      ),
      Promise.resolve().then(async ()=>{
        await delay(FIVE_MINUTES);
        try {
          await increaseDelayWhenData(chunker, TWO_MINUTES);
        }catch(e){
          Promise.resolve().then(()=>{
            try {
              // Cleanup streams after throw
              cancel();
              chunker.destroy();
              mongoWriter.destroy();
            }catch(e){
              console.error("❌ Error cleaning up streams", e);
            }
          });
          throw new Error("Chunking took too long");
        }
      })
    ]);
    const length = await doc.getChunkLength();
    await model.findByIdAndUpdate(
      doc._id,
      { $set: { status: RagVectorTextChunksStatus.EDITING, indexOffset: length } },
    );
  }catch(e: any){
    await model.findByIdAndUpdate(
      doc._id,
      { $set: {
        status: RagVectorTextChunksStatus.FAILED_TO_CHUNK,
        failureReason: e.message,
      } },
    );
  }
};
