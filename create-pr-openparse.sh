#!/bin/bash

# Script to create a PR for OpenParse-RAG with batched commits

# Reset the working directory to a clean state
git reset --hard

# Create a new branch from develop
git checkout -b clean-PR-Split-4-OpenParse-RAG-v2 origin/develop

# Get the list of files that match the pattern from the current branch
FILES=$(git diff --name-only origin/develop origin/AS-211_AS-176-Workflow-Polish_2 | grep -E "open-parse|RAG|workflow|chunking|markitdown" || true)

if [ -z "$FILES" ]; then
  echo "No files found matching pattern"
  git checkout AS-211_AS-176-Workflow-Polish_2
  exit 1
fi

echo "Found $(echo "$FILES" | wc -l) files matching pattern"

# Create batches of files (10 files per batch)
BATCH_SIZE=10
TOTAL_FILES=$(echo "$FILES" | wc -l)
BATCH_COUNT=$(( (TOTAL_FILES + BATCH_SIZE - 1) / BATCH_SIZE ))

echo "Processing $TOTAL_FILES files in $BATCH_COUNT batches"

# Process each batch
for ((i=1; i<=BATCH_COUNT; i++)); do
  echo "Processing batch $i of $BATCH_COUNT"
  
  # Get the files for this batch
  START_LINE=$(( (i-1) * BATCH_SIZE + 1 ))
  END_LINE=$(( i * BATCH_SIZE ))
  BATCH_FILES=$(echo "$FILES" | sed -n "${START_LINE},${END_LINE}p")
  
  # For each file in the batch
  for FILE in $BATCH_FILES; do
    echo "Processing file: $FILE"
    
    # Check if the file exists in the source branch
    if git show origin/AS-211_AS-176-Workflow-Polish_2:$FILE > /dev/null 2>&1; then
      # Create the directory structure if it doesn't exist
      mkdir -p $(dirname "$FILE")
      
      # Copy the file from the source branch
      git show origin/AS-211_AS-176-Workflow-Polish_2:$FILE > "$FILE"
      
      # Add the file to the staging area
      git add "$FILE"
    else
      echo "File not found in AS-211_AS-176-Workflow-Polish_2: $FILE"
    fi
  done
  
  # Commit the batch
  git commit -m "Add files for PR-Split-4-OpenParse-RAG (batch $i of $BATCH_COUNT)"
done

# Push the branch
git push -f --no-verify origin clean-PR-Split-4-OpenParse-RAG-v2:PR-Split-4-OpenParse-RAG-v2

# Create the PR
gh pr create \
  --base develop \
  --head PR-Split-4-OpenParse-RAG-v2 \
  --title "PR-Split-4: Open-Parse Tool and RAG Workflow" \
  --body "## Description
Enhances open-parse tool with file URL support and improves RAG workflow.

## Key Changes
- Adds file URL support to open-parse tool
- Improves RAG workflow
- Enhances chunking capabilities

## Notes
This is part of the PR split from the original PR #579 (AS-211_AS-176-Workflow-Polish_2).
This PR contains only the Open-Parse tool and RAG workflow changes.

## Files Changed
- Open-parse and RAG workflow related files
- Chunking implementation files

## Merge Order
This PR should be merged fourth, after PR-Split-2-mTLS-Security."

# Go back to the original branch
git checkout AS-211_AS-176-Workflow-Polish_2

echo "PR created successfully!"
