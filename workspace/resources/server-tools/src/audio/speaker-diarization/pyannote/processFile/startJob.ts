import { handleFetch } from "@divinci-ai/utils";

<<<<<<< HEAD
import { PYANNOTE_APIKEY, PyannoteJobStatus } from "./pyannote-constants";
import { requireEnvVar } from "@divinci-ai/server-utils";

// Get the local Pyannote service URL
const DIVINCI_AUDIO_DIARIZER_PYANNOTE_URL = requireEnvVar("DIVINCI_AUDIO_DIARIZER_PYANNOTE_URL");

export async function startJob({ publicAudioUrl }: { publicAudioUrl: string }){
  // Check if we're in local mode
  const NODE_ENV = process.env.NODE_ENV || "";
  const ENVIRONMENT = process.env.ENVIRONMENT || "";
  const IS_LOCAL_MODE = NODE_ENV === "development" || NODE_ENV === "local" ||
                      ENVIRONMENT === "local" || ENVIRONMENT === "development";

  // For local mode, we need to send the URL to the Pyannote service
  if (IS_LOCAL_MODE) {
    console.log(`🔍 Using local Pyannote service at: ${DIVINCI_AUDIO_DIARIZER_PYANNOTE_URL}`);

    try {
      // Send the URL to the local Pyannote service
      console.log(`📤 Sending URL to local Pyannote service: ${publicAudioUrl}`);

      // Use JSON request with URL instead of form upload
      const response = await fetch(DIVINCI_AUDIO_DIARIZER_PYANNOTE_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: publicAudioUrl
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Local Pyannote service error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      // Parse the response
      const diarization = await response.json();

      // Return a compatible response format
      return {
        jobId: "local-job",
        status: "succeeded" as PyannoteJobStatus,
        output: { diarization }
      };
    } catch (error) {
      console.error("❌ Error in local Pyannote processing:", error);
      throw error;
    }
  } else {
    // Use the external Pyannote API for non-local environments
    console.log(`🔍 Using external Pyannote API`);

    return await handleFetch(fetch(
      "https://api.pyannote.ai/v1/diarize",
      {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${PYANNOTE_APIKEY}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          url: publicAudioUrl
        })
      }
    )) as {
      "jobId": string,
      "status": PyannoteJobStatus,
      "output"?: {
        "diarization": any
      }
    };
  }
=======
import {
  PYANNOTE_APIKEY,
  PYANNOTE_ENDPOINTS,
  PyannoteJobStatus
} from "./pyannote-constants";
import { requireEnvVar } from "@divinci-ai/server-utils";
import type { DiarizationConfig, VoiceprintConfig, VoiceprintInfo } from "../../types";

// Note: This file is for the Official Pyannote API service only
// The local Divinci Pyannote service is handled separately

// Enhanced diarization job with confidence and speaker options
// This function is for the OFFICIAL Pyannote API service and should ALWAYS use the external API
export async function startJob({
  publicAudioUrl,
  config = {}
}: {
  publicAudioUrl: string;
  config?: Partial<DiarizationConfig>;
}){
  // CRITICAL: Official Pyannote API MUST ALWAYS use the external API service
  // This should NEVER fall back to local services, regardless of environment
  console.log(`🔍 OFFICIAL PYANNOTE API: Using external service at ${PYANNOTE_ENDPOINTS.DIARIZE}`);
  console.log(`🔍 OFFICIAL PYANNOTE API: This should NEVER use local Divinci service`);

  // Verify we're using the correct endpoint
  if (!PYANNOTE_ENDPOINTS.DIARIZE.includes('api.pyannote.ai')) {
    console.error(`🚨 CRITICAL ERROR: Official Pyannote API endpoint is incorrect: ${PYANNOTE_ENDPOINTS.DIARIZE}`);
    throw new Error(`Official Pyannote API endpoint is incorrect. Expected api.pyannote.ai, got: ${PYANNOTE_ENDPOINTS.DIARIZE}`);
  }

  // Build request body with optional parameters
  const requestBody: any = {
    url: publicAudioUrl,
  };

  // Add optional parameters if provided
  if (config.confidence !== undefined) {
    requestBody.confidence = config.confidence;
  }
  if (config.numSpeakers !== undefined) {
    requestBody.numSpeakers = config.numSpeakers;
  }
  if (config.webhook) {
    requestBody.webhook = config.webhook;
  }

  console.log(`📤 OFFICIAL PYANNOTE API: Sending request to ${PYANNOTE_ENDPOINTS.DIARIZE}`);
  console.log(`📤 OFFICIAL PYANNOTE API: Request config:`, requestBody);

  try {
    const response = await handleFetch(fetch(
      PYANNOTE_ENDPOINTS.DIARIZE,
      {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${PYANNOTE_APIKEY}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody)
      }
    )) as {
      "jobId": string,
      "status": PyannoteJobStatus,
      "output"?: {
        "diarization": any,
        "confidence"?: any
      }
    };

    console.log(`✅ OFFICIAL PYANNOTE API: Successfully submitted job with ID: ${response.jobId}`);
    return response;
  } catch (error: any) {
    console.error(`🚨 OFFICIAL PYANNOTE API: Error calling external service:`, error);

    // Log detailed error information if available
    if (error.json) {
      console.error(`🚨 OFFICIAL PYANNOTE API: Detailed error response:`, JSON.stringify(error.json, null, 2));

      // Log individual error objects if they exist
      if (error.json.errors && Array.isArray(error.json.errors)) {
        console.error(`🚨 OFFICIAL PYANNOTE API: Individual errors:`);
        error.json.errors.forEach((err: any, index: number) => {
          console.error(`  Error ${index + 1}:`, JSON.stringify(err, null, 2));
        });
      }
    }
    if (error.statusCode) {
      console.error(`🚨 OFFICIAL PYANNOTE API: Status code:`, error.statusCode);
    }
    if (error.url) {
      console.error(`🚨 OFFICIAL PYANNOTE API: Request URL:`, error.url);
    }

    throw new Error(`Official Pyannote API error: ${error}`);
  }
}

// Create voiceprint from audio URL
export async function createVoiceprint({
  audioUrl,
  config = {}
}: {
  audioUrl: string;
  config?: Partial<VoiceprintConfig>;
}): Promise<VoiceprintInfo> {
  console.log(`🎤 Creating voiceprint from: ${audioUrl}`);

  // Build request body
  const requestBody: any = {
    url: audioUrl,
  };

  // Add optional webhook
  if (config.webhook) {
    requestBody.webhook = config.webhook;
  }

  console.log(`📤 Sending voiceprint creation request:`, requestBody);
  console.log(`🔑 Using API key (first 10 chars): ${PYANNOTE_APIKEY.substring(0, 10)}...`);
  console.log(`🌐 Endpoint: ${PYANNOTE_ENDPOINTS.VOICEPRINT}`);

  try {
    const response = await handleFetch(fetch(
      PYANNOTE_ENDPOINTS.VOICEPRINT,
      {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${PYANNOTE_APIKEY}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody)
      }
    )) as {
      "jobId": string,
      "status": PyannoteJobStatus,
      "output"?: any
    };

    // Create VoiceprintInfo object
    const voiceprintInfo: VoiceprintInfo = {
      jobId: response.jobId,
      status: response.status,
      voiceprintData: response.output,
      createdFrom: {
        speaker: config.metadata?.speakerLabel || "unknown",
        start: 0, // Will be filled in by caller
        end: 0,   // Will be filled in by caller
        audioUrl: audioUrl,
      },
      timestamp: Date.now(),
      name: config.metadata?.name,
      speakerLabel: config.metadata?.speakerLabel,
    };

    console.log(`✅ Voiceprint job created with ID: ${response.jobId}`);
    return voiceprintInfo;
  } catch (error: any) {
    console.error(`❌ [VOICEPRINT-CREATE] Pyannote API error details:`, {
      statusCode: error.statusCode,
      url: error.url,
      message: error.message,
      errorDetails: error.json,
      stack: error.stack
    });

    // Log the request details for debugging
    console.error(`🔍 [VOICEPRINT-CREATE] Request details:`, {
      endpoint: PYANNOTE_ENDPOINTS.VOICEPRINT,
      apiKeyPresent: !!PYANNOTE_APIKEY,
      apiKeyLength: PYANNOTE_APIKEY?.length,
      requestBody: requestBody
    });

    // Log the specific error details if available
    if (error.json && error.json.errors) {
      console.error(`❌ [VOICEPRINT-CREATE] Pyannote validation errors:`, error.json.errors);

      // Log each error object individually for better debugging
      if (Array.isArray(error.json.errors)) {
        console.error(`🔍 [VOICEPRINT-CREATE] Individual error details:`);
        error.json.errors.forEach((err: any, index: number) => {
          console.error(`  Error ${index + 1}:`, JSON.stringify(err, null, 2));
        });
      }
    }

    throw error;
  }
}

// Get job status (for both diarization and voiceprint jobs)
export async function getJobStatus(jobId: string): Promise<{
  jobId: string;
  status: PyannoteJobStatus;
  output?: any;
}> {
  console.log(`🔍 Checking job status for: ${jobId}`);

  return await handleFetch(fetch(
    PYANNOTE_ENDPOINTS.JOB_STATUS(jobId),
    {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${PYANNOTE_APIKEY}`,
      },
    }
  )) as {
    jobId: string;
    status: PyannoteJobStatus;
    output?: any;
  };
>>>>>>> WA-170_MCP
}
