
> @divinci-ai/public-api@0.3.0 start /workspaces/server/workspace/servers/public-api
> node dist/index.js

🐞LOG_DEBUG MODE:  1
🔎 CURRENT ENVIRONMENT: dev | NODE_ENV: development | FORCE_R2_STORAGE: undefined
🔎 CORS FULL ORIGINS (raw): api.slack.com,dev-46tiys6hnb6vbg17-cd-hljzhimsn7o28fvq.edge.tenants.us.auth0.com,dev-46tiys6hnb6vbg17.us.auth0.com,local-chunks-vectorized,127.0.0.1:,obscure-chainsaw-jj4x9rq9gx4qcqw45-8080.app.github.dev,obscure-chainsaw-jj4x9rq9gx4qcqw45-8081.app.github.dev,obscure-chainsaw-jj4x9rq9gx4qcqw45-8082.app.github.dev,obscure-chainsaw-jj4x9rq9gx4qcqw45-8083.app.github.dev,obscure-chainsaw-jj4x9rq9gx4qcqw45-9080.app.github.dev
🔎 CORS FULL ORIGINS (cleaned): [
  'api.slack.com',
  'dev-46tiys6hnb6vbg17-cd-hljzhimsn7o28fvq.edge.tenants.us.auth0.com',
  'dev-46tiys6hnb6vbg17.us.auth0.com',
  'local-chunks-vectorized',
  '127.0.0.1:',
  'obscure-chainsaw-jj4x9rq9gx4qcqw45-8080.app.github.dev',
  'obscure-chainsaw-jj4x9rq9gx4qcqw45-8081.app.github.dev',
  'obscure-chainsaw-jj4x9rq9gx4qcqw45-8082.app.github.dev',
  'obscure-chainsaw-jj4x9rq9gx4qcqw45-8083.app.github.dev',
  'obscure-chainsaw-jj4x9rq9gx4qcqw45-9080.app.github.dev'
]
🔑 [DROPBOX-ENV] Loading Dropbox environment variables...
🔑 [DROPBOX-ENV] Dropbox environment variables loaded successfully {
  clientId: 'snqjeycw...',
  clientSecret: '[PRESENT]',
  redirectUri: 'http://localhost:9080/auth/dropbox/callback'
}
🎈 Attempting to connect to Redis in dev environment: redis://default:<EMAIL>:15121
🔄 Creating simple R2 client for fine-tune in local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🔄 Creating simple Audio R2 client for local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🧪 [TEMP TEST] Using Audio R2 client for chunks files instead of Whitelabel Vector R2 client
🧪 [TEMP TEST] Using workspace-audio bucket for chunks files instead of whitelabel-vector-index
🪣 [API] Bucket selection - isLocalMode: true, forceR2Storage: false
🪣 [API] Selected bucket: rag-files-local
🪣 Using bucket: rag-files-local for environment: dev, forceR2Storage: false
🔍 [JOB-MANAGER] Starting monitoring service (every 5 minutes)
🤖 [RECOVERY] Starting auto-recovery (every 10 minutes)
🔎 [R2] ENVIRONMENT: dev
🔎 [R2] NODE_ENV: development
🔎 [R2] FORCE_R2_STORAGE: undefined
🔎 [R2] isLocalMode: true forceR2Storage: false
🔄 Creating simple R2 client for RAG vectors in local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🔄 Creating simple Public Transcript File R2 client for local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🔧 Local mode detected, using MinIO with path-style URLs
🔧 Using S3 endpoint: http://minio.divinci.local:9000
🔧 Using reliable MinIO endpoint: http://minio.divinci.local:9000
🪣 Using bucket name: workspace-audio, isLocalMode: true
📦 [DROPBOX] Dropbox main router initialized
🔐 [DROPBOX-AUTH] Auth router initialized
🔐 [DROPBOX-AUTH] Registering auth routes: /initiate, /status, /disconnect, /refresh
🔐 [DROPBOX-AUTH] Note: /callback route is handled at router level without auth
🔐 [DROPBOX-AUTH] All auth routes registered successfully
📦 [DROPBOX] Mounting sub-routers: /auth, /files, /sync
📦 [DROPBOX] All sub-routers mounted successfully
🔧 [DROPBOX] Registering Dropbox callback route (no auth required)
🔧 [DROPBOX] Registering Dropbox router at /:whitelabelId/dropbox
🙌 Successful external call to: https://pokeapi.co/api/v2/pokemon/ditto
Attempting Mongoose Connection
🎈 MONGO_CONNECTION_URL: mongodb+srv://serverlessinstance0.c4pobzg.mongodb.net/divinci-dev
Attempting Redis Connection... 
🟢 Connected to Redis in dev environment at 2025-06-17T22:34:29.861Z
✅🌱 Successfully connected to Redis.
✅🌱 Successfully connected to MongoDB.
🔐 mTLS is enabled
🔑 mTLS OPTIONS bypass: enabled
📎 Certificate directory: /workspaces/server/private-keys/local-fast
🔍 Looking for server certificate at /workspaces/server/private-keys/local-fast/server.crt
📄 Certificate content (first 100 chars): -----BEGIN CERTIFICATE-----
MIIDCTCCAfGgAwIBAgIUVES76Ty2CRnyLZu8QlZsKERBaPcwDQYJKoZIhvcNAQEL
BQAwFDE
✅ Loaded server certificate from /workspaces/server/private-keys/local-fast/server.crt
🔍 Looking for server key at /workspaces/server/private-keys/local-fast/server.key
📄 Key content (first 100 chars): -----BEGIN PRIVATE KEY-----
MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC2ikXlFtX4qWKy
avfl+ek
✅ Loaded server key from /workspaces/server/private-keys/local-fast/server.key
🔐 Setting up server-only mTLS (no client certificate verification)
🔐 Set up server-only TLS context that allows all clients
🔐 Creating HTTPS server with mTLS options
🚀 Creating separate HTTP server for OPTIONS requests to bypass mTLS entirely
⏰ Server timeouts configured:
   - Request timeout: 900s
   - Keep-alive timeout: 960s
   - Headers timeout: 1020s
uncaughtException Error: listen EADDRINUSE: address already in use :::8082
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at /workspaces/server/workspace/servers/public-api/dist/app.js:33:27
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8082
}
❌ Error while starting the server:  Error: listen EADDRINUSE: address already in use :::9080
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at waitForListen (/workspaces/server/workspace/servers/public-api/dist/app.js:102:12)
    at /workspaces/server/workspace/servers/public-api/dist/app.js:85:11
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 9080
}
📊 [JOB-MANAGER] Job stats: {
  total: 0,
  succeeded: 0,
  failed: 0,
  processing: 0,
  polling: 0,
  stuck: 0
}
✅ [RECOVERY] System healthy, no recovery needed
✅ [RECOVERY] System healthy, no recovery needed
📊 [JOB-MANAGER] Job stats: {
  total: 0,
  succeeded: 0,
  failed: 0,
  processing: 0,
  polling: 0,
  stuck: 0
}
