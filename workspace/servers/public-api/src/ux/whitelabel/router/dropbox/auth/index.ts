/**
 * Dropbox Authentication Router
 */

import { Router } from "express";

export const router = Router({ mergeParams: true });

console.log("🔐 [DROPBOX-AUTH] Auth router initialized");

// Add middleware to log all requests to auth router
router.use((req, res, next) => {
  console.log(`🔐 [DROPBOX-AUTH] ${req.method} ${req.path}`, {
    originalUrl: req.originalUrl,
    params: req.params,
    hasAuth: !!req.headers.authorization
  });
  next();
});

// Import route handlers
import { initiateDropboxAuth } from "./initiate-auth";
import { handleDropboxCallback } from "./callback";
import { getDropboxConnectionStatus } from "./status";
import { disconnectDropbox } from "./disconnect";
import { refreshDropboxToken } from "./refresh-token";

console.log("🔐 [DROPBOX-AUTH] Registering auth routes: /initiate, /status, /disconnect, /refresh");
console.log("🔐 [DROPBOX-AUTH] Note: /callback route is handled at router level without auth");

// Authentication routes
router.post("/initiate", initiateDropboxAuth);
// router.get("/callback", handleDropboxCallback); // Handled at router level without auth
router.get("/status", getDropboxConnectionStatus);
router.post("/disconnect", disconnectDropbox);
router.post("/refresh", refreshDropboxToken);

console.log("🔐 [DROPBOX-AUTH] All auth routes registered successfully");
