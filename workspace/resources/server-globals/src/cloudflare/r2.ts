import { getOrCreate } from "../util/getOrCreate";
import { S3 as R2 } from "@aws-sdk/client-s3";
import { requireEnvVar } from "@divinci-ai/server-utils";
import mimetypes from "mime-types";

import {
  CLOUDFLARE_ACCOUNT_ID,
  CLOUDFLARE_ACCESS_KEY_ID,
  CLOUDFLARE_ACCESS_KEY_SECRET,
} from "./constants";
// For S3 clients

const R2_CLIENT_CONFIG = {
  requestTimeout: 900000,        // 30 seconds
  connectTimeout: 900000,        // 10 seconds
  maxAttempts: 3,              // Retry up to 3 times
  retryMode: "adaptive" as const,
  //📓 Compatibility Warning: REQUIRED: requestChecksumCalculation, responseChecksumValidation
  // - https://developers.cloudflare.com/r2/examples/aws/aws-sdk-js-v3/
  requestChecksumCalculation: "WHEN_REQUIRED" as const,
  responseChecksumValidation: "WHEN_REQUIRED" as const,
};

export const R2Config = {
  accountId: CLOUDFLARE_ACCOUNT_ID,
  accessKeyId: CLOUDFLARE_ACCESS_KEY_ID,
  secretAccessKey: CLOUDFLARE_ACCESS_KEY_SECRET,
};
const R2_ENDPOINT = `https://${CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`;

export const getR2Instance = getOrCreate(()=>{
  // Check if we're in local development mode
  const isLocalMode = process.env.ENVIRONMENT === "local" ||
                      process.env.NODE_ENV === "development" ||
                      process.env.NODE_ENV === "local";
<<<<<<< HEAD

  if (!isLocalMode) {
    // Production mode - use Cloudflare R2
=======
  const forceR2Storage = process.env.FORCE_R2_STORAGE === "true";
  console.log("🔎 [R2] ENVIRONMENT:", process.env.ENVIRONMENT);
  console.log("🔎 [R2] NODE_ENV:", process.env.NODE_ENV);
  console.log("🔎 [R2] FORCE_R2_STORAGE:", process.env.FORCE_R2_STORAGE);
  console.log("🔎 [R2] isLocalMode:", isLocalMode, "forceR2Storage:", forceR2Storage);

  if (forceR2Storage || !isLocalMode) {
    // Production mode OR forced R2 mode - use Cloudflare R2
    const forcedMsg = (!isLocalMode && forceR2Storage) ? ' (forced in local mode)' : '';
    console.log(`🔄 Creating Cloudflare R2 client${forcedMsg}`);
>>>>>>> WA-170_MCP
    return new R2({
      endpoint: R2_ENDPOINT,
      credentials: {
        accessKeyId: R2Config.accessKeyId,
        secretAccessKey: R2Config.secretAccessKey,
      },
      region: "auto",
      ...R2_CLIENT_CONFIG,
    });
  }

  // Local mode - use a simple client with fixed credentials
<<<<<<< HEAD
  console.log(`🔄 Creating simple R2 client for local development`);

  // Use environment variable if set, otherwise use the reliable endpoint
  const endpoint = process.env.MINIO_ENDPOINT || "http://minio.divinci.local:9000";
  console.log(`🔄 Using MinIO endpoint: ${endpoint}`);

  // Create the client with fixed credentials
  return new R2({
    endpoint: endpoint,
    credentials: {
      accessKeyId: "minioadmin",
      secretAccessKey: "minioadmin",
    },
    region: "auto",
    forcePathStyle: true,
    ...R2_CLIENT_CONFIG,
  });
=======
  // Only use this block if not forcing R2 storage
  if (isLocalMode && !forceR2Storage) {
    console.log(`🔄 Creating simple R2 client for local development`);

    // Use environment variable if set, otherwise use the reliable endpoint
    const endpoint = process.env.MINIO_ENDPOINT || "http://minio.divinci.local:9000";
    console.log(`🔄 Using MinIO endpoint: ${endpoint}`);

    // Create the client with fixed credentials
    return new R2({
      endpoint: endpoint,
      credentials: {
        accessKeyId: "minioadmin",
        secretAccessKey: "minioadmin",
      },
      region: "auto",
      forcePathStyle: true,
      ...R2_CLIENT_CONFIG,
    });
  }
>>>>>>> WA-170_MCP
});

import {
  CLOUDFLARE_PUBLIC_TRANSCRIPT_FILE_ACCESS_KEY_ID,
  CLOUDFLARE_PUBLIC_TRANSCRIPT_FILE_ACCESS_KEY_SECRET,
  CLOUDFLARE_PUBLIC_TRANSCRIPT_FILE_ACCESS_BUCKET,
  CLOUDFLARE_PUBLIC_TRANSCRIPT_FILE_ACCESS_PUBLIC_HOST,
} from "./constants";

export const getPublicTranscriptFileR2Instance = getOrCreate(()=>{
  // Check if we're in local development mode
  const isLocalMode = process.env.ENVIRONMENT === "local" ||
                      process.env.NODE_ENV === "development" ||
                      process.env.NODE_ENV === "local";
<<<<<<< HEAD

  let r2;

  if (!isLocalMode) {
    // Production mode - use Cloudflare R2
=======
  const forceR2Storage = process.env.FORCE_R2_STORAGE === "true";

  let r2;

  if (forceR2Storage || !isLocalMode) {
    // Production mode OR forced R2 mode - use Cloudflare R2
    const forcedMsg = (isLocalMode && forceR2Storage) ? ' (forced in local mode)' : '';
    console.log(`🔄 Creating Cloudflare R2 Public Transcript File client${forcedMsg}`);
>>>>>>> WA-170_MCP
    r2 = new R2({
      endpoint: R2_ENDPOINT,
      credentials: {
          accessKeyId: CLOUDFLARE_PUBLIC_TRANSCRIPT_FILE_ACCESS_KEY_ID,
          secretAccessKey: CLOUDFLARE_PUBLIC_TRANSCRIPT_FILE_ACCESS_KEY_SECRET,
      },
      region: "auto",
      ...R2_CLIENT_CONFIG,
    });
  } else {
    // Local mode - use a simple client with fixed credentials
    console.log(`🔄 Creating simple Public Transcript File R2 client for local development`);
<<<<<<< HEAD

    // Use environment variable if set, otherwise use a default endpoint
    const endpoint = process.env.MINIO_ENDPOINT || "http://local-minio:9000";
    console.log(`🔄 Using MinIO endpoint: ${endpoint}`);

=======
    // Use environment variable if set, otherwise use a default endpoint
    const endpoint = process.env.MINIO_ENDPOINT || "http://local-minio:9000";
    console.log(`🔄 Using MinIO endpoint: ${endpoint}`);
>>>>>>> WA-170_MCP
    r2 = new R2({
      endpoint: endpoint,
      credentials: {
        accessKeyId: "minioadmin",
        secretAccessKey: "minioadmin",
      },
      region: "auto",
      forcePathStyle: true,
      ...R2_CLIENT_CONFIG,
    });
  }

  return {
    r2, uploadFile: async (destinationName: string, value: Buffer | string | Uint8Array)=>{
      const mimetype = mimetypes.contentType(destinationName);
      if(mimetype === false){
        throw new Error("❌ No valid mimetype for " + destinationName);
      }

      const url = new URL(`https://${CLOUDFLARE_PUBLIC_TRANSCRIPT_FILE_ACCESS_PUBLIC_HOST}/${destinationName}`);

      // Convert input to Buffer using safe methods
      const safeBuffer = (
        Buffer.isBuffer(value) ? value :
        typeof value === "string" ? Buffer.from(value) :
        Buffer.from(value.buffer)
      );

      await r2.putObject({
        Bucket: CLOUDFLARE_PUBLIC_TRANSCRIPT_FILE_ACCESS_BUCKET,
        Key: destinationName,
        ContentType: mimetype,
        Body: safeBuffer,
      });

      return url.href;
    }
  };
});


import {
  CLOUDFLARE_WHITELABEL_VECTOR_ACCESS_KEY_ID,
  CLOUDFLARE_WHITELABEL_VECTOR_ACCESS_KEY_SECRET,
} from "./constants";

export const getWhitelabelVectorR2Instance = getOrCreate(()=>{
  // Check if we're in local development mode
  const isLocalMode = process.env.ENVIRONMENT === "local" ||
                      process.env.NODE_ENV === "development" ||
                      process.env.NODE_ENV === "local";

<<<<<<< HEAD
  if (!isLocalMode) {
    // Production mode - use Cloudflare R2
=======
  // Check if we should force R2 usage even in local mode
  const forceR2Storage = process.env.FORCE_R2_STORAGE === "true";
  console.log("🔎 [R2] ENVIRONMENT:", process.env.ENVIRONMENT);
  console.log("🔎 [R2] NODE_ENV:", process.env.NODE_ENV);
  console.log("🔎 [R2] FORCE_R2_STORAGE:", process.env.FORCE_R2_STORAGE);
  console.log("🔎 [R2] isLocalMode:", isLocalMode, "forceR2Storage:", forceR2Storage);

  if (forceR2Storage || !isLocalMode) {
    // Production mode OR forced R2 mode - use Cloudflare R2
    const forcedMsg = (isLocalMode && forceR2Storage) ? ' (forced in local mode)' : '';
    console.log(`🔄 Creating Cloudflare R2 Whitelabel Vector client${forcedMsg}`);
>>>>>>> WA-170_MCP
    return new R2({
      endpoint: R2_ENDPOINT,
      credentials: {
        accessKeyId: CLOUDFLARE_WHITELABEL_VECTOR_ACCESS_KEY_ID,
        secretAccessKey: CLOUDFLARE_WHITELABEL_VECTOR_ACCESS_KEY_SECRET,
      },
      region: "auto",
      ...R2_CLIENT_CONFIG,
    });
  }

  // Local mode - use a simple client with fixed credentials
  console.log(`🔄 Creating simple R2 client for RAG vectors in local development`);

  // Use environment variable if set, otherwise use the reliable endpoint
  const endpoint = process.env.MINIO_ENDPOINT || "http://minio.divinci.local:9000";
  console.log(`🔄 Using MinIO endpoint: ${endpoint}`);

  // Create the client with fixed credentials
  return new R2({
    endpoint: endpoint,
    credentials: {
      accessKeyId: "minioadmin",
      secretAccessKey: "minioadmin",
    },
    region: "auto",
    forcePathStyle: true,
    ...R2_CLIENT_CONFIG,
  });
});

import {  CLOUDFLARE_FINE_TUNE_ACCESS_KEY,  CLOUDFLARE_FINE_TUNE_SECRET_ACCESS_KEY } from "./constants";

export const getFineTuneR2Instance = getOrCreate(()=>{
  // Check if we're in local development mode
  const isLocalMode = process.env.ENVIRONMENT === "local" ||
                      process.env.NODE_ENV === "development" ||
                      process.env.NODE_ENV === "local";

<<<<<<< HEAD
  if (!isLocalMode) {
    // Production mode - use Cloudflare R2
=======
  // Check if we should force R2 usage even in local mode
  const forceR2Storage = process.env.FORCE_R2_STORAGE === "true";

  if (forceR2Storage || !isLocalMode) {
    // Production mode OR forced R2 mode - use Cloudflare R2
    const forcedMsg = (isLocalMode && forceR2Storage) ? ' (forced in local mode)' : '';
    console.log(`🔄 Creating Cloudflare R2 Fine-tune client${forcedMsg}`);
>>>>>>> WA-170_MCP
    return new R2({
      endpoint: R2_ENDPOINT,
      credentials: {
        accessKeyId: CLOUDFLARE_FINE_TUNE_ACCESS_KEY,
        secretAccessKey: CLOUDFLARE_FINE_TUNE_SECRET_ACCESS_KEY,
      },
      region: "auto",
      ...R2_CLIENT_CONFIG,
    });
  }

  // Local mode - use a simple client with fixed credentials
  console.log(`🔄 Creating simple R2 client for fine-tune in local development`);

  // Use environment variable if set, otherwise use the reliable endpoint
  const endpoint = process.env.MINIO_ENDPOINT || "http://minio.divinci.local:9000";
  console.log(`🔄 Using MinIO endpoint: ${endpoint}`);

  // Create the client with fixed credentials
  return new R2({
    endpoint: endpoint,
    credentials: {
      accessKeyId: "minioadmin",
      secretAccessKey: "minioadmin",
    },
    region: "auto",
    forcePathStyle: true,
    ...R2_CLIENT_CONFIG,
  });
});


import {
  CLOUDFLARE_AUDIO_ACCESS_KEY,
  CLOUDFLARE_AUDIO_SECRET_ACCESS_KEY,
  CLOUDFLARE_AUDIO_S3,
} from "./constants";

export const getAudioR2Instance = getOrCreate(()=>{
  // Check if we're in local development mode
  const isLocalMode = process.env.ENVIRONMENT === "local" ||
                      process.env.NODE_ENV === "development" ||
                      process.env.NODE_ENV === "local";

<<<<<<< HEAD
  if (!isLocalMode) {
    // Production mode - use Cloudflare R2
=======
  // Check if we should force R2 usage even in local mode
  const forceR2Storage = process.env.FORCE_R2_STORAGE === "true";

  if (forceR2Storage || !isLocalMode) {
    // Production mode OR forced R2 mode - use Cloudflare R2
    const forcedMsg = (isLocalMode && forceR2Storage) ? ' (forced in local mode)' : '';
    console.log(`🔄 Creating Cloudflare R2 Audio client${forcedMsg}`);
>>>>>>> WA-170_MCP
    return new R2({
      endpoint: CLOUDFLARE_AUDIO_S3,
      credentials: {
        accessKeyId: CLOUDFLARE_AUDIO_ACCESS_KEY,
        secretAccessKey: CLOUDFLARE_AUDIO_SECRET_ACCESS_KEY,
      },
      region: "auto",
      ...R2_CLIENT_CONFIG,
    });
  }

  // Local mode - use a simple client with fixed credentials
  console.log(`🔄 Creating simple Audio R2 client for local development`);

  // Use environment variable if set, otherwise use the reliable endpoint
  const endpoint = process.env.MINIO_ENDPOINT || "http://minio.divinci.local:9000";
  console.log(`🔄 Using MinIO endpoint: ${endpoint}`);

  // Create the client with fixed credentials
  return new R2({
    endpoint: endpoint,
    credentials: {
      accessKeyId: "minioadmin",
      secretAccessKey: "minioadmin",
    },
    region: "auto",
    forcePathStyle: true,
    ...R2_CLIENT_CONFIG,
  });
});
