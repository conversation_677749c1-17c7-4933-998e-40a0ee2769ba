name: divinci-mtls-local

networks:
  mtls-network:
    driver: bridge

services:
  # ========================================================================
  # CLIENT Servers (1, web)
  # ========================================================================
  local-web-client:
    extends:
      file: base.yml
      service: base-client
    working_dir: "/home/<USER>/app/clients/web"
    environment:
      ENV_FOLDER: "/home/<USER>/app/clients/web/env"
      HTTP_PORT: 8080
    volumes:
      - ../private-keys/local:/home/<USER>/app/clients/web/env
      # Standard TLS certificates (not mTLS)
      - ../private-keys/local/certs/ssl/server.crt:/etc/ssl/certs/server.crt:ro
      - ../private-keys/local/certs/ssl/server.key:/etc/ssl/private/server.key:ro
      # Entrypoint script
      - ../deploy/docker/ci/client-docker-entrypoint.sh:/home/<USER>/app/deploy/docker/ci/client-docker-entrypoint.sh:ro
    networks:
      - mtls-network
    ports:
      - 8080:8080
      - 8443:8443
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/"]
    entrypoint: ["/bin/bash", "-c", "cp /home/<USER>/app/deploy/docker/ci/client-docker-entrypoint.sh /tmp/client-entrypoint.sh && chmod +x /tmp/client-entrypoint.sh && /tmp/client-entrypoint.sh"]

  # ========================================================================
  # API Servers (1, api)
  # ========================================================================
  local-api:
    extends:
      file: base.yml
      service: base-api
    working_dir: "/home/<USER>/app/servers/public-api"
    environment:
      ENV_FOLDER: "/home/<USER>/app/servers/public-api/env"
<<<<<<< HEAD
=======
      HTTP_PORT: 8080
>>>>>>> WA-170_MCP
      LOG_DEBUG: "1"
      MTLS_ENABLED: "true"
      MTLS_CERT_DIR: "/etc/ssl"
    volumes:
      - ../private-keys/local:/home/<USER>/app/servers/public-api/env
      # mTLS client certificates for connecting to services
      - ../private-keys/local/certs/mtls/ca/ca.crt:/etc/ssl/ca/ca.crt:ro
      - ../private-keys/local/certs/mtls/services/public-api/client.crt:/etc/ssl/certs/client.crt:ro
      - ../private-keys/local/certs/mtls/services/public-api/client.key:/etc/ssl/private/client.key:ro
      # Entrypoint script
      - ../deploy/docker/ci/api-mtls-entrypoint.sh:/home/<USER>/app/servers/public-api/deploy/docker/ci/api-mtls-entrypoint.sh:ro
    networks:
      - mtls-network
    ports:
      - 9080:8080
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/"]
    links:
      - audio-speak-dia-pyannote
      - audio-splitter-ffmpeg
      - local-open-parse
    depends_on:
      - audio-speak-dia-pyannote
      - audio-splitter-ffmpeg
      - local-open-parse
    entrypoint: ["/bin/bash", "-c", "cp /home/<USER>/app/servers/public-api/deploy/docker/ci/api-mtls-entrypoint.sh /tmp/api-entrypoint.sh && chmod +x /tmp/api-entrypoint.sh && /tmp/api-entrypoint.sh"]

  # ========================================================================
  # OPEN-PARSE
  # RAG DOCUMENT CHUNKER
  # ========================================================================
  local-open-parse:
    build:
      context: ../workspace/workers/open-parse
      dockerfile: open-parse.Dockerfile
    volumes:
      # Temp File Storage
      - ../hidden/servers/open-parse:/openparse-tmp
      # Private Keys
      - ../private-keys/local:/home/<USER>/app/env
      # mTLS certificates
      - ../private-keys/local/certs/mtls/ca/ca.crt:/etc/ssl/ca/ca.crt:ro
      - ../private-keys/local/certs/mtls/services/open-parse/server.crt:/etc/ssl/certs/server.crt:ro
      - ../private-keys/local/certs/mtls/services/open-parse/server.key:/etc/ssl/private/server.key:ro
      # Entrypoint script
      - ../deploy/docker/ci/service-mtls-entrypoint.sh:/home/<USER>/app/deploy/docker/ci/service-mtls-entrypoint.sh:ro
    environment:
      OPEN_PARSE_HTTP_PORT: 5000
      ENV_FOLDER: "/home/<USER>/app/env"
      PYTHONUNBUFFERED: 1
      PYTHONPATH: "/home/<USER>/app"
      LOG_LEVEL: DEBUG
      OPENPARSE_LOG_LEVEL: DEBUG
      ENVIRONMENT: "development"
      MTLS_ENABLED: "true"
    networks:
      - mtls-network
    ports:
      - 19002:5000
    entrypoint: ["/bin/bash", "-c", "cp /home/<USER>/app/deploy/docker/ci/service-mtls-entrypoint.sh /tmp/ && chmod +x /tmp/service-mtls-entrypoint.sh && /tmp/service-mtls-entrypoint.sh open-parse"]

  # ========================================================================
  # AUDIO SERVICES
  # ========================================================================
  audio-speak-dia-pyannote:
    build:
      context: ../workspace/workers/audio-speaker-diarization@pyannote
      dockerfile: python.Dockerfile
    volumes:
      # Temp File Storage
      - ../hidden/workers/audio-speaker-diarization@pyannote:/file-tmp
      # Private Keys
      - ../private-keys/local:/home/<USER>/app/env
      # mTLS certificates
      - ../private-keys/local/certs/mtls/ca/ca.crt:/etc/ssl/ca/ca.crt:ro
      - ../private-keys/local/certs/mtls/services/pyannote/server.crt:/etc/ssl/certs/server.crt:ro
      - ../private-keys/local/certs/mtls/services/pyannote/server.key:/etc/ssl/private/server.key:ro
      # Entrypoint script
      - ../deploy/docker/ci/service-mtls-entrypoint.sh:/home/<USER>/app/deploy/docker/ci/service-mtls-entrypoint.sh:ro
    environment:
      HTTP_PORT: 5001
      ENV_FOLDER: "/home/<USER>/app/env"
      MTLS_ENABLED: "true"
    networks:
      - mtls-network
    ports:
      - 5001:5001
    entrypoint: ["/bin/bash", "-c", "cp /home/<USER>/app/deploy/docker/ci/service-mtls-entrypoint.sh /tmp/ && chmod +x /tmp/service-mtls-entrypoint.sh && /tmp/service-mtls-entrypoint.sh pyannote"]

  nginx-mtls-pyannote:
    image: nginx:alpine
    volumes:
      - ./nginx-mtls.conf:/etc/nginx/conf.d/default.conf:ro
      - ../private-keys/local/certs/mtls/ca/ca.crt:/etc/ssl/ca/ca.crt:ro
      - ../private-keys/local/certs/mtls/services/pyannote/server.crt:/etc/ssl/certs/server.crt:ro
      - ../private-keys/local/certs/mtls/services/pyannote/server.key:/etc/ssl/private/server.key:ro
    networks:
      - mtls-network
    ports:
      - 19000:19000
    depends_on:
      - audio-speak-dia-pyannote

  audio-splitter-ffmpeg:
    build:
      context: ../workspace/workers/audio-splitter@ffmpeg
      dockerfile: typescript.Dockerfile
    volumes:
      # Private Keys
      - ../private-keys/local:/home/<USER>/env
      # mTLS certificates
      - ../private-keys/local/certs/mtls/ca/ca.crt:/etc/ssl/ca/ca.crt:ro
      - ../private-keys/local/certs/mtls/services/ffmpeg/server.crt:/etc/ssl/certs/server.crt:ro
      - ../private-keys/local/certs/mtls/services/ffmpeg/server.key:/etc/ssl/private/server.key:ro
      # Entrypoint script
      - ../deploy/docker/ci/service-mtls-entrypoint.sh:/home/<USER>/src/deploy/docker/ci/service-mtls-entrypoint.sh:ro
    environment:
      HTTP_PORT: 5002
      ENV_FOLDER: "/home/<USER>/env"
      MTLS_ENABLED: "true"
    networks:
      - mtls-network
    ports:
      - 19001:5002
    entrypoint: ["/bin/bash", "-c", "cp /home/<USER>/src/deploy/docker/ci/service-mtls-entrypoint.sh /tmp/ && chmod +x /tmp/service-mtls-entrypoint.sh && /tmp/service-mtls-entrypoint.sh ffmpeg"]

  # ========================================================================
  # DATABASE (2, mongodb, redis)
  # ========================================================================
  local-mongodb:
    extends:
      file: base.yml
      service: base-mongodb
    env_file:
      - ../private-keys/local/mongodb.env
    volumes:
      - ../hidden/mongodb:/data/db
    networks:
      - mtls-network
    ports:
      - "27017:27017"

  local-redis:
    extends:
      file: base.yml
      service: base-redis
    env_file:
      - ../private-keys/local/redis.env
    volumes:
      - ../hidden/redis:/data
    networks:
      - mtls-network
