import React, { useEffect, useState } from "react";

import { dataSourceAudioList } from "@divinci-ai/actions";
import { useTrackedGetter, useFetchWithParams } from "../../../../../../util/react/tracked-value";
import { replaceParams } from "@divinci-ai/utils";
import { useParams } from "react-router";
import { Link } from "react-router-dom";
<<<<<<< HEAD
import { PATH_WHITELABEL_DATASOURCE_AUDIO_ITEM } from "../paths";
import { AudioTranscriptStatus, AudioTranscriptDoc, AudioTranscriptPopulated } from "@divinci-ai/models";
import { useAuth0Fetch } from "../../../../../../globals/auth0-user";
=======
import { PATH_WHITELABEL_DATASOURCE_AUDIO_ITEM, PATH_WHITELABEL_DATASOURCE_AUDIO_EXPORTS } from "../paths";
import { AudioTranscriptStatus, AudioTranscriptDoc, AudioTranscriptPopulated } from "@divinci-ai/models";
import { useAuth0Fetch } from "../../../../../../globals/auth0-user";
import { BatchRagProcessor } from "./BatchRagProcessor";
>>>>>>> WA-170_MCP

// Define a type for the audio transcript item
type AudioTranscriptItem = AudioTranscriptDoc & AudioTranscriptPopulated;

// Define a type for the audio processing progress
interface ProcessingProgress {
  status: string;
  progress: number;
  step: string;
}

// Utility function to format file size
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Utility function to format duration in seconds to MM:SS format
const formatDuration = (seconds: number): string => {
  if (!seconds && seconds !== 0) return 'Unknown';

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};
export function AudioTranscriptionList({ refreshInterval = 5000, highlightId = "" }: { refreshInterval?: number, highlightId?: string }){
  const params = useParams();
  const auth0Fetch = useAuth0Fetch();
  const [processingProgress, setProcessingProgress] = useState<Record<string, ProcessingProgress>>({});

<<<<<<< HEAD
=======
  // Batch processing state
  const [selectedAudioFiles, setSelectedAudioFiles] = useState<Set<string>>(new Set());
  const [showBatchProcessor, setShowBatchProcessor] = useState(false);
  const [existingRagFiles, setExistingRagFiles] = useState<Set<string>>(new Set());

>>>>>>> WA-170_MCP
  const { value, loading, error, update } = useTrackedGetter({
    getter: useFetchWithParams(
      dataSourceAudioList, ["whitelabelId"], void 0
    )
  });

<<<<<<< HEAD
  // Set up polling for auto-refresh
  useEffect(() => {
    // Set up polling interval
    const intervalId = setInterval(() => {
      update();
    }, refreshInterval);

    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, [refreshInterval, update]);
=======
  // Smart polling for auto-refresh - adjust frequency based on processing status
  useEffect(() => {
    // Check if we have any processing files
    const hasProcessingFiles = value && value.some(audio =>
      audio.processStatus === AudioTranscriptStatus.Diarization ||
      audio.processStatus === AudioTranscriptStatus.Transcription
    );

    // Use faster polling when files are processing, slower when idle
    const smartInterval = hasProcessingFiles ? refreshInterval : refreshInterval * 3;

    const intervalId = setInterval(() => {
      update();
    }, smartInterval);

    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, [refreshInterval, update, value]);
>>>>>>> WA-170_MCP

  // Poll for processing status of in-progress audio files
  useEffect(() => {
    if (!value || value.length === 0) return;

    // Find audio files that are still processing
    const processingFiles = value.filter(audio =>
      audio.processStatus === AudioTranscriptStatus.Diarization ||
      audio.processStatus === AudioTranscriptStatus.Transcription
    );

    if (processingFiles.length === 0) return;

    // Set up polling for each processing file
    const pollingIntervals = processingFiles.map(audio => {
      return setInterval(async () => {
        try {
          // Fetch the current status
          const response = await auth0Fetch(`/white-label/${params.whitelabelId}/data-source/audio-transcript/${audio._id}/status`);
          const data = await response.json();

          // Update the progress state
          setProcessingProgress(prev => ({
            ...prev,
            [audio._id]: {
              status: data.status || audio.processStatus,
              progress: data.progress || 0,
<<<<<<< HEAD
              step: data.step || (audio.processStatus === AudioTranscriptStatus.Diarization ? 'Diarization' : 'Transcription')
=======
              step: data.step || (audio.processStatus === AudioTranscriptStatus.Diarization ?
                ((audio as any)?.voiceprintConfig?.enabled && (audio as any)?.voiceprintConfig?.existingVoiceprints && (audio as any).voiceprintConfig.existingVoiceprints.length > 0 ? 'Identification' : 'Diarization') :
                'Transcription')
>>>>>>> WA-170_MCP
            }
          }));

          // If processing is complete, update the list
          if (data.status === AudioTranscriptStatus.Completed || data.status === AudioTranscriptStatus.Failed) {
            update();
          }
        } catch (error) {
          console.error(`Error fetching status for audio ${audio._id}:`, error);
        }
      }, refreshInterval);
    });

    // Clean up intervals on unmount or when value changes
    return () => {
      pollingIntervals.forEach(interval => clearInterval(interval));
    };
  }, [value, auth0Fetch, params.whitelabelId, refreshInterval, update]);

<<<<<<< HEAD
=======
  // Extract existing RAG files from the populated audio transcript data
  useEffect(() => {
    if (!value || value.length === 0) {
      setExistingRagFiles(new Set());
      return;
    }

    // Use the ragFiles data that's already populated in the audio transcript objects
    const ragFileSet = new Set(
      value
        .filter(audio => audio.ragFiles && audio.ragFiles.length > 0)
        .map(audio => audio._id)
    );

    setExistingRagFiles(ragFileSet);
  }, [value]);

  // Batch processing functions
  const handleSelectAudio = (audioId: string) => {
    setSelectedAudioFiles(prev => {
      const newSet = new Set(prev);
      if (newSet.has(audioId)) {
        newSet.delete(audioId);
      } else {
        newSet.add(audioId);
      }
      return newSet;
    });
  };

  const handleSelectAll = () => {
    if (!value) return;

    const eligibleAudioIds = value
      .filter(audio =>
        audio.processStatus === AudioTranscriptStatus.Completed &&
        !existingRagFiles.has(audio._id)
      )
      .map(audio => audio._id);

    if (selectedAudioFiles.size === eligibleAudioIds.length) {
      // Deselect all
      setSelectedAudioFiles(new Set());
    } else {
      // Select all eligible
      setSelectedAudioFiles(new Set(eligibleAudioIds));
    }
  };

  const getSelectedAudioFiles = (): AudioTranscriptItem[] => {
    if (!value) return [];
    return value.filter(audio => selectedAudioFiles.has(audio._id));
  };

  const isAudioEligibleForBatch = (audio: AudioTranscriptItem): boolean => {
    return audio.processStatus === AudioTranscriptStatus.Completed && !existingRagFiles.has(audio._id);
  };

  const eligibleAudioCount = value ? value.filter(isAudioEligibleForBatch).length : 0;
  const allEligibleSelected = eligibleAudioCount > 0 && selectedAudioFiles.size === eligibleAudioCount;

>>>>>>> WA-170_MCP
  // Function to get status badge class based on process status
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case AudioTranscriptStatus.Completed:
        return "is-success";
      case AudioTranscriptStatus.Failed:
        return "is-danger";
      case AudioTranscriptStatus.Diarization:
      case AudioTranscriptStatus.Transcription:
        return "is-warning";
      default:
        return "is-light";
    }
  };

<<<<<<< HEAD
=======
  // Function to get the display status text (diarization vs identification)
  const getStatusDisplayText = (audio: AudioTranscriptItem) => {
    // Check if this audio used identification mode
    // Use type assertion to access voiceprintConfig since it might not be in the type definition
    const audioWithConfig = audio as any;
    const usedIdentification = audioWithConfig?.voiceprintConfig?.enabled &&
                              audioWithConfig?.voiceprintConfig?.existingVoiceprints &&
                              Array.isArray(audioWithConfig.voiceprintConfig.existingVoiceprints) &&
                              audioWithConfig.voiceprintConfig.existingVoiceprints.length > 0;

    if (audio.processStatus === AudioTranscriptStatus.Diarization) {
      return usedIdentification ? "identification" : "diarization";
    }

    return audio.processStatus;
  };

>>>>>>> WA-170_MCP
  // Function to render progress indicator
  const renderProgressIndicator = (audio: AudioTranscriptItem) => {
    const progress = processingProgress[audio._id];

    if (!progress || audio.processStatus === AudioTranscriptStatus.Completed || audio.processStatus === AudioTranscriptStatus.Failed) {
      return null;
    }

    return (
      <div className="mt-2" data-testid={`progress-${audio._id}`}>
        <progress
          className="progress is-small is-primary"
          value={progress.progress}
          max="100"
          data-testid={`progress-bar-${audio._id}`}
        >
          {progress.progress}%
        </progress>
        <p className="is-size-7 has-text-grey">
<<<<<<< HEAD
          {progress.step}: {progress.progress}% complete
=======
          {progress.step || getStatusDisplayText(audio)}: {progress.progress}% complete
>>>>>>> WA-170_MCP
        </p>
      </div>
    );
  };

  // Show loading state
  if (loading) {
    return (
      <div className="section">
        <div className="container">
          <div className="has-text-centered">
            <span className="icon is-large">
              <i className="fas fa-spinner fa-pulse"></i>
            </span>
            <p>Loading transcripts...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="section">
        <div className="container">
          <div className="notification is-danger">
            <p className="has-text-weight-bold">Error loading transcripts</p>
            <p>{String(error)}</p>
            <button className="button is-small mt-3" onClick={() => update()}>
              <span className="icon">
                <i className="fas fa-sync-alt"></i>
              </span>
              <span>Try Again</span>
            </button>
          </div>
        </div>
      </div>
    );
  }

  if(!value) return null;

  return (
    <div className="section">
      <div className="container">
<<<<<<< HEAD
        <h1 className="title">Audio Transcripts</h1>
=======
        <div className="level">
          <div className="level-left">
            <div className="level-item">
              <h1 className="title">Audio Transcripts</h1>
            </div>
            <div className="level-item">
              <Link
                to={replaceParams(
                  PATH_WHITELABEL_DATASOURCE_AUDIO_EXPORTS,
                  { whitelabelId: params.whitelabelId }
                )}
                className="button is-outlined is-info"
              >
                <span className="icon">
                  <i className="fas fa-download"></i>
                </span>
                <span>View Exports</span>
              </Link>
            </div>
          </div>
          <div className="level-right">
            <div className="level-item">
              {eligibleAudioCount > 0 && (
                <div className="field is-grouped">
                  <div className="control">
                    <button
                      className="button is-small"
                      onClick={handleSelectAll}
                      title={allEligibleSelected ? "Deselect all" : "Select all eligible"}
                    >
                      <span className="icon">
                        <i className={`fas ${allEligibleSelected ? 'fa-square' : 'fa-square-check'}`}></i>
                      </span>
                      <span>{allEligibleSelected ? 'Deselect All' : 'Select All'}</span>
                    </button>
                  </div>
                  {selectedAudioFiles.size > 0 && (
                    <div className="control">
                      <button
                        className="button is-primary is-small"
                        onClick={() => setShowBatchProcessor(true)}
                      >
                        <span className="icon">
                          <i className="fas fa-layer-group"></i>
                        </span>
                        <span>Create RAG Files ({selectedAudioFiles.size})</span>
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {eligibleAudioCount > 0 && (
          <div className="notification is-info is-light">
            <div className="content">
              <p>
                <strong>Batch RAG Processing Available!</strong>
                You have {eligibleAudioCount} completed audio transcript{eligibleAudioCount !== 1 ? 's' : ''}
                that can be converted to RAG files. Select multiple files and click "Create RAG Files" to process them in batch.
              </p>
              {existingRagFiles.size > 0 && (
                <p className="is-size-7 has-text-grey">
                  Note: {existingRagFiles.size} file{existingRagFiles.size !== 1 ? 's' : ''} already have RAG files and are excluded from batch processing.
                </p>
              )}
            </div>
          </div>
        )}
>>>>>>> WA-170_MCP

        <div className="table-container">
          <table className="table is-fullwidth is-striped is-hoverable">
            <thead>
              <tr>
<<<<<<< HEAD
=======
                {eligibleAudioCount > 0 && <th style={{ width: "40px" }}>
                  <input
                    type="checkbox"
                    checked={allEligibleSelected}
                    onChange={handleSelectAll}
                    title={allEligibleSelected ? "Deselect all" : "Select all eligible"}
                  />
                </th>}
>>>>>>> WA-170_MCP
                <th>Title</th>
                <th>Source</th>
                <th>File ID</th>
                <th>File Size</th>
                <th>Duration</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {value.length === 0 ? (
                <tr>
<<<<<<< HEAD
                  <td colSpan={7} className="has-text-centered">
=======
                  <td colSpan={eligibleAudioCount > 0 ? 8 : 7} className="has-text-centered">
>>>>>>> WA-170_MCP
                    No audio transcripts found
                  </td>
                </tr>
              ) : (
<<<<<<< HEAD
                value.map((audio) => (
                  <tr key={audio._id} className={audio._id === highlightId ? "is-selected" : ""}>
                    <td>
                      <Link
                        to={replaceParams(
                          PATH_WHITELABEL_DATASOURCE_AUDIO_ITEM,
                          { whitelabelId: params.whitelabelId, audioId: audio._id }
                        )}
                      >
                        {audio.userInfo.title}
                      </Link>
                    </td>
=======
                value.map((audio) => {
                  const isEligible = isAudioEligibleForBatch(audio);
                  const isSelected = selectedAudioFiles.has(audio._id);
                  const hasRagFile = existingRagFiles.has(audio._id);

                  return (
                    <tr
                      key={audio._id}
                      className={`${audio._id === highlightId ? "is-selected" : ""} ${isSelected ? "has-background-info-light" : ""}`}
                    >
                      {eligibleAudioCount > 0 && (
                        <td>
                          {isEligible ? (
                            <input
                              type="checkbox"
                              checked={isSelected}
                              onChange={() => handleSelectAudio(audio._id)}
                              title="Select for batch RAG processing"
                            />
                          ) : (
                            <span
                              className="icon has-text-grey-light"
                              title={hasRagFile ? "RAG file already exists" : "Audio not completed"}
                            >
                              {hasRagFile ? (
                                <i className="fas fa-layer-group"></i>
                              ) : (
                                <i className="fas fa-clock"></i>
                              )}
                            </span>
                          )}
                        </td>
                      )}
                      <td>
                        <div className="is-flex is-align-items-center">
                          <Link
                            to={replaceParams(
                              PATH_WHITELABEL_DATASOURCE_AUDIO_ITEM,
                              { whitelabelId: params.whitelabelId, audioId: audio._id }
                            )}
                            className="mr-2"
                          >
                            {audio.userInfo.title}
                          </Link>

                          {/* Voiceprint Indicator */}
                          {audio.voiceprints && audio.voiceprints.length > 0 && (() => {
                            const succeededCount = audio.voiceprints.filter(vp => vp.status === 'succeeded').length;
                            const processingCount = audio.voiceprints.filter(vp => ['created', 'pending', 'running'].includes(vp.status)).length;
                            const failedCount = audio.voiceprints.filter(vp => vp.status === 'failed').length;

                            let tagClass = 'is-info';
                            let icon = 'fa-microphone';
                            let title = `${audio.voiceprints.length} voiceprints`;

                            if (succeededCount > 0) {
                              tagClass = 'is-success';
                              icon = 'fa-microphone';
                              title = `${succeededCount} voiceprints ready`;
                            } else if (processingCount > 0) {
                              tagClass = 'is-warning';
                              icon = 'fa-spinner fa-spin';
                              title = `${processingCount} voiceprints processing`;
                            } else if (failedCount > 0) {
                              tagClass = 'is-danger';
                              icon = 'fa-exclamation-triangle';
                              title = `${failedCount} voiceprints failed`;
                            }

                            return (
                              <span
                                className={`tag is-small ${tagClass} mr-1`}
                                title={title}
                              >
                                <span className="icon is-small">
                                  <i className={`fas ${icon}`}></i>
                                </span>
                                <span>🎤</span>
                              </span>
                            );
                          })()}

                          {hasRagFile && (
                            <span
                              className="tag is-small is-success"
                              title="RAG file exists"
                            >
                              <span className="icon is-small">
                                <i className="fas fa-layer-group"></i>
                              </span>
                              <span>RAG</span>
                            </span>
                          )}
                        </div>
                      </td>
>>>>>>> WA-170_MCP
                    <td>{audio.sourceOrigin.sourceType}</td>
                    <td>
                      <span className="is-family-monospace is-size-7">{audio._id}</span>
                    </td>
                    <td>
                      {audio.sourceOrigin.info && audio.sourceOrigin.info.filesize ?
                        formatFileSize(Number(audio.sourceOrigin.info.filesize)) :
                        'Unknown'}
                    </td>
                    <td>
                      {audio.samples && audio.samples.length > 0 ?
                        formatDuration(audio.samples[audio.samples.length - 1].end) :
                        'Unknown'}
                    </td>
                    <td>
                      <div data-testid={`status-${audio._id}`}>
                        <span className={`tag ${getStatusBadgeClass(audio.processStatus)}`}>
<<<<<<< HEAD
                          {audio.processStatus}
=======
                          {getStatusDisplayText(audio)}
>>>>>>> WA-170_MCP
                        </span>
                        {renderProgressIndicator(audio)}
                      </div>
                    </td>
                    <td>
                      <div className="buttons are-small">
                        <Link
                          className="button is-info"
                          to={replaceParams(
                            PATH_WHITELABEL_DATASOURCE_AUDIO_ITEM,
                            { whitelabelId: params.whitelabelId, audioId: audio._id }
                          )}
                        >
                          <span className="icon">
                            <i className="fas fa-eye"></i>
                          </span>
                          <span>View</span>
                        </Link>
<<<<<<< HEAD
                      </div>
                    </td>
                  </tr>
                ))
=======
                        {isEligible && (
                          <button
                            className="button is-small is-primary is-outlined"
                            onClick={() => {
                              setSelectedAudioFiles(new Set([audio._id]));
                              setShowBatchProcessor(true);
                            }}
                            title="Create RAG file for this audio"
                          >
                            <span className="icon">
                              <i className="fas fa-layer-group"></i>
                            </span>
                            <span>Create RAG</span>
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                  );
                })
>>>>>>> WA-170_MCP
              )}
            </tbody>
          </table>
        </div>
<<<<<<< HEAD
=======

        {/* Batch RAG Processor Modal */}
        {showBatchProcessor && (
          <BatchRagProcessor
            selectedAudioFiles={getSelectedAudioFiles()}
            onClose={() => {
              setShowBatchProcessor(false);
              setSelectedAudioFiles(new Set());
            }}
            onComplete={() => {
              // Refresh the list to update RAG file status
              update();
              // Clear selections
              setSelectedAudioFiles(new Set());
            }}
          />
        )}
>>>>>>> WA-170_MCP
      </div>
    </div>
  );
}

