import { test, expect } from '@playwright/test';
import { getCloudTestConfig, getServiceEndpoints, isCloudEnvironment } from '../config/cloud-config';

// Only run this test in production environment
test.skip(process.env.TARGET_ENVIRONMENT !== 'production', 'Skipping production health check - not in production environment');

test.describe('Production Environment Health Check @smoke @production @readonly', () => {
  let config: ReturnType<typeof getCloudTestConfig>;
  let endpoints: ReturnType<typeof getServiceEndpoints>;

  test.beforeAll(async () => {
    config = getCloudTestConfig();
    endpoints = getServiceEndpoints(config);
    
    console.log(`🏭 Running PRODUCTION health checks against ${config.environment} environment`);
    console.log(`🔗 Base URL: ${config.baseURL}`);
    console.log(`🔗 API URL: ${config.apiURL}`);
    console.log(`⚠️ READ-ONLY MODE: No data modifications will be made`);
  });

  test('Production API should be accessible and healthy', async ({ request }) => {
    console.log(`🔍 Checking production API health at ${endpoints.api.health}`);
    
    const response = await request.get(endpoints.api.health, {
      headers: config.headers,
      timeout: config.timeouts.expect,
    });
    
    expect(response.status()).toBe(200);
    
    const healthData = await response.json();
    expect(healthData).toHaveProperty('hello');
    expect(healthData.hello).toBe('world!');
    expect(healthData).toHaveProperty('whoami');
    expect(healthData.whoami).toBe('An api server!');
    
    console.log('✅ Production API health check passed');
  });

  test('Production web application should load successfully', async ({ page }) => {
    console.log(`🔍 Loading production web application at ${config.baseURL}`);
    
    // Navigate to the application
    await page.goto(config.baseURL, {
      timeout: config.timeouts.navigation,
      waitUntil: 'networkidle',
    });
    
    // Check that we get a response (might be CF Access redirect)
    const currentUrl = page.url();
    const isCloudflareAccess = currentUrl.includes('cloudflareaccess.com');
    
    if (isCloudflareAccess) {
      console.log('✅ Redirected to Cloudflare Access (expected for protected environments)');
      expect(currentUrl).toContain('cloudflareaccess.com');
    } else {
      expect(currentUrl).toContain(config.baseURL);
    }

    // Look for common application elements
    const title = await page.title();
    expect(title).toBeTruthy();
    expect(title.length).toBeGreaterThan(0);
    
    console.log(`✅ Production web application loaded successfully (title: "${title}")`);
  });

  test('Production API endpoints should respond correctly (read-only)', async ({ request }) => {
    const endpointsToTest = [
      { name: 'Health', url: endpoints.api.health, expectedStatus: 200 },
      // Only test read-only endpoints in production
      { name: 'Auth Info', url: endpoints.api.auth, expectedStatus: [200, 401, 403, 404] },
    ];

    for (const endpoint of endpointsToTest) {
      console.log(`🔍 Testing ${endpoint.name} endpoint: ${endpoint.url}`);
      
      const response = await request.get(endpoint.url, {
        headers: config.headers,
        timeout: config.timeouts.expect,
      });
      
      const expectedStatuses = Array.isArray(endpoint.expectedStatus) 
        ? endpoint.expectedStatus 
        : [endpoint.expectedStatus];
      
      expect(expectedStatuses).toContain(response.status());
      console.log(`✅ ${endpoint.name} endpoint responded with status ${response.status()}`);
    }
  });

  test('Production Cloudflare Access should be properly configured', async ({ request }) => {
    // Skip if Cloudflare Access is not enabled
    test.skip(!config.features.cloudflareAccess, 'Cloudflare Access not enabled for this environment');
    
    console.log('🔍 Testing production Cloudflare Access configuration');
    
    // Test with Cloudflare headers
    const responseWithHeaders = await request.get(endpoints.api.health, {
      headers: config.headers,
      timeout: config.timeouts.expect,
    });
    
    expect(responseWithHeaders.status()).toBe(200);
    
    console.log('✅ Production Cloudflare Access is properly configured');
  });

  test('Production performance should meet baseline requirements', async ({ page }) => {
    console.log('🔍 Running production performance baseline check');
    
    const startTime = Date.now();
    
    await page.goto(config.baseURL, {
      timeout: config.timeouts.navigation,
      waitUntil: 'networkidle',
    });
    
    const loadTime = Date.now() - startTime;
    
    // Stricter performance requirements for production
    const maxLoadTime = 5000; // 5 seconds for production
    
    expect(loadTime).toBeLessThan(maxLoadTime);
    
    console.log(`✅ Production page loaded in ${loadTime}ms (max: ${maxLoadTime}ms)`);
    
    // Additional production performance checks
    const performanceMetrics = await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      return {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
        firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0,
      };
    });
    
    console.log(`📊 Performance metrics:`);
    console.log(`   DOM Content Loaded: ${performanceMetrics.domContentLoaded}ms`);
    console.log(`   Load Complete: ${performanceMetrics.loadComplete}ms`);
    console.log(`   First Paint: ${performanceMetrics.firstPaint}ms`);
    console.log(`   First Contentful Paint: ${performanceMetrics.firstContentfulPaint}ms`);
    
    // Validate performance metrics
    expect(performanceMetrics.domContentLoaded).toBeLessThan(3000); // 3s for DOM ready
    expect(performanceMetrics.firstContentfulPaint).toBeLessThan(2000); // 2s for FCP
  });

  test('Production security headers should be present', async ({ request }) => {
    console.log('🔍 Checking production security headers');
    
    const response = await request.get(config.baseURL, {
      timeout: config.timeouts.expect,
    });
    
    const headers = response.headers();
    
    // Check for important security headers
    const securityHeaders = [
      'strict-transport-security',
      'x-content-type-options',
      'x-frame-options',
      'x-xss-protection',
      'referrer-policy',
    ];
    
    for (const header of securityHeaders) {
      expect(headers).toHaveProperty(header);
      console.log(`✅ ${header}: ${headers[header]}`);
    }
    
    // Check for Cloudflare headers
    if (headers['server'] === 'cloudflare') {
      console.log('✅ Cloudflare protection is active');
      expect(headers).toHaveProperty('cf-ray');
    }
  });

  test('Production should not expose debug information', async ({ page }) => {
    console.log('🔍 Checking that production does not expose debug information');
    
    await page.goto(config.baseURL, {
      timeout: config.timeouts.navigation,
      waitUntil: 'networkidle',
    });
    
    const pageContent = await page.content();
    const lowerContent = pageContent.toLowerCase();
    
    // Check that debug information is not exposed
    const debugTerms = ['debug', 'development', 'localhost', 'console.log', 'stacktrace', 'error trace'];
    
    for (const term of debugTerms) {
      expect(lowerContent).not.toContain(term);
    }
    
    console.log('✅ No debug information exposed in production');
  });

  test.afterAll(async () => {
    console.log(`🎉 Production health checks completed for ${config.environment} environment`);
    console.log(`⚠️ All tests were read-only - no data was modified`);
  });
});
