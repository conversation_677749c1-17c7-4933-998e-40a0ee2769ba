/**
 * Get sync job status and progress
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { ensureWhitelabel } from "../util/ensure-whitelabel";
import { DropboxSyncJobModel } from "@divinci-ai/server-models";

interface SyncStatusQuery {
  jobId?: string,
}

export const getDropboxSyncStatus: RequestHandler = async function(req, res, next){
  try {
    const { whitelabel, userId } = await ensureWhitelabel(req);
    const { jobId }: SyncStatusQuery = req.query;

    if(jobId) {
      // Get specific job status
      const job = await DropboxSyncJobModel.findById(jobId);

      if(!job) {
        return res.status(404).json({
          success: false,
          error: "job_not_found",
          message: "Sync job not found"
        });
      }

      // Check if user has access to this job
      if(job.whitelabelId !== whitelabel._id.toString() || job.userId !== userId) {
        return res.status(403).json({
          success: false,
          error: "access_denied",
          message: "Access denied to this sync job"
        });
      }

      const responseData = {
        jobId: job._id.toString(),
        status: job.status,
        targetType: job.targetType,
        progress: {
          ...job.progress,
          completionPercentage: job.getCompletionPercentage()
        },
        sourceFiles: job.sourceFiles.map(file=>({
          dropboxFileId: file.dropboxFileId,
          fileName: file.fileName,
          status: file.status,
          errorMessage: file.errorMessage,
          internalFileId: file.internalFileId,
          transferredBytes: file.transferredBytes,
          startedAt: file.startedAt,
          completedAt: file.completedAt
        })),
        createdAt: job.createdAt,
        startedAt: job.startedAt,
        completedAt: job.completedAt,
        duration: job.getDuration(),
        errors: job.errors,
        retryCount: job.retryCount,
        canRetry: job.canRetry(),
        metadata: job.metadata
      };

      // Add queue position if job is pending
      if(job.status === "pending") {
        (responseData as any).queuePosition = await DropboxSyncJobModel.getQueuePosition(jobId);
      }

      res.status(200).json({
        success: true,
        data: responseData
      });

    } else {
      // Get all jobs for this user
      if(!userId) {
        return res.status(400).json({
          success: false,
          error: "missing_user_id",
          message: "User ID is required"
        });
      }

      const jobs = await DropboxSyncJobModel.getJobHistory(
        whitelabel._id.toString(),
        userId,
        50
      );

      const jobsData = jobs.map(job=>({
        jobId: job._id.toString(),
        status: job.status,
        targetType: job.targetType,
        progress: {
          totalFiles: job.progress.totalFiles,
          processedFiles: job.progress.processedFiles,
          failedFiles: job.progress.failedFiles,
          completionPercentage: job.getCompletionPercentage()
        },
        createdAt: job.createdAt,
        startedAt: job.startedAt,
        completedAt: job.completedAt,
        duration: job.getDuration(),
        errorCount: Array.isArray(job.errors) ? job.errors.length : (job.errors ? 1 : 0),
        retryCount: job.retryCount,
        canRetry: job.canRetry(),
        metadata: job.metadata
      }));

      res.status(200).json({
        success: true,
        data: {
          jobs: jobsData,
          totalCount: jobsData.length
        }
      });
    }

  }catch(error) {
    next(error);
  }
};
