import { defineConfig } from '@playwright/test';
import { getCloudTestConfig, getServiceEndpoints } from './src/config/cloud-config';

// Get cloud-specific configuration
const cloudConfig = getCloudTestConfig();
const endpoints = getServiceEndpoints(cloudConfig);

console.log(`🌍 Configuring Playwright for ${cloudConfig.environment} environment`);
console.log(`🔗 Base URL: ${cloudConfig.baseURL}`);
console.log(`🔗 API URL: ${cloudConfig.apiURL}`);

export default defineConfig({
  testDir: './src/e2e',
  outputDir: 'test-results-cloud',
  timeout: cloudConfig.timeouts.test,
  expect: {
    timeout: cloudConfig.timeouts.expect,
  },
  
  // Retry configuration based on environment
  retries: process.env.CI ? cloudConfig.retries.test : 1,
  
  // Worker configuration - be conservative with cloud testing
  workers: process.env.CI ? 2 : 1,
  
  // Reporter configuration
  reporter: [
    ['html', { outputFolder: 'playwright-report-cloud' }],
    ['json', { outputFile: 'test-results-cloud/results.json' }],
    ['junit', { outputFile: 'test-results-cloud/results.xml' }],
    ['list'],
  ],
  
  // Global test configuration
  use: {
    // Base URL for the application
    baseURL: cloudConfig.baseURL,
    
    // Navigation timeout
    navigationTimeout: cloudConfig.timeouts.navigation,
    
    // Action timeout
    actionTimeout: cloudConfig.timeouts.expect,
    
    // Screenshots and videos
    trace: 'retain-on-failure',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    
    // HTTP headers for all requests
    extraHTTPHeaders: cloudConfig.headers,
    
    // Ignore HTTPS errors in non-production environments
    ignoreHTTPSErrors: cloudConfig.environment !== 'production',
  },
  
  // Test projects for different test types
  projects: [
    // Setup project for authentication
    {
      name: 'cloud-auth-setup',
      testMatch: /.*\.setup\.ts/,
      use: {
        browserName: 'chromium',
      },
    },
    
    // Smoke tests - quick validation of core functionality
    {
      name: 'smoke-tests',
      testMatch: /.*\.smoke\.spec\.ts/,
      use: {
        browserName: 'chromium',
      },
      dependencies: ['cloud-auth-setup'],
    },
    
    // Integration tests - full workflow testing
    {
      name: 'integration-tests',
      testMatch: /.*\.integration\.spec\.ts/,
      use: {
        browserName: 'chromium',
      },
      dependencies: ['cloud-auth-setup', 'smoke-tests'],
    },
    
    // Performance tests - load and response time validation
    {
      name: 'performance-tests',
      testMatch: /.*\.performance\.spec\.ts/,
      use: {
        browserName: 'chromium',
      },
      dependencies: ['cloud-auth-setup'],
    },
    
    // Security tests - authentication and authorization validation
    {
      name: 'security-tests',
      testMatch: /.*\.security\.spec\.ts/,
      use: {
        browserName: 'chromium',
      },
      dependencies: ['cloud-auth-setup'],
    },
    
    // Cross-browser testing (only for critical paths in staging/production)
    ...(cloudConfig.environment !== 'develop' ? [
      {
        name: 'firefox-critical',
        testMatch: /.*\.critical\.spec\.ts/,
        use: {
          browserName: 'firefox',
        },
        dependencies: ['cloud-auth-setup'],
      },
      {
        name: 'webkit-critical',
        testMatch: /.*\.critical\.spec\.ts/,
        use: {
          browserName: 'webkit',
        },
        dependencies: ['cloud-auth-setup'],
      },
    ] : []),
  ],
  
  // Global setup and teardown
  globalSetup: require.resolve('./src/global-setup-cloud.ts'),
  globalTeardown: require.resolve('./src/global-teardown-cloud.ts'),
  
  // Web server configuration (not needed for cloud testing)
  // webServer: undefined,
});
