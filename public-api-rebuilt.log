
> @divinci-ai/public-api@0.3.0 start /workspaces/server/workspace/servers/public-api
> node dist/index.js

🔎 CURRENT ENVIRONMENT: development
🔎 CORS FULL ORIGINS (raw): api.slack.com,dev-46tiys6hnb6vbg17-cd-hljzhimsn7o28fvq.edge.tenants.us.auth0.com,dev-46tiys6hnb6vbg17.us.auth0.com,local-chunks-vectorized,127.0.0.1:,effective-invention-xjwwp64g36r4j-8080.app.github.dev,effective-invention-xjwwp64g36r4j-8081.app.github.dev,effective-invention-xjwwp64g36r4j-8082.app.github.dev,effective-invention-xjwwp64g36r4j-8083.app.github.dev,effective-invention-xjwwp64g36r4j-9080.app.github.dev
🔎 CORS FULL ORIGINS (cleaned): [
  'api.slack.com',
  'dev-46tiys6hnb6vbg17-cd-hljzhimsn7o28fvq.edge.tenants.us.auth0.com',
  'dev-46tiys6hnb6vbg17.us.auth0.com',
  'local-chunks-vectorized',
  '127.0.0.1:',
  'effective-invention-xjwwp64g36r4j-8080.app.github.dev',
  'effective-invention-xjwwp64g36r4j-8081.app.github.dev',
  'effective-invention-xjwwp64g36r4j-8082.app.github.dev',
  'effective-invention-xjwwp64g36r4j-8083.app.github.dev',
  'effective-invention-xjwwp64g36r4j-9080.app.github.dev'
]
🎈 Attempting to connect to Redis in development environment: redis://default:<EMAIL>:15121
🔄 Creating simple R2 client for fine-tune in local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🔄 Creating simple R2 client for RAG vectors in local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🔄 Creating simple Audio R2 client for local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🔄 Creating simple Public Transcript File R2 client for local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🔧 Local mode detected, using path-style URLs for S3 buckets
🔧 Using S3 endpoint: http://minio.divinci.local:9000
🔧 Using reliable MinIO endpoint: http://minio.divinci.local:9000
🪣 Using bucket name: workspace-audio, isLocalMode: true
🙌 Successful external call to: https://pokeapi.co/api/v2/pokemon/ditto
Attempting Mongoose Connection
🎈 MONGO_CONNECTION_URL: mongodb+srv://serverlessinstance0.c4pobzg.mongodb.net/divinci-development
Attempting Redis Connection... 
🟢 Connected to Redis in development environment at 2025-06-14T22:24:00.545Z
✅🌱 Successfully connected to Redis.
✅🌱 Successfully connected to MongoDB.
🔑 mTLS is disabled, using regular HTTP server
✅ Server is running on port: 8081
🪪 remote address:  ::ffff:127.0.0.1
🔍 applyCorsHeaders: Checking origin: "https://effective-invention-xjwwp64g36r4j-8080.app.github.dev"
🔍 applyCorsHeaders: isCloudEnvironment: false
✅ applyCorsHeaders: Allowing GitHub Codespaces domain
🪪 remote address:  ::ffff:127.0.0.1
🙅🏻‍♂️🪪 No auth in req:  undefined
🔍 CORS debug: Checking origin: "https://effective-invention-xjwwp64g36r4j-8080.app.github.dev"
🔍 CORS debug: isCloudEnvironment: true
🔍 CORS debug: Environment: development
✅ CORS: Allowing explicitly configured origin: https://effective-invention-xjwwp64g36r4j-8080.app.github.dev
🙋🏻‍♂️ getTrendingChats(req) req:  undefined
🙅🏻‍♂️🪪 No auth in req:  undefined
