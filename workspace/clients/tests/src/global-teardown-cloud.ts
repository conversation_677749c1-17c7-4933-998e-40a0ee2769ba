import { FullConfig } from '@playwright/test';
import { getCloudTestConfig } from './config/cloud-config';

/**
 * Global teardown for cloud E2E testing
 * This runs once after all tests complete
 */
async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global teardown for cloud E2E testing...');
  
  const cloudConfig = getCloudTestConfig();
  
  console.log(`🎯 Environment: ${cloudConfig.environment}`);
  
  try {
    // 1. Generate test summary
    await generateTestSummary(cloudConfig);
    
    // 2. Environment-specific cleanup
    await performEnvironmentSpecificCleanup(cloudConfig);
    
    // 3. Archive test artifacts
    await archiveTestArtifacts(cloudConfig);
    
    console.log('🎉 Global teardown completed successfully');
    
  } catch (error) {
    console.error('❌ Error during global teardown:', error);
    // Don't throw - we don't want teardown failures to fail the entire test run
  }
}

/**
 * Generate a summary of the test run
 */
async function generateTestSummary(cloudConfig: ReturnType<typeof getCloudTestConfig>) {
  console.log('📊 Generating test summary...');
  
  const summary = {
    environment: cloudConfig.environment,
    timestamp: new Date().toISOString(),
    baseURL: cloudConfig.baseURL,
    apiURL: cloudConfig.apiURL,
    features: cloudConfig.features,
    testRun: {
      startTime: process.env.TEST_START_TIME || 'unknown',
      endTime: new Date().toISOString(),
      duration: process.env.TEST_START_TIME 
        ? Date.now() - parseInt(process.env.TEST_START_TIME) 
        : 'unknown',
    },
  };
  
  console.log('📋 Test Summary:');
  console.log(JSON.stringify(summary, null, 2));
  
  // Write summary to file
  const fs = await import('fs');
  const path = await import('path');
  
  const summaryPath = path.join(process.cwd(), 'test-results-cloud', 'summary.json');
  
  // Ensure directory exists
  const summaryDir = path.dirname(summaryPath);
  if (!fs.existsSync(summaryDir)) {
    fs.mkdirSync(summaryDir, { recursive: true });
  }
  
  fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
  
  console.log(`✅ Test summary written to ${summaryPath}`);
}

/**
 * Perform environment-specific cleanup
 */
async function performEnvironmentSpecificCleanup(cloudConfig: ReturnType<typeof getCloudTestConfig>) {
  console.log(`🔧 Performing ${cloudConfig.environment}-specific cleanup...`);
  
  switch (cloudConfig.environment) {
    case 'develop':
      // Development environment cleanup
      console.log('🛠️ Development environment - cleaning up debug artifacts');
      break;
      
    case 'staging':
      // Staging environment cleanup
      console.log('🎭 Staging environment - cleaning up test data');
      
      // Clean up any test data created during the test run
      await cleanupTestData(cloudConfig);
      break;
      
    case 'production':
      // Production environment cleanup
      console.log('🏭 Production environment - no cleanup needed (read-only)');
      break;
  }
  
  console.log(`✅ ${cloudConfig.environment} environment cleanup completed`);
}

/**
 * Clean up test data (for staging environment)
 */
async function cleanupTestData(cloudConfig: ReturnType<typeof getCloudTestConfig>) {
  console.log('🧹 Cleaning up test data...');
  
  // This would implement cleanup logic specific to your application
  // For example:
  // - Delete test users created during tests
  // - Remove test files uploaded during tests
  // - Clear test chat conversations
  // - Reset test RAG vectors
  
  // Example implementation:
  try {
    // const { chromium } = await import('@playwright/test');
    // const browser = await chromium.launch();
    // const context = await browser.newContext({
    //   extraHTTPHeaders: cloudConfig.headers,
    // });
    // const page = await context.newPage();
    
    // // Perform cleanup operations
    // // await deleteTestUsers(page);
    // // await deleteTestFiles(page);
    
    // await context.close();
    // await browser.close();
    
    console.log('✅ Test data cleanup completed');
  } catch (error) {
    console.warn('⚠️ Test data cleanup failed:', error);
  }
}

/**
 * Archive test artifacts
 */
async function archiveTestArtifacts(cloudConfig: ReturnType<typeof getCloudTestConfig>) {
  console.log('📦 Archiving test artifacts...');
  
  const fs = await import('fs');
  const path = await import('path');
  
  const artifactsDir = path.join(process.cwd(), 'test-results-cloud');
  
  if (!fs.existsSync(artifactsDir)) {
    console.log('📁 No test artifacts to archive');
    return;
  }
  
  // Create archive directory with timestamp
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const archiveName = `test-artifacts-${cloudConfig.environment}-${timestamp}`;
  const archiveDir = path.join(process.cwd(), 'archived-test-results', archiveName);
  
  try {
    // Ensure archive directory exists
    if (!fs.existsSync(path.dirname(archiveDir))) {
      fs.mkdirSync(path.dirname(archiveDir), { recursive: true });
    }
    
    // Copy artifacts to archive directory
    const { execSync } = await import('child_process');
    execSync(`cp -r "${artifactsDir}" "${archiveDir}"`);
    
    console.log(`✅ Test artifacts archived to ${archiveDir}`);
    
    // Optionally compress the archive
    if (process.env.COMPRESS_ARTIFACTS === 'true') {
      execSync(`tar -czf "${archiveDir}.tar.gz" -C "${path.dirname(archiveDir)}" "${archiveName}"`);
      console.log(`✅ Test artifacts compressed to ${archiveDir}.tar.gz`);
    }
    
  } catch (error) {
    console.warn('⚠️ Failed to archive test artifacts:', error);
  }
}

export default globalTeardown;
