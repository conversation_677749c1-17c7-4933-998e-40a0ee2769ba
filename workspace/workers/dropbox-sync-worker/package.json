{"name": "dropbox-sync-worker", "version": "1.0.0", "description": "Cloudflare Worker for syncing Dropbox files to RAG system", "main": "src/index.ts", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "deploy:dev": "wrangler deploy --env dev", "deploy:stage": "wrangler deploy --env stage", "deploy:production": "wrangler deploy --env production", "test": "vitest", "test:watch": "vitest --watch", "prepare": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"hono": "^4.6.3", "@hono/zod-validator": "^0.2.2", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.14.9", "@cloudflare/workers-types": "^4.20240909.0", "typescript": "^5.5.2", "vitest": "^2.0.2", "wrangler": "^3.78.2"}, "engines": {"node": ">=20", "pnpm": ">=9"}}