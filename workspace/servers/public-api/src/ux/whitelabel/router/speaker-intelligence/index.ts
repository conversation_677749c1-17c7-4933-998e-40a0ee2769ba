import { Router } from "express";
import { 
  analyzeSpeakers, 
  getSmartSuggestions, 
  applyUnifiedIdentity,
  getSpeakerMatches,
  getIdentityDetails
} from "./intelligence";

/**
 * Speaker Intelligence Router
 * 
 * Advanced AI-powered speaker identification and management endpoints.
 * Provides cross-audio matching, smart naming, and unified speaker identities.
 */

const router = Router({ mergeParams: true });

// Core intelligence endpoints
router.post("/analyze", analyzeSpeakers);
router.get("/matches", getSpeakerMatches);
router.get("/suggestions/:audioId/:speakerLabel", getSmartSuggestions);
router.post("/apply-identity", applyUnifiedIdentity);
router.get("/identity/:speakerLabel", getIdentityDetails);

export default router;
