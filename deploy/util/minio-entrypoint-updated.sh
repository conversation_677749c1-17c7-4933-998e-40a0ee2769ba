#!/bin/sh

# Start <PERSON><PERSON> in the background
minio server /data --console-address ':9001' &
MINIO_PID=$!

# Wait for Min<PERSON> to be ready
echo "Waiting for Min<PERSON> to start..."
for i in $(seq 1 30); do
  if curl -s -f -o /dev/null http://localhost:9000/minio/health/live; then
    echo "<PERSON><PERSON> is ready!"
    break
  fi
  echo "Waiting for Min<PERSON> to start... ($i/30)"
  sleep 2
done

# MinIO client (mc) is already included in the minio/minio image
echo "Using pre-installed MinIO client..."

# Get credentials from environment variables or use defaults
MINIO_ROOT_USER=${MINIO_ROOT_USER:-minioadmin}
MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD:-minioadmin}

echo "Using MinIO root credentials: $MINIO_ROOT_USER / $MINIO_ROOT_PASSWORD"

# Configure MinIO client
echo "Configuring MinIO client..."
/usr/bin/mc alias set local-minio http://localhost:9000 "$MINIO_ROOT_USER" "$MINIO_ROOT_PASSWORD"

# Verify the connection
echo "Verifying connection to MinIO..."
/usr/bin/mc admin info local-minio

# Create buckets
echo "Creating buckets..."
/usr/bin/mc mb --ignore-existing local-minio/workspace-audio
/usr/bin/mc mb --ignore-existing local-minio/rag-origin-files-local
/usr/bin/mc mb --ignore-existing local-minio/rag-files-local
/usr/bin/mc mb --ignore-existing local-minio/audio-transcript-files
/usr/bin/mc mb --ignore-existing local-minio/local-audio
/usr/bin/mc mb --ignore-existing local-minio/private-temporary-uploads

<<<<<<< HEAD
=======
# Create a custom policy that allows all operations including listing
echo "Creating custom policy for full access..."
cat > /tmp/full-access-policy.json << 'EOF'
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": "*",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject",
        "s3:ListBucket",
        "s3:GetBucketLocation"
      ],
      "Resource": [
        "arn:aws:s3:::*",
        "arn:aws:s3:::*/*"
      ]
    }
  ]
}
EOF

# Add the policy to MinIO
/usr/bin/mc admin policy create local-minio full-access /tmp/full-access-policy.json

>>>>>>> WA-170_MCP
# Set bucket policies to public read-write for local development
echo "Setting bucket policies..."
/usr/bin/mc anonymous set public local-minio/workspace-audio
/usr/bin/mc anonymous set public local-minio/rag-origin-files-local
/usr/bin/mc anonymous set public local-minio/rag-files-local
/usr/bin/mc anonymous set public local-minio/audio-transcript-files
/usr/bin/mc anonymous set public local-minio/local-audio
/usr/bin/mc anonymous set public local-minio/private-temporary-uploads

<<<<<<< HEAD
=======
# Also set bucket policies using the custom policy
echo "Setting custom bucket policies..."
/usr/bin/mc admin policy attach local-minio full-access --user "$MINIO_ROOT_USER"

>>>>>>> WA-170_MCP
# Note: We no longer need to create additional users
# We're using the root user (MINIO_ROOT_USER/MINIO_ROOT_PASSWORD) for all operations
echo "Using root user for all operations: $MINIO_ROOT_USER"

# Create audio-user
if /usr/bin/mc admin user info local-minio "audio-user" >/dev/null 2>&1; then
  echo "User audio-user already exists, skipping creation"
else
  echo "Creating user audio-user"
  /usr/bin/mc admin user add local-minio "audio-user" "audio-password"
  /usr/bin/mc admin policy attach local-minio readwrite --user "audio-user"
  echo "User audio-user created and policy attached"
fi

# Create divinci-user
if /usr/bin/mc admin user info local-minio "divinci-user" >/dev/null 2>&1; then
  echo "User divinci-user already exists, skipping creation"
else
  echo "Creating user divinci-user"
  /usr/bin/mc admin user add local-minio "divinci-user" "divinci-password"
  /usr/bin/mc admin policy attach local-minio readwrite --user "divinci-user"
  echo "User divinci-user created and policy attached"
fi

# Create minio-user
if /usr/bin/mc admin user info local-minio "minio-user" >/dev/null 2>&1; then
  echo "User minio-user already exists, skipping creation"
else
  echo "Creating user minio-user"
  /usr/bin/mc admin user add local-minio "minio-user" "minio-password"
  /usr/bin/mc admin policy attach local-minio readwrite --user "minio-user"
  echo "User minio-user created and policy attached"
fi

# Get the current date for directory structure
YEAR=$(date +%Y)
MONTH=$(date +%m)
DAY=$(date +%d)

# Create organized directory structure in workspace-audio bucket
echo "Creating directory structure in workspace-audio bucket..."

# Default whitelabel ID used in local development
WHITELABEL_ID="682415a03d653676ebe89b06"

# Create empty file for directory markers
echo "Creating empty marker file..."
echo "" > /tmp/empty-marker.txt

# Function to create a directory with a marker file
create_dir() {
  echo "Creating directory: $1"
  /usr/bin/mc cp /tmp/empty-marker.txt "$1/.keep"
}

# Create base directories
create_dir "local-minio/workspace-audio"
create_dir "local-minio/workspace-audio/${WHITELABEL_ID}"
create_dir "local-minio/workspace-audio/${WHITELABEL_ID}/audio"
create_dir "local-minio/workspace-audio/${WHITELABEL_ID}/original"

# Create year/month/day structure for audio files
echo "Creating year/month/day structure for audio files..."
create_dir "local-minio/workspace-audio/${WHITELABEL_ID}/audio/${YEAR}"
create_dir "local-minio/workspace-audio/${WHITELABEL_ID}/audio/${YEAR}/${MONTH}"
create_dir "local-minio/workspace-audio/${WHITELABEL_ID}/audio/${YEAR}/${MONTH}/${DAY}"

# Create year/month/day structure for original files
echo "Creating year/month/day structure for original files..."
create_dir "local-minio/workspace-audio/${WHITELABEL_ID}/original/${YEAR}"
create_dir "local-minio/workspace-audio/${WHITELABEL_ID}/original/${YEAR}/${MONTH}"
create_dir "local-minio/workspace-audio/${WHITELABEL_ID}/original/${YEAR}/${MONTH}/${DAY}"

# Create additional directories in audio-transcript-files
echo "Creating directory structure in audio-transcript-files bucket..."
create_dir "local-minio/audio-transcript-files"
create_dir "local-minio/audio-transcript-files/${WHITELABEL_ID}"
create_dir "local-minio/audio-transcript-files/${WHITELABEL_ID}/${YEAR}"
create_dir "local-minio/audio-transcript-files/${WHITELABEL_ID}/${YEAR}/${MONTH}"
create_dir "local-minio/audio-transcript-files/${WHITELABEL_ID}/${YEAR}/${MONTH}/${DAY}"

# Create test files to verify visibility
echo "Creating test files to verify MinIO visibility..."
echo "This is a test file for audio" > /tmp/test-audio.txt
echo "This is a test file for original" > /tmp/test-original.txt

# Function to upload a test file
upload_test_file() {
  local source=$1
  local dest=$2
  echo "Uploading test file: $source to $dest"
  /usr/bin/mc cp "$source" "$dest"
}

# Upload test files to verify visibility
upload_test_file "/tmp/test-audio.txt" "local-minio/workspace-audio/${WHITELABEL_ID}/audio/${YEAR}/${MONTH}/${DAY}/test-audio.txt"
upload_test_file "/tmp/test-original.txt" "local-minio/workspace-audio/${WHITELABEL_ID}/original/${YEAR}/${MONTH}/${DAY}/test-original.txt"

# Display objects to verify creation
<<<<<<< HEAD
echo "Verifying objects in workspace-audio bucket:"
/usr/bin/mc ls --recursive local-minio/workspace-audio
=======
# echo "Verifying objects in workspace-audio bucket:"
# /usr/bin/mc ls --recursive local-minio/workspace-audio
>>>>>>> WA-170_MCP

echo "MinIO setup completed successfully!"

# Wait for MinIO to exit
wait $MINIO_PID