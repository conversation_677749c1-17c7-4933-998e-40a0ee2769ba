#!/bin/bash
# Service health check script for cloud environments
# Usage: ./scripts/wait-for-services.sh [environment]

set -e

ENVIRONMENT=${1:-develop}
MAX_WAIT=300  # 5 minutes
WAIT_INTERVAL=10

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Environment-specific URLs
case $ENVIRONMENT in
    "develop")
        BASE_URL="https://api.dev.divinci.app"
        WEB_URL="https://chat.dev.divinci.app"
        ;;
    "staging")
        BASE_URL="https://api.stage.divinci.app"
        WEB_URL="https://chat.stage.divinci.app"
        ;;
    "production")
        BASE_URL="https://api.divinci.app"
        WEB_URL="https://chat.divinci.app"
        ;;
    *)
        echo -e "${RED}❌ Unknown environment: $ENVIRONMENT${NC}"
        echo -e "${YELLOW}Supported environments: develop, staging, production${NC}"
        exit 1
        ;;
esac

echo -e "${BLUE}🔍 Waiting for services to be ready in $ENVIRONMENT environment...${NC}"
echo -e "${BLUE}API URL: $BASE_URL${NC}"
echo -e "${BLUE}Web URL: $WEB_URL${NC}"

# Function to wait for a service to become ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local health_endpoint=${3:-"/health"}
    local elapsed=0

    echo -e "${YELLOW}⏳ Checking $service_name at $url$health_endpoint${NC}"

    # First, do a quick diagnostic check
    echo -e "${BLUE}🔍 Running diagnostic check...${NC}"
    local diagnostic_response=$(curl -I -s --max-time 10 "$url$health_endpoint" 2>&1)
    local diagnostic_status=$(echo "$diagnostic_response" | head -n1 | grep -o '[0-9]\{3\}' || echo "no-response")

    echo -e "${BLUE}📊 Diagnostic result: HTTP $diagnostic_status${NC}"

    # Check for common issues
    case $diagnostic_status in
        "200")
            echo -e "${GREEN}✅ $service_name is already ready!${NC}"
            return 0
            ;;
        "302"|"301")
            echo -e "${YELLOW}🔄 Service is redirecting (likely Cloudflare Access)${NC}"
            ;;
        "403")
            echo -e "${YELLOW}🔒 Service requires authentication (Cloudflare Access)${NC}"
            ;;
        "525")
            echo -e "${RED}🚨 SSL Handshake Failed - backend service may not be running${NC}"
            echo -e "${YELLOW}💡 This usually means the service is not deployed or misconfigured${NC}"
            ;;
        "no-response")
            echo -e "${RED}🚨 No response from service - may not be deployed${NC}"
            ;;
        *)
            echo -e "${YELLOW}⚠️ Unexpected response: $diagnostic_status${NC}"
            ;;
    esac

    # If it's a 525 or no response, don't wait - it's likely not deployed
    if [ "$diagnostic_status" = "525" ] || [ "$diagnostic_status" = "no-response" ]; then
        echo -e "${RED}❌ $service_name appears to not be deployed. Skipping wait.${NC}"
        return 1
    fi

    # For other status codes, try with CF headers if available
    while [ $elapsed -lt $MAX_WAIT ]; do
        # Use curl with proper headers for Cloudflare Access if needed
        local curl_cmd="curl -f -s --max-time 10"

        # Add Cloudflare Access headers if available
        if [ -n "$CF_ACCESS_CLIENT_ID" ] && [ -n "$CF_ACCESS_CLIENT_SECRET" ]; then
            curl_cmd="$curl_cmd -H 'CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID' -H 'CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET'"
            echo -e "${BLUE}🔑 Using Cloudflare Access credentials${NC}"
        fi

        if eval "$curl_cmd '$url$health_endpoint'" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ $service_name is ready${NC}"
            return 0
        fi

        echo -e "${YELLOW}⏳ Waiting for $service_name... (${elapsed}s/${MAX_WAIT}s)${NC}"
        sleep $WAIT_INTERVAL
        elapsed=$((elapsed + WAIT_INTERVAL))
    done

    echo -e "${RED}❌ $service_name failed to become ready within ${MAX_WAIT}s${NC}"
    return 1
}

# Function to check basic connectivity (for services without health endpoints)
check_connectivity() {
    local url=$1
    local service_name=$2
    local elapsed=0

    echo -e "${YELLOW}⏳ Checking connectivity to $service_name at $url${NC}"

    # Do a quick diagnostic first
    local diagnostic_response=$(curl -I -s --max-time 10 "$url" 2>&1)
    local diagnostic_status=$(echo "$diagnostic_response" | head -n1 | grep -o '[0-9]\{3\}' || echo "no-response")

    echo -e "${BLUE}📊 Connectivity diagnostic: HTTP $diagnostic_status${NC}"

    # If we get a 525 or no response, don't wait
    if [ "$diagnostic_status" = "525" ] || [ "$diagnostic_status" = "no-response" ]; then
        echo -e "${RED}❌ $service_name appears to not be deployed. Skipping connectivity wait.${NC}"
        return 1
    fi

    # For other responses (including redirects), consider it accessible
    if [ "$diagnostic_status" != "no-response" ]; then
        echo -e "${GREEN}✅ $service_name is accessible (HTTP $diagnostic_status)${NC}"
        return 0
    fi

    # Fallback: traditional wait loop (shouldn't reach here with current logic)
    while [ $elapsed -lt $MAX_WAIT ]; do
        if curl -f -s --max-time 10 "$url" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ $service_name is accessible${NC}"
            return 0
        fi

        echo -e "${YELLOW}⏳ Waiting for $service_name connectivity... (${elapsed}s/${MAX_WAIT}s)${NC}"
        sleep $WAIT_INTERVAL
        elapsed=$((elapsed + WAIT_INTERVAL))
    done

    echo -e "${RED}❌ $service_name failed to become accessible within ${MAX_WAIT}s${NC}"
    return 1
}

# Track individual service status
API_SUCCESS=false
WEB_SUCCESS=false

# Check API service health
echo -e "${BLUE}🔍 Checking API Service...${NC}"
if wait_for_service "$BASE_URL" "API Service" "/"; then
    API_SUCCESS=true
else
    echo -e "${YELLOW}⚠️ Health endpoint failed, trying basic connectivity...${NC}"
    if check_connectivity "$BASE_URL" "API Service"; then
        API_SUCCESS=true
    else
        echo -e "${RED}❌ API Service is not accessible${NC}"
    fi
fi

# Check Web client accessibility
echo -e "${BLUE}🔍 Checking Web Client...${NC}"
if check_connectivity "$WEB_URL" "Web Client"; then
    WEB_SUCCESS=true
else
    echo -e "${RED}❌ Web Client is not accessible${NC}"
fi

# Additional service checks based on environment
if [ "$ENVIRONMENT" != "production" ]; then
    echo -e "${BLUE}🔍 Running additional development/staging checks...${NC}"

    # Check if we can reach the API documentation (if available)
    echo -e "${YELLOW}⏳ Checking for API documentation...${NC}"
    docs_response=$(curl -I -s --max-time 5 "$BASE_URL/docs" 2>&1)
    docs_status=$(echo "$docs_response" | head -n1 | grep -o '[0-9]\{3\}' || echo "no-response")

    if [ "$docs_status" = "200" ]; then
        echo -e "${GREEN}✅ API documentation is accessible${NC}"
    else
        echo -e "${YELLOW}⚠️ API documentation not accessible (HTTP $docs_status - this is normal for some environments)${NC}"
    fi
fi

# Determine overall success and status messages
if [ "$API_SUCCESS" = true ]; then
    API_STATUS="✅ Ready"
else
    API_STATUS="❌ Not Ready"
fi

if [ "$WEB_SUCCESS" = true ]; then
    WEB_STATUS="✅ Ready"
else
    WEB_STATUS="❌ Not Ready"
fi

# Final status report
if [ "$API_SUCCESS" = true ] && [ "$WEB_SUCCESS" = true ]; then
    echo -e "${GREEN}🎉 All required services are ready in $ENVIRONMENT environment!${NC}"
    OVERALL_SUCCESS=true
else
    echo -e "${RED}🚨 Some services are not ready in $ENVIRONMENT environment${NC}"
    OVERALL_SUCCESS=false
fi

# Output service status summary
echo -e "${BLUE}📊 Service Status Summary:${NC}"
echo -e "Environment: $ENVIRONMENT"
echo -e "API Service: $API_STATUS"
echo -e "Web Client: $WEB_STATUS"
echo -e "Timestamp: $(date)"

# Exit with appropriate code
if [ "$OVERALL_SUCCESS" = true ]; then
    exit 0
else
    exit 1
fi
