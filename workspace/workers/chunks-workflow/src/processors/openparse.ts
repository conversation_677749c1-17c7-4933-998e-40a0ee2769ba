import { Env } from "../types";
import { DocumentProcessor, ProcessorConfig } from "./types";
import { retry, RetryOptions, DEFAULT_RETRY_OPTIONS, isTransientError } from "../utils/retry";

// Define OpenParseConfig interface locally to avoid dependency on @divinci-ai/models
interface OpenParseConfig extends ProcessorConfig {
  semantic?: boolean,
  embeddingsProvider?: string,
  useTokens?: boolean,
  minTokens?: number,
  maxTokens?: number,
  chunkOverlap?: number,
  relevanceThreshold?: number,
  skipDeJunk?: boolean,
}

interface OpenParseInitResponse {
  total_chunks: number,
  session_id: string,
}

export class OpenParseProcessor implements DocumentProcessor {
  private currentConfig?: OpenParseConfig;
  private currentFilePath?: string;
  private existingSessionId?: string;

  constructor(
    private apiKey: string,
    private apiUrl: string,
    private env: Env,
    private options: {
      useStreaming: boolean,
      retryOptions?: Partial<RetryOptions>,
    } = {
      useStreaming: true,
      retryOptions: DEFAULT_RETRY_OPTIONS
    }
  ){}

  private getBucketNameForEnvironment(environment: string): string {
    // TEMPORARY TEST: Use workspace-audio bucket to test if the issue is bucket-specific
    const forceR2Storage = this.env.FORCE_R2_STORAGE === "true";

    switch (environment) {
      case "local":
      case "development":
        // If forcing R2 storage in local mode, use workspace-audio bucket
        return forceR2Storage ? "workspace-audio" : "rag-files-local";
      case "stage":
      case "staging":
        return "rag-origin-files-stage";
      case "production":
      case "prod":
        return "workspace-audio";  // TEMPORARY TEST: Use workspace-audio instead of whitelabel-vector-index
      default:
        return forceR2Storage ? "workspace-audio" : "rag-files-local";
    }
  }

  async initializeSession(
    fileUrl: string,
    config: OpenParseConfig
  ): Promise<{ sessionId: string, totalChunks: number }>{
    try {
      // Store current config and file path for potential reconnection
      this.currentConfig = config;
      this.currentFilePath = fileUrl;

      console.log("🔄 Initializing OpenParse processing config:", config);
      console.log("🔄🌐 Initializing OpenParse processing this.apiUrl:", this.apiUrl);
      console.log("🔄🌐 Initializing OpenParse processing fileUrl:", fileUrl);

      // IMPORTANT: In BARE METAL MODE, we should not have any special handling for LOCAL MODE.
      // The environment configuration should handle the differences between environments.
      // The URL passed to OpenParse should be the same regardless of environment.

      // Extract embeddings provider
      const embeddingsProvider: string = config.embeddingsProvider || "none";

      // Create a proper JSON object for the configuration
<<<<<<< HEAD
      // Ensure we're using the reliable MinIO hostname
      let fixedFileUrl = fileUrl;

      // Fix hostname to use the reliable minio.divinci.local
      if (fileUrl.includes('local-minio:9000') || fileUrl.includes('localhost:9000') || fileUrl.includes('127.0.0.1:9000')) {
        fixedFileUrl = fileUrl.replace(/(?:local-minio|localhost|127\.0\.0\.1):9000/g, 'minio.divinci.local:9000');
        console.log(`🔄 Fixed MinIO hostname: ${fixedFileUrl}`);
      }

      // Now check if the URL includes the bucket name
      // The URL format should be http://minio.divinci.local:9000/audio-transcript-files/682415a03d653676ebe89b06/...
      // But it's currently http://minio.divinci.local:9000/682415a03d653676ebe89b06/...

      // Parse the URL to extract the path
      const urlObj = new URL(fixedFileUrl);
      const pathParts = urlObj.pathname.split('/').filter(part => part.length > 0);

      // If the first part of the path looks like a MongoDB ID (24 hex chars),
      // then we need to add the bucket name
      if (pathParts.length > 0 && /^[0-9a-f]{24}$/.test(pathParts[0])) {
        // This is a MongoDB ID, so we need to add the bucket name
        // FIXED: Use the correct bucket name where RAG files are actually stored
        const bucketName = 'rag-files-local';
        urlObj.pathname = `/${bucketName}${urlObj.pathname}`;
        fixedFileUrl = urlObj.toString();
        console.log(`🔄 Added bucket name to URL: ${fixedFileUrl}`);
=======
      // Handle URL construction based on environment
      const environment = this.env.ENVIRONMENT || "local";
      const bucketName = this.getBucketNameForEnvironment(environment);
      let fixedFileUrl = fileUrl;

      console.log(`🔄 Environment: ${environment}`);
      console.log(`🔄 Bucket name: ${bucketName}`);
      console.log(`🔄 Original file URL: ${fileUrl}`);

      // Check if we should force R2 usage even in local mode
      const forceR2Storage = this.env.FORCE_R2_STORAGE === "true";

      if ((environment === "local" || environment === "development") && !forceR2Storage) {
        // Local development with MinIO - use host.docker.internal for container-to-host communication
        if (fileUrl.includes('localhost:9000') || fileUrl.includes('127.0.0.1:9000') || fileUrl.includes('local-minio:9000')) {
          fixedFileUrl = fileUrl.replace(/(?:localhost|127\.0\.0\.1|local-minio):9000/g, 'host.docker.internal:9000');
          console.log(`🔄 Fixed MinIO hostname for container-to-host communication: ${fixedFileUrl}`);
        } else {
          // Keep host.docker.internal as-is for container-to-host communication
          fixedFileUrl = fileUrl;
        }

        // Add bucket name if missing for local development
        const urlObj = new URL(fixedFileUrl);
        const pathParts = urlObj.pathname.split('/').filter(part => part.length > 0);

        if (pathParts.length > 0 && /^[0-9a-f]{24}$/.test(pathParts[0]) && !pathParts.includes(bucketName)) {
          urlObj.pathname = `/${bucketName}${urlObj.pathname}`;
          fixedFileUrl = urlObj.toString();
          console.log(`🔄 Added bucket name (${bucketName}) to local URL: ${fixedFileUrl}`);
        }
      } else if (forceR2Storage) {
        console.log(`🔄 [FORCE_R2_STORAGE] Using R2 URL directly (no MinIO modifications): ${fixedFileUrl}`);
      } else {
        // Staging/Production - use the custom domain which is publicly accessible
        // The file is already accessible at the custom domain, so we can use it directly
        fixedFileUrl = fileUrl; // Use the original URL since it's already accessible
        console.log(`🔄 [${environment.toUpperCase()}] Using custom domain URL: ${fixedFileUrl}`);
        console.log(`🔄 [${environment.toUpperCase()}] File should be accessible at: ${fixedFileUrl}`);
>>>>>>> WA-170_MCP
      }

      const requestBody = {
        url: fixedFileUrl,
        config: {
          semantic_chunking: config.semantic_chunking || false,
          embeddings_provider: config.embeddingsProvider || embeddingsProvider || "none",
          minTokens: config.minTokens || 256,
          maxTokens: config.maxTokens || 1024,
          overlap: config.chunkOverlap || 200,
          useTokens: config.useTokens !== false,
          relevance_threshold: config.relevanceThreshold || 0.3,
        }
      };

      console.log("🔄 OpenParse request body:", JSON.stringify(requestBody, null, 2));

      const response = await fetch(`${this.apiUrl}/fileUrl/init`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify(requestBody)
      });

      if(!response.ok) {
        const errorText = await response.text();
        console.error("❌ OpenParse init failed:", {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });
        throw new Error(`❌ OpenParse init failed: ${response.status} - ${errorText}`);
      }

      const initResponse = await response.json() as OpenParseInitResponse;
      console.log("✅ OpenParse init successful:", initResponse);

      // Store the session ID for future use and cleanup
      this.existingSessionId = initResponse.session_id;
      console.log(`🔑 Stored OpenParse session ID: ${this.existingSessionId}`);

      return {
        sessionId: initResponse.session_id,
        totalChunks: initResponse.total_chunks
      };
    }catch(error) {
      console.error("❌ Failed to initialize OpenParse session:", error);
      throw error;
    }
  }

  async processBatch(
    batchNumber: number,
    config: any
  ): Promise<Array<{ text: string, metadata?: any }> | null>{
    try {
      if(!this.existingSessionId) {
        throw new Error("Session not initialized. Call initializeSession first.");
      }

      console.log(`🔑 Using OpenParse session ID: ${this.existingSessionId} for batch ${batchNumber}`);

      const batchSize = config?.batchSize || 50;
      console.log(`📦 Fetching batch ${batchNumber} with size ${batchSize}`);

      // The storage client handles the differences between environments
      // so we can use the same code path for both local and production

      const response = await fetch(`${this.apiUrl}/fileUrl/batch`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          session_id: this.existingSessionId,
          batch_number: batchNumber,
          batch_size: batchSize
        })
      });

      if(!response.ok) {
        const errorText = await response.text();
        console.error(`❌ Batch fetch failed: ${response.status} - ${errorText}`);

        if(response.status === 404) {
          throw new Error("Session expired");
        }

        throw new Error(`❌ Failed to fetch batch: ${response.status} - ${errorText}`);
      }

      const chunks = await response.json();

      if(!Array.isArray(chunks)) {
        console.error("❌ Invalid response format:", chunks);
        throw new Error("❌ Invalid batch response format");
      }

      // Return null if no chunks (end of processing)
      if(chunks.length === 0) {
        return null;
      }

      console.log(`✅ Successfully fetched ${chunks.length} chunks in batch ${batchNumber}`);
      return chunks;

    }catch(error) {
      console.error(`❌ Error in processBatch ${batchNumber}:`, error);
      throw error;
    }
  }

  async process(
    fileUrl: string,
    config: OpenParseConfig
  ): Promise<Array<{ text: string, metadata?: any }>>{
    await this.initializeSession(fileUrl, config);
    const chunks: Array<{ text: string, metadata?: any }> = [];

    let batchNumber = 0;
    let batch: Array<{ text: string, metadata?: any }> | null;

    do {
      batch = await this.processBatch(batchNumber, { batchSize: 50 });
      if(batch) {
        chunks.push(...batch);
      }
      batchNumber++;
    } while(batch !== null);

    return chunks;
  }

  async dispose(): Promise<void>{
    console.log(`📊 pre-processor.dispose - cleaning up resources`);

    // If we have an existing session ID, we should clean it up
    if(this.existingSessionId) {
      try {
        // Check if the OpenParse service has a cleanup endpoint
        try {
          // Make a request to clean up the session
          const response = await fetch(`${this.apiUrl}/cleanup/${this.existingSessionId}`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${this.apiKey}`
            }
          });

          if(!response.ok) {
            // If the endpoint doesn't exist (404), try the alternative endpoint
            if(response.status === 404) {
              console.log(`🔄 Cleanup endpoint not found, trying alternative endpoint`);

              // Try the session endpoint directly
              const altResponse = await fetch(`${this.apiUrl}/session/${this.existingSessionId}/cleanup`, {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  "Authorization": `Bearer ${this.apiKey}`
                }
              });

              if(!altResponse.ok) {
                console.warn(`⚠️ Failed to clean up OpenParse session with alternative endpoint: ${this.existingSessionId}`);
              } else {
                console.log(`✅ Successfully cleaned up OpenParse session with alternative endpoint: ${this.existingSessionId}`);
              }
            } else {
              console.warn(`⚠️ Failed to clean up OpenParse session: ${this.existingSessionId}`);
            }
          } else {
            console.log(`✅ Successfully cleaned up OpenParse session: ${this.existingSessionId}`);
          }
        }catch(cleanupError) {
          console.warn(`⚠️ Error during OpenParse cleanup: ${cleanupError.message}`);
        }

        // Clear the session ID
        this.existingSessionId = undefined;
      }catch(error) {
        console.warn(`⚠️ Error cleaning up OpenParse session: ${error}`);
      }
    }

    // Clear other resources
    this.currentConfig = undefined;
    this.currentFilePath = undefined;

    console.log(`📊 post-processor.dispose - resources cleaned up`);
    return Promise.resolve();
  }
}
