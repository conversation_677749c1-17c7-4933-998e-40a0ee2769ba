import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import fetch from "node-fetch-commonjs";
import {
  DIVINCI_PYANNOTE_HEALTH_URL,
  DIVINCI_PYANNOTE_ENDPOINTS,
  FFMPEG_HEALTH_URL,
  OFFICIAL_PYANNOTE_HEALTH_URL,
<<<<<<< HEAD
  PYANNOTE_APIKEY
=======
  getPyannoteApiKey
>>>>>>> WA-170_MCP
} from "./pyannote-constants";

// Define interface for service health check
interface ServiceHealthCheck {
  url: string;
  status: string;
  headers?: Record<string, string>;
  alternateUrls?: string[]; // For services with multiple possible endpoints
}

/**
 * Check the health of audio processing services
 */
export const checkSystemHealth: RequestHandler = async function(req, res, next) {
  try {
    const services: Record<string, ServiceHealthCheck> = {
      divinci_pyannote: {
        url: DIVINCI_PYANNOTE_HEALTH_URL,
        status: "checking",
        alternateUrls: DIVINCI_PYANNOTE_ENDPOINTS.map(endpoint => `${endpoint}/health`)
      },
      ffmpeg: {
        url: FFMPEG_HEALTH_URL,
        status: "checking"
      },
      pyannote: {
        url: OFFICIAL_PYANNOTE_HEALTH_URL,
        status: "checking",
        headers: {
<<<<<<< HEAD
          "Authorization": `Bearer ${PYANNOTE_APIKEY}`
=======
          "Authorization": `Bearer ${getPyannoteApiKey()}`
>>>>>>> WA-170_MCP
        }
      }
    };

    const results: Record<string, { status: string, message?: string }> = {};

    // Check each service
    for (const [name, service] of Object.entries(services)) {
      try {
        console.log(`🔍 Checking health of ${name} service at ${service.url}`);

        // Create fetch options with optional headers
        const fetchOptions: any = {};

        // Add headers if provided
        if (service.headers) {
          fetchOptions.headers = service.headers;
        }

        // Try the primary URL first
        let success = false;
        let lastError: Error | null = null;

        try {
          // Create an AbortController with a timeout
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 5000);

          fetchOptions.signal = controller.signal;

          const response = await fetch(service.url, fetchOptions);

          // Clear the timeout since the request completed
          clearTimeout(timeoutId);

          if (response.ok) {
            results[name] = { status: "available" };
            console.log(`✅ ${name} service is available at ${service.url}`);
            success = true;
          } else {
            lastError = new Error(`Service returned status ${response.status}`);
            console.log(`❌ ${name} service returned status ${response.status} at ${service.url}`);
          }
        } catch (error: any) {
          lastError = error;

          // Check if the error was due to timeout
          if (error && error.name === 'AbortError') {
            console.log(`🌡️ System Health: ❌ ${name} service request timed out at ${service.url}`);
          } else {
            console.log(`❌ Error checking ${name} service at ${service.url}: ${error.message}`);
          }
        }

        // If primary URL failed and we have alternate URLs, try them
        if (!success && service.alternateUrls && service.alternateUrls.length > 0) {
          console.log(`🔄 Trying alternate endpoints for ${name} service`);

          for (const alternateUrl of service.alternateUrls) {
            // Skip the primary URL if it's in the alternates list
            if (alternateUrl === service.url) continue;

            try {
              console.log(`🔍 Trying alternate endpoint for ${name}: ${alternateUrl}`);

              // Create a new AbortController for each attempt
              const controller = new AbortController();
              const timeoutId = setTimeout(() => controller.abort(), 5000);

              const altFetchOptions = { ...fetchOptions, signal: controller.signal };

              const response = await fetch(alternateUrl, altFetchOptions);

              // Clear the timeout
              clearTimeout(timeoutId);

              if (response.ok) {
                results[name] = { status: "available" };
                console.log(`✅ ${name} service is available at alternate endpoint: ${alternateUrl}`);
                success = true;
                break;
              } else {
                console.log(`❌ ${name} service returned status ${response.status} at alternate endpoint: ${alternateUrl}`);
              }
            } catch (error: any) {
              // Just log and continue to the next alternate
              if (error && error.name === 'AbortError') {
                console.log(`🌡️ System Health: ❌ ${name} service request timed out at alternate endpoint: ${alternateUrl}`);
              } else {
                console.log(`❌ Error checking ${name} service at alternate endpoint ${alternateUrl}: ${error.message}`);
              }
            }
          }
        }

        // If all attempts failed, set the service as unavailable
        if (!success) {
          results[name] = {
            status: "unavailable",
            message: lastError ? (lastError instanceof Error ? lastError.message : String(lastError)) : "Unknown error"
          };
        }
      } catch (error) {
        results[name] = {
          status: "unavailable",
          message: error instanceof Error ? error.message : String(error)
        };
        console.log(`❌ Error checking ${name} service: ${error}`);
      }
    }

    const response = {
      timestamp: new Date().toISOString(),
      services: results
    };

    console.log('System health check response:', JSON.stringify(response, null, 2));
    res.json(response);
  } catch (error) {
    next(error);
  }
};
