# MCP (Model Context Protocol) E2E Testing Guide

This directory contains comprehensive End-to-End tests for the MCP (Model Context Protocol) implementation in the Divinci AI platform.

## 📋 Test Files Overview

### Core Test Suites

1. **`mcp-server-integration.spec.ts`** - Core MCP server functionality tests
   - Server health checks
   - Tool discovery and listing
   - Individual tool execution tests
   - Error handling validation
   - Performance and load testing
   - Whitelabel context integration

2. **`mcp-inspector-integration.spec.ts`** - MCP Inspector compatibility tests
   - SSE endpoint compatibility
   - Protocol initialization
   - Inspector connection simulation
   - Claude Desktop configuration validation
   - Concurrent connection handling

3. **`mcp-test-runner.spec.ts`** - Comprehensive workflow tests
   - Prerequisites checking
   - Complete integration workflows
   - Stress testing
   - Error recovery testing

### Test Runner Script

- **`../run-mcp-tests.sh`** - Automated test execution script
  - Validates MCP server status
  - Runs quick validation checks
  - Executes all test suites
  - Provides comprehensive reporting

## 🚀 Quick Start

### Prerequisites

1. **MCP Server Running**
   ```bash
   cd workspace/workers/mcp-server
   npm run dev
   ```

2. **Test Dependencies Installed**
   ```bash
   cd workspace/clients/tests
   npm install
   ```

### Running Tests

#### Option 1: Automated Test Runner (Recommended)
```bash
cd workspace/clients/tests
./run-mcp-tests.sh
```

#### Option 2: Individual Test Suites
```bash
# Core integration tests
npx playwright test src/e2e/mcp-server-integration.spec.ts

# Inspector compatibility tests
npx playwright test src/e2e/mcp-inspector-integration.spec.ts

# Comprehensive workflow tests
npx playwright test src/e2e/mcp-test-runner.spec.ts
```

#### Option 3: Specific Test Cases
```bash
# Run only health check tests
npx playwright test src/e2e/mcp-server-integration.spec.ts -g "Health Check"

# Run only tool execution tests
npx playwright test src/e2e/mcp-server-integration.spec.ts -g "MCP Tool"

# Run stress tests
npx playwright test src/e2e/mcp-test-runner.spec.ts -g "Stress Test"
```

## 🧪 Test Coverage

### Functional Tests
- ✅ MCP server health and availability
- ✅ Tool discovery (`tools/list`)
- ✅ Tool execution (`tools/call`)
- ✅ Protocol initialization (`initialize`)
- ✅ Error handling and recovery
- ✅ JSON-RPC compliance

### Integration Tests
- ✅ SSE (Server-Sent Events) endpoint
- ✅ MCP Inspector compatibility
- ✅ Claude Desktop configuration
- ✅ Whitelabel context integration
- ✅ Concurrent client connections

### Performance Tests
- ✅ Load testing (multiple concurrent requests)
- ✅ Stress testing (sustained high load)
- ✅ Response time validation
- ✅ Error rate monitoring

### Tool-Specific Tests
- ✅ `get_user_profile` - User information retrieval
- ✅ `create_chat` - Chat conversation creation
- ✅ `send_message` - Message sending with AI responses
- ✅ `list_chats` - Chat conversation listing

## 📊 Test Results Interpretation

### Success Indicators
- ✅ All health checks pass
- ✅ All 4 tools are discoverable
- ✅ Tool execution returns expected responses
- ✅ Error handling works correctly
- ✅ Performance meets thresholds (95%+ success rate)

### Common Issues and Solutions

#### MCP Server Not Running
```
❌ MCP Server is not running
```
**Solution:** Start the MCP server:
```bash
cd workspace/workers/mcp-server
npm run dev
```

#### Tool Execution Failures
```
❌ Tool execution failed
```
**Solution:** Check server logs and verify tool implementations

#### Performance Issues
```
❌ Success rate below 95%
```
**Solution:** Check server resources and concurrent connection limits

## 🔧 Test Configuration

### Environment Variables
- `WEB_URL` - Base URL for the web application (default: `http://localhost:8080`)
- `MCP_SERVER_URL` - MCP server URL (default: `http://localhost:8793`)

### Test Timeouts
- Individual tests: 2 minutes
- Comprehensive tests: 3 minutes
- Stress tests: 5 minutes

### Playwright Configuration
Tests use the existing Playwright configuration in `playwright.config.ts` with:
- Chrome browser
- Authentication state from storage
- Network idle waiting
- Screenshot capture on failure

## 🎯 Integration Points Tested

### MCP Inspector
- Connection via `npx mcp-remote http://localhost:8793/sse`
- Tool discovery and execution
- Interactive testing capabilities

### Claude Desktop
- Configuration validation
- Protocol handshake
- Tool integration
- Error handling

### Divinci Platform
- Whitelabel context
- User authentication
- API integration
- Error recovery

## 📈 Continuous Integration

### CI/CD Integration
Add to your CI pipeline:
```yaml
- name: Run MCP E2E Tests
  run: |
    cd workspace/workers/mcp-server
    npm run dev &
    sleep 10
    cd ../../../workspace/clients/tests
    ./run-mcp-tests.sh
```

### Test Reporting
Tests generate:
- Console output with detailed progress
- Playwright HTML reports
- Screenshots on failure
- Performance metrics

## 🔍 Debugging Tests

### Verbose Output
```bash
npx playwright test src/e2e/mcp-server-integration.spec.ts --reporter=line --verbose
```

### Debug Mode
```bash
npx playwright test src/e2e/mcp-server-integration.spec.ts --debug
```

### Screenshots and Traces
Failed tests automatically capture:
- Screenshots at failure point
- Network traces
- Console logs
- Server responses

## 🚀 Next Steps

After tests pass:

1. **Test with MCP Inspector**
   ```bash
   npx @modelcontextprotocol/inspector@latest
   ```

2. **Configure Claude Desktop**
   ```json
   {
     "mcpServers": {
       "divinci": {
         "command": "npx",
         "args": ["mcp-remote", "http://localhost:8793/sse"]
       }
     }
   }
   ```

3. **Deploy to Production**
   - Update environment variables
   - Configure production URLs
   - Run tests against staging environment

## 📚 Additional Resources

- [MCP Specification](https://modelcontextprotocol.io/docs)
- [MCP Inspector Documentation](https://github.com/modelcontextprotocol/inspector)
- [Claude Desktop Integration Guide](https://docs.anthropic.com/claude/docs/mcp)
- [Playwright Testing Documentation](https://playwright.dev/docs/intro)
