#!/bin/bash
# Deployment Diagnostic Script
# Investigates deployment issues and provides actionable insights

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 Deployment Diagnostic Tool${NC}"
echo -e "${BLUE}=============================${NC}"
echo ""

# Function to check URL with detailed diagnostics
check_url_detailed() {
    local url="$1"
    local name="$2"
    
    echo -e "${YELLOW}🔍 Checking $name: $url${NC}"
    
    # Basic connectivity test
    local response=$(curl -I -s --max-time 10 "$url" 2>&1)
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        local status=$(echo "$response" | head -n1 | grep -o '[0-9]\{3\}' || echo "unknown")
        local server=$(echo "$response" | grep -i "^server:" | cut -d' ' -f2- || echo "unknown")
        local cf_ray=$(echo "$response" | grep -i "^cf-ray:" | cut -d' ' -f2 || echo "none")
        
        echo -e "  ${GREEN}✅ Response received${NC}"
        echo -e "  📊 Status: $status"
        echo -e "  🖥️ Server: $server"
        echo -e "  ☁️ CF-Ray: $cf_ray"
        
        case $status in
            "200")
                echo -e "  ${GREEN}✅ Service is working correctly${NC}"
                ;;
            "302"|"301")
                local location=$(echo "$response" | grep -i "^location:" | cut -d' ' -f2- || echo "unknown")
                echo -e "  ${YELLOW}🔄 Redirecting to: $location${NC}"
                ;;
            "403")
                echo -e "  ${YELLOW}🔒 Access forbidden (likely Cloudflare Access)${NC}"
                ;;
            "404")
                echo -e "  ${YELLOW}❓ Endpoint not found${NC}"
                ;;
            "525")
                echo -e "  ${RED}❌ SSL Handshake Failed - Backend service issue${NC}"
                echo -e "  ${YELLOW}💡 This usually means:${NC}"
                echo -e "     - Backend service is not running"
                echo -e "     - SSL certificate issues"
                echo -e "     - Origin server misconfiguration"
                ;;
            "502")
                echo -e "  ${RED}❌ Bad Gateway - Backend service down${NC}"
                ;;
            "503")
                echo -e "  ${RED}❌ Service Unavailable - Backend overloaded${NC}"
                ;;
            *)
                echo -e "  ${YELLOW}⚠️ Unexpected status: $status${NC}"
                ;;
        esac
    else
        echo -e "  ${RED}❌ No response (connection failed)${NC}"
        echo -e "  ${YELLOW}💡 Possible causes:${NC}"
        echo -e "     - DNS resolution failure"
        echo -e "     - Network connectivity issues"
        echo -e "     - Service completely down"
    fi
    
    echo ""
}

# Function to check GCP Cloud Run services
check_gcp_services() {
    echo -e "${BLUE}☁️ Checking GCP Cloud Run Services${NC}"
    echo -e "${BLUE}==================================${NC}"
    
    # Check if gcloud is configured
    if ! command -v gcloud &> /dev/null; then
        echo -e "${YELLOW}⚠️ gcloud CLI not found. Install it to check GCP services.${NC}"
        echo -e "${BLUE}💡 Install: https://cloud.google.com/sdk/docs/install${NC}"
        return 1
    fi
    
    # Check authentication
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 > /dev/null 2>&1; then
        echo -e "${YELLOW}⚠️ gcloud not authenticated. Run: gcloud auth login${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ gcloud CLI is available and authenticated${NC}"
    echo ""
    
    # List Cloud Run services
    echo -e "${YELLOW}🔍 Listing Cloud Run services...${NC}"
    
    local regions=("us-central1" "us-east1" "us-west1" "europe-west1")
    
    for region in "${regions[@]}"; do
        echo -e "${BLUE}📍 Region: $region${NC}"
        
        local services=$(gcloud run services list --region="$region" --format="value(metadata.name,status.url)" 2>/dev/null || echo "")
        
        if [ -n "$services" ]; then
            echo "$services" | while IFS=$'\t' read -r name url; do
                if [[ "$name" == *"api"* ]] || [[ "$url" == *"api"* ]]; then
                    echo -e "  🔗 $name: $url"
                    
                    # Check if this matches our expected URLs
                    if [[ "$url" == *"api.dev.divinci.app"* ]]; then
                        echo -e "    ${GREEN}✅ This is the develop API service${NC}"
                    elif [[ "$url" == *"api.stage.divinci.app"* ]]; then
                        echo -e "    ${GREEN}✅ This is the staging API service${NC}"
                    elif [[ "$url" == *"api.divinci.app"* ]]; then
                        echo -e "    ${GREEN}✅ This is the production API service${NC}"
                    fi
                fi
            done
        else
            echo -e "  ${YELLOW}No services found in $region${NC}"
        fi
        echo ""
    done
}

# Function to check Cloudflare DNS
check_cloudflare_dns() {
    echo -e "${BLUE}🌐 Checking Cloudflare DNS Configuration${NC}"
    echo -e "${BLUE}======================================${NC}"
    
    local domains=("api.dev.divinci.app" "api.stage.divinci.app" "api.divinci.app" "chat.dev.divinci.app" "chat.stage.divinci.app" "chat.divinci.app")
    
    for domain in "${domains[@]}"; do
        echo -e "${YELLOW}🔍 Checking DNS for $domain${NC}"
        
        # DNS lookup
        local dns_result=$(nslookup "$domain" 2>/dev/null || echo "DNS lookup failed")
        
        if [[ "$dns_result" == *"DNS lookup failed"* ]]; then
            echo -e "  ${RED}❌ DNS lookup failed${NC}"
        else
            local ip=$(echo "$dns_result" | grep "Address:" | tail -n1 | awk '{print $2}' || echo "unknown")
            echo -e "  ${GREEN}✅ Resolves to: $ip${NC}"
            
            # Check if it's a Cloudflare IP
            if [[ "$ip" == 104.* ]] || [[ "$ip" == 172.* ]] || [[ "$ip" == 162.* ]]; then
                echo -e "  ${GREEN}☁️ Using Cloudflare proxy${NC}"
            else
                echo -e "  ${YELLOW}⚠️ Not using Cloudflare proxy${NC}"
            fi
        fi
        echo ""
    done
}

# Function to check recent deployments
check_recent_deployments() {
    echo -e "${BLUE}📋 Checking Recent Deployments${NC}"
    echo -e "${BLUE}==============================${NC}"
    
    # Check GitHub Actions runs (if gh CLI is available)
    if command -v gh &> /dev/null; then
        echo -e "${YELLOW}🔍 Recent GitHub Actions runs...${NC}"
        gh run list --limit 10 --json status,conclusion,createdAt,headBranch,workflowName 2>/dev/null | \
        jq -r '.[] | "\(.createdAt) | \(.workflowName) | \(.headBranch) | \(.status) | \(.conclusion)"' | \
        while IFS='|' read -r date workflow branch status conclusion; do
            local status_icon="❓"
            case "$status" in
                "completed")
                    case "$conclusion" in
                        "success") status_icon="✅" ;;
                        "failure") status_icon="❌" ;;
                        "cancelled") status_icon="⏹️" ;;
                        *) status_icon="❓" ;;
                    esac
                    ;;
                "in_progress") status_icon="🔄" ;;
                *) status_icon="❓" ;;
            esac
            
            echo -e "  $status_icon $(echo $date | cut -d'T' -f1) | $workflow | $branch"
        done
    else
        echo -e "${YELLOW}⚠️ GitHub CLI not found. Install 'gh' to check recent deployments.${NC}"
    fi
    echo ""
}

# Function to provide recommendations
provide_recommendations() {
    echo -e "${BLUE}💡 Recommendations${NC}"
    echo -e "${BLUE}=================${NC}"
    
    echo -e "${YELLOW}Based on the diagnostics, here are the recommended actions:${NC}"
    echo ""
    
    echo -e "${GREEN}1. For 525 SSL Handshake Failed errors:${NC}"
    echo -e "   - Check if the backend service is deployed and running"
    echo -e "   - Verify SSL certificate configuration"
    echo -e "   - Check Cloud Run service logs for errors"
    echo -e "   - Ensure the service is listening on the correct port"
    echo ""
    
    echo -e "${GREEN}2. For missing services:${NC}"
    echo -e "   - Deploy the service using GitHub Actions comment triggers"
    echo -e "   - Check if the deployment workflow completed successfully"
    echo -e "   - Verify the service configuration and environment variables"
    echo ""
    
    echo -e "${GREEN}3. For DNS issues:${NC}"
    echo -e "   - Check Cloudflare DNS settings"
    echo -e "   - Verify domain configuration"
    echo -e "   - Ensure proper proxy settings"
    echo ""
    
    echo -e "${GREEN}4. Next steps:${NC}"
    echo -e "   - Run: ${YELLOW}gcloud run services describe SERVICE_NAME --region=REGION${NC}"
    echo -e "   - Check logs: ${YELLOW}gcloud logs read --service=SERVICE_NAME${NC}"
    echo -e "   - Deploy service: ${YELLOW}@github-actions [deploy:develop]${NC}"
    echo ""
}

# Main diagnostic function
main() {
    echo -e "${GREEN}This script will diagnose deployment issues across environments.${NC}"
    echo ""
    
    # Check all environments
    echo -e "${BLUE}🌍 Environment Health Check${NC}"
    echo -e "${BLUE}===========================${NC}"
    
    # API endpoints
    check_url_detailed "https://api.dev.divinci.app/" "Develop API"
    check_url_detailed "https://api.stage.divinci.app/" "Staging API"
    check_url_detailed "https://api.divinci.app/" "Production API"
    
    # Web endpoints
    check_url_detailed "https://chat.dev.divinci.app/" "Develop Web"
    check_url_detailed "https://chat.stage.divinci.app/" "Staging Web"
    check_url_detailed "https://chat.divinci.app/" "Production Web"
    
    # GCP services check
    check_gcp_services
    
    # DNS check
    check_cloudflare_dns
    
    # Recent deployments
    check_recent_deployments
    
    # Recommendations
    provide_recommendations
    
    echo -e "${GREEN}🎉 Diagnostic complete!${NC}"
}

# Run main function
main "$@"
