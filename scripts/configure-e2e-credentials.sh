#!/bin/bash
# E2E Testing Credential Configuration Script
# This script helps you securely configure real credentials for E2E testing

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔐 E2E Testing Credential Configuration${NC}"
echo -e "${BLUE}======================================${NC}"
echo ""

# Function to prompt for input with validation
prompt_for_input() {
    local prompt="$1"
    local var_name="$2"
    local is_secret="${3:-false}"
    local current_value="$4"
    
    if [ "$is_secret" = "true" ]; then
        echo -e "${YELLOW}🔒 $prompt${NC}"
        if [ -n "$current_value" ] && [ "$current_value" != "your_"*"_here" ]; then
            echo -e "${GREEN}✅ Current value is set (hidden for security)${NC}"
            read -p "Keep current value? (y/n): " keep_current
            if [ "$keep_current" = "y" ] || [ "$keep_current" = "Y" ]; then
                echo "$current_value"
                return
            fi
        fi
        read -s -p "Enter $var_name: " value
        echo ""
    else
        echo -e "${YELLOW}📝 $prompt${NC}"
        if [ -n "$current_value" ] && [ "$current_value" != "your_"*"_here" ]; then
            echo -e "${GREEN}Current: $current_value${NC}"
            read -p "Keep current value? (y/n): " keep_current
            if [ "$keep_current" = "y" ] || [ "$keep_current" = "Y" ]; then
                echo "$current_value"
                return
            fi
        fi
        read -p "Enter $var_name: " value
    fi
    
    echo "$value"
}

# Function to configure environment
configure_environment() {
    local env="$1"
    local env_file="private-keys/$env/test.env"
    
    echo -e "${BLUE}🌍 Configuring $env environment${NC}"
    echo -e "${BLUE}================================${NC}"
    
    if [ ! -f "$env_file" ]; then
        echo -e "${RED}❌ Environment file not found: $env_file${NC}"
        return 1
    fi
    
    # Read current values
    source "$env_file" 2>/dev/null || true
    
    echo -e "${YELLOW}📋 Current configuration for $env:${NC}"
    echo -e "API URL: ${API_URL:-Not set}"
    echo -e "Web URL: ${WEB_URL:-Not set}"
    echo -e "Auth0 Base URL: ${AUTH0_BASE_URL:-Not set}"
    echo -e "CF Access Client ID: ${CF_ACCESS_CLIENT_ID:+Set (hidden)}${CF_ACCESS_CLIENT_ID:-Not set}"
    echo ""
    
    read -p "Do you want to update $env credentials? (y/n): " update_env
    if [ "$update_env" != "y" ] && [ "$update_env" != "Y" ]; then
        echo -e "${YELLOW}⏭️ Skipping $env environment${NC}"
        return 0
    fi
    
    echo ""
    echo -e "${GREEN}🔧 Configuring $env environment credentials...${NC}"
    echo ""
    
    # Auth0 Configuration
    echo -e "${BLUE}🔐 Auth0 Configuration${NC}"
    AUTH0_BASE_URL=$(prompt_for_input "Auth0 Domain (e.g., https://your-tenant.us.auth0.com)" "AUTH0_BASE_URL" false "$AUTH0_BASE_URL")
    AUTH0_CLIENT_ID=$(prompt_for_input "Auth0 Client ID" "AUTH0_CLIENT_ID" false "$AUTH0_CLIENT_ID")
    AUTH0_AUDIENCE=$(prompt_for_input "Auth0 API Audience (e.g., https://api.$env.divinci.app)" "AUTH0_AUDIENCE" false "$AUTH0_AUDIENCE")
    AUTH0_S2S_CLIENT_ID=$(prompt_for_input "Auth0 Machine-to-Machine Client ID" "AUTH0_S2S_CLIENT_ID" false "$AUTH0_S2S_CLIENT_ID")
    AUTH0_S2S_CLIENT_SECRET=$(prompt_for_input "Auth0 Machine-to-Machine Client Secret" "AUTH0_S2S_CLIENT_SECRET" true "$AUTH0_S2S_CLIENT_SECRET")
    
    echo ""
    
    # Test User Credentials
    echo -e "${BLUE}👤 Test User Credentials${NC}"
    AUTH0_TEST_USER_EMAIL=$(prompt_for_input "Test User Email" "AUTH0_TEST_USER_EMAIL" false "$AUTH0_TEST_USER_EMAIL")
    AUTH0_TEST_USER_PASSWORD=$(prompt_for_input "Test User Password" "AUTH0_TEST_USER_PASSWORD" true "$AUTH0_TEST_USER_PASSWORD")
    AUTH0_ADMIN_USER_EMAIL=$(prompt_for_input "Admin User Email" "AUTH0_ADMIN_USER_EMAIL" false "$AUTH0_ADMIN_USER_EMAIL")
    AUTH0_ADMIN_USER_PASSWORD=$(prompt_for_input "Admin User Password" "AUTH0_ADMIN_USER_PASSWORD" true "$AUTH0_ADMIN_USER_PASSWORD")
    
    echo ""
    
    # Cloudflare Access Configuration
    echo -e "${BLUE}☁️ Cloudflare Access Configuration${NC}"
    CF_ACCESS_CLIENT_ID=$(prompt_for_input "Cloudflare Access Service Token Client ID" "CF_ACCESS_CLIENT_ID" false "$CF_ACCESS_CLIENT_ID")
    CF_ACCESS_CLIENT_SECRET=$(prompt_for_input "Cloudflare Access Service Token Secret" "CF_ACCESS_CLIENT_SECRET" true "$CF_ACCESS_CLIENT_SECRET")
    
    echo ""
    
    # Create backup
    cp "$env_file" "$env_file.backup.$(date +%Y%m%d_%H%M%S)"
    echo -e "${GREEN}📋 Created backup: $env_file.backup.$(date +%Y%m%d_%H%M%S)${NC}"
    
    # Update the environment file
    cat > "$env_file" << EOF
# Environment file for $env E2E testing
NODE_ENV=$env
TARGET_ENVIRONMENT=$env
TEST_ENV=$env

# Cloud URLs for $env environment
API_URL=https://api$([ "$env" != "production" ] && echo ".$env").divinci.app
WEB_URL=https://chat$([ "$env" != "production" ] && echo ".$env").divinci.app
TEST_SERVER_ENDPOINT_ORIGIN=https://chat$([ "$env" != "production" ] && echo ".$env").divinci.app

# Local fallback URLs
LOCAL_API_URL=http://localhost:8080
LOCAL_WEB_URL=http://localhost:8081

# Database URLs (for local testing)
DATABASE_URL=mongodb://localhost:27017/divinci-$env
REDIS_URL=redis://localhost:6379

# Auth0 Configuration for $env
AUTH0_BASE_URL=$AUTH0_BASE_URL
AUTH0_CLIENT_ID=$AUTH0_CLIENT_ID
AUTH0_AUDIENCE=$AUTH0_AUDIENCE
AUTH0_S2S_CLIENT_ID=$AUTH0_S2S_CLIENT_ID
AUTH0_S2S_CLIENT_SECRET=$AUTH0_S2S_CLIENT_SECRET

# Test user credentials for $env
AUTH0_TEST_USER_EMAIL=$AUTH0_TEST_USER_EMAIL
AUTH0_TEST_USER_PASSWORD=$AUTH0_TEST_USER_PASSWORD
AUTH0_ADMIN_USER_EMAIL=$AUTH0_ADMIN_USER_EMAIL
AUTH0_ADMIN_USER_PASSWORD=$AUTH0_ADMIN_USER_PASSWORD

# Cloudflare Access credentials for $env
CF_ACCESS_CLIENT_ID=$CF_ACCESS_CLIENT_ID
CF_ACCESS_CLIENT_SECRET=$CF_ACCESS_CLIENT_SECRET
CF_ACCESS_CLIENT_ID_$(echo $env | tr '[:lower:]' '[:upper:]')=$CF_ACCESS_CLIENT_ID
CF_ACCESS_CLIENT_SECRET_$(echo $env | tr '[:lower:]' '[:upper:]')=$CF_ACCESS_CLIENT_SECRET

# Test configuration
MAX_WAIT_TIME=300
TEST_TIMEOUT=$([ "$env" = "production" ] && echo "120000" || echo "90000")
EXPECT_TIMEOUT=$([ "$env" = "production" ] && echo "20000" || echo "15000")
NAVIGATION_TIMEOUT=$([ "$env" = "production" ] && echo "60000" || echo "45000")
EOF
    
    echo -e "${GREEN}✅ Updated $env environment configuration${NC}"
    echo ""
}

# Function to test credentials
test_credentials() {
    local env="$1"
    local env_file="private-keys/$env/test.env"
    
    echo -e "${BLUE}🧪 Testing $env environment credentials${NC}"
    echo -e "${BLUE}====================================${NC}"
    
    if [ ! -f "$env_file" ]; then
        echo -e "${RED}❌ Environment file not found: $env_file${NC}"
        return 1
    fi
    
    # Source the environment file
    source "$env_file"
    
    # Test API connectivity
    echo -e "${YELLOW}🔍 Testing API connectivity...${NC}"
    if curl -f -s --max-time 10 "$API_URL/" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ API is accessible${NC}"
    else
        echo -e "${RED}❌ API is not accessible${NC}"
    fi
    
    # Test Cloudflare Access
    if [ -n "$CF_ACCESS_CLIENT_ID" ] && [ -n "$CF_ACCESS_CLIENT_SECRET" ]; then
        echo -e "${YELLOW}🔍 Testing Cloudflare Access...${NC}"
        if curl -f -s --max-time 10 \
            -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" \
            -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" \
            "$API_URL/" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ Cloudflare Access is working${NC}"
        else
            echo -e "${YELLOW}⚠️ Cloudflare Access test failed (may be normal if not required)${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️ Cloudflare Access credentials not configured${NC}"
    fi
    
    # Test Auth0 connectivity
    if [ -n "$AUTH0_BASE_URL" ]; then
        echo -e "${YELLOW}🔍 Testing Auth0 connectivity...${NC}"
        if curl -f -s --max-time 10 "$AUTH0_BASE_URL/.well-known/openid_configuration" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ Auth0 is accessible${NC}"
        else
            echo -e "${RED}❌ Auth0 is not accessible${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️ Auth0 base URL not configured${NC}"
    fi
    
    echo ""
}

# Main script
main() {
    echo -e "${GREEN}This script will help you configure real credentials for E2E testing.${NC}"
    echo -e "${YELLOW}⚠️ Make sure you have the following information ready:${NC}"
    echo ""
    echo -e "📋 ${BLUE}Required Information:${NC}"
    echo -e "   🔐 Auth0 Domain and Client IDs"
    echo -e "   👤 Test user credentials"
    echo -e "   ☁️ Cloudflare Access Service Token"
    echo ""
    
    read -p "Do you want to continue? (y/n): " continue_setup
    if [ "$continue_setup" != "y" ] && [ "$continue_setup" != "Y" ]; then
        echo -e "${YELLOW}Setup cancelled.${NC}"
        exit 0
    fi
    
    echo ""
    
    # Configure environments
    for env in develop staging production; do
        if [ -d "private-keys/$env" ]; then
            configure_environment "$env"
        else
            echo -e "${YELLOW}⚠️ Directory private-keys/$env not found, skipping${NC}"
        fi
    done
    
    echo -e "${GREEN}🎉 Credential configuration complete!${NC}"
    echo ""
    
    # Test credentials
    read -p "Do you want to test the configured credentials? (y/n): " test_creds
    if [ "$test_creds" = "y" ] || [ "$test_creds" = "Y" ]; then
        echo ""
        for env in develop staging production; do
            if [ -f "private-keys/$env/test.env" ]; then
                test_credentials "$env"
            fi
        done
    fi
    
    echo -e "${GREEN}🚀 You can now run E2E tests with real credentials!${NC}"
    echo ""
    echo -e "${BLUE}Next steps:${NC}"
    echo -e "1. Run health checks: ${YELLOW}./scripts/wait-for-services.sh staging${NC}"
    echo -e "2. Run E2E tests: ${YELLOW}TARGET_ENVIRONMENT=staging npx playwright test --config=playwright.cloud.config.ts${NC}"
    echo -e "3. Test with act: ${YELLOW}.github/act/test-cloud-e2e.sh staging e2e-only${NC}"
}

# Run main function
main "$@"
