/**
 * Whitelabel API
<<<<<<< HEAD
 *
=======
 * 
>>>>>>> WA-170_MCP
 * This module provides API client functions for working with whitelabels.
 * It includes both real API calls and mock implementations for testing.
 */

import { ApiClient } from './api-client';
import { getAuthFileForRole, getUserIdFromAuthState, UserRole } from '../auth/auth-utils';
import { randomString } from '../utils/test-utils';

// Flag to use mock implementations for testing
const USE_MOCK = true;

/**
 * Whitelabel
 */
export interface Whitelabel {
  /** Whitelabel ID */
  _id: string;
  /** Whitelabel name */
  name: string;
  /** Whitelabel slug */
  slug: string;
  /** Group ID */
  groupId: string;
  /** Owner user ID */
  ownerUser: string;
  /** Created at timestamp */
  createdAt: string;
  /** Updated at timestamp */
  updatedAt: string;
}

// In-memory store for mock whitelabels
const mockWhitelabels = new Map<string, Whitelabel>();

/**
 * Create a whitelabel
 * @param role User role
 * @param params Whitelabel parameters
 * @returns A promise that resolves with the created whitelabel
 */
export async function createWhitelabel(
  role: UserRole,
  params: { name: string; groupId: string; slug?: string }
): Promise<Whitelabel> {
  if (USE_MOCK) {
    return createMockWhitelabel(role, params);
  }
<<<<<<< HEAD

  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);

  // Generate a unique slug if not provided
  const slug = params.slug || `${params.name.toLowerCase().replace(/\s+/g, '-')}-${Date.now().toString(36)}`;

=======
  
  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);
  
  // Generate a unique slug if not provided
  const slug = params.slug || `${params.name.toLowerCase().replace(/\s+/g, '-')}-${Date.now().toString(36)}`;
  
>>>>>>> WA-170_MCP
  // Create the whitelabel
  return client.post<Whitelabel>('whitelabel', {
    name: params.name,
    slug,
    groupId: params.groupId
  });
}

/**
 * Get a whitelabel by ID
 * @param role User role
 * @param whitelabelId Whitelabel ID
 * @returns A promise that resolves with the whitelabel
 */
export async function getWhitelabel(role: UserRole, whitelabelId: string): Promise<Whitelabel> {
  if (USE_MOCK) {
    return getMockWhitelabel(role, whitelabelId);
  }
<<<<<<< HEAD

  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);

=======
  
  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);
  
>>>>>>> WA-170_MCP
  // Get the whitelabel
  return client.get<Whitelabel>(`whitelabel/${whitelabelId}`);
}

/**
 * Get all whitelabels for a group
 * @param role User role
 * @param groupId Group ID
 * @returns A promise that resolves with the whitelabels
 */
export async function getWhitelabels(role: UserRole, groupId: string): Promise<Whitelabel[]> {
  if (USE_MOCK) {
    return getMockWhitelabels(role, groupId);
  }
<<<<<<< HEAD

  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);

=======
  
  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);
  
>>>>>>> WA-170_MCP
  // Get the whitelabels
  return client.get<Whitelabel[]>(`whitelabel?groupId=${groupId}`);
}

/**
 * Delete a whitelabel
 * @param role User role
 * @param whitelabelId Whitelabel ID
 * @returns A promise that resolves when the whitelabel is deleted
 */
export async function deleteWhitelabel(role: UserRole, whitelabelId: string): Promise<void> {
  if (USE_MOCK) {
    return deleteMockWhitelabel(role, whitelabelId);
  }
<<<<<<< HEAD

  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);

=======
  
  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);
  
>>>>>>> WA-170_MCP
  // Delete the whitelabel
  await client.delete(`whitelabel/${whitelabelId}`);
}

/**
 * Update a whitelabel
 * @param role User role
 * @param whitelabelId Whitelabel ID
 * @param updates Updates to apply
 * @returns A promise that resolves with the updated whitelabel
 */
export async function updateWhitelabel(
  role: UserRole,
  whitelabelId: string,
  updates: Partial<Pick<Whitelabel, 'name' | 'slug'>>
): Promise<Whitelabel> {
  if (USE_MOCK) {
    return updateMockWhitelabel(role, whitelabelId, updates);
  }
<<<<<<< HEAD

  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);

=======
  
  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);
  
>>>>>>> WA-170_MCP
  // Update the whitelabel
  return client.put<Whitelabel>(`whitelabel/${whitelabelId}`, updates);
}

// Mock implementations

/**
 * Create a mock whitelabel
 * @param role User role
 * @param params Whitelabel parameters
 * @returns A promise that resolves with the created whitelabel
 */
async function createMockWhitelabel(
  role: UserRole,
  params: { name: string; groupId: string; slug?: string }
): Promise<Whitelabel> {
  console.log(`[Mock] Creating whitelabel: ${params.name} for group ${params.groupId}`);
<<<<<<< HEAD

  // Validate input - simulate real API validation
  if (!params.name || params.name.trim() === '') {
    throw new Error('Whitelabel name is required and cannot be empty');
  }

  if (params.name.length > 100) {
    throw new Error('Whitelabel name cannot exceed 100 characters');
  }

  if (!params.groupId || params.groupId.trim() === '') {
    throw new Error('Group ID is required');
  }

  const authFile = getAuthFileForRole(role);
  const userId = getUserIdFromAuthState(authFile) || `mock-${role}-user`;

=======
  
  const authFile = getAuthFileForRole(role);
  const userId = getUserIdFromAuthState(authFile) || `mock-${role}-user`;
  
>>>>>>> WA-170_MCP
  // Generate a unique ID and slug
  const id = `mock-whitelabel-${randomString()}`;
  const slug = params.slug || `${params.name.toLowerCase().replace(/\s+/g, '-')}-${Date.now().toString(36)}`;
  const now = new Date().toISOString();
<<<<<<< HEAD

=======
  
>>>>>>> WA-170_MCP
  // Create the whitelabel
  const whitelabel: Whitelabel = {
    _id: id,
    name: params.name,
    slug,
    groupId: params.groupId,
    ownerUser: userId,
    createdAt: now,
    updatedAt: now
  };
<<<<<<< HEAD

  // Store the whitelabel
  mockWhitelabels.set(id, whitelabel);

  console.log(`[Mock] Created whitelabel: ${params.name} (${id})`);

=======
  
  // Store the whitelabel
  mockWhitelabels.set(id, whitelabel);
  
  console.log(`[Mock] Created whitelabel: ${params.name} (${id})`);
  
>>>>>>> WA-170_MCP
  return whitelabel;
}

/**
 * Get a mock whitelabel by ID
 * @param role User role
 * @param whitelabelId Whitelabel ID
 * @returns A promise that resolves with the whitelabel
 */
async function getMockWhitelabel(role: UserRole, whitelabelId: string): Promise<Whitelabel> {
  console.log(`[Mock] Getting whitelabel: ${whitelabelId}`);
<<<<<<< HEAD

  const whitelabel = mockWhitelabels.get(whitelabelId);

  if (!whitelabel) {
    throw new Error(`Whitelabel not found: ${whitelabelId}`);
  }

  // Simulate authorization check
  const authFile = getAuthFileForRole(role);
  const userId = getUserIdFromAuthState(authFile) || `mock-${role}-user`;

  // Check if user has access to this whitelabel
  if (role !== 'admin' && whitelabel.ownerUser !== userId) {
    throw new Error('Unauthorized: You do not have permission to access this whitelabel');
  }

  console.log(`[Mock] Retrieved whitelabel: ${whitelabel.name} (${whitelabelId})`);

=======
  
  const whitelabel = mockWhitelabels.get(whitelabelId);
  
  if (!whitelabel) {
    throw new Error(`Whitelabel not found: ${whitelabelId}`);
  }
  
  console.log(`[Mock] Retrieved whitelabel: ${whitelabel.name} (${whitelabelId})`);
  
>>>>>>> WA-170_MCP
  return whitelabel;
}

/**
 * Get all mock whitelabels for a group
 * @param role User role
 * @param groupId Group ID
 * @returns A promise that resolves with the whitelabels
 */
async function getMockWhitelabels(role: UserRole, groupId: string): Promise<Whitelabel[]> {
  console.log(`[Mock] Getting whitelabels for group: ${groupId}`);
<<<<<<< HEAD

=======
  
>>>>>>> WA-170_MCP
  // Filter whitelabels by group ID
  const whitelabels = Array.from(mockWhitelabels.values()).filter(whitelabel => {
    return whitelabel.groupId === groupId;
  });
<<<<<<< HEAD

  console.log(`[Mock] Retrieved ${whitelabels.length} whitelabels for group: ${groupId}`);

=======
  
  console.log(`[Mock] Retrieved ${whitelabels.length} whitelabels for group: ${groupId}`);
  
>>>>>>> WA-170_MCP
  return whitelabels;
}

/**
 * Delete a mock whitelabel
 * @param role User role
 * @param whitelabelId Whitelabel ID
 * @returns A promise that resolves when the whitelabel is deleted
 */
async function deleteMockWhitelabel(role: UserRole, whitelabelId: string): Promise<void> {
  console.log(`[Mock] Deleting whitelabel: ${whitelabelId}`);
<<<<<<< HEAD

  const whitelabel = mockWhitelabels.get(whitelabelId);

  if (!whitelabel) {
    throw new Error(`Whitelabel not found: ${whitelabelId}`);
  }

  // Delete the whitelabel
  mockWhitelabels.delete(whitelabelId);

=======
  
  const whitelabel = mockWhitelabels.get(whitelabelId);
  
  if (!whitelabel) {
    throw new Error(`Whitelabel not found: ${whitelabelId}`);
  }
  
  // Delete the whitelabel
  mockWhitelabels.delete(whitelabelId);
  
>>>>>>> WA-170_MCP
  console.log(`[Mock] Deleted whitelabel: ${whitelabelId}`);
}

/**
 * Update a mock whitelabel
 * @param role User role
 * @param whitelabelId Whitelabel ID
 * @param updates Updates to apply
 * @returns A promise that resolves with the updated whitelabel
 */
async function updateMockWhitelabel(
  role: UserRole,
  whitelabelId: string,
  updates: Partial<Pick<Whitelabel, 'name' | 'slug'>>
): Promise<Whitelabel> {
  console.log(`[Mock] Updating whitelabel: ${whitelabelId}`);
<<<<<<< HEAD

  // Validate input - simulate real API validation
  if (updates.name !== undefined && (!updates.name || updates.name.trim() === '')) {
    throw new Error('Whitelabel name is required and cannot be empty');
  }

  if (updates.name && updates.name.length > 100) {
    throw new Error('Whitelabel name cannot exceed 100 characters');
  }

  const whitelabel = mockWhitelabels.get(whitelabelId);

  if (!whitelabel) {
    throw new Error(`Whitelabel not found: ${whitelabelId}`);
  }

  // Simulate authorization check
  const authFile = getAuthFileForRole(role);
  const userId = getUserIdFromAuthState(authFile) || `mock-${role}-user`;

  // Check if user has access to this whitelabel
  if (role !== 'admin' && whitelabel.ownerUser !== userId) {
    throw new Error('Unauthorized: You do not have permission to update this whitelabel');
  }

=======
  
  const whitelabel = mockWhitelabels.get(whitelabelId);
  
  if (!whitelabel) {
    throw new Error(`Whitelabel not found: ${whitelabelId}`);
  }
  
>>>>>>> WA-170_MCP
  // Update the whitelabel
  if (updates.name) {
    whitelabel.name = updates.name;
  }
<<<<<<< HEAD

  if (updates.slug) {
    whitelabel.slug = updates.slug;
  }

  // Update the updatedAt timestamp
  whitelabel.updatedAt = new Date().toISOString();

  console.log(`[Mock] Updated whitelabel: ${whitelabel.name} (${whitelabelId})`);

=======
  
  if (updates.slug) {
    whitelabel.slug = updates.slug;
  }
  
  // Update the updatedAt timestamp
  whitelabel.updatedAt = new Date().toISOString();
  
  console.log(`[Mock] Updated whitelabel: ${whitelabel.name} (${whitelabelId})`);
  
>>>>>>> WA-170_MCP
  return whitelabel;
}
