import { chromium, FullConfig } from '@playwright/test';
import { getCloudTestConfig, getServiceEndpoints } from './config/cloud-config';

/**
 * Global setup for production E2E testing
 * This runs once before all tests and ensures the production environment is ready
 * 
 * IMPORTANT: This is READ-ONLY testing - no data modifications are made
 */
async function globalSetupProduction(config: FullConfig) {
  console.log('🏭 Starting global setup for PRODUCTION E2E testing...');
  console.log('⚠️ READ-ONLY MODE: No data modifications will be made');
  
  const cloudConfig = getCloudTestConfig();
  const endpoints = getServiceEndpoints(cloudConfig);
  
  console.log(`🎯 Target environment: ${cloudConfig.environment}`);
  console.log(`🔗 Base URL: ${cloudConfig.baseURL}`);
  console.log(`🔗 API URL: ${cloudConfig.apiURL}`);
  
  // Ensure we're in production mode
  if (cloudConfig.environment !== 'production') {
    throw new Error(`Expected production environment, got: ${cloudConfig.environment}`);
  }
  
  // Set read-only mode
  process.env.READ_ONLY_MODE = 'true';
  
  // Launch a browser for setup tasks
  const browser = await chromium.launch();
  const context = await browser.newContext({
    extraHTTPHeaders: {
      ...cloudConfig.headers,
      'User-Agent': 'Divinci-E2E-Tests/1.0 (Production-ReadOnly-Setup)',
    },
    ignoreHTTPSErrors: false, // Never ignore HTTPS errors in production
  });
  
  try {
    // 1. Verify production API is accessible
    console.log('🔍 Verifying production API accessibility...');
    const page = await context.newPage();
    
    try {
      const response = await page.request.get(endpoints.api.health, {
        timeout: 30000, // 30 second timeout for production check
      });
      
      if (response.status() !== 200) {
        throw new Error(`Production API health check failed with status ${response.status()}`);
      }
      
      const healthData = await response.json();
      if (!healthData.hello || healthData.hello !== 'world!') {
        throw new Error('Production API returned unexpected response format');
      }
      
      console.log('✅ Production API is accessible and healthy');
    } catch (error) {
      console.error('❌ Production API health check failed:', error);
      throw new Error(`Production API is not accessible at ${endpoints.api.health}. Cannot proceed with production testing.`);
    }
    
    // 2. Verify production web application is accessible
    console.log('🔍 Verifying production web application accessibility...');
    
    try {
      await page.goto(cloudConfig.baseURL, {
        timeout: 30000,
        waitUntil: 'networkidle',
      });
      
      const title = await page.title();
      if (!title || title.length === 0) {
        throw new Error('Production web application loaded but has no title');
      }
      
      console.log(`✅ Production web application is accessible (title: "${title}")`);
    } catch (error) {
      console.error('❌ Production web application accessibility check failed:', error);
      throw new Error(`Production web application is not accessible at ${cloudConfig.baseURL}. Cannot proceed with production testing.`);
    }
    
    // 3. Verify production security headers
    console.log('🔍 Verifying production security configuration...');
    
    try {
      const response = await page.request.get(cloudConfig.baseURL, {
        timeout: 15000,
      });
      
      const headers = response.headers();
      const requiredSecurityHeaders = [
        'strict-transport-security',
        'x-content-type-options',
        'x-frame-options',
      ];
      
      for (const header of requiredSecurityHeaders) {
        if (!headers[header]) {
          console.warn(`⚠️ Missing security header: ${header}`);
        }
      }
      
      // Check for Cloudflare protection
      if (headers['server'] === 'cloudflare') {
        console.log('✅ Cloudflare protection is active');
      }
      
      console.log('✅ Production security configuration verified');
    } catch (error) {
      console.warn('⚠️ Production security verification failed:', error);
      // Don't fail setup for security header issues
    }
    
    // 4. Verify Cloudflare Access configuration (if enabled)
    if (cloudConfig.features.cloudflareAccess) {
      console.log('🔍 Verifying production Cloudflare Access configuration...');
      
      const cfHeaders = {
        'CF-Access-Client-Id': cloudConfig.headers['CF-Access-Client-Id'],
        'CF-Access-Client-Secret': cloudConfig.headers['CF-Access-Client-Secret'],
      };
      
      if (!cfHeaders['CF-Access-Client-Id'] || !cfHeaders['CF-Access-Client-Secret']) {
        console.warn('⚠️ Cloudflare Access credentials not found - some tests may fail');
      } else {
        try {
          const response = await page.request.get(endpoints.api.health, {
            headers: cfHeaders,
            timeout: 15000,
          });
          
          if (response.status() === 200) {
            console.log('✅ Production Cloudflare Access is properly configured');
          } else {
            console.warn(`⚠️ Cloudflare Access check returned status ${response.status()}`);
          }
        } catch (error) {
          console.warn('⚠️ Cloudflare Access verification failed:', error);
        }
      }
    }
    
    // 5. Production-specific setup
    await performProductionSpecificSetup(cloudConfig, page);
    
    console.log('🎉 Production global setup completed successfully');
    console.log('⚠️ All tests will run in READ-ONLY mode');
    
  } finally {
    await context.close();
    await browser.close();
  }
}

/**
 * Perform production-specific setup tasks
 */
async function performProductionSpecificSetup(cloudConfig: ReturnType<typeof getCloudTestConfig>, page: any) {
  console.log(`🔧 Performing production-specific setup...`);
  
  // Ensure read-only mode is enforced
  process.env.READ_ONLY_MODE = 'true';
  
  // Set conservative timeouts for production
  process.env.PRODUCTION_TIMEOUT_MULTIPLIER = '2';
  
  // Disable any debug features
  process.env.DEBUG = '';
  process.env.NODE_ENV = 'production';
  
  // Log production testing start
  console.log('🏭 Production environment - READ-ONLY testing mode enabled');
  console.log('🛡️ No data modifications will be made');
  console.log('📊 Performance monitoring enabled');
  console.log('🔒 Security validation enabled');
  
  console.log(`✅ Production environment setup completed`);
}

export default globalSetupProduction;
