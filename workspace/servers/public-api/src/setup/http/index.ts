import express, { Request, Response, NextFunction } from "express";
import cors from "cors";

import {
  optionalAuthMiddlewareHTTP,
  createCorsOptions,
} from "@divinci-ai/server-globals";
import { HTTP_ERRORS } from "@divinci-ai/server-utils";

import { handleError } from "./handle-error";
import { localCorsMiddleware } from "../../middleware/local-cors";

import { router as chatRouter } from "../../ux/ai-chat/router";
import { router as whitelabelRouter } from "../../ux/whitelabel/router";
import { router as socialRouter } from "../../ux/social/router";
import { router as userRouter } from "../../ux/user/router";
import { router as userGroupRouter } from "../../ux/user-group/router";
import { router as moneyRouter } from "../../ux/money";
import { router as whitelabelReleaseRouter } from "../../ux/whitelabel-releases";
import { router as systemRouter } from "../../ux/system";
<<<<<<< HEAD
=======
import { handleGenericDropboxCallback } from "../../ux/whitelabel/router/dropbox/generic-callback";
>>>>>>> WA-170_MCP

export async function setupHttpApp(){
  const app = express();

  // Apply local CORS middleware first (only applies in local environment)
  app.use(localCorsMiddleware);

  // Use standard cors middleware with proper options
  const corsOptions = createCorsOptions();

<<<<<<< HEAD
  // Log CORS options for debugging
  // console.log("🔍 CORS Options:", JSON.stringify({
  //   origin: typeof corsOptions.origin === "function" ? "Function" : corsOptions.origin,
  //   methods: corsOptions.methods,
  //   credentials: corsOptions.credentials,
  //   allowedHeaders: corsOptions.allowedHeaders,
  //   exposedHeaders: corsOptions.exposedHeaders,
  //   preflightContinue: corsOptions.preflightContinue,
  //   optionsSuccessStatus: corsOptions.optionsSuccessStatus
  // }, null, 2));

=======
>>>>>>> WA-170_MCP
  app.use(cors(corsOptions));

  app.use(optionalAuthMiddlewareHTTP);

  // Add a global OPTIONS handler to ensure all endpoints can handle OPTIONS requests
  app.options("*", (req, res)=>{
    // console.log("✅ Global OPTIONS handler for:", req.path);
    // CORS headers are already set by the cors middleware above
    // Just need to end the response with 204 No Content
    res.status(204).end();
  });

  // 📓 https://github.com/microsoft/TypeScript/issues/9458
  app.get("/", function(_, res){
    res.setHeader("Content-Type", "application/json; charset=utf-8");
    res.json({
      hello: "world!",
      whoami: "An api server!",
      server: process.env.HELLO_WORLD,
    });
  });

  app.use("/ai-chat", chatRouter);
  app.use("/white-label", whitelabelRouter);
  app.use("/social", socialRouter);
  app.use("/user", userRouter);
  app.use("/user-group", userGroupRouter);
  app.use("/money", moneyRouter);
  app.use("/white-label-release", whitelabelReleaseRouter);
  app.use("/system", systemRouter);
<<<<<<< HEAD
=======

  // Generic Dropbox callback route (works for any whitelabel)
  app.get("/dropbox/auth/callback", handleGenericDropboxCallback);
>>>>>>> WA-170_MCP

  // If we hit this point, the route was not found
  // throw a 404 error
  app.use((req, res, next)=>(
    next(HTTP_ERRORS.NOT_FOUND)
  ));
  app.use(errorHandler);

  return app;
}

// Ignoring the no unused vars rule because express expects 4 arguments for cacthing errors
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const errorHandler = (err: any, req: Request, res: Response, next: NextFunction)=>{
  try {
    const { statusCode, message, context } = handleError(err);

    console.log("☹️ HTTP ERR: ", req.originalUrl, err, message, context);

    res.statusCode = statusCode;
    return res.json({
      status: "error",
      message: message,
      context: context,
    });
  }catch(e: any){
    // Should never reach here but typescript wants next to be used
    // If we do hit this point, its a serious problem
    console.log("🔥 Error in error handler: ", e);
    res.statusCode = 500;
    return res.json({
      status: "error",
      message: e.message,
    });
  }
};
