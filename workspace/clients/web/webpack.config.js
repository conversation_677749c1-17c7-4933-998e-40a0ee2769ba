/* eslint-disable @typescript-eslint/no-var-requires */

const path = require("node:path");
const { setupEnv } = require("@divinci-ai/server-utils");

setupEnv({ envPath: path.resolve(__dirname, "./env"), client: true });

const webpack = require("webpack");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const NodePolyfillPlugin = require("node-polyfill-webpack-plugin");

module.exports = {
  entry: "./src/index.tsx",
  output: {
    filename: "hidden.build.js",
    path: path.resolve(__dirname, "public"),
    publicPath: "/",
  },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: "ts-loader",
        exclude: /node_modules/,
      },
      {
        test: /\.css$/,
        oneOf: [
          {
            // Enable CSS Modules for files ending in `.module.css`
            test: /\.module\.css$/,
            use: [
              "style-loader",
              {
                loader: "css-loader",
                options: {
                  modules: {
                    localIdentName: "[name]__[local]___[hash:base64:5]",
                  },
                },
              },
            ],
          },
          {
            // Regular global CSS files
            use: ["style-loader", "css-loader"],
          },
        ],
      },
      {
        test: /\.svg$/i,
        issuer: /\.[jt]sx?$/,
        use: ["@svgr/webpack"],
      },
      {
        test: /\.txt$/i,
        use: "raw-loader",
      },
      {
        test: /\.md$/i,
        use: "raw-loader",
      },
    ],
  },
  resolve: {
    extensions: [".tsx", ".ts", ".js", ".cjs", ".mjs"],
    alias: {
      "@divinci-ai/utils": path.resolve(__dirname, "../../resources/utils"),
      "@divinci-ai/models": path.resolve(__dirname, "../../resources/models"),
      "@styles": path.resolve(__dirname, "./public"),
      process: "process/browser.js",
    },
    fallback: {
      stream: require.resolve("stream-browserify"),
      buffer: require.resolve("buffer/"),
    }
  },
  plugins: [
    new webpack.ProvidePlugin({
      process: "process/browser.js",
      Buffer: ["buffer", "Buffer"],
    }),
    new webpack.DefinePlugin({
      "process.env": JSON.stringify(process.env),
    }),
    new HtmlWebpackPlugin({
      template: "./public/template.html",
    }),
    new NodePolyfillPlugin({
      includeAliases: ["events"],
    }),
  ],
  mode: process.env.NODE_ENV || "development",
  devtool: "inline-source-map",
  optimization: {
    usedExports: true,
  },
  devServer: {
    hot: true,
    port: process.env.HTTP_PORT || 8080,
    host: "0.0.0.0",
    server: "http", // Use HTTP for Codespaces compatibility
    historyApiFallback: true,
    static: {
      directory: path.join(__dirname, "public"),
    },
    allowedHosts: "all",
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, PATCH, OPTIONS",
      "Access-Control-Allow-Headers":
        "X-Requested-With, content-type, Authorization",
    },
    client: {
      logging: "verbose", // For debugging WebSocket issues
    },
    compress: false, // Disable compression for WebSocket compatibility
    webSocketServer: "ws", // Ensure ws is used for the WebSocket server
  },
  // 📓 For much webpack more logs, turn this puppy on!
  // stats: {
  //   errorDetails: true,
  // },
};
