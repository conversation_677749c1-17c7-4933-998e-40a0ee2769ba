{
  "cookies": [],
  "origins": [
    {
      "origin": "http://localhost:8080",
      "localStorage": [
        {
<<<<<<< HEAD
          "name": "@@auth0spajs@@::waO2KMbVwo3xXu1wqZyUeLA1cTinVpXx::chat.divinci.app:8081::openid profile email",
          "value": "{\"body\":{\"access_token\":\"***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\",\"id_token\":\"real-id-token\",\"scope\":\"openid profile email\",\"expires_in\":86400,\"token_type\":\"Bearer\"},\"expiresAt\":1748092895367}"
=======
          "name": "@@auth0spajs@@::mock-client-id::https://api.divinci.app::openid profile email",
          "value": "{\"body\":{\"access_token\":\"mock-admin-token\",\"id_token\":\"mock-id-token\",\"scope\":\"openid profile email\",\"expires_in\":86400,\"token_type\":\"Bearer\"},\"expiresAt\":9999999999}"
>>>>>>> WA-170_MCP
        },
        {
          "name": "auth0.user",
          "value": "{\"sub\":\"auth0|admin\",\"email\":\"<EMAIL>\",\"email_verified\":true}"
        }
      ]
    }
  ]
}