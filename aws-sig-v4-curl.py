#!/usr/bin/env python3
import sys
import os
import datetime
import hashlib
import hmac
import base64
import urllib.parse

# AWS Signature Version 4 implementation for curl
# Based on AWS documentation

# Configuration
access_key = 'nCLhErHAN2AKoT1pJypT'
secret_key = 'TRDKEXukNu3O6gCQrEMcqIPlWZyiyrDovHjn91CX'
region = 'us-east-1'  # MinIO uses this as default
service = 's3'
host = '127.0.0.1:9000'
endpoint = f'http://{host}'
<<<<<<< HEAD
bucket = 'rag-origin-files-local'
=======
# bucket = 'rag-origin-files-local'
bucket = 'rag-files-local'
>>>>>>> WA-170_MCP
object_key = 'test-file.txt'

# Create a test file
with open('test-file.txt', 'w') as f:
    f.write('This is a test file for MinIO')

# Read the file content
with open('test-file.txt', 'rb') as f:
    payload_body = f.read()

# Request details
http_method = 'PUT'
canonical_uri = f'/{bucket}/{object_key}'
canonical_querystring = ''
content_type = 'text/plain'

# Create timestamp for headers and credential string
t = datetime.datetime.utcnow()
amz_date = t.strftime('%Y%m%dT%H%M%SZ')
date_stamp = t.strftime('%Y%m%d')

# Create canonical headers
canonical_headers = f'content-type:{content_type}\nhost:{host}\nx-amz-content-sha256:{hashlib.sha256(payload_body).hexdigest()}\nx-amz-date:{amz_date}\n'
signed_headers = 'content-type;host;x-amz-content-sha256;x-amz-date'

# Create payload hash
payload_hash = hashlib.sha256(payload_body).hexdigest()

# Combine elements to create canonical request
canonical_request = f'{http_method}\n{canonical_uri}\n{canonical_querystring}\n{canonical_headers}\n{signed_headers}\n{payload_hash}'

# Create string to sign
algorithm = 'AWS4-HMAC-SHA256'
credential_scope = f'{date_stamp}/{region}/{service}/aws4_request'
string_to_sign = f'{algorithm}\n{amz_date}\n{credential_scope}\n{hashlib.sha256(canonical_request.encode("utf-8")).hexdigest()}'

# Calculate signature
def sign(key, msg):
    return hmac.new(key, msg.encode('utf-8'), hashlib.sha256).digest()

def getSignatureKey(key, dateStamp, regionName, serviceName):
    kDate = sign(('AWS4' + key).encode('utf-8'), dateStamp)
    kRegion = sign(kDate, regionName)
    kService = sign(kRegion, serviceName)
    kSigning = sign(kService, 'aws4_request')
    return kSigning

signing_key = getSignatureKey(secret_key, date_stamp, region, service)
signature = hmac.new(signing_key, string_to_sign.encode('utf-8'), hashlib.sha256).hexdigest()

# Create authorization header
authorization_header = f'{algorithm} Credential={access_key}/{credential_scope}, SignedHeaders={signed_headers}, Signature={signature}'

# Print curl command
curl_command = f'''
curl -v -X {http_method} \\
  -H "Content-Type: {content_type}" \\
  -H "X-Amz-Content-SHA256: {payload_hash}" \\
  -H "X-Amz-Date: {amz_date}" \\
  -H "Authorization: {authorization_header}" \\
  --data-binary @test-file.txt \\
  "{endpoint}{canonical_uri}"
'''

print(curl_command)

# Create a script to run the curl command
with open('run-aws-sig-v4-curl.sh', 'w') as f:
    f.write('#!/bin/bash\n')
    f.write(curl_command)

os.chmod('run-aws-sig-v4-curl.sh', 0o755)
print("\nScript saved as run-aws-sig-v4-curl.sh. Run it to execute the curl command.")
