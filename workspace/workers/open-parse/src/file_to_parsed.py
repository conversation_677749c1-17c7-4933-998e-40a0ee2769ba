import os
import uuid
import time
import logging
import tempfile
import requests
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

# Get MinIO credentials from environment variables
<<<<<<< HEAD
# First try R2 credentials (which might be set for production)
R2_ACCESS_KEY_ID = os.environ.get("R2_ACCESS_KEY_ID", None)
R2_SECRET_ACCESS_KEY = os.environ.get("R2_SECRET_ACCESS_KEY", None)

# If R2 credentials aren't set, fall back to MinIO credentials
if not R2_ACCESS_KEY_ID or not R2_SECRET_ACCESS_KEY:
    MINIO_ROOT_USER = os.environ.get("MINIO_ROOT_USER", "minioadmin")
    MINIO_ROOT_PASSWORD = os.environ.get("MINIO_ROOT_PASSWORD", "minioadmin")
    logger.info(f"Using MinIO credentials: {MINIO_ROOT_USER[:4]}.../{MINIO_ROOT_PASSWORD[:4]}...")
else:
    MINIO_ROOT_USER = R2_ACCESS_KEY_ID
    MINIO_ROOT_PASSWORD = R2_SECRET_ACCESS_KEY
    logger.info(f"Using R2 credentials: {MINIO_ROOT_USER[:4]}.../{MINIO_ROOT_PASSWORD[:4]}...")

# Get MinIO host and port from environment variables
MINIO_HOST = os.environ.get("MINIO_HOST", "local-minio")
=======
# For local development, prioritize MinIO credentials over R2 credentials
def get_minio_credentials():
    """Get the appropriate credentials based on environment."""
    # Check if we should force R2 storage even in local environment
    force_r2_storage = os.environ.get("FORCE_R2_STORAGE", "false").lower() == "true"

    # Check if we have MinIO host configured (indicates local development)
    minio_host = os.environ.get("MINIO_HOST", "localhost")
    is_local_minio = ("localhost" in minio_host or
                      "host.docker.internal" in minio_host or
                      "minio.divinci.local" in minio_host or
                      "local-minio" in minio_host)

    logger.info(f"MINIO_HOST: '{minio_host}', is_local_minio: {is_local_minio}, FORCE_R2_STORAGE: {force_r2_storage}")
    logger.info(f"Available environment variables: MINIO_ROOT_USER={os.environ.get('MINIO_ROOT_USER', 'NOT_SET')}, R2_ACCESS_KEY_ID={os.environ.get('R2_ACCESS_KEY_ID', 'NOT_SET')}")

    if is_local_minio and not force_r2_storage:
        # For local development, always use MinIO credentials
        user = os.environ.get("MINIO_ROOT_USER", "minioadmin")
        password = os.environ.get("MINIO_ROOT_PASSWORD", "minioadmin")
        logger.info(f"Using MinIO credentials for local development: {user[:4]}.../{password[:4]}...")
        return user, password
    else:
        # For production, try R2 credentials first, then fall back to MinIO credentials
        R2_ACCESS_KEY_ID = os.environ.get("R2_ACCESS_KEY_ID", None)
        R2_SECRET_ACCESS_KEY = os.environ.get("R2_SECRET_ACCESS_KEY", None)

        if not R2_ACCESS_KEY_ID or not R2_SECRET_ACCESS_KEY:
            user = os.environ.get("MINIO_ROOT_USER", "minioadmin")
            password = os.environ.get("MINIO_ROOT_PASSWORD", "minioadmin")
            logger.info(f"Using MinIO credentials as fallback: {user[:4]}.../{password[:4]}...")
            return user, password
        else:
            logger.info(f"Using R2 credentials: {R2_ACCESS_KEY_ID[:4]}.../{R2_SECRET_ACCESS_KEY[:4]}...")
            return R2_ACCESS_KEY_ID, R2_SECRET_ACCESS_KEY

# Get initial credentials (will be used for HTTP auth fallback)
MINIO_ROOT_USER, MINIO_ROOT_PASSWORD = get_minio_credentials()

# Get MinIO host and port from environment variables
MINIO_HOST = os.environ.get("MINIO_HOST", "localhost")
>>>>>>> WA-170_MCP
MINIO_PORT = os.environ.get("MINIO_PORT", "9000")

UPLOAD_FOLDER = '/openparse-tmp'

# import openparse
from openparse import processing, DocumentParser


# Ollama setup
# semantic_pipeline = processing.SemanticIngestionPipeline(
#     embedding_provider="ollama",
#     # model="bge-large",  # or "nomic-embed-text"
#     model="nomic-embed-text",  # or "nomic-embed-text"
#     min_tokens=64,
#     max_tokens=1024,
# )

"""
Ollama Config
{
  embeddings_provider: "ollama",
  api_url: os.environ.get("OLLAMA_API_URL")
}
"""

"""
Cloudflare Config
{
  embeddings_provider: "cloudflare",
  model: "@cf/baai/bge-base-en-v1.5",  # Cloudflare's BGE model
  api_token: os.environ.get("CLOUDFLARE_API_TOKEN"),
  account_id: os.environ.get("CLOUDFLARE_ACCOUNT_ID"),
}
"""

semantic_pipeline = processing.SemanticIngestionPipeline(
    min_tokens=64,
    max_tokens=1024,
    embeddings_provider="cloudflare",
    model="@cf/baai/bge-base-en-v1.5",
    api_token=os.environ.get("CLOUDFLARE_API_KEY"),
    account_id=os.environ.get("CLOUDFLARE_ACCOUNT_ID"),
)

def save_file_as_unique(uploaded_file):
    _, ext = os.path.splitext(uploaded_file.filename)
    random_name = f"{uuid.uuid4()}{ext}"
    full_path = os.path.join(UPLOAD_FOLDER, random_name)
    uploaded_file.save(full_path)
    return full_path


try:
  # parser = openparse.DocumentParser()
    parser = DocumentParser(
        use_markitdown=True,
        processing_pipeline=semantic_pipeline
    )
    logger.info("✅ Parser initialized")
except Exception as e:
    logger.error(f"❌ Error initializing parser: {e}")
    parser = None

def file_to_parsed(
    file_path: str,
    config: dict = None,
    filename: str = None
) -> List[Dict[str, Any]]:
    """Process a file and return parsed chunks."""
    logger.info(f"📄 Processing file URL: {file_path}")
    logger.info(f"⚙️ file_to_parsed config: {config}")
    logger.info(f"📛 Filename: {filename}")

    try:
        # Extract config values for logging purposes
        min_tokens = config.get('minTokens', 256) if config else 256
        max_tokens = config.get('maxTokens', 1024) if config else 1024
        semantic_chunking = config.get('semantic', False) if config else False
        # Get embeddings provider from nested config
        embeddings_provider = 'none'
        if config and 'embeddings' in config and isinstance(config['embeddings'], dict):
            embeddings_provider = config['embeddings'].get('provider', 'none')

        # Log the actual credentials being used for the request
        logger.info("Making request with credentials:")
        logger.info(f"API Token length: {len(os.environ.get('CLOUDFLARE_API_KEY', ''))}")
        logger.info(f"Account ID present: {'CLOUDFLARE_ACCOUNT_ID' in os.environ}")

        # Use semantic chunking if specified
        if semantic_chunking:
            processing_pipeline = processing.SemanticIngestionPipeline(
                min_tokens=min_tokens,
                max_tokens=max_tokens,
                embeddings_provider=embeddings_provider,
                model="@cf/baai/bge-base-en-v1.5",
                api_token=os.environ.get("CLOUDFLARE_API_KEY"),
                account_id=os.environ.get("CLOUDFLARE_ACCOUNT_ID"),
            )
        else:
            processing_pipeline = processing.BasicIngestionPipeline()

        # Initialize parser with the chosen pipeline and pass the entire config object
        parser = DocumentParser(
            use_markitdown=True,
            processing_pipeline=processing_pipeline,
            config=config  # Pass the entire config object as a kwarg
        )

        # CRITICAL: Fix for Docker networking
        # We need to handle various URL formats in different environments
        logger.info(f"Original file URL: {file_path}")

        # Parse the URL to extract the host, port, bucket, and key
        from urllib.parse import urlparse
        parsed_url = urlparse(file_path)
        original_host = parsed_url.netloc
        path_parts = parsed_url.path.strip('/').split('/')

        # Check if the URL contains a bucket name and key
        # The URL format should be http://minio.divinci.local:9000/audio-transcript-files/682415a03d653676ebe89b06/...
        if len(path_parts) >= 2:
            # The first part is the bucket name
            bucket = path_parts[0]
            # The rest of the parts form the key
            key = '/'.join(path_parts[1:])
            logger.info(f"Extracted bucket: {bucket}, key: {key}")

            # Try to use the S3 client to download the file
            try:
                logger.info(f"Attempting to download file using S3 client with bucket: {bucket}, key: {key}")
                from src.s3_client import get_s3_client

                # Create a temporary file to download to
                import tempfile
                with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{path_parts[-1]}") as temp_file:
                    temp_path = temp_file.name

                logger.info(f"Downloading to temporary file: {temp_path}")

<<<<<<< HEAD
                # Use the reliable minio.divinci.local endpoint
                logger.info("Using minio.divinci.local endpoint")
                s3_client = get_s3_client(endpoint_url="http://minio.divinci.local:9000")
=======
                # Use the configured MinIO endpoint (localhost for open-parse service)
                minio_host = os.environ.get('MINIO_HOST', 'localhost')
                minio_port = os.environ.get('MINIO_PORT', '9000')
                endpoint_url = f"http://{minio_host}:{minio_port}"
                logger.info(f"Using configured MinIO endpoint: {endpoint_url}")
                s3_client = get_s3_client(endpoint_url=endpoint_url)
>>>>>>> WA-170_MCP
                s3_client.download_file(bucket, key, temp_path)
                logger.info(f"✅ Successfully downloaded file using S3 client to: {temp_path}")

                # Parse the document
                result = parser.parse(
                    temp_path,
                    embeddings_provider=embeddings_provider if semantic_chunking else None
                )

                # Clean up the temporary file
                try:
                    os.unlink(temp_path)
                except Exception as cleanup_e:
                    logger.warning(f"Error deleting S3 temporary file: {cleanup_e}")

                return result

            except Exception as e:
                logger.error(f"❌ Failed to download file using S3 client: {str(e)}")
                logger.info("Falling back to HTTP download method")

<<<<<<< HEAD
        # Use the reliable MinIO hostname
        if "127.0.0.1" in file_path or "localhost" in file_path:
            logger.info(f"🔧 Replacing localhost/127.0.0.1 with minio.divinci.local in URL")
            file_path = file_path.replace("127.0.0.1:9000", "minio.divinci.local:9000")
            file_path = file_path.replace("localhost:9000", "minio.divinci.local:9000")
            logger.info(f"🔧 Updated URL: {file_path}")

=======
        # Use the configured MinIO hostname (host.docker.internal for container-to-host communication)
        minio_host = os.environ.get('MINIO_HOST', 'host.docker.internal')
        minio_port = os.environ.get('MINIO_PORT', '9000')
        if "127.0.0.1" in file_path:
            logger.info(f"🔧 Replacing 127.0.0.1 with {minio_host} in URL")
            file_path = file_path.replace(f"127.0.0.1:{minio_port}", f"{minio_host}:{minio_port}")
            logger.info(f"🔧 Updated URL: {file_path}")

        # CRITICAL FIX: Since bucket is public, try to find the actual file with timestamp prefix
        if "host.docker.internal" in file_path or "localhost" in file_path:
            logger.info(f"🔍 Attempting to find timestamped version of file")
            # Extract bucket and key from the URL
            url_parts = file_path.split('/')
            if len(url_parts) >= 5:  # http://host/bucket/key/parts
                bucket_name = url_parts[3]  # rag-files-local
                key_parts = url_parts[4:]   # [whitelabel, uuid, filename]

                if len(key_parts) >= 3:
                    whitelabel_id = key_parts[0]
                    file_uuid = key_parts[1]
                    original_filename = key_parts[2]

                    # Use S3 client to list files in the directory to find the timestamped version
                    try:
                        s3_client = get_s3_client()
                        prefix = f"{whitelabel_id}/{file_uuid}/"

                        logger.info(f"🔍 Listing files with prefix: {prefix}")
                        response = s3_client.list_objects_v2(Bucket=bucket_name, Prefix=prefix)

                        if 'Contents' in response:
                            for obj in response['Contents']:
                                obj_key = obj['Key']
                                filename_part = obj_key.split('/')[-1]  # Get just the filename

                                # Check if this file ends with our target filename (with timestamp prefix)
                                if filename_part.endswith(original_filename) and filename_part != original_filename:
                                    logger.info(f"🎯 Found timestamped file: {obj_key}")
                                    file_path = f"http://{minio_host}:{minio_port}/{bucket_name}/{obj_key}"
                                    break
                            else:
                                logger.info(f"⚠️ No timestamped version found, using original: {file_path}")
                        else:
                            logger.info(f"⚠️ No files found with prefix {prefix}, using original: {file_path}")
                    except Exception as e:
                        logger.error(f"❌ Error listing files: {e}")
                        logger.info(f"⚠️ Falling back to original URL: {file_path}")

>>>>>>> WA-170_MCP
        logger.info(f"🔍 Using MinIO URL: {file_path}")

        # Create a temporary file but don't close it yet - we'll need it later for writing
        logger.info(f"Starting file download preparation")
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(filename)[1] if filename else '')
        temp_filename = temp_file.name  # Store the filename so we can use it later
        logger.info(f"Created temporary file: {temp_filename}")

        # Download the file directly
        logger.info(f"🔄 Downloading file from: {file_path}")

        # Add User-Agent and other headers to help with debugging
        headers = {
            'User-Agent': 'OpenParse-Service/1.0',
            'X-Source': 'OpenParse-Docker',
            'X-Request-ID': str(uuid.uuid4())[:8]
        }

        # Try with anonymous access first
        logger.info(f"📡 Sending request to: {file_path}")
        response = requests.get(
            file_path,
            timeout=30,  # Longer timeout for file downloads
            headers=headers
        )

<<<<<<< HEAD
        # If anonymous access fails, try with credentials
        if not response.ok:
            logger.info(f"🔑 Anonymous access failed, trying with credentials")

            # Try with the environment credentials first
            response = requests.get(
                file_path,
                timeout=30,
                headers=headers,
                auth=(MINIO_ROOT_USER, MINIO_ROOT_PASSWORD)
            )

            # If that fails, try with the default minioadmin credentials
            if not response.ok:
                logger.info(f"🔑 Environment credentials failed, trying with default minioadmin credentials")
=======
        # If anonymous access fails, try with S3 client (which uses proper AWS signature)
        if not response.ok:
            logger.info(f"🔑 Anonymous access failed, trying with S3 client for signed URL")

            try:
                # Try to use S3 client to generate a presigned URL
                from src.s3_client import get_s3_client

                # Parse the URL to extract bucket and key
                from urllib.parse import urlparse
                parsed_url = urlparse(file_path)
                path_parts = parsed_url.path.strip('/').split('/')

                if len(path_parts) >= 2:
                    bucket = path_parts[0]
                    key = '/'.join(path_parts[1:])

                    # CRITICAL FIX: Find the actual timestamped key before generating presigned URL
                    actual_key = key  # Default to original key
                    try:
                        logger.info(f"🔍 Searching for timestamped version before presigned URL: {key}")
                        s3_client = get_s3_client()

                        # Extract the directory and filename parts
                        key_parts = key.split('/')
                        if len(key_parts) >= 3:  # whitelabel/uuid/filename
                            directory_prefix = '/'.join(key_parts[:-1]) + '/'  # whitelabel/uuid/
                            original_filename = key_parts[-1]  # filename.pdf

                            logger.info(f"🔍 Listing files with prefix: {directory_prefix}")
                            response = s3_client.list_objects_v2(Bucket=bucket, Prefix=directory_prefix)

                            if 'Contents' in response:
                                for obj in response['Contents']:
                                    obj_key = obj['Key']
                                    filename_part = obj_key.split('/')[-1]  # Get just the filename

                                    # Check if this file ends with our target filename (with timestamp prefix)
                                    if filename_part.endswith(original_filename) and filename_part != original_filename:
                                        logger.info(f"🎯 Found timestamped file for presigned URL: {obj_key}")
                                        actual_key = obj_key
                                        break
                                else:
                                    logger.info(f"⚠️ No timestamped version found for presigned URL, using original: {key}")
                            else:
                                logger.info(f"⚠️ No files found with prefix {directory_prefix} for presigned URL")
                    except Exception as e:
                        logger.error(f"❌ Error searching for timestamped file for presigned URL: {e}")
                        logger.info(f"⚠️ Falling back to original key for presigned URL: {key}")

                    logger.info(f"🔑 Generating presigned URL for bucket: {bucket}, key: {actual_key}")

                    # Create S3 client - it will automatically handle FORCE_R2_STORAGE logic
                    logger.info(f"🔑 Using final key for presigned URL: {actual_key}")

                    presigned_url = s3_client.generate_presigned_url(
                        'get_object',
                        Params={'Bucket': bucket, 'Key': actual_key},
                        ExpiresIn=300  # 5 minutes
                    )

                    logger.info(f"🔑 Using presigned URL for download")
                    response = requests.get(
                        presigned_url,
                        timeout=30,
                        headers=headers
                    )

            except Exception as presigned_error:
                logger.warning(f"🔑 Presigned URL generation failed: {str(presigned_error)}")
                logger.info(f"🔑 Falling back to basic auth (may not work with MinIO)")

                # Fallback to basic auth (likely to fail with MinIO)
>>>>>>> WA-170_MCP
                response = requests.get(
                    file_path,
                    timeout=30,
                    headers=headers,
<<<<<<< HEAD
                    auth=("minioadmin", "minioadmin")
=======
                    auth=(MINIO_ROOT_USER, MINIO_ROOT_PASSWORD)
>>>>>>> WA-170_MCP
                )

        # Check if the download was successful
        if response.ok:
            logger.info(f"✅ Successfully downloaded file from {file_path}")
            logger.info(f"Response status code: {response.status_code}")
            logger.info(f"Content length: {len(response.content)} bytes")

            # Write content to the temporary file
            logger.info(f"Writing content to temporary file")
            with open(temp_filename, 'wb') as write_file:
                write_file.write(response.content)
                write_file.flush()

            # Parse the document using the filename we saved
            logger.info("Starting document parsing...")
            result = parser.parse(
                temp_filename,
                embeddings_provider=embeddings_provider if semantic_chunking else None
            )
            logger.info("Document parsing completed successfully")
        else:
            logger.error(f"❌ Failed to download file: {response.status_code}")
            logger.error(f"Response content: {response.text}")
            response.raise_for_status()

        # Clean up - close the temp file if it's still open
        if not temp_file.closed:
            temp_file.close()

        # Delete the file from disk
        logger.info(f"Cleaning up temporary file: {temp_filename}")
        try:
            os.unlink(temp_filename)
        except Exception as e:
            logger.warning(f"Error deleting temporary file: {e}")
            # Non-critical error, continue execution

        return result

    except requests.exceptions.RequestException as e:
        logger.error(f"Request error: {str(e)}")
        logger.error(f"Request details - URL: {file_path}")
        if hasattr(e.response, 'text'):
            logger.error(f"Response content: {e.response.text}")
        raise
    except Exception as e:
        logger.error(f"Error processing file: {str(e)}")
        raise


def parsed_to_jsontext(result):
    return result.model_dump_json()

def parsed_to_text(result):
    result = ""
    for node in result.nodes:
        if(node.text): result += " " + node.text
    return result

def parsed_to_text_stream(result):
    for node in result.nodes:
        if(node.text): yield node.text
