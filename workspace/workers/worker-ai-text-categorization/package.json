{
	"name": "worker-ai-text-categorization",
	"version": "0.0.1",
	"private": true,
	"scripts": {
		"deploy": "wrangler deploy",
		"dev": "wrangler dev",
		"start": "wrangler dev",
		"test": "vitest run",
		"test:watch": "vitest",
		"test:coverage": "vitest run --coverage",
		"cf-typegen": "wrangler types"
	},
	"devDependencies": {
		"@cloudflare/vitest-pool-workers": "^0.8.18",
		"@cloudflare/workers-types": "^4.20240529.0",
		"typescript": "^5.6.2",
		"vitest": "^3.1.1",
<<<<<<< HEAD
		"wrangler": "^4.16.1"
=======
		"wrangler": "^4.20.0"
>>>>>>> WA-170_MCP
	}
}
