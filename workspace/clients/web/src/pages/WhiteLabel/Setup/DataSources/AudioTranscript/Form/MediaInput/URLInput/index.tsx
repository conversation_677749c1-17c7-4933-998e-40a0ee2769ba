import React, { useEffect, useState, useRef } from "react";
import { InputProps } from "../../../../../../../../util/react/input";
import { MediaDisplay } from "../MediaDisplay";
import {
  AUDIOSOURCE_SUPPORTED_AUDIO_EXTENSIONS,
  AUDIOSOURCE_SUPPORTED_VIDEO_EXTENSIONS,
} from "@divinci-ai/models";
<<<<<<< HEAD
=======
import { validateAndConvertUrl, isDropboxFolder } from "../utils/dropboxUtils";
>>>>>>> WA-170_MCP

export function URLInput({ value, onChange }: InputProps<string>) {
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
<<<<<<< HEAD
=======
  const [wasConverted, setWasConverted] = useState(false);
>>>>>>> WA-170_MCP
  const currentURL = useRef<string>("");

  // Get supported file extensions for display
  const supportedExtensions = [...AUDIOSOURCE_SUPPORTED_AUDIO_EXTENSIONS, ...AUDIOSOURCE_SUPPORTED_VIDEO_EXTENSIONS].join(', ');

  useEffect(() => {
    currentURL.current = value;

    if (!value) {
      setError(null);
<<<<<<< HEAD
=======
      setWasConverted(false);
>>>>>>> WA-170_MCP
      return;
    }

    try {
<<<<<<< HEAD
      if (!URL.canParse(value)) {
        setError("Invalid URL format");
        return;
      }

      const url = new URL(value);
      const extension = url.pathname.split('.').pop()?.toLowerCase();

      if (!extension) {
        setError("URL must point to a file with an extension");
        return;
      }

      const isAudioExtension = AUDIOSOURCE_SUPPORTED_AUDIO_EXTENSIONS.has(extension);
      const isVideoExtension = AUDIOSOURCE_SUPPORTED_VIDEO_EXTENSIONS.has(extension);

      if (!isAudioExtension && !isVideoExtension) {
        setError(`Unsupported file type. Supported formats: ${supportedExtensions}`);
        return;
      }

      setError(null);
    } catch (e: any) {
      setError(e.message);
    }
  }, [value, supportedExtensions]);
=======
      const result = validateAndConvertUrl(
        value,
        new Set(AUDIOSOURCE_SUPPORTED_AUDIO_EXTENSIONS),
        new Set(AUDIOSOURCE_SUPPORTED_VIDEO_EXTENSIONS)
      );

      if (!result.isValid) {
        setError(result.error || "Invalid URL");
        setWasConverted(false);
        return;
      }

      setError(null);
      setWasConverted(result.isDropboxUrl && !!result.wasConverted);

      // If the URL was converted, update the parent with the processed URL
      if (result.processedUrl !== value) {
        onChange(result.processedUrl);
      }
    } catch (e: any) {
      setError(e.message);
      setWasConverted(false);
    }
  }, [value, supportedExtensions, onChange]);
>>>>>>> WA-170_MCP

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);

    if (!newValue) {
      setError(null);
    }
  };

  const handleClearInput = () => {
    onChange("");
    setError(null);
<<<<<<< HEAD
=======
    setWasConverted(false);
>>>>>>> WA-170_MCP
  };

  return (
    <div className="mb-5">
      <h2 className="title is-4">
        <span className="icon-text">
          <span className="icon">
            <i className="fas fa-link"></i>
          </span>
          <span>Audio or Video URL</span>
        </span>
      </h2>

      <div className="field">
        <div className={`control has-icons-left has-icons-right ${isLoading ? 'is-loading' : ''}`}>
          <input
            className={`input is-medium ${error ? 'is-danger' : value ? 'is-success' : ''}`}
            type="text"
<<<<<<< HEAD
            placeholder="Enter URL to audio or video file..."
=======
            placeholder="Enter URL to audio/video file or Dropbox folder (Dropbox URLs supported)..."
>>>>>>> WA-170_MCP
            value={value}
            onChange={handleInputChange}
          />
          <span className="icon is-small is-left">
            <i className="fas fa-globe"></i>
          </span>

          {value && (
            <span className="icon is-small is-right" style={{ pointerEvents: 'all', cursor: 'pointer' }} onClick={handleClearInput}>
              <i className="fas fa-times-circle"></i>
            </span>
          )}
        </div>

        {error ? (
          <p className="help is-danger">{error}</p>
        ) : value ? (
<<<<<<< HEAD
          <p className="help is-success">Valid URL format</p>
        ) : (
          <p className="help">Enter a direct URL to an audio or video file</p>
=======
          <div>
            <p className="help is-success">Valid URL format</p>
            {wasConverted && (
              <p className="help is-info">
                <span className="icon is-small mr-1">
                  <i className="fas fa-exchange-alt"></i>
                </span>
                Dropbox {isDropboxFolder(value) ? 'folder' : 'file'} URL converted to direct download link
              </p>
            )}
          </div>
        ) : (
          <p className="help">Enter a direct URL to an audio or video file, or a Dropbox folder. Dropbox share URLs are automatically converted.</p>
>>>>>>> WA-170_MCP
        )}

        <p className="help has-text-grey mt-2">
          Supported formats: {supportedExtensions}
        </p>
<<<<<<< HEAD
=======

        <p className="help has-text-grey">
          <span className="icon is-small mr-1">
            <i className="fab fa-dropbox"></i>
          </span>
          Dropbox URLs to files and folders are supported and will be automatically converted to direct download links
        </p>
>>>>>>> WA-170_MCP
      </div>

      {value && !error && (
        <div className="box mt-4">
          <h3 className="subtitle is-5 mb-3">Preview from URL</h3>
          <DisplayURL url={value} setIsLoading={setIsLoading} />
        </div>
      )}
    </div>
  );
}

function DisplayURL({ url, setIsLoading }: { url: string, setIsLoading: (loading: boolean) => void }) {
  const [isMediaLoading, setIsMediaLoading] = useState(true);

  useEffect(() => {
    setIsLoading(true);
    setIsMediaLoading(true);
  }, [url, setIsLoading]);

  const handleMediaLoaded = () => {
    setIsMediaLoading(false);
    setIsLoading(false);
  };

  try {
    const urlObj = new URL(url);

    return (
      <>
        {isMediaLoading && (
          <div className="has-text-centered py-4">
            <span className="icon is-large">
              <i className="fas fa-spinner fa-pulse fa-2x"></i>
            </span>
            <p className="mt-2">Loading media from URL...</p>
          </div>
        )}

        <div className={isMediaLoading ? 'is-hidden' : ''}>
          <MediaDisplay
            filename={urlObj.pathname}
            src={url}
            onLoaded={handleMediaLoaded}
          />
        </div>
      </>
    );
  } catch (e) {
    setIsLoading(false);
    return (
      <div className="notification is-warning">
        <p>Unable to parse URL for preview</p>
      </div>
    );
  }
}
