export const PATH_WHITELABEL_INDEX = "/white-label";
export const R2_LIST_PATH = "/r2-list";

export const PATH_WHITELABEL_SEARCH = `${PATH_WHITELABEL_INDEX}/available`;
export const PATH_WHITELABEL_TRENDING = `${PATH_WHITELABEL_INDEX}/trending`;
export const PATH_WHITELABEL_CONTREVERSIAL = `${PATH_WHITELABEL_INDEX}/contreversial`;

export const PATH_WHITELABEL_ITEM = `${PATH_WHITELABEL_INDEX}/:whitelabelId`;
export const PATH_WHITELABEL_TRANSCRIPTID = `${PATH_WHITELABEL_ITEM}/transcript/:transcriptId`;
export const PATH_WHITELABEL_USER_TRANSCRIPTS = `${PATH_WHITELABEL_ITEM}/transcripts`;

export const PATH_WHITELABEL_MANAGE_PERMISSIONS = `${PATH_WHITELABEL_ITEM}/permissions`;
export const PATH_WHITELABEL_MANAGE_PERMISSIONS_INVITATION = `${PATH_WHITELABEL_MANAGE_PERMISSIONS}/invitation`;

export const PATH_WHITELABEL_MANAGE_SETTINGS = `${PATH_WHITELABEL_ITEM}/settings`;
export const PATH_WHITELABEL_MANAGE_NOTIFICATION_SETTINGS = `${PATH_WHITELABEL_ITEM}/notification-settings`;
export const PATH_WHITELABEL_MANAGE_MCP_SETTINGS = `${PATH_WHITELABEL_ITEM}/mcp-settings`;

export const PATH_WHITELABEL_MANAGE_RELEASE = `${PATH_WHITELABEL_ITEM}/release`;
export const PATH_WHITELABEL_MANAGE_VECTOR_LIST = `${PATH_WHITELABEL_ITEM}/vector-list`;

export const PATH_WHITELABEL_MANAGE_TRANSCRIPT = `${PATH_WHITELABEL_ITEM}/transcript`;
export const PATH_WHITELABEL_MANAGE_GRAPH = `${PATH_WHITELABEL_ITEM}/graph`;
export const PATH_WHITELABEL_MANAGE_QATESTING = `${PATH_WHITELABEL_ITEM}/qa-testing`;
export const PATH_WHITELABEL_VOICEPRINTS = `${PATH_WHITELABEL_ITEM}/voiceprints`;

export const PATH_WHITELABEL_THREADS_LIST = `${PATH_WHITELABEL_ITEM}/threads`;
export const PATH_WHITELABEL_THREADS_ITEM = `${PATH_WHITELABEL_ITEM}/threads/:chatId`;
export const PATH_WHITELABEL_THREADS_ITEM_GRAPH = `${PATH_WHITELABEL_ITEM}/threads/:chatId/graph`;

export const PATH_WHITELABEL_NOTIFICATION_SETTINGS = "/notifications-settings";
export const PATH_WHITELABEL_NOTIFICATION_SETTINGS_DEFAULT = `${PATH_WHITELABEL_NOTIFICATION_SETTINGS}-default`;
