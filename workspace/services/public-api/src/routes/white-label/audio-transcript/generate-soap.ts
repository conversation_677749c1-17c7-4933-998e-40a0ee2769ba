import { Request, Response } from 'express';
import { jsonBody } from '../../../util/request';
import { AudioTranscriptDoc, AudioTranscriptStatus } from '@divinci-ai/models';
import { SOAPGenerationService, SOAPGenerationOptions } from '../../../services/SOAPGenerationService';
import { getAudioTranscriptCollection } from '../../../data/audio-transcript';

interface GenerateSOAPRequest {
  options?: {
    confidenceThreshold?: number;
    providerName?: string;
    customPrompts?: {
      systemPrompt?: string;
      subjectivePrompt?: string;
      objectivePrompt?: string;
      assessmentPrompt?: string;
      planPrompt?: string;
    };
    includeTimestamps?: boolean;
    speakerMapping?: Record<string, string>;
  };
}

interface GenerateSOAPResponse {
  success: boolean;
  soapNotes?: any;
  error?: string;
  processingTime?: number;
  warnings?: string[];
}

export async function generateSOAP(req: Request, res: Response): Promise<void> {
  try {
    const { whitelabelId, audioId } = req.params;
    const body = jsonBody<GenerateSOAPRequest>(req);
    
    // Validate required parameters
    if (!whitelabelId || !audioId) {
      res.status(400).json({
        success: false,
        error: 'Missing required parameters: whitelabelId and audioId'
      });
      return;
    }

    // Get audio transcript
    const audioTranscriptCollection = getAudioTranscriptCollection();
    const audioTranscript = await audioTranscriptCollection.findOne({
      _id: audioId,
      whitelabelId: whitelabelId
    });

    if (!audioTranscript) {
      res.status(404).json({
        success: false,
        error: 'Audio transcript not found'
      });
      return;
    }

    // Check if audio transcript is completed
    if (audioTranscript.processStatus !== AudioTranscriptStatus.Completed) {
      res.status(400).json({
        success: false,
        error: 'Audio transcript must be completed before generating SOAP notes'
      });
      return;
    }

    // Check if transcript has samples
    if (!audioTranscript.samples || audioTranscript.samples.length === 0) {
      res.status(400).json({
        success: false,
        error: 'Audio transcript has no samples to process'
      });
      return;
    }

    // Prepare generation options with defaults
    const options: SOAPGenerationOptions = {
      confidenceThreshold: body.options?.confidenceThreshold ?? 70,
      providerName: body.options?.providerName ?? 'Healthcare Provider',
      customPrompts: body.options?.customPrompts,
      includeTimestamps: body.options?.includeTimestamps ?? true,
      speakerMapping: body.options?.speakerMapping ?? {}
    };

    // Generate SOAP notes
    const soapGenerationService = new SOAPGenerationService();
    const soapNotes = await soapGenerationService.generateSOAP(audioTranscript, options);

    // Return successful response
    const response: GenerateSOAPResponse = {
      success: true,
      soapNotes,
      processingTime: soapNotes.metadata.processingTime,
      warnings: soapNotes.warnings
    };

    res.status(200).json(response);

  } catch (error) {
    console.error('SOAP generation error:', error);
    
    const response: GenerateSOAPResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred during SOAP generation'
    };

    res.status(500).json(response);
  }
}

// Endpoint for regenerating a specific SOAP section
export async function regenerateSOAPSection(req: Request, res: Response): Promise<void> {
  try {
    const { whitelabelId, audioId } = req.params;
    const body = jsonBody<{
      sectionName: 'subjective' | 'objective' | 'assessment' | 'plan';
      options?: SOAPGenerationOptions;
    }>(req);
    
    if (!whitelabelId || !audioId || !body.sectionName) {
      res.status(400).json({
        success: false,
        error: 'Missing required parameters'
      });
      return;
    }

    // Get audio transcript
    const audioTranscriptCollection = getAudioTranscriptCollection();
    const audioTranscript = await audioTranscriptCollection.findOne({
      _id: audioId,
      whitelabelId: whitelabelId
    });

    if (!audioTranscript) {
      res.status(404).json({
        success: false,
        error: 'Audio transcript not found'
      });
      return;
    }

    // Prepare transcript text
    const soapGenerationService = new SOAPGenerationService();
    const transcriptText = (soapGenerationService as any).prepareTranscriptText(
      audioTranscript, 
      body.options || { includeTimestamps: true, confidenceThreshold: 70 }
    );

    // Regenerate specific section
    const regeneratedSection = await soapGenerationService.regenerateSection(
      transcriptText,
      body.sectionName,
      body.options || { includeTimestamps: true, confidenceThreshold: 70 }
    );

    res.status(200).json({
      success: true,
      section: regeneratedSection,
      sectionName: body.sectionName
    });

  } catch (error) {
    console.error('SOAP section regeneration error:', error);
    
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred during section regeneration'
    });
  }
}

// Endpoint for getting default prompts (for user customization)
export async function getDefaultPrompts(req: Request, res: Response): Promise<void> {
  try {
    const soapGenerationService = new SOAPGenerationService();
    const defaultPrompts = (soapGenerationService as any).defaultPrompts;

    res.status(200).json({
      success: true,
      prompts: defaultPrompts
    });

  } catch (error) {
    console.error('Error getting default prompts:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve default prompts'
    });
  }
}
