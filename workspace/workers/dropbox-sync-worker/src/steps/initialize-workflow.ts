/**
 * Initialize Dropbox Sync Workflow
 */

// WorkflowStep type (would be imported from cloudflare:workers in real deployment)
type WorkflowStep = any;
import {
  StepContext,
  InitializeWorkflowResult,
  DropboxSyncFile,
  DropboxSyncProcessingConfig,
  WorkflowMetadata,
  DropboxSyncJobStatus,
  DropboxFileProcessingStatus
} from "./types";

export async function initializeWorkflow(
  step: WorkflowStep,
  context: StepContext,
  sourceFiles: DropboxSyncFile[],
  processingConfig: DropboxSyncProcessingConfig,
  targetType: string,
  instanceId: string
): Promise<InitializeWorkflowResult> {
  return await step.do("initializeWorkflow", async () => {
    console.log("🚀 Initializing Dropbox sync workflow", {
      jobId: context.jobId,
      whitelabelId: context.whitelabelId,
      totalFiles: sourceFiles.length,
      targetType
    });

    // Validate source files
    const validatedFiles: DropboxSyncFile[] = sourceFiles.map(file => ({
      ...file,
      status: DropboxFileProcessingStatus.PENDING,
      startedAt: new Date()
    }));

    // Calculate total bytes
    const totalBytes = validatedFiles.reduce((sum, file) => sum + file.fileSize, 0);

    // Create workflow metadata
    const metadata: WorkflowMetadata = {
      workflowId: instanceId,
      jobId: context.jobId,
      targetType: targetType as any,
      totalFiles: validatedFiles.length,
      steps: {
        downloadFiles: {
          startTime: new Date().toISOString(),
          filesDownloaded: 0,
          totalBytes
        },
        processAudio: {
          filesProcessed: 0,
          transcriptionResults: [],
          diarizationResults: []
        },
        triggerChunksWorkflow: {
          workflowIds: []
        },
        updateDatabase: {
          updatedRecords: 0
        }
      },
      status: DropboxSyncJobStatus.RUNNING,
      startTime: new Date().toISOString()
    };

    // Validate processing configuration
    if (targetType === "rag" && !processingConfig.ragId) {
      throw new Error("RAG ID is required for RAG target type");
    }

    if (targetType === "fine-tuning" && !processingConfig.fineTuningDatasetId) {
      throw new Error("Fine-tuning dataset ID is required for fine-tuning target type");
    }

    // Set default audio processing tools if not specified
    const normalizedConfig: DropboxSyncProcessingConfig = {
      transcriptionTool: "whisper-large-v3",
      diarizationTool: "pyannote",
      chunkingTool: "unstructured",
      skipExistingFiles: false,
      maxFileSize: 2 * 1024 * 1024 * 1024, // 2GB default
      ...processingConfig
    };

    console.log("✅ Workflow initialized successfully", {
      totalFiles: validatedFiles.length,
      totalBytes,
      processingConfig: normalizedConfig
    });

    return {
      metadata,
      validatedFiles,
      processingConfig: normalizedConfig
    };
  });
}
