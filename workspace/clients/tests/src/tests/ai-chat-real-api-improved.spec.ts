/**
 * Improved Real AI Chat API Tests
 *
 * This file demonstrates the improved real API testing approach with:
 * - Automatic resource cleanup
 * - Real Auth0 authentication
 * - Proper error handling
 * - Resource pool integration
 */

import { test, expect, realApiExpect, createRealApiTestHelper } from '../utils/real-api-test-helper';

test.describe('AI Chat Real API - Improved', () => {
  test.describe('Chat Management with Cleanup', () => {
    test('should create and manage chats with automatic cleanup', async ({ adminClient, cleanupManager }) => {
      const helper = createRealApiTestHelper(adminClient, cleanupManager);

      // Create a chat using the helper (automatically tracked for cleanup)
      const response = await helper.createChat({
        title: 'Test Chat - Improved',
        releases: []
      });

      // Use improved assertions
      realApiExpect.chatResponse(response, 'Test Chat - Improved');

      const chatId = response.chat._id;

      // Test chat retrieval
      const chatDetail = await helper.getChat(chatId);
      realApiExpect.chatDetailResponse(chatDetail, chatId);

      // Test chat listing
      const chatList = await helper.listChats();
      realApiExpect.chatListResponse(chatList);

      // Verify our chat is in the list
      const foundChat = chatList.find(chat => chat._id === chatId);
      expect(foundChat).toBeDefined();
      expect(foundChat.title).toBe('Test Chat - Improved');

      // Test chat update (rename)
      const updatedChat = await helper.updateChat(chatId, {
        title: 'Updated Test Chat - Improved'
      });
      expect(updatedChat.title).toBe('Updated Test Chat - Improved');

      // Cleanup stats should show tracked resources
      const stats = helper.getCleanupStats();
      expect(stats.total).toBeGreaterThan(0);
      expect(stats.byType.chat).toBeGreaterThan(0);

      // Note: Cleanup happens automatically in the fixture teardown
    });

    test('should handle multiple chats with proper cleanup', async ({ adminClient, cleanupManager }) => {
      const helper = createRealApiTestHelper(adminClient, cleanupManager);

      // Create multiple chats
      const chat1 = await helper.createChat({
        title: 'Chat 1 - Cleanup Test',
        releases: []
      });

      const chat2 = await helper.createChat({
        title: 'Chat 2 - Cleanup Test',
        releases: []
      });

      const chat3 = await helper.createChat({
        title: 'Chat 3 - Cleanup Test',
        releases: []
      });

      // Verify all chats were created
      realApiExpect.chatResponse(chat1, 'Chat 1 - Cleanup Test');
      realApiExpect.chatResponse(chat2, 'Chat 2 - Cleanup Test');
      realApiExpect.chatResponse(chat3, 'Chat 3 - Cleanup Test');

      // Check cleanup stats
      const stats = helper.getCleanupStats();
      expect(stats.total).toBe(3);
      expect(stats.byType.chat).toBe(3);

      // Manually delete one chat to test cleanup tracking
      await helper.deleteChat(chat2.chat._id);

      // Stats should reflect the manual deletion
      const updatedStats = helper.getCleanupStats();
      expect(updatedStats.total).toBe(2); // chat2 removed from tracking
      expect(updatedStats.byType.chat).toBe(2);

      // Remaining chats will be cleaned up automatically
    });
  });

  test.describe('Error Conditions with Real API', () => {
    test('should handle validation errors correctly', async ({ adminClient, cleanupManager }) => {
      const helper = createRealApiTestHelper(adminClient, cleanupManager);

      // Test missing title
      try {
        await adminClient.post('ai-chat', {
          releases: []
          // Missing title
        });
        expect(true).toBe(false); // Should not reach here
      } catch (error) {
        realApiExpect.errorResponse(error, 400);
        expect(error.body.context).toContain('title');
      }

      // Test missing releases
      try {
        await adminClient.post('ai-chat', {
          title: 'Test Chat'
          // Missing releases
        });
        expect(true).toBe(false); // Should not reach here
      } catch (error) {
        realApiExpect.errorResponse(error, 400);
        expect(error.body.context).toContain('releases');
      }
    });

    test('should handle non-existent resource errors', async ({ adminClient, cleanupManager }) => {
      const helper = createRealApiTestHelper(adminClient, cleanupManager);

      // Test non-existent chat ID (but valid ObjectId format)
      try {
        await helper.getChat('507f1f77bcf86cd799439011'); // Valid ObjectId format
        expect(true).toBe(false); // Should not reach here
      } catch (error) {
        realApiExpect.errorResponse(error, 404);
      }

      // Test invalid ObjectId format
      try {
        await helper.getChat('invalid-id');
        expect(true).toBe(false); // Should not reach here
      } catch (error) {
        realApiExpect.errorResponse(error, 400);
        expect(error.body.context).toContain('ObjectId');
      }
    });
  });

  test.describe('Authentication Testing', () => {
    test('should work with admin authentication', async ({ adminClient, cleanupManager }) => {
      const helper = createRealApiTestHelper(adminClient, cleanupManager);

      const response = await helper.createChat({
        title: 'Admin Auth Test',
        releases: []
      });

      realApiExpect.chatResponse(response, 'Admin Auth Test');

      // Verify the owner is set correctly (should be the service account)
      expect(response.chat.ownerUser).toContain('@clients');
    });

    test('should work with user authentication', async ({ userClient, cleanupManager }) => {
      const helper = createRealApiTestHelper(userClient, cleanupManager);

      const response = await helper.createChat({
        title: 'User Auth Test',
        releases: []
      });

      realApiExpect.chatResponse(response, 'User Auth Test');

      // User authentication might fall back to service token
      expect(response.chat.ownerUser).toBeDefined();
    });

    test('should work with owner authentication', async ({ ownerClient, cleanupManager }) => {
      const helper = createRealApiTestHelper(ownerClient, cleanupManager);

      const response = await helper.createChat({
        title: 'Owner Auth Test',
        releases: []
      });

      realApiExpect.chatResponse(response, 'Owner Auth Test');

      // Verify the owner is set correctly
      expect(response.chat.ownerUser).toContain('@clients');
    });
  });

  test.describe('Edge Cases with Real API', () => {
    test('should handle special characters in titles', async ({ adminClient, cleanupManager }) => {
      const helper = createRealApiTestHelper(adminClient, cleanupManager);

      const specialTitle = '!@#$%^&*()_+{}[]|\\:;"\'<>,.?/~`';

      const response = await helper.createChat({
        title: specialTitle,
        releases: []
      });

      realApiExpect.chatResponse(response, specialTitle);
    });

    test('should handle emoji in titles', async ({ adminClient, cleanupManager }) => {
      const helper = createRealApiTestHelper(adminClient, cleanupManager);

      const emojiTitle = '😀 Chat with emojis 🚀';

      const response = await helper.createChat({
        title: emojiTitle,
        releases: []
      });

      realApiExpect.chatResponse(response, emojiTitle);
    });

    test('should handle long titles appropriately', async ({ adminClient, cleanupManager }) => {
      const helper = createRealApiTestHelper(adminClient, cleanupManager);

      const longTitle = 'Very long title '.repeat(50); // 800 characters

      try {
        const response = await helper.createChat({
          title: longTitle,
          releases: []
        });

        // If accepted, verify it's handled properly
        realApiExpect.chatResponse(response);
        expect(response.chat.title.length).toBeGreaterThan(0);
      } catch (error) {
        // If rejected, verify it's a proper validation error
        realApiExpect.errorResponse(error, 400);
      }
    });
  });

  test.describe('Performance and Reliability', () => {
    test('should handle concurrent chat creation', async ({ adminClient, cleanupManager }) => {
      const helper = createRealApiTestHelper(adminClient, cleanupManager);

      // Create multiple chats concurrently
      const chatPromises = Array.from({ length: 5 }, (_, i) =>
        helper.createChat({
          title: `Concurrent Chat ${i + 1}`,
          releases: []
        })
      );

      const responses = await Promise.all(chatPromises);

      // Verify all chats were created successfully
      responses.forEach((response, i) => {
        realApiExpect.chatResponse(response, `Concurrent Chat ${i + 1}`);
      });

      // Verify cleanup tracking
      const stats = helper.getCleanupStats();
      expect(stats.total).toBe(5);
      expect(stats.byType.chat).toBe(5);
    });

    test('should handle API timeouts gracefully', async ({ adminClient, cleanupManager }) => {
      const helper = createRealApiTestHelper(adminClient, cleanupManager);

      // This test verifies that the API client handles timeouts properly
      // In a real scenario, this might involve creating a chat with a very large payload
      // or during high server load

      const response = await helper.createChat({
        title: 'Timeout Test Chat',
        releases: []
      });

      realApiExpect.chatResponse(response, 'Timeout Test Chat');
    });
  });
});
