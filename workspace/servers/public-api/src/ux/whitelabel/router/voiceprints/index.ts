import { Router } from "express";
import { listVoiceprints, listVoiceprintsLive } from "./list";
import {
  getJobStatistics,
  getAllJobs,
  getStuckJobs,
  recoverStuckJobs,
  retryJob,
  getJobDetails,
  getSystemHealth,
  getRecoveryHealth,
  checkPyannoteStatus,
  checkSpecificJob
} from "./management";
import { uploadVoiceprintAudio } from "./upload-audio";
import { createVoiceprint } from "./create";
import { deleteVoiceprint } from "./delete";
import { refreshVoiceprintJob } from "./refresh";

export const router = Router({ mergeParams: true });

// GET /api/whitelabel/:whitelabelId/voiceprints
router.get("/", listVoiceprints);

// GET /api/whitelabel/:whitelabelId/voiceprints/live (from Pyannote API)
router.get("/live", listVoiceprintsLive);

// POST /api/whitelabel/:whitelabelId/voiceprints
router.post("/", createVoiceprint);

// POST /api/whitelabel/:whitelabelId/voiceprints/upload-audio
router.post("/upload-audio", uploadVoiceprintAudio);

// DELETE /api/whitelabel/:whitelabelId/voiceprints/:voiceprintId
router.delete("/:voiceprintId", deleteVoiceprint);

// POST /api/whitelabel/:whitelabelId/voiceprints/:voiceprintId/refresh
router.post("/:voiceprintId/refresh", refreshVoiceprintJob);

// Management endpoints
router.get("/stats", getJobStatistics);
router.get("/jobs", getAllJobs);
router.get("/stuck", getStuckJobs);
router.post("/recover", recoverStuckJobs);
router.post("/retry/:jobId", retryJob);
router.get("/job/:jobId", getJobDetails);
router.get("/health", getSystemHealth);
router.get("/recovery-health", getRecoveryHealth);
router.get("/pyannote-status", checkPyannoteStatus);
router.get("/check-job/:jobId", checkSpecificJob);
