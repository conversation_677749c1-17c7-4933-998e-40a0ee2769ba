/**
 * Process audio files (transcription and diarization)
 */

// WorkflowStep type (would be imported from cloudflare:workers in real deployment)
type WorkflowStep = any;
import { 
  StepContext, 
  ProcessAudioResult,
  DropboxSyncProcessingConfig,
  FileDownloadResult,
  AudioProcessingResult,
  DropboxFileProcessingStatus
} from "./types";

export async function processAudio(
  step: WorkflowStep,
  context: StepContext,
  downloadedFiles: (FileDownloadResult & { 
    dropboxFileId: string;
    originalFile: any;
  })[],
  processingConfig: DropboxSyncProcessingConfig
): Promise<ProcessAudioResult> {
  return await step.do("processAudio", async () => {
    console.log("🎵 Starting audio processing", {
      totalFiles: downloadedFiles.length,
      transcriptionTool: processingConfig.transcriptionTool,
      diarizationTool: processingConfig.diarizationTool
    });

    const processedFiles: (AudioProcessingResult & {
      dropboxFileId: string;
      fileName: string;
      downloadResult: FileDownloadResult;
    })[] = [];
    
    const failedProcessing: {
      dropboxFileId: string;
      fileName: string;
      error: string;
    }[] = [];

    // Filter to only audio/video files
    const audioFiles = downloadedFiles.filter(file => 
      file.originalFile.isAudio || file.originalFile.isVideo
    );

    console.log(`🎵 Processing ${audioFiles.length} audio/video files`);

    // Process files sequentially to avoid overwhelming audio workers
    for (const file of audioFiles) {
      try {
        console.log(`🎵 Processing audio: ${file.fileName}`);

        // Update status to transcribing
        await updateFileStatus(context, file.dropboxFileId, DropboxFileProcessingStatus.TRANSCRIBING);

        const processingStartTime = Date.now();
        
        // Process transcription and diarization in parallel
        const [transcriptionResult, diarizationResult] = await Promise.allSettled([
          processTranscription(context, file, processingConfig),
          processDiarization(context, file, processingConfig)
        ]);

        const processingDuration = (Date.now() - processingStartTime) / 1000;

        // Collect results
        const audioResult: AudioProcessingResult = {
          processingDuration,
          transcriptionId: transcriptionResult.status === "fulfilled" ? transcriptionResult.value.transcriptionId : undefined,
          transcriptionText: transcriptionResult.status === "fulfilled" ? transcriptionResult.value.text : undefined,
          diarizationId: diarizationResult.status === "fulfilled" ? diarizationResult.value.diarizationId : undefined,
          speakerSegments: diarizationResult.status === "fulfilled" ? diarizationResult.value.segments : undefined
        };

        // Check if at least one processing succeeded
        if (!audioResult.transcriptionId && !audioResult.diarizationId) {
          throw new Error("Both transcription and diarization failed");
        }

        processedFiles.push({
          ...audioResult,
          dropboxFileId: file.dropboxFileId,
          fileName: file.fileName,
          downloadResult: file
        });

        console.log(`✅ Successfully processed audio: ${file.fileName}`, {
          transcriptionId: audioResult.transcriptionId,
          diarizationId: audioResult.diarizationId,
          processingDuration
        });

      } catch (error) {
        console.error(`❌ Failed to process audio: ${file.fileName}`, error);
        
        failedProcessing.push({
          dropboxFileId: file.dropboxFileId,
          fileName: file.fileName,
          error: error instanceof Error ? error.message : String(error)
        });

        // Update file status to failed
        await updateFileStatus(
          context, 
          file.dropboxFileId, 
          DropboxFileProcessingStatus.FAILED,
          error instanceof Error ? error.message : String(error)
        );
      }
    }

    console.log("🎵 Audio processing completed", {
      successful: processedFiles.length,
      failed: failedProcessing.length
    });

    return {
      processedFiles,
      failedProcessing
    };
  });
}

/**
 * Process audio transcription using Whisper
 */
async function processTranscription(
  context: StepContext,
  file: FileDownloadResult & { dropboxFileId: string },
  config: DropboxSyncProcessingConfig
): Promise<{ transcriptionId: string; text: string; metadata: any }> {
  console.log(`🎤 Starting transcription for: ${file.fileName}`);

  const response = await fetch(`${context.env.AUDIO_TRANSCRIPT_WORKER_URL}/transcribe`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${context.auth0Token}`,
      "cloudflare-worker-x-dev-auth": context.env.CLOUDFLARE_WORKER_X_AUTH_DEV || ""
    },
    body: JSON.stringify({
      audioUrl: file.r2Url,
      fileName: file.fileName,
      options: {
        model: config.transcriptionTool || "whisper-large-v3",
        language: "auto",
        task: "transcribe"
      },
      metadata: {
        dropboxFileId: file.dropboxFileId,
        jobId: context.jobId,
        whitelabelId: context.whitelabelId
      }
    })
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Transcription failed: ${response.status} ${errorText}`);
  }

  const result = await response.json();
  
  if (!result.success) {
    throw new Error(`Transcription failed: ${result.error || "Unknown error"}`);
  }

  console.log(`✅ Transcription completed for: ${file.fileName}`, {
    transcriptionId: result.data.transcriptionId,
    textLength: result.data.text?.length || 0
  });

  return {
    transcriptionId: result.data.transcriptionId,
    text: result.data.text,
    metadata: result.data.metadata || {}
  };
}

/**
 * Process speaker diarization using Pyannote
 */
async function processDiarization(
  context: StepContext,
  file: FileDownloadResult & { dropboxFileId: string },
  config: DropboxSyncProcessingConfig
): Promise<{ diarizationId: string; segments: any[]; metadata: any }> {
  console.log(`👥 Starting diarization for: ${file.fileName}`);

  const response = await fetch(`${context.env.AUDIO_DIARIZATION_WORKER_URL}/diarize`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${context.auth0Token}`,
      "cloudflare-worker-x-dev-auth": context.env.CLOUDFLARE_WORKER_X_AUTH_DEV || ""
    },
    body: JSON.stringify({
      audioUrl: file.r2Url,
      fileName: file.fileName,
      options: {
        model: config.diarizationTool || "pyannote",
        minSpeakers: 1,
        maxSpeakers: 10
      },
      metadata: {
        dropboxFileId: file.dropboxFileId,
        jobId: context.jobId,
        whitelabelId: context.whitelabelId
      }
    })
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Diarization failed: ${response.status} ${errorText}`);
  }

  const result = await response.json();
  
  if (!result.success) {
    throw new Error(`Diarization failed: ${result.error || "Unknown error"}`);
  }

  console.log(`✅ Diarization completed for: ${file.fileName}`, {
    diarizationId: result.data.diarizationId,
    segmentCount: result.data.segments?.length || 0
  });

  return {
    diarizationId: result.data.diarizationId,
    segments: result.data.segments || [],
    metadata: result.data.metadata || {}
  };
}

/**
 * Update file processing status via API
 */
async function updateFileStatus(
  context: StepContext,
  dropboxFileId: string,
  status: DropboxFileProcessingStatus,
  errorMessage?: string
): Promise<void> {
  try {
    const response = await fetch(`${context.env.API_HOST}/ux/whitelabel/${context.whitelabelId}/dropbox/sync/update-file-status`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${context.auth0Token}`,
        "cloudflare-worker-x-dev-auth": context.env.CLOUDFLARE_WORKER_X_AUTH_DEV || ""
      },
      body: JSON.stringify({
        jobId: context.jobId,
        dropboxFileId,
        status,
        errorMessage,
        timestamp: new Date().toISOString()
      })
    });

    if (!response.ok) {
      console.warn(`Failed to update file status: ${response.status}`);
    }
  } catch (error) {
    console.warn("Failed to update file status:", error);
    // Don't throw - this is a non-critical operation
  }
}
