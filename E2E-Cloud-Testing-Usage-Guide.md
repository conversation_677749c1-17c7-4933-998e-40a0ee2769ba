# E2E Cloud Testing Usage Guide

This guide explains how to use the enhanced E2E testing system to run tests against remote cloud environments using `act` and GitHub Actions.

## Quick Start

### 1. Test Against Development Environment
```bash
# Run smoke tests against development
./scripts/run-e2e-against-cloud.sh develop smoke

# Run full test suite against development
./scripts/run-e2e-against-cloud.sh develop full
```

### 2. Simulate Deployment with E2E Testing using Act
```bash
# Test deployment workflow with E2E tests
cd .github/act
./test-cloud-e2e.sh develop deploy-e2e

# Test only E2E against existing staging deployment
./test-cloud-e2e.sh staging e2e-only
```

### 3. Run Tests with Playwright Cloud Config
```bash
cd workspace/clients/tests

# Run against staging with cloud configuration
TARGET_ENVIRONMENT=staging npx playwright test --config=playwright.cloud.config.ts

# Run specific test suite
TARGET_ENVIRONMENT=staging npx playwright test --config=playwright.cloud.config.ts --project=smoke-tests
```

## Environment Setup

### Required Environment Variables

Create environment-specific files in `private-keys/[environment]/test.env`:

```bash
# private-keys/develop/test.env
TARGET_ENVIRONMENT=develop
TEST_ENV=develop
AUTH0_BASE_URL=https://dev-divinci.us.auth0.com
AUTH0_CLIENT_ID=your_dev_client_id
AUTH0_AUDIENCE=https://api.dev.divinci.app
CF_ACCESS_CLIENT_ID=your_dev_cf_client_id
CF_ACCESS_CLIENT_SECRET=your_dev_cf_secret
AUTH0_TEST_USER_EMAIL=<EMAIL>
AUTH0_TEST_USER_PASSWORD=test_password
```

### Cloudflare Access Configuration

For environments protected by Cloudflare Access, ensure you have:

1. **Service Token**: Create in Cloudflare Dashboard > Access > Service Auth > Service Tokens
2. **Client ID and Secret**: Add to environment variables
3. **Proper Policies**: Ensure the service token has access to your API endpoints

## Test Types and Projects

### Available Test Projects

1. **smoke-tests**: Quick validation of core functionality
   ```bash
   npx playwright test --project=smoke-tests
   ```

2. **integration-tests**: Full workflow testing
   ```bash
   npx playwright test --project=integration-tests
   ```

3. **performance-tests**: Load and response time validation
   ```bash
   npx playwright test --project=performance-tests
   ```

4. **security-tests**: Authentication and authorization validation
   ```bash
   npx playwright test --project=security-tests
   ```

### Test Tags

Use test tags to run specific categories:

```bash
# Run all smoke tests
npx playwright test --grep="@smoke"

# Run tests for specific environment
npx playwright test --grep="@staging"

# Run Auth0-related tests
npx playwright test --grep="@auth0"

# Run Cloudflare-related tests
npx playwright test --grep="@cloudflare"
```

## Act Workflow Testing

### Available Act Commands

#### 1. Deploy with E2E Testing
```bash
# Deploy to develop and run E2E tests
.github/act/test-cloud-e2e.sh develop deploy-e2e

# Deploy to staging and run E2E tests
.github/act/test-cloud-e2e.sh staging deploy-e2e

# Deploy specific services and test
.github/act/test-cloud-e2e.sh develop deploy-e2e "workspace/servers/public-api"
```

#### 2. E2E Only Testing
```bash
# Run E2E tests against existing deployment
.github/act/test-cloud-e2e.sh staging e2e-only

# Run specific test type
.github/act/test-cloud-e2e.sh develop e2e-only smoke
```

#### 3. Custom Event Testing
```bash
# Use custom event file
act issue_comment \
  -e .github/act/events/deploy-staging-with-e2e.json \
  --secret-file .github/act/secrets.env \
  --container-architecture linux/amd64
```

### Act Event Files

The following event files are available:

- `deploy-develop-with-e2e.json`: Deploy to develop with E2E testing
- `deploy-staging-with-e2e.json`: Deploy to staging with E2E testing
- `e2e-only-staging.json`: Run E2E tests against existing staging deployment

## Service Health Checks

Before running tests, the system automatically checks service health:

```bash
# Manual health check
./scripts/wait-for-services.sh staging

# Health check with custom timeout
MAX_WAIT=600 ./scripts/wait-for-services.sh production
```

### Health Check Endpoints

The system checks these endpoints:

- **API Health**: `https://api.[env].divinci.app/health`
- **Web Accessibility**: `https://chat.[env].divinci.app`
- **API Documentation**: `https://api.[env].divinci.app/docs` (dev/staging only)

## Configuration

### Cloud Configuration

The `cloud-config.ts` file provides environment-specific settings:

```typescript
// Get configuration for current environment
const config = getCloudTestConfig();

// Get service endpoints
const endpoints = getServiceEndpoints(config);

// Check if running in cloud
const isCloud = isCloudEnvironment();
```

### Playwright Configuration

Use `playwright.cloud.config.ts` for cloud testing:

```typescript
// Environment-specific timeouts
timeout: cloudConfig.timeouts.test,
expect: { timeout: cloudConfig.timeouts.expect },

// Retry configuration
retries: cloudConfig.retries.test,

// Headers for Cloudflare Access
extraHTTPHeaders: cloudConfig.headers,
```

## Debugging

### Enable Debug Mode

```bash
# Enable debug mode for act
DEBUG=true .github/act/test-cloud-e2e.sh develop deploy-e2e

# Enable Playwright debug
DEBUG=pw:* npx playwright test --config=playwright.cloud.config.ts
```

### Common Issues and Solutions

#### 1. Cloudflare Access 403 Errors
```bash
# Check CF credentials
echo $CF_ACCESS_CLIENT_ID
echo $CF_ACCESS_CLIENT_SECRET

# Test CF access manually
curl -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" \
     -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" \
     https://api.dev.divinci.app/health
```

#### 2. Service Not Ready
```bash
# Check service status
./scripts/wait-for-services.sh develop

# Check deployment status
gcloud run services list --region=us-central1
```

#### 3. Authentication Issues
```bash
# Verify Auth0 configuration
echo $AUTH0_BASE_URL
echo $AUTH0_CLIENT_ID
echo $AUTH0_AUDIENCE

# Test Auth0 connection
curl -X POST "$AUTH0_BASE_URL/oauth/token" \
  -H "Content-Type: application/json" \
  -d '{"client_id":"'$AUTH0_CLIENT_ID'","audience":"'$AUTH0_AUDIENCE'","grant_type":"client_credentials"}'
```

## CI/CD Integration

### GitHub Actions Integration

The enhanced workflow automatically:

1. **Deploys services** based on changed folders
2. **Waits for deployment** to complete
3. **Runs health checks** to ensure services are ready
4. **Executes E2E tests** against the deployed environment
5. **Reports results** and artifacts
6. **Rolls back on failure** (staging only)

### Comment Triggers

Use these comment patterns in PRs:

```bash
# Deploy and test all services
@github-actions [deploy:develop:e2e]

# Deploy and test specific services
@github-actions [deploy:staging:e2e:services=workspace/servers/public-api]

# Run E2E tests only (no deployment)
@github-actions [e2e:staging]

# Fast deploy with limited testing
@github-actions [deploy:develop:e2e:fast]
```

## Best Practices

### 1. Test Organization

- **Smoke tests**: Quick validation, run on every deployment
- **Integration tests**: Full workflows, run on staging
- **Performance tests**: Load testing, run periodically
- **Security tests**: Auth/authz validation, run on security changes

### 2. Environment Strategy

- **Develop**: Frequent testing, debug features enabled
- **Staging**: Production-like testing, comprehensive test suite
- **Production**: Read-only testing, minimal smoke tests

### 3. Test Data Management

- Use **test-specific data** that doesn't interfere with real users
- **Clean up test data** after test runs (staging only)
- **Isolate test environments** to prevent cross-contamination

### 4. Monitoring and Alerting

- **Monitor test results** and set up alerts for failures
- **Track performance metrics** across environments
- **Archive test artifacts** for debugging and compliance

## Troubleshooting

### Test Failures

1. **Check service health** first
2. **Verify environment variables** are set correctly
3. **Check Cloudflare Access** configuration
4. **Review test logs** and screenshots
5. **Validate deployment status** in GCP

### Performance Issues

1. **Increase timeouts** for slower environments
2. **Reduce parallel workers** if resources are limited
3. **Check network connectivity** between test runner and services
4. **Monitor resource usage** during test execution

### Act Issues

1. **Ensure act is up to date**: `act --version`
2. **Check Docker resources**: Ensure sufficient memory/disk
3. **Verify event files**: Ensure JSON is valid
4. **Check secrets**: Ensure all required secrets are present

## Examples

### Complete Workflow Example

```bash
# 1. Deploy to staging and run comprehensive tests
.github/act/test-cloud-e2e.sh staging deploy-e2e

# 2. If staging tests pass, run production smoke tests
TARGET_ENVIRONMENT=production npx playwright test --config=playwright.cloud.config.ts --project=smoke-tests

# 3. Archive results
tar -czf test-results-$(date +%Y%m%d-%H%M%S).tar.gz test-results-cloud/
```

### Custom Test Suite Example

```bash
# Run custom test pattern against specific environment
TARGET_ENVIRONMENT=staging \
CF_ACCESS_CLIENT_ID=your_id \
CF_ACCESS_CLIENT_SECRET=your_secret \
npx playwright test --config=playwright.cloud.config.ts --grep="audio.*upload"
```

This enhanced E2E testing system provides comprehensive validation of your cloud deployments while maintaining the flexibility to test different scenarios and environments.
