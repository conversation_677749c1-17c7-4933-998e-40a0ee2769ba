/**
 * Record Tracker
 * 
 * This utility tracks all database records created during tests and ensures they are properly cleaned up.
 * It provides a way to register records when they are created and verify that they are deleted at the end of tests.
 */

import { UserRole } from '../auth/auth-utils';

/**
 * Interface for a tracked record
 */
export interface TrackedRecord {
  /** The type of the record (e.g., 'user-group', 'whitelabel', 'vector') */
  type: string;
  /** The ID of the record */
  id: string;
  /** The role used to create the record */
  role: UserRole;
  /** The timestamp when the record was created */
  createdAt: number;
  /** Whether the record has been deleted */
  deleted: boolean;
  /** The timestamp when the record was deleted (if applicable) */
  deletedAt?: number;
  /** Additional metadata about the record */
  metadata?: Record<string, any>;
}

/**
 * Record tracker singleton
 */
class RecordTracker {
  /** Map of tracked records by ID */
  private records: Map<string, TrackedRecord> = new Map();
  /** Whether debug logging is enabled */
  private debug: boolean = false;

  /**
   * Enable or disable debug logging
   * @param debug Whether to enable debug logging
   */
  setDebug(debug: boolean): void {
    this.debug = debug;
  }

  /**
   * Track a new record
   * @param type The type of the record
   * @param id The ID of the record
   * @param role The role used to create the record
   * @param metadata Additional metadata about the record
   * @returns The tracked record
   */
  trackRecord(type: string, id: string, role: UserRole, metadata?: Record<string, any>): TrackedRecord {
    const record: TrackedRecord = {
      type,
      id,
      role,
      createdAt: Date.now(),
      deleted: false,
      metadata
    };

    this.records.set(id, record);
    this.log(`📝 Tracked new ${type} record: ${id}`);

    return record;
  }

  /**
   * Mark a record as deleted
   * @param id The ID of the record to mark as deleted
   * @returns Whether the record was found and marked as deleted
   */
  markDeleted(id: string): boolean {
    const record = this.records.get(id);
    if (record) {
      record.deleted = true;
      record.deletedAt = Date.now();
      this.log(`🗑️ Marked ${record.type} record as deleted: ${id}`);
      return true;
    }

    this.log(`⚠️ Attempted to mark non-existent record as deleted: ${id}`);
    return false;
  }

  /**
   * Get all tracked records
   * @returns An array of all tracked records
   */
  getAllRecords(): TrackedRecord[] {
    return Array.from(this.records.values());
  }

  /**
   * Get all undeleted records
   * @returns An array of all undeleted records
   */
  getUndeletedRecords(): TrackedRecord[] {
    return this.getAllRecords().filter(record => !record.deleted);
  }

  /**
   * Get all records of a specific type
   * @param type The type of records to get
   * @returns An array of records of the specified type
   */
  getRecordsByType(type: string): TrackedRecord[] {
    return this.getAllRecords().filter(record => record.type === type);
  }

  /**
   * Get all undeleted records of a specific type
   * @param type The type of records to get
   * @returns An array of undeleted records of the specified type
   */
  getUndeletedRecordsByType(type: string): TrackedRecord[] {
    return this.getUndeletedRecords().filter(record => record.type === type);
  }

  /**
   * Clear all tracked records
   */
  clearRecords(): void {
    this.records.clear();
    this.log('🧹 Cleared all tracked records');
  }

  /**
   * Get a summary of tracked records
   * @returns A summary of tracked records by type
   */
  getSummary(): Record<string, { total: number, deleted: number, undeleted: number }> {
    const summary: Record<string, { total: number, deleted: number, undeleted: number }> = {};
    
    for (const record of this.records.values()) {
      if (!summary[record.type]) {
        summary[record.type] = { total: 0, deleted: 0, undeleted: 0 };
      }
      
      summary[record.type].total++;
      if (record.deleted) {
        summary[record.type].deleted++;
      } else {
        summary[record.type].undeleted++;
      }
    }
    
    return summary;
  }

  /**
   * Log a message if debug is enabled
   * @param message The message to log
   */
  private log(message: string): void {
    if (this.debug) {
      console.log(message);
    }
  }
}

// Export a singleton instance of the record tracker
export const recordTracker = new RecordTracker();
