#!/bin/bash
# Stop all main services started by start-codespace.sh

# Find and kill processes running on the main dev ports (8080, 8081, etc.)
PORTS=(8080 8081 9081)
for PORT in "${PORTS[@]}"; do
  PIDS=$(lsof -t -i :$PORT)
  if [ -n "$PIDS" ]; then
    echo "Killing processes on port $PORT: $PIDS"
    kill $PIDS
  fi
done

# Also kill any background jobs started by start-codespace.sh if their PIDs are available
if [ -n "$PUBLIC_API_PID" ]; then
  kill $PUBLIC_API_PID 2>/dev/null && echo "Stopped public-api ($PUBLIC_API_PID)"
fi
if [ -n "$PUBLIC_API_LIVE_PID" ]; then
  kill $PUBLIC_API_LIVE_PID 2>/dev/null && echo "Stopped public-api-live ($PUBLIC_API_LIVE_PID)"
fi
if [ -n "$PUBLIC_API_WEBHOOK_PID" ]; then
  kill $PUBLIC_API_WEBHOOK_PID 2>/dev/null && echo "Stopped public-api-webhook ($PUBLIC_API_WEBHOOK_PID)"
fi
if [ -n "$WEB_CLIENT_PID" ]; then
  kill $WEB_CLIENT_PID 2>/dev/null && echo "Stopped web-client ($WEB_CLIENT_PID)"
fi

echo "All main Codespace services stopped."
