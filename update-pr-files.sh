#!/bin/bash

# <PERSON><PERSON>t to update files in PR-Split branches with their latest versions from AS-211_AS-176-Workflow-Polish_2

# Check if PR number is provided
if [ -z "$1" ]; then
  echo "Usage: $0 <PR_NUMBER>"
  echo "Example: $0 2 (for PR-Split-2-mTLS-Security)"
  exit 1
fi

PR_NUMBER=$1
PR_BRANCH="PR-Split-${PR_NUMBER}-"

# Map PR number to branch name
case $PR_NUMBER in
  1)
    PR_BRANCH="PR-Split-1-GitHub-Actions"
    ;;
  2)
    PR_BRANCH="PR-Split-2-mTLS-Security"
    ;;
  3)
    PR_BRANCH="PR-Split-3-Audio-Processing"
    ;;
  4)
    PR_BRANCH="PR-Split-4-OpenParse-RAG"
    ;;
  5)
    PR_BRANCH="PR-Split-5-Docker-Env"
    ;;
  6)
    PR_BRANCH="PR-Split-6-Client-Side"
    ;;
  *)
    echo "Invalid PR number: $PR_NUMBER"
    exit 1
    ;;
esac

TEMP_BRANCH="temp-${PR_<PERSON>ANCH}"
ANALYSIS_FILE="pr-analysis/pr${PR_NUMBER}-files.txt"

echo "Updating files for $PR_BRANCH..."

# Check if analysis file exists
if [ ! -f "$ANALYSIS_FILE" ]; then
  echo "Analysis file not found: $ANALYSIS_FILE"
  exit 1
fi

# Create or checkout temporary branch
if git show-ref --verify --quiet refs/heads/$TEMP_BRANCH; then
  git checkout $TEMP_BRANCH
else
  git checkout -b $TEMP_BRANCH origin/$PR_BRANCH
fi

# Get the list of files from the analysis file, skipping the header lines
FILES=$(tail -n +4 $ANALYSIS_FILE)

# Create a temporary directory to store the files
mkdir -p temp_files

# For each file in the list
for FILE in $FILES; do
  # Skip empty lines
  if [ -z "$FILE" ]; then
    continue
  fi
  
  # Skip lines that start with dashes (separator lines)
  if [[ $FILE == -* ]]; then
    continue
  fi
  
  echo "Processing file: $FILE"
  
  # Check if the file exists in the current branch
  if git show origin/AS-211_AS-176-Workflow-Polish_2:$FILE > /dev/null 2>&1; then
    # Create the directory structure if it doesn't exist
    mkdir -p $(dirname "temp_files/$FILE")
    
    # Copy the file from the current branch to the temporary directory
    git show origin/AS-211_AS-176-Workflow-Polish_2:$FILE > "temp_files/$FILE"
    
    # Create the directory structure in the target branch if it doesn't exist
    mkdir -p $(dirname "$FILE")
    
    # Copy the file to the target branch
    cp "temp_files/$FILE" "$FILE"
    
    # Add the file to the staging area
    git add "$FILE"
  else
    echo "File not found in AS-211_AS-176-Workflow-Polish_2: $FILE"
  fi
done

# Commit the changes
git commit -m "Update files from AS-211_AS-176-Workflow-Polish_2"

# Push the changes
git push -f --no-verify origin $TEMP_BRANCH:$PR_BRANCH

# Clean up
rm -rf temp_files

echo "Files updated successfully for $PR_BRANCH!"
