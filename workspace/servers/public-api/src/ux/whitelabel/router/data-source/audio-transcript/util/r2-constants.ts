import { getAudioR2Instance } from "@divinci-ai/server-globals";
import { requireEnvVar } from "@divinci-ai/server-utils";

// Use requireEnvVar with a fallback for local development
const getEnvWithFallback = (key: string, fallback: string) => {
  try {
    return requireEnvVar(key);
  } catch (error) {
    console.warn(`⚠️ Environment variable ${key} not found, using fallback value`);
    return fallback;
  }
};

export const CLOUDFLARE_AUDIO_PUBLIC_URL = getEnvWithFallback("CLOUDFLARE_AUDIO_PUBLIC_URL", "https://assets.divinci.ai");

// Add local audio public URL for local development
export const LOCAL_AUDIO_PUBLIC_URL = process.env.LOCAL_AUDIO_PUBLIC_URL || "http://minio.divinci.local:9000";

// Set this flag for detecting local mode globally
export const IS_LOCAL_MODE = process.env.ENVIRONMENT === "local" || process.env.NODE_ENV === "development" || process.env.NODE_ENV === "local";

<<<<<<< HEAD
// Force the AWS SDK to use path-style URLs for S3 buckets in local mode
if (IS_LOCAL_MODE) {
  process.env.AWS_S3_FORCE_PATH_STYLE = "true";

  // Always use the reliable MinIO hostname in local mode
  // Use the reliable DNS alias for MinIO
  process.env.MINIO_ENDPOINT = "http://minio.divinci.local:9000";

  console.log("🔧 Local mode detected, using path-style URLs for S3 buckets");
=======
// Check if we should force R2 usage even in local mode
const FORCE_R2_STORAGE = process.env.FORCE_R2_STORAGE === "true";

// Configure storage settings based on mode and force flag
if (IS_LOCAL_MODE && !FORCE_R2_STORAGE) {
  // Local mode without forced R2 - use MinIO
  process.env.AWS_S3_FORCE_PATH_STYLE = "true";

  // Use the reliable DNS alias for MinIO
  process.env.MINIO_ENDPOINT = "http://minio.divinci.local:9000";

  console.log("🔧 Local mode detected, using MinIO with path-style URLs");
>>>>>>> WA-170_MCP
  console.log(`🔧 Using S3 endpoint: ${process.env.MINIO_ENDPOINT}`);

  // Use the reliable MinIO endpoint for local mode
  const minioEndpoint = "http://minio.divinci.local:9000";

  console.log("🔧 Using reliable MinIO endpoint:", minioEndpoint);
<<<<<<< HEAD
=======
} else if (IS_LOCAL_MODE && FORCE_R2_STORAGE) {
  // Local mode with forced R2 - use R2 settings
  console.log("🔧 Local mode with FORCE_R2_STORAGE=true, using Cloudflare R2");
  console.log("🔧 Skipping MinIO configuration, using R2 client directly");
>>>>>>> WA-170_MCP
}

// Get the audio R2 instance with the configured endpoint
export const r2 = getAudioR2Instance();
