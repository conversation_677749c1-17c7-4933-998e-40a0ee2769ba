{
  "name": "d1-doc-elements",
  "version": "0.3.0",
  "private": true,
  "scripts": {
    "deploy": "wrangler deploy",
    "dev": "wrangler dev",
    "start": "wrangler dev",
    "test": "vitest run",
    "test:watch": "vitest",
    "test:coverage": "vitest run --coverage"
  },
  "devDependencies": {
    "@types/node": "^22.5.5",
    "@types/stream-json": "^1.7.7",
    "@vitest/coverage-v8": "^3.1.1",
    "typescript": "^5.6.2",
    "vite-tsconfig-paths": "^5.1.4",
    "vitest": "^3.1.1",
<<<<<<< HEAD
    "wrangler": "^4.16.1"
=======
    "wrangler": "^4.20.0"
>>>>>>> WA-170_MCP
  },
  "dependencies": {
    "hono": "^4.6.5",
    "stream-json": "^1.8.0"
  }
}
