import { requireEnvVar } from "@divinci-ai/server-utils";

// Import Pyannote environment variables
export const DIVINCI_AUDIO_DIARIZER_PYANNOTE_URL = process.env.DIVINCI_AUDIO_DIARIZER_PYANNOTE_URL || "http://audio-speak-dia-pyannote:5001";
export const DIVINCI_AUDIO_SPLITTER_FFMPEG_URL = process.env.DIVINCI_AUDIO_SPLITTER_FFMPEG_URL || "http://audio-splitter-ffmpeg:5002";
<<<<<<< HEAD
export const PYANNOTE_APIKEY = requireEnvVar("PYANNOTE_APIKEY");
=======

// Lazy-load PYANNOTE_APIKEY to avoid startup errors
export function getPyannoteApiKey(): string {
  return requireEnvVar("PYANNOTE_APIKEY");
}
>>>>>>> WA-170_MCP

// Construct the health check URL using the environment variables
export const DIVINCI_PYANNOTE_HEALTH_URL = `${DIVINCI_AUDIO_DIARIZER_PYANNOTE_URL}/health`;
export const FFMPEG_HEALTH_URL = `${DIVINCI_AUDIO_SPLITTER_FFMPEG_URL}/health`;

// Official Pyannote API health check URL
export const OFFICIAL_PYANNOTE_HEALTH_URL = "https://api.pyannote.ai/v1/test";

// Define multiple possible endpoints for local development
export const DIVINCI_PYANNOTE_ENDPOINTS = [
  "http://audio-speak-dia-pyannote:5001",
  "http://audio-speak-dia-pyannote.divinci.local:5001",
  "http://audio-speak-dia-pyannote.local-network:5001",
  "http://localhost:19000"
];
