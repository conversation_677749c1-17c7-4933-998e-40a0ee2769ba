#!/bin/bash

# <PERSON>ript to run API tests for each test suite defined in api-test-mapping.ts
# and track the results

# Define the test suites
TEST_SUITES=(
  "AI Chats"
  "White Label"
  "Fine Tune"
  "Prompt Moderation"
  "Thread Prefix"
  "Message Prefix"
  "RAG"
  "Workspace Release"
  "Audio Transcript"
)

# Create a results file
RESULTS_FILE="api-test-results.md"

# Function to run a test suite
run_test_suite() {
  local suite=$1
  local folder=""
  
  # Map test suite to folder
  case "$suite" in
    "AI Chats")
      folder="workspace/servers/public-api/src/routes/ai-chat"
      ;;
    "White Label")
      folder="workspace/servers/public-api/src/routes/whitelabel"
      ;;
    "Fine Tune")
      folder="workspace/servers/public-api/src/routes/finetune"
      ;;
    "Prompt Moderation")
      folder="workspace/servers/public-api/src/routes/moderation"
      ;;
    "Thread Prefix")
      folder="workspace/servers/public-api/src/routes/thread"
      ;;
    "Message Prefix")
      folder="workspace/servers/public-api/src/routes/message"
      ;;
    "RAG")
      folder="workspace/servers/public-api/src/routes/rag"
      ;;
    "Workspace Release")
      folder="workspace/servers/public-api/src/routes/workspace"
      ;;
    "Audio Transcript")
      folder="workspace/resources/actions/src/workspace/data-source/audio"
      ;;
  esac
  
  echo "Running tests for $suite (folder: $folder)..."
  
  # Run the tests
  cd /Users/<USER>/Documents/server3/server/workspace/clients/tests
  CHANGED_FOLDERS=$folder pnpm start:dev > "../../${suite// /-}-test-output.txt" 2>&1
  
  # Check if the tests passed
  if [ $? -eq 0 ]; then
    echo "Tests for $suite passed!"
    sed -i '' "s/| $suite | Not Run |/| $suite | Passed |/g" "../../$RESULTS_FILE"
  else
    echo "Tests for $suite failed!"
    sed -i '' "s/| $suite | Not Run |/| $suite | Failed |/g" "../../$RESULTS_FILE"
    
    # Extract failing tests
    echo -e "\n## $suite Failing Tests\n" >> "../../$RESULTS_FILE"
    grep -A 5 "Error in" "../../${suite// /-}-test-output.txt" >> "../../$RESULTS_FILE" || true
    echo -e "\n" >> "../../$RESULTS_FILE"
  fi
}

# Run each test suite
for suite in "${TEST_SUITES[@]}"; do
  run_test_suite "$suite"
done

echo "All tests completed. Results are in $RESULTS_FILE"
