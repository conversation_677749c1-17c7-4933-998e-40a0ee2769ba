import { ChunkWorkflowClient } from "@divinci-ai/server-tools";
import {
  IRagVectorFileModelType,
  RagVectorTextChunksStatus
} from "../../types";
import {
  DivinciTestProcessConfig,
  DIVINCI_TEST_PROCESS_CONFIG as DIVINCI_PROCESS,
  auth0APIFetcher
} from "@divinci-ai/server-globals";
import { RawFileToChunks } from "@divinci-ai/server-tools";
import { delay } from "@divinci-ai/utils";

// Timeouts
const INITIAL_TIMEOUT_MS = 1000 * 60 * 5; // 5 minutes
const WORKFLOW_POLL_INTERVAL_MS = 5000;   // 5 seconds
const MAX_WAIT_TIME_MS = 1000 * 60 * 10;  // 10 minutes

/**
 * Uses Cloudflare Workflow to chunk and save documents
 *
 * @param doc Document model instance
 * @param tool RawFileToChunks tool (used to determine processor type)
 * @param processConfig Process configuration
<<<<<<< HEAD
=======
 * @param ragVectors List of RAG ID to link the file to a RAG vector
>>>>>>> WA-170_MCP
 * @returns Promise void
 */
export const chunkAndSaveWorkflow = async function(
  doc: InstanceType<IRagVectorFileModelType>,
  tool: RawFileToChunks,
<<<<<<< HEAD
  processConfig: null | DivinciTestProcessConfig
): Promise<void> {
=======
  processConfig: null | DivinciTestProcessConfig,
  ragVectors?: Array<string>
): Promise<void>{
>>>>>>> WA-170_MCP
  const model = doc.constructor as IRagVectorFileModelType;
  let workflowId: string | undefined;

  try {
    // Execute any test process configuration
    await DIVINCI_PROCESS.runConfig(processConfig);

    // Get processor type from tool or chunkingTool property
    let processorType: string;

    // Check if the doc has a chunkingTool property and use that if available
<<<<<<< HEAD
    if (doc.chunkingTool && typeof doc.chunkingTool === 'string') {
=======
    if(doc.chunkingTool && typeof doc.chunkingTool === "string") {
>>>>>>> WA-170_MCP
      processorType = doc.chunkingTool.toLowerCase();
      console.log(`📄 Using processor type from doc.chunkingTool: ${processorType}`);
    } else {
      // Fallback to determining from the tool constructor name
      processorType = tool.constructor.name.toLowerCase().includes("openparse")
        ? "openparse"
        : "unstructured";
      console.log(`📄 Using processor type from tool constructor: ${processorType}`);
    }

    // Validate processor type
<<<<<<< HEAD
    if (processorType !== "openparse" && processorType !== "unstructured") {
=======
    if(processorType !== "openparse" && processorType !== "unstructured") {
>>>>>>> WA-170_MCP
      console.warn(`⚠️ Invalid processor type: ${processorType}, defaulting to unstructured`);
      processorType = "unstructured";
    }

    // Extract whitelabel ID from document target
    // The target format is expected to be: mongodb/rag-vector/{whitelabelId}
<<<<<<< HEAD
    const targetParts = doc.target.split('/');
    let whitelabelId: string;

    if (targetParts.length === 3 && targetParts[0] === 'mongodb' && targetParts[1] === 'rag-vector') {
      // Target is in the correct format: mongodb/rag-vector/{whitelabelId}
      whitelabelId = targetParts[2];
      console.log(`📄 Using whitelabelId from target parts[2]: ${whitelabelId}`);
    } else {
      // Try to extract from rawFileKey
      if (doc.rawFileKey && doc.rawFileKey.includes('/')) {
        whitelabelId = doc.rawFileKey.split('/')[0];
=======
    const targetParts = doc.target.split("/");
    let whitelabelId: string;

    if(targetParts.length === 3 && targetParts[0] === "mongodb" && targetParts[1] === "rag-vector") {
      // Target is in the correct format: mongodb/rag-vector/{whitelabelId}
      // But it might have query parameters like: mongodb/rag-vector/{whitelabelId}?ragId=...
      const rawWhitelabelId = targetParts[2];

      // Check if there are query parameters and extract just the whitelabel ID
      if(rawWhitelabelId.includes("?")) {
        whitelabelId = rawWhitelabelId.split("?")[0];
        console.log(`📄 Extracted whitelabelId from target with query params: ${whitelabelId} (from ${rawWhitelabelId})`);
      } else {
        whitelabelId = rawWhitelabelId;
        console.log(`📄 Using whitelabelId from target parts[2]: ${whitelabelId}`);
      }
    } else {
      // Try to extract from rawFileKey
      if(doc.rawFileKey && doc.rawFileKey.includes("/")) {
        whitelabelId = doc.rawFileKey.split("/")[0];
>>>>>>> WA-170_MCP
        console.log(`📄 Using whitelabelId from rawFileKey: ${whitelabelId}`);
      } else {
        // Fallback to using the getWhitelabelIdFromTarget function
        whitelabelId = getWhitelabelIdFromTarget(doc.target);
        console.log(`📄 Using whitelabelId from getWhitelabelIdFromTarget: ${whitelabelId}`);
      }
    }

    // Initialize the client
    const workflowUrl = process.env.CHUNKS_VECTORIZED_WORKFLOW_URL;
<<<<<<< HEAD
    if (!workflowUrl) {
=======
    if(!workflowUrl) {
>>>>>>> WA-170_MCP
      console.log("⚠️ CHUNKS_VECTORIZED_WORKFLOW_URL environment variable is not set, using local URL");
    }

    // Use the correct Docker service name for local development
    const localWorkflowUrl = "http://local-chunks-vectorized:8791";

    // Log the whitelabel ID for debugging
    console.log(`📄 Using whitelabel ID: ${whitelabelId} for document ${doc._id}`);

<<<<<<< HEAD
=======
    console.log(`📄 Creating ChunkWorkflowClient with ragVectors: ${ragVectors?.join(", ") || "none"}`);
>>>>>>> WA-170_MCP
    console.log(`Initializing ChunkWorkflowClient with API URL: ${workflowUrl || localWorkflowUrl}`);
    const client = new ChunkWorkflowClient({
      auth0Token: await getAuth0Token(),
      whitelabelId: whitelabelId,
<<<<<<< HEAD
=======
      ragVectors: ragVectors, // Pass the ragId to the client
>>>>>>> WA-170_MCP
      apiUrl: workflowUrl || localWorkflowUrl
    });

    // Prepare file location
    const fileLocation = doc.rawR2Pointer();

    // Submit the document to workflow
    console.log(`📄 Submitting document ${doc._id} to Cloudflare Workflow`);

    // Log detailed information about the submission
    const processorConfig = getProcessorConfig(processorType, tool);
    console.log(`📄 Document submission details:`, {
      fileId: doc._id,
      processorType,
      fileLocation,
      processorConfig,
      // Only log properties that we know exist
      title: doc.title,
      description: doc.description
    });

    try {
      const submission = await client.submitDocument(
        fileLocation,
        processorType as "openparse" | "unstructured",
        processorConfig,
        doc.title || "Untitled Document", // Add title
<<<<<<< HEAD
        doc.description || "Processed document" // Add description
=======
        doc.description || "Processed document", // Add description
        doc.target // Add target
>>>>>>> WA-170_MCP
      );

      // Log the submission result
      console.log(`📄 Document submission result:`, {
        success: submission.success,
        workflowId: submission.workflowId,
        error: submission.error,
        objectKey: submission.objectKey,
        id: submission.id
      });

<<<<<<< HEAD
      if (!submission.success || !submission.workflowId) {
=======
      if(!submission.success || !submission.workflowId) {
>>>>>>> WA-170_MCP
        console.error(`❌ Failed to submit document to workflow:`, {
          error: submission.error || "Unknown error",
          fileId: doc._id,
          processorType,
          fileLocation
        });
        throw new Error(`Failed to submit document to workflow: ${submission.error || "Unknown error"}`);
      }

      workflowId = submission.workflowId;
      console.log(`✅ Document ${doc._id} submitted to workflow ${workflowId}`);
<<<<<<< HEAD
    } catch (error) {
=======
    }catch(error) {
>>>>>>> WA-170_MCP
      console.error(`❌ Error submitting document to workflow:`, {
        error: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : undefined,
        fileId: doc._id,
        processorType,
        fileLocation
      });
      throw error;
    }

    // Update status to chunking
    await model.findByIdAndUpdate(
      doc._id,
      { $set: { status: RagVectorTextChunksStatus.CHUNKING } }
    );

    // Wait for workflow to complete with timeout
    console.log(`Waiting for workflow ${workflowId} to complete`);
    const finalStatus = await Promise.race([
      client.waitForWorkflowCompletion(workflowId, WORKFLOW_POLL_INTERVAL_MS, MAX_WAIT_TIME_MS),
<<<<<<< HEAD
      new Promise<null>(async (resolve) => {
        await delay(INITIAL_TIMEOUT_MS);
        console.log(`Initial timeout reached for workflow ${workflowId}`);
        resolve(null);
=======
      Promise.resolve().then(async ()=>{
        await delay(INITIAL_TIMEOUT_MS);
        console.log(`Initial timeout reached for workflow ${workflowId}`);
        return null;
>>>>>>> WA-170_MCP
      })
    ]);

    // Handle completion and update status
<<<<<<< HEAD
    if (!finalStatus) {
=======
    if(!finalStatus) {
>>>>>>> WA-170_MCP
      console.log(`Workflow ${workflowId} did not complete within timeout, but will continue processing`);

      // Just update to editing status as the workflow will continue
      // Eventually, the workflow will update the status to completed
      const length = await doc.getChunkLength();
      await model.findByIdAndUpdate(
        doc._id,
        { $set: { status: RagVectorTextChunksStatus.EDITING, indexOffset: length } }
      );
      return;
    }

    // Check for workflow failure
<<<<<<< HEAD
    if (finalStatus.error || finalStatus.state.name === "failed") {
=======
    if(finalStatus.error || finalStatus.state.name === "failed") {
>>>>>>> WA-170_MCP
      throw new Error(`Workflow failed: ${finalStatus.error?.message || "Unknown workflow error"}`);
    }

    // Workflow completed successfully
    console.log(`Workflow ${workflowId} completed successfully`);
    const length = await doc.getChunkLength();
    await model.findByIdAndUpdate(
      doc._id,
      { $set: { status: RagVectorTextChunksStatus.EDITING, indexOffset: length } }
    );
<<<<<<< HEAD
  } catch(e: any) {
=======
  }catch(e: any) {
>>>>>>> WA-170_MCP
    console.error(`Error in chunkAndSaveWorkflow for document ${doc._id}:`, e);

    // Update document status to failed
    await model.findByIdAndUpdate(
      doc._id,
      {
        $set: {
          status: RagVectorTextChunksStatus.FAILED_TO_CHUNK,
          failureReason: e.message,
        }
      }
    );

    // If we have a workflow ID, try to log its status for debugging
<<<<<<< HEAD
    if (workflowId) {
=======
    if(workflowId) {
>>>>>>> WA-170_MCP
      try {
        const client = new ChunkWorkflowClient({
          auth0Token: await getAuth0Token(),
          whitelabelId: getWhitelabelIdFromTarget(doc.target)
        });

        const workflowStatus = await client.getWorkflowStatus(workflowId);
        console.error(`Workflow ${workflowId} status at failure:`, workflowStatus);
<<<<<<< HEAD
      } catch (statusError) {
=======
      }catch(statusError) {
>>>>>>> WA-170_MCP
        console.error(`Failed to get workflow status for ${workflowId}:`, statusError);
      }
    }
  }
};

/**
 * Helper to extract whitelabel ID from target
 */
<<<<<<< HEAD
function getWhitelabelIdFromTarget(target: string): string {
=======
function getWhitelabelIdFromTarget(target: string): string{
>>>>>>> WA-170_MCP
  return target.split("/")[0] || "default";
}

/**
 * Helper to get Auth0 token
 */
<<<<<<< HEAD
async function getAuth0Token(): Promise<string> {
  try {
    // Check if we're in a test environment
    if (process.env.NODE_ENV === 'test') {
=======
async function getAuth0Token(): Promise<string>{
  try {
    // Check if we're in a test environment
    if(process.env.NODE_ENV === "test") {
      console.log("🔑 Using test auth token for NODE_ENV=test");
>>>>>>> WA-170_MCP
      return "test-auth-token";
    }

    // Use the auth0APIFetcher from server-globals
<<<<<<< HEAD
    const token = await auth0APIFetcher.getToken();
    return token.access_token;
  } catch (error) {
    console.error("Failed to get Auth0 token:", error);
=======
    console.log("🔑 Acquiring Auth0 token from auth0APIFetcher...");
    const token = await auth0APIFetcher.getToken();
    console.log(`🔑 Successfully acquired Auth0 token (first 10 chars): ${token.access_token.substring(0, 10)}...`);
    console.log(`🔑 Token details:`, JSON.stringify(token, null, 2));
    return token.access_token;
  }catch(error) {
    console.error("❌ Failed to get Auth0 token:", error);
>>>>>>> WA-170_MCP
    throw new Error("Auth0 authentication failed");
  }
}

/**
 * Convert tool config to processor config
 */
function getProcessorConfig(
  processorType: string,
  _tool: RawFileToChunks // Prefix with underscore to indicate it's not used
<<<<<<< HEAD
): any {
  // Get the document's chunking tool config from the model
  // In a real implementation, we would extract from the model config

  if (processorType === "openparse") {
=======
): any{
  // Get the document's chunking tool config from the model
  // In a real implementation, we would extract from the model config

  if(processorType === "openparse") {
>>>>>>> WA-170_MCP
    // Configuration for OpenParse optimized for text files
    return {
      semantic_chunking: true,
      embeddings_provider: "cloudflare",
      minTokens: 256,
      maxTokens: 1024,
      chunkOverlap: 125,  // Add top-level config
      useTokens: true,    // Add top-level config
      relevanceThreshold: 0.3, // Add relevance threshold
      openparse: {
        semantic: true,
        minTokens: 256,
        maxTokens: 1024,
        chunkOverlap: 125,
        useTokens: true,
        embeddings: {
          provider: "cloudflare"
        }
      }
    };
  } else {
    // Configuration for Unstructured
    return {
      skipDeJunk: false,  // Enable de-junking
      unstructured: {
        chunkingStrategy: "by_title",
        maxCharacters: 1024,
        includeOriginalElements: false,
        multipageSections: true
      }
    };
  }
}