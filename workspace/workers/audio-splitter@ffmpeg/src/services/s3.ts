import { S3 } from "@aws-sdk/client-s3";

import { requireEnvVar, getOptionalEnvVar } from "../utils/env";
const CLOUDFLARE_AUDIO_ACCESS_KEY = requireEnvVar("CLOUDFLARE_AUDIO_ACCESS_KEY");
const CLOUDFLARE_AUDIO_SECRET_ACCESS_KEY = requireEnvVar("CLOUDFLARE_AUDIO_SECRET_ACCESS_KEY");
const CLOUDFLARE_AUDIO_S3 = requireEnvVar("CLOUDFLARE_AUDIO_S3");

// Check for local mode environment
const NODE_ENV = getOptionalEnvVar("NODE_ENV") || "";
const ENVIRONMENT = getOptionalEnvVar("ENVIRONMENT") || "";
const IS_LOCAL_MODE = NODE_ENV === "development" || NODE_ENV === "local" ||
                      ENVIRONMENT === "local" || ENVIRONMENT === "development";

<<<<<<< HEAD
=======
// Check if we should force R2 usage even in local mode
const FORCE_R2_STORAGE = getOptionalEnvVar("FORCE_R2_STORAGE") === "true";

// Debug logging for environment variables
console.log(`🔧 DEBUG: NODE_ENV=${NODE_ENV}, ENVIRONMENT=${ENVIRONMENT}`);
console.log(`🔧 DEBUG: IS_LOCAL_MODE=${IS_LOCAL_MODE}, FORCE_R2_STORAGE=${FORCE_R2_STORAGE}`);

>>>>>>> WA-170_MCP
// Check if MINIO_ENDPOINT is provided as fallback for local development
// const MINIO_ENDPOINT = getOptionalEnvVar("MINIO_ENDPOINT") || (IS_LOCAL_MODE ? "http://local-minio:9000" : undefined);

// Use the reliable MinIO endpoint for local mode
const minioEndpoint = "http://minio.divinci.local:9000";

// Define MinIO credentials for local development - using the root credentials
const MINIO_ROOT_USER = process.env.MINIO_ROOT_USER || "minioadmin";  // Root user from environment
const MINIO_ROOT_PASSWORD = process.env.MINIO_ROOT_PASSWORD || "minioadmin";  // Root password from environment

// Log the credentials being used for debugging
console.log(`🔑 Using MinIO root credentials: accessKey=${MINIO_ROOT_USER}, secretKey=${MINIO_ROOT_PASSWORD.substring(0, 3)}***`);

<<<<<<< HEAD
console.log(`🔄 S3 client initialization`, {
  isLocalMode: IS_LOCAL_MODE,
  endpoint: IS_LOCAL_MODE ? minioEndpoint : CLOUDFLARE_AUDIO_S3,
  minioEndpoint: IS_LOCAL_MODE ? minioEndpoint : null
});

// Create S3 client based on environment with enhanced debugging
export const s3 = new S3({
  endpoint: IS_LOCAL_MODE ? "http://minio.divinci.local:9000" : CLOUDFLARE_AUDIO_S3,
  credentials: {
    accessKeyId: IS_LOCAL_MODE ? MINIO_ROOT_USER : CLOUDFLARE_AUDIO_ACCESS_KEY,
    secretAccessKey: IS_LOCAL_MODE ? MINIO_ROOT_PASSWORD : CLOUDFLARE_AUDIO_SECRET_ACCESS_KEY,
  },
  region: "auto",
  forcePathStyle: IS_LOCAL_MODE, // Required for MinIO compatibility
=======
// Determine which storage to use
const shouldUseR2 = !IS_LOCAL_MODE || FORCE_R2_STORAGE;

console.log(`🔄 S3 client initialization`, {
  isLocalMode: IS_LOCAL_MODE,
  forceR2Storage: FORCE_R2_STORAGE,
  shouldUseR2: shouldUseR2,
  endpoint: shouldUseR2 ? CLOUDFLARE_AUDIO_S3 : minioEndpoint,
  storageType: shouldUseR2 ? 'Cloudflare R2' : 'MinIO'
});

// Create S3 client based on environment and force flag
export const s3 = new S3({
  endpoint: shouldUseR2 ? CLOUDFLARE_AUDIO_S3 : "http://minio.divinci.local:9000",
  credentials: {
    accessKeyId: shouldUseR2 ? CLOUDFLARE_AUDIO_ACCESS_KEY : MINIO_ROOT_USER,
    secretAccessKey: shouldUseR2 ? CLOUDFLARE_AUDIO_SECRET_ACCESS_KEY : MINIO_ROOT_PASSWORD,
  },
  region: "auto",
  forcePathStyle: !shouldUseR2, // Required for MinIO compatibility, not needed for R2
>>>>>>> WA-170_MCP
});

// Add a middleware to log all S3 requests for debugging
s3.middlewareStack.add(
  (next) => async (args) => {
    // Use safe type checking for logging
    try {
      if (args && typeof args === 'object' && 'request' in args && args.request) {
        const req = args.request as any;
        console.log(`🔍 S3 Request: ${req.method || 'UNKNOWN'} ${req.hostname || 'unknown-host'}${req.path || ''}`);
      } else {
        console.log(`🔍 S3 Request: [Unable to log request details]`);
      }
    } catch (logError) {
      console.log(`🔍 S3 Request: [Error logging request]`);
    }

    try {
      const result = await next(args);
      console.log(`✅ S3 Response: Success`);
      return result;
    } catch (error: any) {
      console.log(`❌ S3 Response: Error - ${error?.message || 'Unknown error'}`);
      throw error;
    }
  },
  {
    step: "build",
    name: "logRequests",
  }
);



import { Readable } from "node:stream";

/**
 * Get an S3 readable stream with retry logic for local MinIO connections
 * Automatically tries multiple endpoints in local mode
 */
export async function getS3Readable(
  s3: S3, { Bucket, Key }: { Bucket: string, Key: string }
): Promise<Readable>{
  // Try main client first
  try {
    console.log(`🔄 Getting S3 object from ${Bucket}/${Key}`);
    const abortController = new AbortController();
    const response = await s3.getObject(
      { Bucket, Key, },
      { abortSignal: abortController.signal }
    );
    if(!response.Body) {
      throw new Error("❌ No body in get checks response. ");
    }

    const stream = response.Body as Readable;

    let finishedNormally = false;

    // Listen for successful completion
    stream.on("end", ()=>finishedNormally = true);
    // Only abort if the stream did not finish normally
    stream.on("close", ()=>{
      if(!finishedNormally) abortController.abort();
    });
    stream.on("destroy", ()=>abortController.abort());

    return stream;
  } catch (error: any) {
<<<<<<< HEAD
    // Only retry with alternative endpoints in local mode
    if (!IS_LOCAL_MODE) {
=======
    // Only retry with alternative endpoints in local mode without forced R2
    if (!IS_LOCAL_MODE || FORCE_R2_STORAGE) {
>>>>>>> WA-170_MCP
      throw error;
    }

    console.log(`⚠️ Error getting S3 object with primary endpoint. Trying alternative endpoints.`);

<<<<<<< HEAD
    // Try the reliable MinIO endpoint in local development mode
    if (IS_LOCAL_MODE) {
=======
    // Try the reliable MinIO endpoint in local development mode (only if not forcing R2)
    if (IS_LOCAL_MODE && !FORCE_R2_STORAGE) {
>>>>>>> WA-170_MCP
      try {
        console.log(`🔄 Trying reliable MinIO endpoint: ${minioEndpoint}`);

        // Create a client with the reliable endpoint
        const altS3 = new S3({
          endpoint: minioEndpoint,
          credentials: {
            accessKeyId: MINIO_ROOT_USER,
            secretAccessKey: MINIO_ROOT_PASSWORD,
          },
          region: "auto",
          forcePathStyle: true,
        });

        const abortController = new AbortController();
        const response = await altS3.getObject(
          { Bucket, Key, },
          { abortSignal: abortController.signal }
        );

        if (!response.Body) {
          console.log(`⚠️ No body in response from ${minioEndpoint}`);
          throw new Error("No body in response");
        }

        const stream = response.Body as Readable;

        let finishedNormally = false;

        // Listen for successful completion
        stream.on("end", () => finishedNormally = true);
        // Only abort if the stream did not finish normally
        stream.on("close", () => {
          if(!finishedNormally) abortController.abort();
        });
        stream.on("destroy", () => abortController.abort());

        console.log(`✅ Successfully got S3 object using endpoint: ${minioEndpoint}`);
        return stream;
      } catch (endpointError: any) {
        console.log(`⚠️ Failed with endpoint ${minioEndpoint}: ${endpointError?.message || String(endpointError)}`);
        throw endpointError;
      }
    }

    // If all endpoints failed, provide a more detailed error message
    console.error(`❌ All S3 endpoints failed. Original error: ${error?.message || String(error)}`);

    // Provide more detailed error messages based on the error type
    if (error.name === 'InvalidAccessKeyId') {
      console.error("MinIO authentication error. Check your credentials.");
      throw new Error(
        "Failed to authenticate with MinIO. Please check your credentials."
      );
    } else if (error.name === 'NoSuchKey') {
      console.error("File not found in MinIO.");
      throw new Error(
        "The requested file was not found in storage."
      );
    } else if (error.name === 'NoSuchBucket') {
      console.error("Bucket not found in MinIO.");
      throw new Error(
        "The requested bucket was not found in storage."
      );
    }

    // If it's a generic error, provide a more user-friendly message
    throw new Error(
      `Failed to retrieve file from storage: ${error.message || "Unknown error"}`
    );
  }
}


import { PassThrough, Writable } from "node:stream";
import { Upload } from "@aws-sdk/lib-storage";

const UPLOAD_FINISHED = Symbol("Upload Finished");
export class S3Writable extends Writable {
  public upload: Upload;
  private bodyStream = new PassThrough();
  private [UPLOAD_FINISHED] = false;
  private uploadPromise: Promise<any>;
  constructor(
    private r2: S3,
    public config: { Bucket: string, Key: string, ContentType: string, Metadata: Record<string, string> }
  ){
    super();
    this.upload = new Upload({
      client: this.r2,
      params: {
        ...config,
        Body: this.bodyStream,
      }
    });
    this.uploadPromise = this.upload.done().catch((e)=>{
      console.error("S3 Writable Upload Error:", e);
    });
  }

  _write(chunk: any, encoding: BufferEncoding, callback: (error?: Error | null) => void){
    this.bodyStream.write(chunk, encoding, callback);
  }

  async _final(callback: (error?: any) => void){
    try {
      this.bodyStream.end();
      this[UPLOAD_FINISHED] = true;
      await this.uploadPromise;
      callback();
    }catch(e){
      callback(e);
    }
  }

  async _destroy(error: Error | null, callback: (error?: Error | null) => void){
    if(this[UPLOAD_FINISHED]) return callback(error);
    try { await this.upload.abort(); }catch(e){ console.error("Ignore abort error"); }
    callback(error);
  }
}


