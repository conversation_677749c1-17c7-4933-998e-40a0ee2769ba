import { Request<PERSON><PERSON><PERSON> } from "express";
import {
  R2File,
  getUserId,
  getR2Instance,
  getUserById,
} from "@divinci-ai/server-globals";

import { RAWFILE_TO_CHUNKS } from "@divinci-ai/server-tools";
import {
  WhiteLabelModel,
  TranscriptModel,
  NotificationSettingsDefaultModel,
  NotificationSettingsModel,
  VerifiedEmail,
  IVerifiedEmailDoc,
  INotificationDefaultSettingsDoc,
  INotificationSettingsDoc,
  RagVectorModel,
  RagVectorFileModel,
} from "@divinci-ai/server-models";

import {
  jsonBody,
  HTTP_ERRORS,
} from "@divinci-ai/server-utils";
import { castShallowObject } from "@divinci-ai/utils";
import {
  AI_CATEGORY_ENUM,
  WHITE_LABEL_LOCATION,
  condenseTarget,
} from "@divinci-ai/models";
import { Types } from "mongoose";


export const createWhiteLabel: RequestHandler = async (req, res, next)=>{
  const file = req.file as any as R2File;
  let whitelabel;

  try {
    const user_id = getUserId(req);

    const user = await getUserById(user_id);

    const body =
      req.headers["content-type"] === "application/json"
        ? await jsonBody(req)
        : req.body;

    const { title, description } = castShallowObject(
      body,
      {
        title: "string",
        description: "string",
      },
      HTTP_ERRORS.BAD_FORM,
    );

    console.log(
      "🌱🗄️ createWhiteLabel title, description, creatorEmail: ",
      title,
      description,
      user.email,
    );
    console.log("🌱🗄️ createWhiteLabel file info: ", req.file);
    console.log("🌱🗄️ createWhiteLabel uploadAndProcessPDF done");

    const transcript = await TranscriptModel.create({
      awaitingResponse: false,
      messages: [],
    });

    console.log("🌱🗄️ createWhiteLabel TranscriptModel created");

    whitelabel = new WhiteLabelModel({
      ownerUser: user_id,
      title,
      description,
      creatorEmail: user.email,

      version: 0,
      releaseStatus: "development",

      privacy: {
        publicChat: false,
        privateChat: false,
        deprecatedChat: false,
      },

      transcriptIds: [transcript._id],
    });

    console.log("...🌱🗄️ createWhiteLabel WhitelabelModel created");

    await whitelabel.save();

    const newEmail = await findCreateVerifiedEmail(
      whitelabel.creatorEmail,
      whitelabel._id.toString(),
    );
    const defaultNotificationSettings = blankNotificationSettingsDefaultModel(
      newEmail,
      whitelabel._id,
    );
    const notificationSettings = blankNotificationSettingsModel(
      newEmail,
      whitelabel._id,
    );

    await defaultNotificationSettings.save();
    await notificationSettings.save();

    console.log("..🌱🗄️ createWhiteLabel chat saved");

    let uploadSuccess = true;

    try {
      if(file) {
        const target = condenseTarget({
          ...WHITE_LABEL_LOCATION,
          id: whitelabel._id.toString(),
        });

        const doc = new RagVectorModel({
          target: target,
          whitelabel: whitelabel._id.toString(),
          category: AI_CATEGORY_ENUM.TEXT,

          title: "New RAG Vector",
          description: "RAG Vector For Whitelabel",

          contextPerMessage: 5,
          minimumSimilarity: 0.62,
        });

        await doc.save();

        await RagVectorFileModel.addNewFile(
          target,
          {
            bucket: file.r2Info.bucket,
            objectKey: file.r2Info.objectKey,
            originalName: file.originalName,
          },
          Object.values(RAWFILE_TO_CHUNKS).filter((chunker)=>(!chunker.deprecated))[0].id,
          {
            title: "First RAG File",
            description: "Default File for RAG",
          },
          null
        );
      }
    }catch(e) {
      console.error("❌ createWhiteLabel failed upload: \n", e);
      uploadSuccess = false;
    }

    res.statusCode = 200;
    res.json({ whitelabel, uploadSuccess });

    console.log("🏁🌱🗄️ createWhiteLabel:", whitelabel);
  }catch(e) {
    if(whitelabel?._id && req.file) {
      const r2 = getR2Instance();
<<<<<<< HEAD
      r2.deleteObject({
        Bucket: file.r2Info.bucket,
        Key: file.r2Info.objectKey,
      }).catch((e)=>{
        console.log("👎🏻 didn't delete object, continuing anyway 🤷🏻‍♂️", e);
      });
=======
      if (r2) {
        r2.deleteObject({
          Bucket: file.r2Info.bucket,
          Key: file.r2Info.objectKey,
        }).catch((e)=>{
          console.log("👎🏻 didn't delete object, continuing anyway 🤷🏻‍♂️", e);
        });
      } else {
        console.warn("⚠️ r2 instance is undefined, cannot delete object");
      }
>>>>>>> WA-170_MCP
    }
    next(e);
  }
};

export async function findCreateVerifiedEmail(
  email: string,
  whitelabelId: string,
): Promise<IVerifiedEmailDoc>{
  console.log("findCreateVerifiedEmail::whitelabelId: ", whitelabelId);
  // Create a new VerifiedEmail entry
  const verifiedEmailDoc = await VerifiedEmail.findOne({
    // email: whitelabel.creatorEmail, // Default value
    email, // Default value
  });
  if(!verifiedEmailDoc) {
    const verifiedEmailDoc = new VerifiedEmail({
      email,
      whitelabelIds: [whitelabelId],
      verified: false,
    });
    return await verifiedEmailDoc.save();
  }
  return verifiedEmailDoc;
}


function blankNotificationSettingsDefaultModel(
  newEmail: IVerifiedEmailDoc,
  whitelabelId: Types.ObjectId,
): INotificationDefaultSettingsDoc{
  return new NotificationSettingsDefaultModel({
    emailEnabled: true,
    smsEnabled: false,
    emails: [newEmail],
    phoneNumbers: [],
    whitelabelId,
  });
}

function blankNotificationSettingsModel(
  newEmail: IVerifiedEmailDoc,
  whitelabelId: Types.ObjectId,
): INotificationSettingsDoc{
  return new NotificationSettingsModel({
    emailEnabled: true,
    smsEnabled: false,
    emails: [newEmail],
    phoneNumbers: [],
    triggerDescription: "",
    whitelabelId,
  });
}
