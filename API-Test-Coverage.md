# API Test Coverage Guide

This document explains how to use the test coverage tools to measure and improve API test coverage.

## Overview

The test coverage system uses Istanbul/nyc to track which parts of the API code are executed during tests. This helps identify areas that need more testing and ensures that critical code paths are properly tested.

## Running Tests with Coverage

### API Tests

To run API tests with coverage:

```bash
pnpm test:coverage:api
```

This will run all API tests and collect coverage data.

### E2E Tests

To run E2E tests with coverage:

```bash
pnpm test:coverage:e2e
```

### All Tests

To run all tests with coverage:

```bash
pnpm test:coverage:all
```

## Viewing Coverage Reports

After running tests with coverage, you can generate and view coverage reports:

```bash
pnpm test:coverage:report
```

This will generate HTML, lcov, and text reports in the `coverage` directory. Open `coverage/index.html` in a browser to view the HTML report.

## Analyzing Coverage

To analyze coverage and identify gaps:

```bash
pnpm test:coverage:analyze
```

This will:
1. Analyze the coverage data
2. Compare it with the API routes defined in the codebase
3. Identify routes with low or no coverage
4. Generate a report showing which areas need more testing

## Coverage Thresholds

The coverage system enforces the following thresholds:

- **Statements**: 80%
- **Branches**: 70%
- **Functions**: 75%
- **Lines**: 80%

These thresholds can be adjusted in the `.nycrc` configuration file.

## Adding Tests for Uncovered Areas

When the coverage analysis identifies areas with low or no coverage, follow these steps to add tests:

1. **Identify the API endpoint**: Look at the coverage report to find endpoints with low coverage.

2. **Check existing tests**: Review existing tests in `src/tests` to understand the testing patterns.

3. **Create a new test file**: If needed, create a new test file for the uncovered endpoint.

4. **Add tests for uncovered paths**: Write tests that exercise the uncovered code paths.

5. **Run tests with coverage**: Run the tests with coverage to verify that the new tests improve coverage.

## Best Practices

1. **Focus on critical paths**: Prioritize testing critical business logic and error handling.

2. **Test edge cases**: Include tests for edge cases and error conditions.

3. **Maintain high coverage**: Aim to maintain coverage above the thresholds for all API endpoints.

4. **Run coverage regularly**: Run coverage analysis regularly to catch areas where coverage has decreased.

5. **Add tests for new features**: When adding new features, also add tests to maintain high coverage.

## Troubleshooting

### No Coverage Data

If you see "Coverage data not found" when running the analyzer, make sure you've run tests with coverage first:

```bash
pnpm test:coverage:api
pnpm test:coverage:analyze
```

### Low Coverage Despite Tests

If you have tests but coverage is still low, check that:

1. Your tests are actually executing the code paths you think they are.
2. You're testing error conditions and edge cases.
3. The tests are properly configured to run against the right environment.

## Integration with CI/CD

The coverage tools can be integrated with CI/CD pipelines to:

1. Run tests with coverage on every pull request
2. Fail the build if coverage drops below thresholds
3. Generate coverage reports as artifacts

This ensures that test coverage remains high as the codebase evolves.
