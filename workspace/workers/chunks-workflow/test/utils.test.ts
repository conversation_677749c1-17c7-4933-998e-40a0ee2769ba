import { describe, it, expect, vi } from "vitest";
import { countTokens, createVectorId, getTableName, getRelevanceThreshold } from "../src/utils";

// Mock GPTTokens
vi.mock("gpt-tokens", ()=>({
  GPTTokens: vi.fn().mockImplementation(()=>({
    usedTokens: 10
  }))
}));

describe("utils", ()=>{
  describe("countTokens", ()=>{
    it("should return token count for a given text", ()=>{
      const result = countTokens("Hello, world!");
      expect(result).toBe(10);
    });
  });

  describe("createVectorId", ()=>{
    it("should create a vector ID from file ID and index", ()=>{
      const fileId = "file123";
      const index = 5;
      const result = createVectorId(fileId, index);
      expect(result).toBe("file123-chunk_5");
    });
  });

  describe("getTableName", ()=>{
    it("should create a table name from whitelabel ID", ()=>{
      const whitelabelId = "wl123";
      const result = getTableName(whitelabelId);
      expect(result).toBe("d1_chunks_wl123");
    });
<<<<<<< HEAD
=======

    it("should replace hyphens with underscores in whitelabel ID", ()=>{
      const whitelabelId = "test-whitelabel";
      const result = getTableName(whitelabelId);
      expect(result).toBe("d1_chunks_test_whitelabel");
    });
>>>>>>> WA-170_MCP
  });

  describe("getRelevanceThreshold", ()=>{
    it("should return default threshold when no config is provided", ()=>{
      const result = getRelevanceThreshold();
      expect(result).toBe(0.3);
    });

    it("should return default threshold when config has no relevanceThreshold", ()=>{
      const result = getRelevanceThreshold({});
      expect(result).toBe(0.3);
    });

    it("should return configured threshold when provided", ()=>{
      const result = getRelevanceThreshold({ relevanceThreshold: 0.5 });
      expect(result).toBe(0.5);
    });
  });
});
