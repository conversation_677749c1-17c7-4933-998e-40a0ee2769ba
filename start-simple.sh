#!/bin/bash
# Simple startup script for GitHub Codespaces - mirrors manual startup process
# Uses development mode with hot reloading for all services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting Divinci AI services in Codespace...${NC}"

# Load environment variables
echo -e "${YELLOW}📋 Loading environment variables...${NC}"
set -a
for envfile in /workspaces/server/private-keys/local-fast/*.env; do
  if [ -f "$envfile" ]; then
    echo "  Loading $(basename "$envfile")"
    # shellcheck disable=SC1090
    . "$envfile"
  fi
done
set +a

# Install dependencies
echo -e "${YELLOW}📦 Installing dependencies...${NC}"
cd /workspaces/server
pnpm install --frozen-lockfile

# Create log directory
mkdir -p /workspaces/server/logs

# Function to start a service
start_service() {
  local service_name=$1
  local service_path=$2
  local npm_command=$3
  local port_env_var=$4
  local port_value=$5
  
  echo -e "${BLUE}🔧 Starting $service_name on port $port_value...${NC}"
  cd "$service_path"
  
  # Export the port environment variable
  export "$port_env_var"="$port_value"
  
  # Start the service in background
  pnpm run "$npm_command" > "/workspaces/server/logs/${service_name}.log" 2>&1 &
  local pid=$!
  
  # Store PID for later cleanup
  echo "$pid" > "/workspaces/server/logs/${service_name}.pid"
  
  echo -e "${GREEN}✅ $service_name started (PID: $pid)${NC}"
}

# Start all services
start_service "public-api" "/workspaces/server/workspace/servers/public-api" "start:dev" "PUBLIC_API_PORT" "9080"
start_service "public-api-live" "/workspaces/server/workspace/servers/public-api-live" "start:dev" "PUBLIC_API_LIVE_PORT" "8082"
start_service "public-api-webhook" "/workspaces/server/workspace/servers/public-api-webhook" "start:dev" "PUBLIC_API_WEBHOOK_PORT" "8083"

# Start web client (special handling for ENV_FOLDER)
echo -e "${BLUE}🔧 Starting web-client on port 8080...${NC}"
cd /workspaces/server/workspace/clients/web
export ENV_FOLDER="/workspaces/server/workspace/clients/web/env"
pnpm run webpack:codespace > /workspaces/server/logs/web-client.log 2>&1 &
WEB_CLIENT_PID=$!
echo "$WEB_CLIENT_PID" > /workspaces/server/logs/web-client.pid
echo -e "${GREEN}✅ web-client started (PID: $WEB_CLIENT_PID)${NC}"

# Return to workspace root
cd /workspaces/server

echo -e "${YELLOW}📊 Service status:${NC}"
echo "  🌐 Web Client:      https://sturdy-space-broccoli-g4xpjgv6376q-8080.app.github.dev/"
echo "  🔌 Public API:      https://sturdy-space-broccoli-g4xpjgv6376q-9080.app.github.dev/"
echo "  📡 Public API Live: https://sturdy-space-broccoli-g4xpjgv6376q-8082.app.github.dev/"
echo "  🪝 Webhook API:     https://sturdy-space-broccoli-g4xpjgv6376q-8083.app.github.dev/"

echo -e "${YELLOW}📋 Log files:${NC}"
echo "  tail -f /workspaces/server/logs/public-api.log"
echo "  tail -f /workspaces/server/logs/public-api-live.log"
echo "  tail -f /workspaces/server/logs/public-api-webhook.log"
echo "  tail -f /workspaces/server/logs/web-client.log"

# Health check function
check_health() {
  local port=$1
  local service_name=$2
  local protocol="http"
  
  # Web client uses HTTPS locally
  if [ "$port" = "8080" ]; then
    protocol="https"
  fi
  
  local local_url="${protocol}://localhost:${port}/"
  local codespace_url="https://sturdy-space-broccoli-g4xpjgv6376q-${port}.app.github.dev/"
  
  echo -n "  Checking $service_name locally... "
  
  # For HTTPS, ignore certificate issues with -k
  local curl_opts=""
  if [ "$protocol" = "https" ]; then
    curl_opts="-k"
  fi
  
  # Try with a longer timeout and retry once if it fails
  local status=$(curl -s -o /dev/null -w "%{http_code}" $curl_opts "$local_url" --max-time 10 --connect-timeout 5 2>/dev/null || echo "000")
  
  # If first attempt failed and it's the web client, try once more (webpack can be slow)
  if [ "$status" = "000" ] && [ "$port" = "8080" ]; then
    sleep 3
    status=$(curl -s -o /dev/null -w "%{http_code}" $curl_opts "$local_url" --max-time 10 --connect-timeout 5 2>/dev/null || echo "000")
  fi
  
  if [ "$status" = "200" ] || [ "$status" = "404" ]; then
    echo -e "${GREEN}✅ OK (HTTP $status)${NC}"
    echo "    🌐 Codespace URL: $codespace_url"
    return 0
  else
    echo -e "${RED}❌ Failed (HTTP $status)${NC}"
    echo "    🔧 Local URL: $local_url"
    echo "    🌐 Codespace URL: $codespace_url"
    echo "    💡 Note: Codespace URLs may require GitHub auth for private ports"
    return 1
  fi
}

# Wait for services to start
echo -e "${YELLOW}⏳ Waiting for services to start...${NC}"
sleep 20

# Run health checks
echo -e "${YELLOW}🏥 Running health checks...${NC}"
FAILED=0
check_health "8080" "Web Client" || FAILED=1
check_health "9080" "Public API" || FAILED=1
check_health "8082" "Public API Live" || FAILED=1
check_health "8083" "Webhook API" || FAILED=1

if [ $FAILED -eq 0 ]; then
  echo -e "${GREEN}🎉 All services are running successfully!${NC}"
  echo -e "${YELLOW}💡 Port Access Tips:${NC}"
  echo "  • Use localhost URLs for development testing (faster)"
  echo "  • Use Codespace URLs for external sharing" 
  echo "  • Configure port visibility in VS Code PORTS panel"
  echo "  • For API testing: Add 'X-Github-Token: \$GITHUB_TOKEN' header if needed"
else
  echo -e "${RED}⚠️  Some services may have issues. Check the logs above.${NC}"
fi

# Show stop command
echo -e "${YELLOW}🛑 To stop all services, run:${NC}"
echo "  ./stop-simple.sh"
echo
echo -e "${BLUE}📡 Configure port forwarding with:${NC}"
echo "  ./configure-ports.sh"
