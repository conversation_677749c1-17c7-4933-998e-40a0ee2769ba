/**
 * Handle Dropbox OAuth2 callback
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { dropboxOAuth, DropboxAuthUtils } from "@divinci-ai/server-globals";
import { DropboxConnectionModel } from "@divinci-ai/server-models";

interface CallbackRequest {
  code?: string;
  state?: string;
  error?: string;
  error_description?: string;
}

export const handleDropboxCallback: RequestHandler = async function(req, res, next) {
  try {
    const params: CallbackRequest = req.query as any;

    // Validate OAuth callback parameters
    const validation = DropboxAuthUtils.validateCallbackParams(params);
    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        error: "oauth_error",
        message: validation.error?.message || "Invalid OAuth callback parameters"
      });
    }

    // Handle OAuth callback
    const { tokenResponse, account, stateData } = await dropboxOAuth.handleCallback(
      params.code!,
      params.state!
    );

    console.log("🔗 [DROPBOX-CALLBACK] Token response received:", {
      hasAccessToken: !!tokenResponse.access_token,
      hasRefreshToken: !!tokenResponse.refresh_token,
      expiresIn: tokenResponse.expires_in,
      tokenType: tokenResponse.token_type
    });

    if (!stateData || !stateData.whitelabelId || !stateData.userId) {
      return res.status(400).json({
        success: false,
        error: "invalid_state",
        message: "Invalid OAuth state data"
      });
    }

    // Create or update connection in database
    const connection = await DropboxConnectionModel.createConnection(
      stateData.whitelabelId,
      stateData.userId,
      account.account_id,
      tokenResponse.access_token,
      {
        email: account.email,
        displayName: account.name.display_name,
        accountType: account.account_type[".tag"]
      },
      tokenResponse.refresh_token,
      tokenResponse.expires_in ? new Date(Date.now() + tokenResponse.expires_in * 1000) : undefined
    );

    console.log("🔗 [DROPBOX-CALLBACK] Connection successful, redirecting to frontend", {
      connectionId: connection._id.toString(),
      returnUrl: stateData.returnUrl,
      accountEmail: account.email
    });

    // Redirect back to the frontend with success parameters
    const redirectUrl = new URL(stateData.returnUrl);
    redirectUrl.searchParams.set('connected', 'true');
    redirectUrl.searchParams.set('connectionId', connection._id.toString());
    redirectUrl.searchParams.set('accountEmail', account.email);

    console.log("🔗 [DROPBOX-CALLBACK] Redirecting to:", redirectUrl.toString());
    res.redirect(302, redirectUrl.toString());

  } catch (error) {
    console.error("Dropbox OAuth callback error:", error);

    // Try to redirect back to frontend with error, fallback to JSON if no returnUrl
    const params: CallbackRequest = req.query as any;

    try {
      // Try to get the returnUrl from state if possible
      const { stateData } = await dropboxOAuth.handleCallback(params.code!, params.state!);
      if (stateData?.returnUrl) {
        const redirectUrl = new URL(stateData.returnUrl);
        redirectUrl.searchParams.set('error', 'connection_failed');
        redirectUrl.searchParams.set('message', error instanceof Error ? error.message : 'OAuth callback failed');
        return res.redirect(302, redirectUrl.toString());
      }
    } catch (stateError) {
      // If we can't get returnUrl, fall back to JSON response
      console.error("Could not extract returnUrl for error redirect:", stateError);
    }

    // Fallback to JSON response if redirect fails
    res.status(500).json({
      success: false,
      error: "oauth_callback_failed",
      message: error instanceof Error ? error.message : "OAuth callback failed"
    });
  }
};
