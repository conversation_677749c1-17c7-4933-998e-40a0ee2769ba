/**
 * Dropbox Files Router
 */

import { Router } from "express";

export const router = Router({ mergeParams: true });

// Import route handlers
import { listDropboxFolders } from "./list-folders";
import { listDropboxFiles } from "./list-files";
import { getDropboxFileMetadata } from "./file-metadata";
import { searchDropboxFiles } from "./search-files";

// File operation routes
router.get("/folders", listDropboxFolders);
router.get("/list", listDropboxFiles);
router.get("/metadata", getDropboxFileMetadata);
router.get("/search", searchDropboxFiles);
