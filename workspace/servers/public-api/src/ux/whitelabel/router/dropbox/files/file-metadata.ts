/**
 * Get detailed file metadata from Dropbox
 */

import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { ensureDropboxConnection, handleDropboxError } from "../util/ensure-whitelabel";
import { processDropboxFile, extractEpisodeInfo, formatFileSize, formatDuration } from "@divinci-ai/server-globals";

interface FileMetadataQuery {
  path?: string;
}

export const getDropboxFileMetadata: RequestHandler = async function(req, res, next) {
  try {
    const { connection, dropboxClient } = await ensureDropboxConnection(req);
    const { path }: FileMetadataQuery = req.query;

    if (!path) {
      return res.status(400).json({
        success: false,
        error: "missing_path",
        message: "File path is required"
      });
    }

    // Get file metadata from Dropbox
    const fileMetadata = await dropboxClient.getFileMetadata(path);

    // Process the file metadata
    const processedFile = processDropboxFile(fileMetadata);

    // Extract additional information for media files
    const episodeInfo = extractEpisodeInfo(processedFile.name);

    // Get temporary download link (valid for 4 hours)
    let downloadUrl;
    try {
      downloadUrl = await dropboxClient.getTemporaryLink(path);
    } catch (linkError) {
      console.warn("Failed to get temporary download link:", linkError);
    }

    const responseData = {
      ...processedFile,
      downloadUrl,
      episodeInfo,
      formattedSize: formatFileSize(processedFile.size),
      dropboxMetadata: {
        revision: fileMetadata.rev,
        contentHash: fileMetadata.content_hash,
        clientModified: fileMetadata.client_modified,
        serverModified: fileMetadata.server_modified,
        isDownloadable: fileMetadata.is_downloadable,
        hasExplicitSharedMembers: fileMetadata.has_explicit_shared_members
      }
    };

    // Add media info if available
    if (fileMetadata.media_info && fileMetadata.media_info[".tag"] === "metadata") {
      const mediaMetadata = fileMetadata.media_info.metadata;
      if (mediaMetadata) {
        (responseData as any).mediaInfo = {
          type: mediaMetadata[".tag"],
          dimensions: mediaMetadata.dimensions,
          location: mediaMetadata.location,
          timeTaken: mediaMetadata.time_taken
        };
      }
    }

    res.status(200).json({
      success: true,
      data: responseData
    });

  } catch (error) {
    try {
      await handleDropboxError(error, req.connection);
    } catch (handledError) {
      return next(handledError);
    }
    next(error);
  }
};
