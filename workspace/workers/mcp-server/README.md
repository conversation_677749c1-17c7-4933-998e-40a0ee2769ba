# Divinci AI MCP Server

Model Context Protocol server providing access to Divinci AI services through standardized tools and prompts.

## Features

- **Authentication**: Auth0 OAuth integration with JWT tokens
- **AI Chat Tools**: Create conversations, send messages, manage chats
- **Document Processing**: Upload, process, and search documents using RAG
- **Audio Processing**: Upload audio for transcription and speaker diarization
- **User Management**: Access user profiles and workspaces
- **Secure**: Scoped permissions and secure token handling

## Development

### Prerequisites

- Node.js 20+
- Wrangler CLI
- Auth0 account
- Divinci AI API access

### Setup

1. Install dependencies:
```bash
npm install
```

2. Configure environment variables:
```bash
# Create .dev.vars file
cp .dev.vars.example .dev.vars

# Edit .dev.vars with your configuration
```

3. Start development server:
```bash
npm run dev
```

The MCP server will be available at `http://localhost:8793/sse`

### Testing

Test with MCP Inspector:
```bash
npx @modelcontextprotocol/inspector@latest
```

Connect to: `http://localhost:8793/sse`

## Deployment

### Staging
```bash
npm run deploy:staging
```

### Production
```bash
npm run deploy:production
```

## Configuration

### Environment Variables

Set these via `wrangler secret put`:

- `AUTH0_DOMAIN`: Your Auth0 domain
- `AUTH0_CLIENT_ID`: Auth0 application client ID
- `AUTH0_CLIENT_SECRET`: Auth0 application client secret
- `DIVINCI_API_BASE_URL`: Base URL for Divinci API
- `DIVINCI_API_KEY`: API key for Divinci services
- `CLOUDFLARE_ACCOUNT_ID`: Your Cloudflare account ID

### Auth0 Setup

1. Create Auth0 application
2. Configure callback URLs:
   - Development: `http://localhost:8793/callback`
   - Production: `https://your-worker.workers.dev/callback`
3. Enable required scopes: `openid profile email`

## Available Tools

### AI Chat
- `create_chat`: Create new chat conversation
- `send_message`: Send message to chat
- `list_chats`: List user's chats

### Document Processing
- `upload_document`: Upload and process documents
- `search_documents`: Semantic document search

### Audio Processing
- `upload_audio`: Upload audio for processing

### User Management
- `get_user_profile`: Get user information
- `list_workspaces`: List accessible workspaces

## Integration with Claude Desktop

Add to your Claude Desktop configuration:

```json
{
  "mcpServers": {
    "divinci": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "https://your-mcp-server.workers.dev/sse"
      ]
    }
  }
}
```
