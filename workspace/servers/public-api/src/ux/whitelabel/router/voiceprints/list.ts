import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getWhitelabelTarget } from "../data-source/audio-transcript/util/whitelabel";
import { DataSourceAudioTranscriptModel } from "@divinci-ai/server-models";
import { listJobs, checkJobStatus } from "@divinci-ai/server-tools";

export const listVoiceprints: RequestHandler = async function(req, res, next) {
  try {
    const { target } = await getWhitelabelTarget(req);

    // Check if voiceprints are supported by checking the diarizer tools used
    const diarizerCheck = await DataSourceAudioTranscriptModel.findOne({
      target,
      "tools.diarizer": { $exists: true }
    }).select("tools.diarizer");

    const isDivinciPyannote = diarizerCheck?.tools?.diarizer === '@divinci-ai/pyannote-segmentation';

    if (isDivinciPyannote) {
      console.log("🚫 [VOICEPRINT] Voiceprints not supported with Divinci Pyannote");
      return res.status(200).json({
        success: true,
        data: {
          voiceprints: [],
          stats: { total: 0, ready: 0, processing: 0, failed: 0 },
          supported: false,
          reason: "Voiceprints are not available when using Divinci Pyannote. Please use Official Pyannote API for voiceprint features."
        }
      });
    }

    // Aggregate all voiceprints from all audio transcripts for this whitelabel
    const voiceprintData = await DataSourceAudioTranscriptModel.aggregate([
      {
        // 1. Filter by whitelabel target
        $match: { target }
      },
      {
        // 2. Only include documents that have voiceprints
        $match: { 
          voiceprints: { $exists: true, $ne: [], $not: { $size: 0 } }
        }
      },
      {
        // 3. Unwind the voiceprints array to get individual voiceprints
        $unwind: "$voiceprints"
      },
      {
        // 4. Project the voiceprint data with additional context
        $project: {
          _id: 0,
          voiceprintId: "$voiceprints.jobId",
          jobId: "$voiceprints.jobId",
          status: "$voiceprints.status",
          voiceprintData: "$voiceprints.voiceprintData",
          createdFrom: "$voiceprints.createdFrom",
          timestamp: "$voiceprints.timestamp",
          name: "$voiceprints.name",
          speakerLabel: "$voiceprints.speakerLabel",
          audioTranscriptId: "$_id",
          audioTranscriptTitle: "$userInfo.title",
          // Add computed fields for UI compatibility
          isReady: { $eq: ["$voiceprints.status", "succeeded"] },
          isProcessing: { 
            $in: ["$voiceprints.status", ["pending", "created", "running"]] 
          },
          isFailed: { 
            $in: ["$voiceprints.status", ["failed", "canceled"]] 
          },
          createdAt: { $toDate: "$voiceprints.timestamp" },
          updatedAt: { $toDate: "$voiceprints.timestamp" }
        }
      },
      {
        // 5. Sort by creation time (newest first)
        $sort: { timestamp: -1 }
      }
    ]);

    // Calculate statistics
    const stats = {
      total: voiceprintData.length,
      ready: voiceprintData.filter(vp => vp.isReady).length,
      processing: voiceprintData.filter(vp => vp.isProcessing).length,
      failed: voiceprintData.filter(vp => vp.isFailed).length,
    };

    console.log(`🎤 Found ${voiceprintData.length} voiceprints for whitelabel ${target}`);

    res.statusCode = 200;
    res.json({
      success: true,
      data: {
        voiceprints: voiceprintData,
        stats,
        supported: true
      }
    });
  } catch (e) {
    console.error("❌ Error listing voiceprints:", e);
    next(e);
  }
};

/**
 * List voiceprints from Pyannote API (live data)
 * GET /white-label/:whitelabelId/voiceprints/live
 */
export const listVoiceprintsLive: RequestHandler = async (req, res, next) => {
  try {
    console.log("🎤 [VOICEPRINT-LIST-LIVE] Fetching voiceprints from Pyannote API...");

    const { target } = await getWhitelabelTarget(req);

    // Get jobs from Pyannote API
    const pyannoteResponse = await listJobs({
      take: 100, // Get more jobs to find voiceprints
      status: undefined // Get all statuses
    });

    console.log(`📊 [VOICEPRINT-LIST-LIVE] Found ${pyannoteResponse.items.length} total jobs in Pyannote API`);

    // Filter for voiceprint jobs and enrich with our database info
    const voiceprintJobs = [];

    for (const job of pyannoteResponse.items) {
      // Check if this job exists in our database to get additional metadata
      const audioDoc = await DataSourceAudioTranscriptModel.findOne({
        target: target,
        "voiceprints.jobId": job.id
      }).select("voiceprints");

      let voiceprintData = null;
      if (audioDoc && audioDoc.voiceprints) {
        voiceprintData = audioDoc.voiceprints.find(vp => vp.jobId === job.id);
      }

      // If we have this job in our database, it's a voiceprint job
      if (voiceprintData) {
        voiceprintJobs.push({
          jobId: job.id,
          status: job.status, // Use Pyannote's status as source of truth
          createdAt: job.createdAt,
          // Merge our database info
          name: voiceprintData.name,
          speakerLabel: voiceprintData.speakerLabel,
          createdFrom: voiceprintData.createdFrom,
          timestamp: voiceprintData.timestamp,
          // Add Pyannote API info
          pyannoteStatus: job.status,
          pyannoteCreatedAt: job.createdAt
        });
      }
    }

    console.log(`🎤 [VOICEPRINT-LIST-LIVE] Found ${voiceprintJobs.length} voiceprint jobs`);

    // Sort by creation date (newest first)
    voiceprintJobs.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    res.status(200).json({
      success: true,
      data: {
        voiceprints: voiceprintJobs,
        total: voiceprintJobs.length,
        pyannoteTotal: pyannoteResponse.total,
        source: "pyannote-api"
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("❌ [VOICEPRINT-LIST-LIVE] Failed to list live voiceprints:", error);

    // Fallback to database-only listing if Pyannote API fails
    console.log("🔄 [VOICEPRINT-LIST-LIVE] Falling back to database listing...");
    return listVoiceprints(req, res, next);
  }
};
