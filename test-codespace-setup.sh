#!/bin/bash
# Unit test for codespace setup scripts
# Tests the CORS configuration and endpoint updates

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0

# Helper functions
print_test() {
  echo -e "${YELLOW}🧪 Testing: $1${NC}"
}

print_pass() {
  echo -e "${GREEN}✅ PASS: $1${NC}"
  TESTS_PASSED=$((TESTS_PASSED + 1))
}

print_fail() {
  echo -e "${RED}❌ FAIL: $1${NC}"
  TESTS_FAILED=$((TESTS_FAILED + 1))
}

print_summary() {
  echo ""
  echo "=========================================="
  echo -e "📊 Test Summary:"
  echo -e "   Passed: ${GREEN}$TESTS_PASSED${NC}"
  echo -e "   Failed: ${RED}$TESTS_FAILED${NC}"
  if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "   ${GREEN}🎉 All tests passed!${NC}"
  else
    echo -e "   ${RED}⚠️  Some tests failed.${NC}"
  fi
  echo "=========================================="
}

# Test 1: Check if required files exist
print_test "Required files exist"
REQUIRED_FILES=(
  "/workspaces/server/.devcontainer/postStart.sh"
  "/workspaces/server/start-codespace.sh"
  "/workspaces/server/private-keys/local-fast/cors.env"
)

for file in "${REQUIRED_FILES[@]}"; do
  if [ -f "$file" ]; then
    print_pass "File exists: $(basename "$file")"
  else
    print_fail "Missing file: $file"
  fi
done

# Test 2: Check CORS configuration format
print_test "CORS configuration format"
CORS_FILE="/workspaces/server/private-keys/local-fast/cors.env"

if [ -f "$CORS_FILE" ]; then
  # Check that CORS_FULL_ORIGINS doesn't contain https:// prefixes for GitHub codespace URLs
  if grep -q "https://.*\.app\.github\.dev" "$CORS_FILE"; then
    print_fail "CORS configuration contains https:// prefixes for codespace URLs"
  else
    print_pass "CORS configuration correctly uses hostnames without https:// prefixes"
  fi
  
  # Check that CORS_FULL_ORIGINS line exists
  if grep -q "^CORS_FULL_ORIGINS=" "$CORS_FILE"; then
    print_pass "CORS_FULL_ORIGINS configuration exists"
  else
    print_fail "CORS_FULL_ORIGINS configuration missing"
  fi
else
  print_fail "CORS configuration file not found"
fi

# Test 3: Test CORS update function (dry run simulation)
print_test "CORS update function simulation"

# Create a temporary test environment
TEST_DIR=$(mktemp -d)
TEST_CORS_FILE="$TEST_DIR/cors.env"

# Create a test CORS file
cat > "$TEST_CORS_FILE" << 'EOF'
CORS_SUBDOMAINS="chat,api,live,webhook"
CORS_PORTS="8080,8081,8082,8083,9080"
CORS_FULL_ORIGINS="api.slack.com,localhost:8080,localhost:9080"
EOF

# Simulate the CORS update function
TEST_CODESPACE_NAME="test-codespace-abc123"
CORS_PORTS="8080,8081,8082,8083,9080"

# Build test origins
CODESPACE_ORIGINS=""
for port in ${CORS_PORTS//,/ }; do
  if [ -n "$CODESPACE_ORIGINS" ]; then
    CODESPACE_ORIGINS="$CODESPACE_ORIGINS,"
  fi
  CODESPACE_ORIGINS="$CODESPACE_ORIGINS$TEST_CODESPACE_NAME-$port.app.github.dev"
done

# Read current origins
CURRENT_ORIGINS=$(grep "^CORS_FULL_ORIGINS=" "$TEST_CORS_FILE" | cut -d'=' -f2- | tr -d '"')

# Clean and add new origins
CLEANED_ORIGINS=$(echo "$CURRENT_ORIGINS" | sed 's/,[^,]*\.app\.github\.dev[^,]*//g' | sed 's/^[^,]*\.app\.github\.dev[^,]*,//g' | sed 's/^[^,]*\.app\.github\.dev[^,]*$//g')

if [ -n "$CLEANED_ORIGINS" ]; then
  NEW_ORIGINS="$CLEANED_ORIGINS,$CODESPACE_ORIGINS"
else
  NEW_ORIGINS="$CODESPACE_ORIGINS"
fi

# Update the test file
sed -i "s|^CORS_FULL_ORIGINS=.*|CORS_FULL_ORIGINS=\"$NEW_ORIGINS\"|" "$TEST_CORS_FILE"

# Verify the update
UPDATED_ORIGINS=$(grep "^CORS_FULL_ORIGINS=" "$TEST_CORS_FILE" | cut -d'=' -f2- | tr -d '"')

# Check that all expected codespace origins are present
EXPECTED_PORTS=(8080 8081 8082 8083 9080)
ALL_PORTS_PRESENT=true

for port in "${EXPECTED_PORTS[@]}"; do
  EXPECTED_ORIGIN="$TEST_CODESPACE_NAME-$port.app.github.dev"
  if echo "$UPDATED_ORIGINS" | grep -q "$EXPECTED_ORIGIN"; then
    print_pass "Codespace origin present: $EXPECTED_ORIGIN"
  else
    print_fail "Codespace origin missing: $EXPECTED_ORIGIN"
    ALL_PORTS_PRESENT=false
  fi
done

# Check that no https:// prefixes were added
if echo "$UPDATED_ORIGINS" | grep -q "https://"; then
  print_fail "HTTPS prefixes found in updated CORS origins"
else
  print_pass "No HTTPS prefixes in CORS origins (hostnames only)"
fi

# Check that original non-codespace origins are preserved
if echo "$UPDATED_ORIGINS" | grep -q "api.slack.com"; then
  print_pass "Original CORS origins preserved"
else
  print_fail "Original CORS origins lost during update"
fi

# Cleanup
rm -rf "$TEST_DIR"

# Test 4: Check script functions don't contain https:// in CORS origins
print_test "Script CORS origin generation"

POSTSTART_SCRIPT="/workspaces/server/.devcontainer/postStart.sh"
START_SCRIPT="/workspaces/server/start-codespace.sh"

# Check that the scripts don't generate https:// prefixed CORS origins
if grep -q 'CODESPACE_ORIGINS=".*https://' "$POSTSTART_SCRIPT" "$START_SCRIPT"; then
  print_fail "Scripts still generate CORS origins with https:// prefixes"
else
  print_pass "Scripts generate CORS origins without https:// prefixes"
fi

# Test 5: Check that scripts are executable
print_test "Script executability"

for script in "$POSTSTART_SCRIPT" "$START_SCRIPT"; do
  if [ -x "$script" ]; then
    print_pass "Script is executable: $(basename "$script")"
  else
    print_fail "Script is not executable: $(basename "$script")"
  fi
done

# Test 6: Syntax validation
print_test "Script syntax validation"

for script in "$POSTSTART_SCRIPT" "$START_SCRIPT"; do
  if bash -n "$script" 2>/dev/null; then
    print_pass "Script syntax valid: $(basename "$script")"
  else
    print_fail "Script syntax error: $(basename "$script")"
  fi
done

# Test 7: Check environment variable usage
print_test "Environment variable usage"

# Check that scripts properly use CODESPACE_NAME
if grep -q 'CODESPACE_NAME' "$POSTSTART_SCRIPT" "$START_SCRIPT"; then
  print_pass "Scripts use CODESPACE_NAME environment variable"
else
  print_fail "Scripts don't use CODESPACE_NAME environment variable"
fi

print_summary

# Exit with appropriate code
if [ $TESTS_FAILED -eq 0 ]; then
  exit 0
else
  exit 1
fi
