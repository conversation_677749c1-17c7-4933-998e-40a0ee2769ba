/**
 * Update sync job status and progress (called by worker)
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { DropboxSyncJobModel } from "@divinci-ai/server-models";
import { ensureWhitelabel } from "../util/ensure-whitelabel";

interface UpdateJobRequest {
  jobId: string,
  status?: string,
  progress?: {
    processedFiles?: number,
    failedFiles?: number,
    totalFiles?: number,
    transferredBytes?: number,
    currentFile?: string,
    estimatedTimeRemaining?: number,
    averageSpeed?: number,
  },
  errors?: string[],
  completedAt?: string,
  updatedAt: string,
}

export const updateDropboxSyncJob: RequestHandler = async function(req, res, next){
  try {
    const { whitelabel } = await ensureWhitelabel(req);
    const {
      jobId,
      status,
      progress,
      errors,
      completedAt,
      updatedAt
    }: UpdateJobRequest = req.body;

    if(!jobId) {
      return res.status(400).json({
        success: false,
        error: "missing_job_id",
        message: "Job ID is required"
      });
    }

    // Find the sync job
    const job = await DropboxSyncJobModel.findById(jobId);

    if(!job) {
      return res.status(404).json({
        success: false,
        error: "job_not_found",
        message: "Sync job not found"
      });
    }

    // Verify job belongs to this whitelabel
    if(job.whitelabelId !== whitelabel._id.toString()) {
      return res.status(403).json({
        success: false,
        error: "access_denied",
        message: "Access denied to this sync job"
      });
    }

    // Update job status if provided
    if(status) {
      if(status === "completed") {
        await job.complete();
      } else if(status === "failed") {
        const errorMessage = errors && errors.length > 0 ? errors[0] : "Job failed";
        await job.fail(errorMessage);
      } else {
        job.status = status as any;
      }
    }

    // Update progress if provided
    if(progress) {
      await job.updateProgress(progress);
    }

    // Add errors if provided
    if(errors && errors.length > 0) {
      for(const error of errors) {
        await job.addError(error);
      }
    }

    // Update completion time if provided
    if(completedAt) {
      job.completedAt = new Date(completedAt);
    }

    // Save the job
    await job.save();

    res.status(200).json({
      success: true,
      data: {
        jobId,
        status: job.status,
        progress: job.progress,
        updatedAt
      },
      message: "Sync job updated successfully"
    });

  }catch(error) {
    console.error("Update sync job error:", error);
    next(error);
  }
};
