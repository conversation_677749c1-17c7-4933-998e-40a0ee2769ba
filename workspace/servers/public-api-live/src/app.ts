
import { IncomingMessage, Server, ServerResponse } from "http";
import { WebSocketServer } from "ws";

import { setupDBs } from "./setup/database";
import { setupHttpApp } from "./setup/http";
import { setupWsApp, wsHandleError } from "./setup/websocket";

import { httpUpgradeToWSRequest } from "@divinci-ai/server-utils";

Promise.resolve().then(async ()=>{
  await setupDBs();
  const httpApp = await setupHttpApp();
  const wsApp = await setupWsApp();

  if (httpsEnabled) {
    const sslCertPath = process.env.SSL_CERT_PATH;
    const sslKeyPath = process.env.SSL_KEY_PATH;

  const wsServer = new WebSocketServer({ noServer: true });

  server.on("request", function(req: IncomingMessage, res: ServerResponse){
    console.log("🌐 url: ", req.url);
    // https://stackoverflow.com/questions/8107856/how-to-determine-a-users-ip-address-in-node
    // issue is that it seems docker always returns a docker address
    console.log("🪪 remote address:  ", req.socket.remoteAddress);
    httpApp(req, res);
  });

  server.on("upgrade", function(req: IncomingMessage, socket, head){
    try {
      const wsReq = httpUpgradeToWSRequest(wsServer, req, socket, head);
      wsApp.handleRequest(wsReq, (reason)=>{
        wsHandleError(wsReq, reason);
      });
    }catch(error) {
      console.error("Error handling WebSocket upgrade:", error);
    }

    try {
      const cert = readFileSync(sslCertPath, "utf8");
      const key = readFileSync(sslKeyPath, "utf8");

}).catch(error=>{
  console.error("🔥 Server initialization error: ", error);
});
