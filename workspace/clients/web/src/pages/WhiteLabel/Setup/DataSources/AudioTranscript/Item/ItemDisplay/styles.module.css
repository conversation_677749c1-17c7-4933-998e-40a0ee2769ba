/* Dark mode specific styles */
.darkModeContainer {
  color: #fff;
}

.darkModeContainer .label {
  color: #fff;
}

.darkModeContainer .box {
  background-color: #2b2b2b;
  color: #fff;
}

.darkModeContainer .card {
  background-color: #2b2b2b;
}

.darkModeContainer .card-header-title {
  color: #fff;
}

.darkModeContainer .title,
.darkModeContainer .subtitle {
  color: #fff;
}

.darkModeContainer a {
  color: #3e8ed0;
}

.darkModeContainer .transcript-text {
  background-color: #2b2b2b;
  color: #fff;
  border: 1px solid #4a4a4a;
}

/* Audio player styling */
.audioPlayer {
  margin: 1rem 0;
}

.audioPlayer audio {
  width: 100%;
  max-width: 500px;
}

/* Transcript styling */
.transcriptText {
  padding: 1rem;
  border-radius: 4px;
  line-height: 1.5;
  white-space: pre-wrap;
}

/* Speaker label styling */
.speakerLabel {
  font-weight: bold;
}

/* Time tag styling */
.timeTag {
  font-family: monospace;
}

<<<<<<< HEAD
=======
/* Confidence score styling */
.lowConfidenceTranscript {
  border-left: 4px solid #ff9f43 !important;
  background-color: #fff8f0 !important;
}

.darkModeContainer .lowConfidenceTranscript {
  border-left: 4px solid #ff9f43 !important;
  background-color: #3d2f1f !important;
}

.lowConfidenceBox {
  border-left: 4px solid #ff9f43;
  background-color: #fff8f0;
}

.darkModeContainer .lowConfidenceBox {
  border-left: 4px solid #ff9f43;
  background-color: #3d2f1f;
}

.lowConfidenceText {
  color: #d68910 !important;
  font-weight: 600;
}

.darkModeContainer .lowConfidenceText {
  color: #ff9f43 !important;
}

.reviewTag {
  font-weight: 600;
}

.confidenceBar {
  margin-top: 0.5rem;
}

.lowConfidenceWarning {
  border-left: 4px solid #ff9f43;
}

.darkModeContainer .lowConfidenceWarning {
  background-color: #3d2f1f;
  color: #ff9f43;
}

>>>>>>> WA-170_MCP
/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .sampleContainer .columns {
    display: block;
  }
<<<<<<< HEAD
  
=======

>>>>>>> WA-170_MCP
  .sampleContainer .column {
    width: 100%;
    margin-bottom: 1rem;
  }
}
