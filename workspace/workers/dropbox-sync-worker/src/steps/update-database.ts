/**
 * Update database with final sync results
 */

// WorkflowStep type (would be imported from cloudflare:workers in real deployment)
type WorkflowStep = any;
import { 
  StepContext, 
  UpdateDatabaseResult,
  DropboxSyncProcessingConfig,
  AudioProcessingResult,
  FileDownloadResult,
  ChunksWorkflowResult,
  DropboxFileProcessingStatus,
  DropboxSyncJobStatus
} from "./types";

export async function updateDatabase(
  step: WorkflowStep,
  context: StepContext,
  processedFiles: (AudioProcessingResult & {
    dropboxFileId: string;
    fileName: string;
    downloadResult: FileDownloadResult;
  })[],
  triggeredWorkflows: (ChunksWorkflowResult & {
    dropboxFileId: string;
    fileName: string;
  })[],
  failedFiles: { dropboxFileId: string; fileName: string; error: string }[],
  totalFiles: number
): Promise<UpdateDatabaseResult> {
  return await step.do("updateDatabase", async () => {
    console.log("💾 Starting database updates", {
      processedFiles: processedFiles.length,
      triggeredWorkflows: triggeredWorkflows.length,
      failedFiles: failedFiles.length,
      totalFiles
    });

    const failedUpdates: string[] = [];
    let updatedFileMappings = 0;

    // Update file mappings for successfully processed files
    for (const file of processedFiles) {
      try {
        await updateFileMapping(context, file, triggeredWorkflows);
        await updateFileStatus(context, file.dropboxFileId, DropboxFileProcessingStatus.COMPLETED);
        updatedFileMappings++;
      } catch (error) {
        console.error(`Failed to update file mapping for ${file.fileName}:`, error);
        failedUpdates.push(`File mapping update failed for ${file.fileName}: ${error}`);
      }
    }

    // Update sync job status
    let updatedSyncJob = false;
    try {
      const completedFiles = processedFiles.length;
      const totalFailedFiles = failedFiles.length;
      const jobStatus = totalFailedFiles === totalFiles ? 
        DropboxSyncJobStatus.FAILED : 
        (completedFiles + totalFailedFiles === totalFiles ? 
          DropboxSyncJobStatus.COMPLETED : 
          DropboxSyncJobStatus.RUNNING);

      await updateSyncJobStatus(context, {
        status: jobStatus,
        progress: {
          processedFiles: completedFiles,
          failedFiles: totalFailedFiles,
          totalFiles: totalFiles
        },
        completedAt: jobStatus === DropboxSyncJobStatus.COMPLETED || jobStatus === DropboxSyncJobStatus.FAILED ? 
          new Date().toISOString() : undefined
      });

      updatedSyncJob = true;
    } catch (error) {
      console.error("Failed to update sync job status:", error);
      failedUpdates.push(`Sync job update failed: ${error}`);
    }

    console.log("💾 Database updates completed", {
      updatedSyncJob,
      updatedFileMappings,
      failedUpdates: failedUpdates.length
    });

    return {
      updatedSyncJob,
      updatedFileMappings,
      failedUpdates
    };
  });
}

/**
 * Update file mapping with processing results
 */
async function updateFileMapping(
  context: StepContext,
  file: AudioProcessingResult & {
    dropboxFileId: string;
    fileName: string;
    downloadResult: FileDownloadResult;
  },
  triggeredWorkflows: (ChunksWorkflowResult & {
    dropboxFileId: string;
    fileName: string;
  })[]
): Promise<void> {
  console.log(`💾 Updating file mapping for: ${file.fileName}`);

  // Find corresponding workflow result
  const workflowResult = triggeredWorkflows.find(w => w.dropboxFileId === file.dropboxFileId);

  const response = await fetch(`${context.env.API_HOST}/ux/whitelabel/${context.whitelabelId}/dropbox/sync/update-file-mapping`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${context.auth0Token}`,
      "cloudflare-worker-x-dev-auth": context.env.CLOUDFLARE_WORKER_X_AUTH_DEV || ""
    },
    body: JSON.stringify({
      dropboxFileId: file.dropboxFileId,
      internalFileId: workflowResult?.fileId,
      syncStatus: "completed",
      processingMetadata: {
        transcriptionId: file.transcriptionId,
        diarizationId: file.diarizationId,
        workflowId: workflowResult?.workflowId,
        chunkCount: workflowResult?.chunkCount || 0,
        vectorCount: workflowResult?.vectorCount || 0,
        processingDuration: file.processingDuration,
        r2ObjectKey: file.downloadResult.objectKey,
        r2Url: file.downloadResult.r2Url,
        processedAt: new Date().toISOString()
      }
    })
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`File mapping update failed: ${response.status} ${errorText}`);
  }

  const result = await response.json();
  
  if (!result.success) {
    throw new Error(`File mapping update failed: ${result.error || "Unknown error"}`);
  }

  console.log(`✅ File mapping updated for: ${file.fileName}`);
}

/**
 * Update sync job status and progress
 */
async function updateSyncJobStatus(
  context: StepContext,
  updates: {
    status?: DropboxSyncJobStatus;
    progress?: {
      processedFiles?: number;
      failedFiles?: number;
      totalFiles?: number;
    };
    completedAt?: string;
  }
): Promise<void> {
  console.log("💾 Updating sync job status", { jobId: context.jobId, updates });

  const response = await fetch(`${context.env.API_HOST}/ux/whitelabel/${context.whitelabelId}/dropbox/sync/update-job`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${context.auth0Token}`,
      "cloudflare-worker-x-dev-auth": context.env.CLOUDFLARE_WORKER_X_AUTH_DEV || ""
    },
    body: JSON.stringify({
      jobId: context.jobId,
      ...updates,
      updatedAt: new Date().toISOString()
    })
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Sync job update failed: ${response.status} ${errorText}`);
  }

  const result = await response.json();
  
  if (!result.success) {
    throw new Error(`Sync job update failed: ${result.error || "Unknown error"}`);
  }

  console.log("✅ Sync job status updated");
}

/**
 * Update individual file processing status
 */
async function updateFileStatus(
  context: StepContext,
  dropboxFileId: string,
  status: DropboxFileProcessingStatus,
  errorMessage?: string
): Promise<void> {
  try {
    const response = await fetch(`${context.env.API_HOST}/ux/whitelabel/${context.whitelabelId}/dropbox/sync/update-file-status`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${context.auth0Token}`,
        "cloudflare-worker-x-dev-auth": context.env.CLOUDFLARE_WORKER_X_AUTH_DEV || ""
      },
      body: JSON.stringify({
        jobId: context.jobId,
        dropboxFileId,
        status,
        errorMessage,
        timestamp: new Date().toISOString()
      })
    });

    if (!response.ok) {
      console.warn(`Failed to update file status: ${response.status}`);
    }
  } catch (error) {
    console.warn("Failed to update file status:", error);
    // Don't throw - this is a non-critical operation
  }
}
