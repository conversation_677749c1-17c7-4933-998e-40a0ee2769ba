<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Divinci API Test</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
      }
      .container {
        max-width: 800px;
        margin: 0 auto;
      }
      .test-section {
        margin: 20px 0;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 5px;
      }
      .success {
        background-color: #d4edda;
        border-color: #c3e6cb;
      }
      .error {
        background-color: #f8d7da;
        border-color: #f5c6cb;
      }
      .loading {
        background-color: #d1ecf1;
        border-color: #bee5eb;
      }
      button {
        padding: 10px 20px;
        margin: 5px;
        cursor: pointer;
      }
      pre {
        background: #f8f9fa;
        padding: 10px;
        border-radius: 3px;
        overflow-x: auto;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🚀 Divinci API Test Page</h1>
      <p>
        This page will test connectivity to the Divinci APIs in the GitHub
        Codespace environment.
      </p>

      <div class="test-section" id="env-info">
        <h3>🔍 Environment Information</h3>
        <div id="env-details">Loading...</div>
      </div>

      <div class="test-section" id="api-test">
        <h3>🌐 Public API Test</h3>
        <button onclick="testPublicAPI()">Test Public API</button>
        <div id="api-result">Click to test API connectivity</div>
      </div>

      <div class="test-section" id="api-live-test">
        <h3>📡 Public API Live Test</h3>
        <button onclick="testPublicAPILive()">Test Public API Live</button>
        <div id="api-live-result">Click to test API Live connectivity</div>
      </div>

      <div class="test-section" id="webhook-test">
        <h3>🪝 Webhook API Test</h3>
        <button onclick="testWebhookAPI()">Test Webhook API</button>
        <div id="webhook-result">Click to test Webhook API connectivity</div>
      </div>
    </div>

    <script>
      // Environment detection
      function updateEnvironmentInfo() {
        const info = {
          "User Agent": navigator.userAgent,
          "Current URL": window.location.href,
          Protocol: window.location.protocol,
          Host: window.location.host,
          Timestamp: new Date().toISOString(),
        };

        const envHtml = Object.entries(info)
          .map(([key, value]) => `<strong>${key}:</strong> ${value}`)
          .join("<br>");

        document.getElementById("env-details").innerHTML = envHtml;
      }

      // API testing functions
      async function testAPI(url, resultElementId, apiName) {
        const resultElement = document.getElementById(resultElementId);
        resultElement.className = "loading";
        resultElement.innerHTML = `<p>🔄 Testing ${apiName}...</p>`;

        try {
          const response = await fetch(url, {
            method: "GET",
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
            },
          });

          const responseText = await response.text();
          let responseData;

          try {
            responseData = JSON.parse(responseText);
          } catch {
            responseData = responseText;
          }

          if (response.ok) {
            resultElement.className = "success";
            resultElement.innerHTML = `
                        <p>✅ ${apiName} is responding!</p>
                        <p><strong>Status:</strong> ${response.status} ${
              response.statusText
            }</p>
                        <p><strong>URL:</strong> ${url}</p>
                        <pre>${JSON.stringify(responseData, null, 2)}</pre>
                    `;
          } else {
            resultElement.className = "error";
            resultElement.innerHTML = `
                        <p>⚠️ ${apiName} responded with error</p>
                        <p><strong>Status:</strong> ${response.status} ${
              response.statusText
            }</p>
                        <p><strong>URL:</strong> ${url}</p>
                        <pre>${JSON.stringify(responseData, null, 2)}</pre>
                    `;
          }
        } catch (error) {
          resultElement.className = "error";
          resultElement.innerHTML = `
                    <p>❌ Failed to connect to ${apiName}</p>
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p><strong>URL:</strong> ${url}</p>
                `;
        }
      }

      function testPublicAPI() {
        const apiUrl =
          "https://sturdy-space-broccoli-g4xpjgv6376q-9080.app.github.dev/";
        testAPI(apiUrl, "api-result", "Public API");
      }

      function testPublicAPILive() {
        const apiUrl =
          "https://sturdy-space-broccoli-g4xpjgv6376q-8082.app.github.dev/";
        testAPI(apiUrl, "api-live-result", "Public API Live");
      }

      function testWebhookAPI() {
        const apiUrl =
          "https://sturdy-space-broccoli-g4xpjgv6376q-8083.app.github.dev/";
        testAPI(apiUrl, "webhook-result", "Webhook API");
      }

      // Initialize page
      updateEnvironmentInfo();
    </script>
  </body>
</html>
