/**
 * Update file processing status (called by worker)
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { DropboxSyncJobModel } from "@divinci-ai/server-models";
import { ensureWhitelabel } from "../util/ensure-whitelabel";

interface UpdateFileStatusRequest {
  jobId: string,
  dropboxFileId: string,
  status: string,
  errorMessage?: string,
  transferredBytes?: number,
  timestamp: string,
}

export const updateDropboxFileStatus: RequestHandler = async function(req, res, next){
  try {
    const { whitelabel } = await ensureWhitelabel(req);
    const {
      jobId,
      dropboxFileId,
      status,
      errorMessage,
      transferredBytes,
      timestamp
    }: UpdateFileStatusRequest = req.body;

    if(!jobId || !dropboxFileId || !status) {
      return res.status(400).json({
        success: false,
        error: "missing_required_fields",
        message: "Job ID, Dropbox file ID, and status are required"
      });
    }

    // Find the sync job
    const job = await DropboxSyncJobModel.findById(jobId);

    if(!job) {
      return res.status(404).json({
        success: false,
        error: "job_not_found",
        message: "Sync job not found"
      });
    }

    // Verify job belongs to this whitelabel
    if(job.whitelabelId !== whitelabel._id.toString()) {
      return res.status(403).json({
        success: false,
        error: "access_denied",
        message: "Access denied to this sync job"
      });
    }

    // Update file status
    await job.updateFileStatus(
      dropboxFileId,
      status as any,
      errorMessage,
      undefined // internalFileId will be set later
    );

    res.status(200).json({
      success: true,
      data: {
        jobId,
        dropboxFileId,
        status,
        updatedAt: timestamp
      },
      message: "File status updated successfully"
    });

  }catch(error) {
    console.error("Update file status error:", error);
    next(error);
  }
};
