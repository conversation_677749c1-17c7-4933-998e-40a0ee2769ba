"""
S3 client module for connecting to MinIO or other S3-compatible storage
This module handles proper authentication and URL resolution for connecting
to MinIO from the OpenParse service in various network environments.
"""

import os
import logging
import boto3
from botocore.config import Config
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

def get_s3_client(endpoint_url=None):
    """Creates and returns an S3 client configured for MinIO access."""
<<<<<<< HEAD
    # Get credentials from environment if available
    # First try R2 credentials (which might be set for production)
    access_key = os.environ.get('R2_ACCESS_KEY_ID', None)
    secret_key = os.environ.get('R2_SECRET_ACCESS_KEY', None)

    # If R2 credentials aren't set, fall back to MinIO credentials
    if not access_key or not secret_key:
        # Try both the new open-parse-user credentials and the default minioadmin credentials
        # This ensures compatibility with both configurations
        access_key_options = [
            'open-parse-user',  # New dedicated user with specific permissions
            os.environ.get('MINIO_ROOT_USER', 'minioadmin'),  # From environment or default
            'minioadmin'  # Default fallback
        ]
        secret_key_options = [
            'openparsepassword',  # New dedicated user password
            os.environ.get('MINIO_ROOT_PASSWORD', 'minioadmin'),  # From environment or default
            'minioadmin'  # Default fallback
        ]

        # Use the first set of credentials by default
        access_key = access_key_options[0]
        secret_key = secret_key_options[0]

        logger.info(f"Using MinIO credentials: {access_key[:4]}.../{secret_key[:4]}...")
    else:
        logger.info(f"Using R2 credentials: {access_key[:4]}.../{secret_key[:4]}...")

    # Get endpoint URL from argument or environment
    if not endpoint_url:
        # Use the reliable minio.divinci.local hostname
        minio_host = 'minio.divinci.local'
        minio_port = os.environ.get('MINIO_PORT', '9000')
        endpoint_url = f"http://{minio_host}:{minio_port}"

    logger.info(f"Creating S3 client with endpoint URL: {endpoint_url}")
=======
    # Get endpoint URL from argument or environment
    if not endpoint_url:
        # Use MINIO_HOST from environment (localhost for open-parse service)
        minio_host = os.environ.get('MINIO_HOST', 'localhost')
        minio_port = os.environ.get('MINIO_PORT', '9000')
        endpoint_url = f"http://{minio_host}:{minio_port}"

    # Check if we should force R2 storage even in local environment
    force_r2_storage = os.environ.get("FORCE_R2_STORAGE", "false").lower() == "true"

    # Determine if we're connecting to a local MinIO instance
    is_local_minio = (endpoint_url and
                      ('host.docker.internal' in endpoint_url or
                       'local-minio' in endpoint_url or
                       'minio.divinci.local' in endpoint_url or
                       'localhost' in endpoint_url or
                       '127.0.0.1' in endpoint_url))

    # Choose credentials based on the endpoint and force flag
    # If FORCE_R2_STORAGE is true, treat it as NOT local (use R2 credentials and no endpoint)
    if is_local_minio and not force_r2_storage:
        # For local MinIO, always use MinIO credentials
        access_key = os.environ.get("MINIO_ROOT_USER", "minioadmin")
        secret_key = os.environ.get("MINIO_ROOT_PASSWORD", "minioadmin")
        logger.info(f"Using MinIO credentials for local endpoint: {access_key[:4]}.../{secret_key[:4]}...")
    else:
        # For remote endpoints, try R2 credentials first, then fall back to MinIO
        access_key = os.environ.get('R2_ACCESS_KEY_ID', None)
        secret_key = os.environ.get('R2_SECRET_ACCESS_KEY', None)

        if not access_key or not secret_key:
            access_key = os.environ.get("MINIO_ROOT_USER", "minioadmin")
            secret_key = os.environ.get("MINIO_ROOT_PASSWORD", "minioadmin")
            logger.info(f"Using MinIO credentials as fallback: {access_key[:4]}.../{secret_key[:4]}...")
        else:
            logger.info(f"Using R2 credentials: {access_key[:4]}.../{secret_key[:4]}...")

    # If forcing R2 storage, use the Cloudflare R2 endpoint
    if force_r2_storage:
        cloudflare_account_id = os.environ.get("CLOUDFLARE_ACCOUNT_ID", "4a6fa23390363382f378b5bd4a0f849")
        endpoint_url = f"https://{cloudflare_account_id}.r2.cloudflarestorage.com"
        logger.info(f"🌐💛 Creating S3 client for Cloudflare R2: {endpoint_url}")
    else:
        logger.info(f"🌐💛 Creating S3 client with endpoint URL: {endpoint_url}")

    logger.info(f"🌐💛 Creating S3 client with access_key and secret_key: {access_key[:4]}.../{secret_key[:4]}...")
>>>>>>> WA-170_MCP

    # Create and return the S3 client with v4 signature
    try:
        client = boto3.client(
            's3',
            endpoint_url=endpoint_url,
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            config=Config(signature_version='s3v4'),
            region_name='us-east-1',  # Dummy region for signature
            verify=False  # Skip SSL verification for local development
        )

        # Test the client with a simple operation
        try:
            client.list_buckets()
            logger.info("✅ S3 client successfully connected to MinIO")
        except Exception as e:
            logger.warning(f"⚠️ S3 client test failed: {str(e)}")

        return client

    except Exception as e:
        logger.error(f"❌ Failed to create S3 client: {str(e)}")
        raise

def find_working_endpoint():
    """
<<<<<<< HEAD
    Returns a working S3 client using the reliable minio.divinci.local endpoint.
    """
    minio_port = os.environ.get('MINIO_PORT', '9000')
    endpoint = f"http://minio.divinci.local:{minio_port}"

    logger.info(f"Using reliable S3 endpoint: {endpoint}")
=======
    Returns a working S3 client using the configured MinIO endpoint.
    """
    minio_host = os.environ.get('MINIO_HOST', 'localhost')
    minio_port = os.environ.get('MINIO_PORT', '9000')
    endpoint = f"http://{minio_host}:{minio_port}"

    logger.info(f"Using configured S3 endpoint: {endpoint}")
>>>>>>> WA-170_MCP

    # Get a working S3 client
    client = get_s3_client(endpoint)
    return client, endpoint

def extract_bucket_key_from_url(url):
    """Extracts bucket name and object key from a MinIO URL."""
    try:
        parsed = urlparse(url)
        path_parts = parsed.path.strip('/').split('/', 1)

        if len(path_parts) < 2:
            logger.error(f"Invalid MinIO URL format: {url}")
            return None, None

        bucket = path_parts[0]
        key = path_parts[1]

        return bucket, key
    except Exception as e:
        logger.error(f"Error parsing MinIO URL {url}: {str(e)}")
        return None, None

def download_file_from_s3_url(url, local_path):
    """Downloads a file from S3 given a URL."""
    # Extract bucket and key from URL
    bucket, key = extract_bucket_key_from_url(url)
    if not bucket or not key:
        raise ValueError(f"Could not parse bucket and key from URL: {url}")

    # Get a working S3 client
    client = get_s3_client()

    # Log download attempt
    logger.info(f"Downloading from S3: bucket={bucket}, key={key} to {local_path}")

    try:
        # Download the file
        client.download_file(bucket, key, local_path)
        logger.info(f"✅ Successfully downloaded {url} to {local_path}")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to download from S3: {str(e)}")
        raise

def upload_file_to_s3(local_path, bucket, key):
    """Uploads a file to S3."""
    # Get a working S3 client
    client, endpoint = find_working_endpoint()

    # Log upload attempt
    logger.info(f"Uploading to S3: {local_path} to bucket={bucket}, key={key}")

    try:
        # Upload the file
        client.upload_file(
            local_path,
            bucket,
            key,
            ExtraArgs={'ACL': 'public-read'}  # Make the object publicly readable
        )
        logger.info(f"✅ Successfully uploaded {local_path} to s3://{bucket}/{key}")

        # Generate a URL for the uploaded file
        url = f"{endpoint}/{bucket}/{key}"
        return url
    except Exception as e:
        logger.error(f"❌ Failed to upload to S3: {str(e)}")
        raise