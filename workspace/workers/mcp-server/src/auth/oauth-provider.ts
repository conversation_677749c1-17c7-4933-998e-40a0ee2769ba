/**
 * OAuth Provider for MCP Server
 * Handles Auth0 integration and token management
 */

import { Hono, Context } from 'hono';
import { SignJWT, jwtVerify } from 'jose';
import { z } from 'zod';

interface OAuthConfig {
  apiRoute: string;
  apiHandler: any;
  defaultHandler: (c: Context) => Promise<Response>;
  authorizeEndpoint: string;
  tokenEndpoint: string;
  clientRegistrationEndpoint: string;
}

export class OAuthProvider extends Hono {
  private config: OAuthConfig;

  constructor(config: OAuthConfig) {
    super();
    this.config = config;
    this.setupRoutes();
  }

  private setupRoutes() {
    // Default route
    this.get('/', this.config.defaultHandler);

    // OAuth authorization endpoint
    this.get(this.config.authorizeEndpoint, this.handleAuthorize.bind(this));

    // OAuth token endpoint
    this.post(this.config.tokenEndpoint, this.handleToken.bind(this));

    // Client registration endpoint
    this.post(this.config.clientRegistrationEndpoint, this.handleClientRegistration.bind(this));

    // MCP SSE endpoint
    this.get(this.config.apiRoute, this.handleMCPConnection.bind(this));
  }

  private async handleAuthorize(c: Context) {
    const { client_id, redirect_uri, response_type, state, scope } = c.req.query();

    // Validate required parameters
    if (!client_id || !redirect_uri || response_type !== 'code') {
      return c.json({ error: 'invalid_request' }, 400);
    }

    // Build Auth0 authorization URL
    const auth0Domain = c.env.AUTH0_DOMAIN;
    const auth0ClientId = c.env.AUTH0_CLIENT_ID;
    
    const authUrl = new URL(`https://${auth0Domain}/authorize`);
    authUrl.searchParams.set('client_id', auth0ClientId);
    authUrl.searchParams.set('response_type', 'code');
    authUrl.searchParams.set('redirect_uri', `${new URL(c.req.url).origin}/callback`);
    authUrl.searchParams.set('scope', 'openid profile email');
    authUrl.searchParams.set('state', JSON.stringify({ 
      original_redirect_uri: redirect_uri, 
      client_id, 
      state 
    }));

    return c.redirect(authUrl.toString());
  }

  private async handleToken(c: Context) {
    const body = await c.req.json();
    const { grant_type, code, redirect_uri, client_id } = body;

    if (grant_type !== 'authorization_code' || !code) {
      return c.json({ error: 'invalid_grant' }, 400);
    }

    try {
      // Exchange code for Auth0 token
      const auth0Token = await this.exchangeAuth0Code(c, code);
      
      // Get user info from Auth0
      const userInfo = await this.getAuth0UserInfo(c, auth0Token.access_token);

      // Create MCP access token
      const mcpToken = await this.createMCPToken(c, userInfo);

      return c.json({
        access_token: mcpToken,
        token_type: 'Bearer',
        expires_in: 3600,
        scope: 'mcp:tools'
      });
    } catch (error) {
      console.error('Token exchange error:', error);
      return c.json({ error: 'invalid_grant' }, 400);
    }
  }

  private async handleClientRegistration(c: Context) {
    // For now, return a static client configuration
    // In production, you might want to implement dynamic client registration
    return c.json({
      client_id: 'divinci-mcp-client',
      client_secret: 'not-needed-for-pkce',
      authorization_endpoint: `${new URL(c.req.url).origin}${this.config.authorizeEndpoint}`,
      token_endpoint: `${new URL(c.req.url).origin}${this.config.tokenEndpoint}`,
      grant_types: ['authorization_code'],
      response_types: ['code'],
      token_endpoint_auth_method: 'none'
    });
  }

  private async handleMCPConnection(c: Context) {
    const authHeader = c.req.header('Authorization');
    
    if (!authHeader?.startsWith('Bearer ')) {
      return c.json({ error: 'unauthorized' }, 401);
    }

    const token = authHeader.slice(7);
    
    try {
      // Verify MCP token
      const payload = await this.verifyMCPToken(c, token);
      
      // Set up SSE connection and delegate to MCP handler
      c.req.user = payload;
      return this.config.apiHandler.handle(c);
    } catch (error) {
      console.error('Token verification error:', error);
      return c.json({ error: 'unauthorized' }, 401);
    }
  }

  private async exchangeAuth0Code(c: Context, code: string) {
    const response = await fetch(`https://${c.env.AUTH0_DOMAIN}/oauth/token`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        grant_type: 'authorization_code',
        client_id: c.env.AUTH0_CLIENT_ID,
        client_secret: c.env.AUTH0_CLIENT_SECRET,
        code,
        redirect_uri: `${new URL(c.req.url).origin}/callback`
      })
    });

    if (!response.ok) {
      throw new Error('Failed to exchange Auth0 code');
    }

    return response.json();
  }

  private async getAuth0UserInfo(c: Context, accessToken: string) {
    const response = await fetch(`https://${c.env.AUTH0_DOMAIN}/userinfo`, {
      headers: { Authorization: `Bearer ${accessToken}` }
    });

    if (!response.ok) {
      throw new Error('Failed to get user info');
    }

    return response.json();
  }

  private async createMCPToken(c: Context, userInfo: any) {
    const secret = new TextEncoder().encode(c.env.AUTH0_CLIENT_SECRET);
    
    return await new SignJWT({
      sub: userInfo.sub,
      email: userInfo.email,
      name: userInfo.name,
      scope: 'mcp:tools'
    })
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setExpirationTime('1h')
      .sign(secret);
  }

  private async verifyMCPToken(c: Context, token: string) {
    const secret = new TextEncoder().encode(c.env.AUTH0_CLIENT_SECRET);
    
    const { payload } = await jwtVerify(token, secret);
    return payload;
  }
}
