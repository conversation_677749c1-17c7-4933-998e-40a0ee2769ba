import { defineConfig } from '@playwright/test';
import { getCloudTestConfig, getServiceEndpoints } from './src/config/cloud-config';

// Force production environment
process.env.TARGET_ENVIRONMENT = 'production';
process.env.TEST_ENV = 'production';
process.env.READ_ONLY_MODE = 'true';

// Get production-specific configuration
const cloudConfig = getCloudTestConfig();
const endpoints = getServiceEndpoints(cloudConfig);

console.log(`🏭 Configuring Playwright for PRODUCTION environment`);
console.log(`🔗 Base URL: ${cloudConfig.baseURL}`);
console.log(`🔗 API URL: ${cloudConfig.apiURL}`);
console.log(`⚠️ READ-ONLY MODE: No data modifications will be made`);

export default defineConfig({
  testDir: './src/e2e',
  outputDir: 'test-results-production',
  timeout: cloudConfig.timeouts.test,
  expect: {
    timeout: cloudConfig.timeouts.expect,
  },
  
  // Conservative retry configuration for production
  retries: process.env.CI ? 1 : 0, // Minimal retries for production
  
  // Single worker for production testing
  workers: 1,
  
  // Reporter configuration
  reporter: [
    ['html', { outputFolder: 'playwright-report-production' }],
    ['json', { outputFile: 'test-results-production/results.json' }],
    ['junit', { outputFile: 'test-results-production/results.xml' }],
    ['list'],
  ],
  
  // Global test configuration
  use: {
    // Base URL for the application
    baseURL: cloudConfig.baseURL,
    
    // Conservative timeouts for production
    navigationTimeout: cloudConfig.timeouts.navigation,
    actionTimeout: cloudConfig.timeouts.expect,
    
    // Screenshots and videos for production issues
    trace: 'retain-on-failure',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    
    // HTTP headers for all requests
    extraHTTPHeaders: {
      ...cloudConfig.headers,
      'User-Agent': 'Divinci-E2E-Tests/1.0 (Production-ReadOnly)',
    },
    
    // Never ignore HTTPS errors in production
    ignoreHTTPSErrors: false,
  },
  
  // Test projects for production
  projects: [
    // Production health checks - read-only smoke tests
    {
      name: 'production-health',
      testMatch: /.*production-health-check\.smoke\.spec\.ts/,
      use: {
        browserName: 'chromium',
      },
    },
    
    // Critical path tests - essential functionality only
    {
      name: 'production-critical',
      testMatch: /.*\.critical\.spec\.ts/,
      use: {
        browserName: 'chromium',
      },
      dependencies: ['production-health'],
    },
    
    // Cross-browser critical tests
    {
      name: 'production-firefox-critical',
      testMatch: /.*\.critical\.spec\.ts/,
      use: {
        browserName: 'firefox',
      },
      dependencies: ['production-health'],
    },
    
    {
      name: 'production-webkit-critical',
      testMatch: /.*\.critical\.spec\.ts/,
      use: {
        browserName: 'webkit',
      },
      dependencies: ['production-health'],
    },
  ],
  
  // Global setup and teardown for production
  globalSetup: require.resolve('./src/global-setup-production.ts'),
  globalTeardown: require.resolve('./src/global-teardown-production.ts'),
});
