
> @divinci-ai/public-api-live@0.3.0 start /workspaces/server/workspace/servers/public-api-live
> node dist/index.js

🐞LOG_DEBUG MODE:  1
🐞LOG_DEBUG MODE:  1
🔎 CURRENT ENVIRONMENT: dev
🔎 CORS FULL ORIGINS (raw): api.slack.com,dev-46tiys6hnb6vbg17-cd-hljzhimsn7o28fvq.edge.tenants.us.auth0.com,dev-46tiys6hnb6vbg17.us.auth0.com,local-chunks-vectorized,127.0.0.1:,effective-invention-xjwwp64g36r4j-8080.app.github.dev,effective-invention-xjwwp64g36r4j-8081.app.github.dev,effective-invention-xjwwp64g36r4j-8082.app.github.dev,effective-invention-xjwwp64g36r4j-8083.app.github.dev,effective-invention-xjwwp64g36r4j-9080.app.github.dev
🔎 CORS FULL ORIGINS (cleaned): [
  'api.slack.com',
  'dev-46tiys6hnb6vbg17-cd-hljzhimsn7o28fvq.edge.tenants.us.auth0.com',
  'dev-46tiys6hnb6vbg17.us.auth0.com',
  'local-chunks-vectorized',
  '127.0.0.1:',
  'effective-invention-xjwwp64g36r4j-8080.app.github.dev',
  'effective-invention-xjwwp64g36r4j-8081.app.github.dev',
  'effective-invention-xjwwp64g36r4j-8082.app.github.dev',
  'effective-invention-xjwwp64g36r4j-8083.app.github.dev',
  'effective-invention-xjwwp64g36r4j-9080.app.github.dev'
]
🐞LOG_DEBUG MODE:  1
🐞LOG_DEBUG MODE:  1
🎈 Attempting to connect to Redis in dev environment: redis://default:<EMAIL>:15121
🔄 Creating simple R2 client for fine-tune in local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🔄 Creating simple R2 client for RAG vectors in local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🔄 Creating simple Audio R2 client for local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🐞LOG_DEBUG MODE:  1
🐞LOG_DEBUG MODE:  1
Attempting Mongoose Connection.
🎈 MONGO_CONNECTION_URL: mongodb+srv://serverlessinstance0.c4pobzg.mongodb.net/divinci-dev
Attempting Redis Connection
🟢 Connected to Redis in dev environment at 2025-06-15T04:03:44.238Z
✅🌱 Successfully connected to Redis. 
✅🌱 Successfully connected to MongoDB. 
🔒 Creating HTTPS server on port 8080
🚀 Server listening on:   { address: '::', family: 'IPv6', port: 8081 }
🌐 url:  /
🪪 remote address:   ::ffff:127.0.0.1
[CORS-DEBUG] Received GET request from origin: unknown
[CORS-DEBUG] Request URL: https://localhost:8081/
[CORS-DEBUG] CORS Request Headers:
[CORS-DEBUG] No client certificate provided
[MTLS-DEBUG] Received GET request from ::ffff:127.0.0.1
[MTLS-DEBUG] Request URL: https://localhost:8081/
[MTLS-DEBUG] mTLS is enabled on the server
[MTLS-DEBUG] Certificate directory: /workspaces/server/private-keys/local-fast
[MTLS-DEBUG] Server certificate found at /workspaces/server/private-keys/local-fast/server.crt
[MTLS-DEBUG] Server certificate content starts with: -----BEGIN CERTIFICATE-----
MIIDCTCCAfGg...
[MTLS-DEBUG] Client certificate CA not found in any of the expected locations
[MTLS-DEBUG] No client certificate provided with this request
🔍 CORS debug: Checking origin: "undefined"
🔍 CORS debug: isCloudEnvironment: false
🔍 CORS debug: Environment: dev
[CORS-DEBUG] Setting response header: Access-Control-Allow-Credentials: true
[CORS-DEBUG] Setting response header: Access-Control-Expose-Headers: x-file-name,x-file-id,x-target,x-processor,x-vectorize-config,x-processor-config,x-debug-client,divinci-organization,cloudflare-worker-x-dev-auth,x-worker-local-dev
[CORS-DEBUG] Response status: 200 
[CORS-DEBUG] CORS Response Headers:
[CORS-DEBUG]   Access-Control-Allow-Origin: Not set
[CORS-DEBUG]   Access-Control-Allow-Methods: Not set
[CORS-DEBUG]   Access-Control-Allow-Headers: Not set
[CORS-DEBUG]   Access-Control-Allow-Credentials: true
[CORS-DEBUG]   Access-Control-Expose-Headers: x-file-name,x-file-id,x-target,x-processor,x-vectorize-config,x-processor-config,x-debug-client,divinci-organization,cloudflare-worker-x-dev-auth,x-worker-local-dev
[CORS-DEBUG]   Access-Control-Max-Age: Not set
GET / 200 48 - 3.248 ms
