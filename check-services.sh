#!/bin/bash
echo "🔍 Codespace Service Status Check"
echo "================================="
echo ""

# Array of services to check
declare -A services=(
    ["8080"]="Web Client"
    ["8081"]="Test HTTP Server"
    ["9080"]="Public API"
    ["8082"]="Public API Live"
    ["8083"]="Public API Webhook"
)

# Function to check service health
check_service() {
    local port=$1
    local name=$2
    local url="https://sturdy-space-broccoli-g4xpjgv6376q-${port}.app.github.dev"
    
    echo -n "🌐 ${name} (${port}): "
    
    # Check if port is in use locally
    if command -v lsof >/dev/null 2>&1; then
        if lsof -i :${port} >/dev/null 2>&1; then
            echo -n "✅ Port active, "
        else
            echo -n "❌ Port inactive, "
        fi
    fi
    
    # Check if URL responds
    if command -v curl >/dev/null 2>&1; then
        local response=$(curl -k -s -o /dev/null -w "%{http_code}" --connect-timeout 5 "${url}" 2>/dev/null)
        if [[ "$response" == "200" ]]; then
            echo "✅ URL responding (HTTP 200)"
        elif [[ "$response" == "000" ]]; then
            echo "❌ URL not accessible"
        else
            echo "⚠️ URL responding (HTTP ${response})"
        fi
    else
        echo "❓ Cannot test URL (curl not available)"
    fi
    
    echo "   📍 URL: ${url}"
    echo ""
}

# Check all services
for port in "${!services[@]}"; do
    check_service "$port" "${services[$port]}"
done

echo "📋 VS Code Tasks Available:"
echo "  - Use Ctrl+Shift+P → 'Tasks: Run Task' to start services"
echo "  - Available tasks: Start Web Client, Start Mock API Servers, etc."
echo ""
echo "🌐 Open in browser:"
echo "  - Web Client: https://sturdy-space-broccoli-g4xpjgv6376q-8080.app.github.dev"
echo "  - API Test Page: https://sturdy-space-broccoli-g4xpjgv6376q-8081.app.github.dev/api-test.html"
