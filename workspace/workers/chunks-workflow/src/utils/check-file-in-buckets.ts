/**
<<<<<<< HEAD
=======
 * Get bucket names based on environment
 */
function getBucketNames(environment: string): string[] {
  switch (environment) {
    case "local":
    case "development":
      return ["rag-files-local", "rag-origin-files-local"];
    case "stage":
    case "staging":
      return ["rag-origin-files-stage", "rag-files-stage"];
    case "production":
    case "prod":
      return ["whitelabel-vector-index"];
    default:
      return ["rag-files-local", "rag-origin-files-local"];
  }
}

/**
>>>>>>> WA-170_MCP
 * Checks if a file exists in either the primary or alternate bucket
 * @param env Environment variables
 * @param objectKey The object key to check
 * @returns The file content as a ReadableStream if found, null otherwise
 */
export async function checkFileInBuckets(env: any, objectKey: string): Promise<{
  found: boolean;
  bucket?: string;
  contentType?: string;
  contentLength?: number;
  etag?: string;
  lastModified?: string;
  body?: ReadableStream;
}> {
  console.log(`🔄 [BUCKET-CHECK] Checking for file in buckets: ${objectKey}`);

<<<<<<< HEAD
  // Buckets to check
  const buckets = ["rag-origin-files-local", "rag-files-local"];
=======
  // Buckets to check based on environment
  const environment = env.ENVIRONMENT || "local";
  const buckets = getBucketNames(environment);
>>>>>>> WA-170_MCP

  // Base URL for MinIO - use the reliable endpoint
  const baseUrl = env.R2_BUCKET_URL || "http://minio.divinci.local:9000";

  // Check each bucket using direct fetch
  for (const bucket of buckets) {
    try {
      console.log(`🔄 [BUCKET-CHECK] Checking bucket: ${bucket} for file: ${objectKey}`);

      // Create the URL for the file
      const url = `${baseUrl}/${bucket}/${objectKey}`;

      // Try to get the object with a HEAD request first
      const headResponse = await fetch(url, {
        method: 'HEAD',
        headers: {
          'Authorization': `Basic ${btoa(`${env.R2_ACCESS_KEY_ID || "minioadmin"}:${env.R2_SECRET_ACCESS_KEY || "minioadmin"}`)}`
        }
      });

      if (headResponse.ok) {
        console.log(`✅ [BUCKET-CHECK] File found in bucket: ${bucket} with HEAD request`);

        // Now get the actual file
        const getResponse = await fetch(url, {
          headers: {
            'Authorization': `Basic ${btoa(`${env.R2_ACCESS_KEY_ID || "minioadmin"}:${env.R2_SECRET_ACCESS_KEY || "minioadmin"}`)}`
          }
        });

        if (getResponse.ok) {
          console.log(`✅ [BUCKET-CHECK] Successfully retrieved file from bucket: ${bucket}`);

          // Return the file details
          return {
            found: true,
            bucket,
            contentType: getResponse.headers.get('Content-Type') || 'application/octet-stream',
            contentLength: parseInt(getResponse.headers.get('Content-Length') || '0', 10),
            etag: getResponse.headers.get('ETag') || '',
            lastModified: getResponse.headers.get('Last-Modified') || new Date().toUTCString(),
            body: getResponse.body
          };
        }
      }
    } catch (error: any) {
      console.log(`🔄 [BUCKET-CHECK] Error checking bucket ${bucket}: ${error.message}`);
    }
  }

  // If we get here, the file was not found in any bucket
  console.log(`❌ [BUCKET-CHECK] File not found in any bucket: ${objectKey}`);
  return { found: false };
}
