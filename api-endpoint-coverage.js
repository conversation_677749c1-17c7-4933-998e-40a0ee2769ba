/**
 * API Endpoint Coverage Analyzer
 * 
 * This tool analyzes which API endpoints are being tested and which ones are not.
 * It scans the test files and the API implementation to identify coverage gaps.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// API test mappings from the original file
const API_TEST_MAPPINGS = [
  {
    folder: "workspace/servers/public-api/src/routes/ai-chat",
    testSuites: ["AI Chats"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/whitelabel",
    testSuites: ["White Label"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/finetune",
    testSuites: ["Fine Tune"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/moderation",
    testSuites: ["Prompt Moderation"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/thread",
    testSuites: ["Thread Prefix"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/message",
    testSuites: ["Message Prefix"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/rag",
    testSuites: ["RAG"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/workspace",
    testSuites: ["Workspace Release"]
  }
];

// HTTP methods to look for
const HTTP_METHODS = ['get', 'post', 'put', 'delete', 'patch'];

/**
 * Find all API endpoints in the codebase
 * @returns {Object} Map of API endpoints by category
 */
function findApiEndpoints() {
  const endpoints = {};
  
  for (const mapping of API_TEST_MAPPINGS) {
    const category = mapping.folder.split('/').pop();
    endpoints[category] = [];
    
    // Check if the folder exists
    const folderPath = path.join(process.cwd(), '..', '..', mapping.folder);
    if (!fs.existsSync(folderPath)) {
      console.log(`⚠️ Folder not found: ${mapping.folder}`);
      continue;
    }
    
    // Find all TypeScript files in the folder
    const files = fs.readdirSync(folderPath)
      .filter(file => file.endsWith('.ts') && !file.endsWith('.test.ts') && !file.endsWith('.spec.ts'));
    
    for (const file of files) {
      const filePath = path.join(folderPath, file);
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Extract the endpoint path from the file
      const endpointPath = extractEndpointPath(content);
      
      // Extract HTTP methods from the file
      const methods = extractHttpMethods(content);
      
      endpoints[category].push({
        file,
        path: endpointPath,
        methods,
        filePath
      });
    }
  }
  
  return endpoints;
}

/**
 * Extract the endpoint path from a file
 * @param {string} content File content
 * @returns {string} Endpoint path
 */
function extractEndpointPath(content) {
  // Look for router.use or app.use patterns
  const routerUseMatch = content.match(/router\.use\(['"]([^'"]+)['"]/);
  const appUseMatch = content.match(/app\.use\(['"]([^'"]+)['"]/);
  
  if (routerUseMatch) {
    return routerUseMatch[1];
  } else if (appUseMatch) {
    return appUseMatch[1];
  }
  
  return 'unknown';
}

/**
 * Extract HTTP methods from a file
 * @param {string} content File content
 * @returns {string[]} HTTP methods
 */
function extractHttpMethods(content) {
  const methods = [];
  
  for (const method of HTTP_METHODS) {
    const regex = new RegExp(`router\\.${method}\\(['"]([^'"]+)['"]`, 'g');
    let match;
    
    while ((match = regex.exec(content)) !== null) {
      methods.push({
        method: method.toUpperCase(),
        path: match[1]
      });
    }
  }
  
  return methods;
}

/**
 * Find all API tests in the codebase
 * @returns {Object} Map of API tests by category
 */
function findApiTests() {
  const tests = {};
  
  // Find all test files
  const testFiles = findFiles(path.join(process.cwd(), 'src', 'tests'), '.spec.ts');
  
  // Read the API test mapping file
  const apiTestMappingPath = path.join(process.cwd(), 'src', 'api-test-mapping.ts');
  const apiTestMappingContent = fs.readFileSync(apiTestMappingPath, 'utf8');
  
  // Extract test suites from the mapping
  for (const mapping of API_TEST_MAPPINGS) {
    const category = mapping.folder.split('/').pop();
    tests[category] = {
      testSuites: mapping.testSuites,
      testFiles: [],
      apiCalls: []
    };
    
    // Find test files that match the test suites
    for (const testFile of testFiles) {
      const content = fs.readFileSync(testFile, 'utf8');
      
      // Check if the test file contains any of the test suites
      const matchesTestSuite = mapping.testSuites.some(suite => content.includes(suite));
      
      if (matchesTestSuite) {
        tests[category].testFiles.push(testFile);
        
        // Extract API calls from the test file
        const apiCalls = extractApiCalls(content);
        tests[category].apiCalls.push(...apiCalls);
      }
    }
  }
  
  return tests;
}

/**
 * Extract API calls from a test file
 * @param {string} content File content
 * @returns {Object[]} API calls
 */
function extractApiCalls(content) {
  const apiCalls = [];
  
  // Look for API client calls
  for (const method of HTTP_METHODS) {
    const regex = new RegExp(`client\\.${method}\\(['"]([^'"]+)['"]`, 'g');
    let match;
    
    while ((match = regex.exec(content)) !== null) {
      apiCalls.push({
        method: method.toUpperCase(),
        path: match[1]
      });
    }
  }
  
  return apiCalls;
}

/**
 * Find files recursively
 * @param {string} dir Directory to search
 * @param {string} extension File extension to filter by
 * @returns {string[]} Array of file paths
 */
function findFiles(dir, extension) {
  if (!fs.existsSync(dir)) {
    return [];
  }
  
  let files = [];
  const entries = fs.readdirSync(dir, { withFileTypes: true });
  
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    
    if (entry.isDirectory()) {
      files = files.concat(findFiles(fullPath, extension));
    } else if (entry.name.endsWith(extension)) {
      files.push(fullPath);
    }
  }
  
  return files;
}

/**
 * Analyze API endpoint coverage
 */
function analyzeApiEndpointCoverage() {
  console.log('📊 API Endpoint Coverage Analysis');
  console.log('================================');
  
  // Find all API endpoints
  const endpoints = findApiEndpoints();
  
  // Find all API tests
  const tests = findApiTests();
  
  // Calculate coverage
  let totalEndpoints = 0;
  let coveredEndpoints = 0;
  
  for (const category in endpoints) {
    console.log(`\n📁 ${category}`);
    console.log('------------------');
    
    const categoryEndpoints = endpoints[category];
    const categoryTests = tests[category] || { testFiles: [], apiCalls: [] };
    
    console.log(`Found ${categoryEndpoints.length} endpoints and ${categoryTests.testFiles.length} test files`);
    
    if (categoryEndpoints.length === 0) {
      console.log('⚠️ No endpoints found in this category');
      continue;
    }
    
    if (categoryTests.testFiles.length === 0) {
      console.log('❌ No tests found for this category');
      continue;
    }
    
    // Check each endpoint for coverage
    for (const endpoint of categoryEndpoints) {
      totalEndpoints += endpoint.methods.length || 1;
      
      console.log(`\n📄 ${endpoint.file} (${endpoint.path})`);
      
      if (endpoint.methods.length === 0) {
        console.log('  ⚠️ No HTTP methods found in this file');
        continue;
      }
      
      for (const method of endpoint.methods) {
        const isMethodCovered = categoryTests.apiCalls.some(call => 
          call.method === method.method && 
          (call.path === method.path || call.path.includes(method.path))
        );
        
        if (isMethodCovered) {
          coveredEndpoints++;
          console.log(`  ✅ ${method.method} ${method.path}`);
        } else {
          console.log(`  ❌ ${method.method} ${method.path}`);
        }
      }
    }
  }
  
  // Print summary
  console.log('\n📊 Coverage Summary');
  console.log('------------------');
  
  const coveragePercentage = totalEndpoints > 0 ? (coveredEndpoints / totalEndpoints * 100).toFixed(2) : 0;
  
  console.log(`Total Endpoints: ${totalEndpoints}`);
  console.log(`Covered Endpoints: ${coveredEndpoints}`);
  console.log(`Coverage: ${coveragePercentage}%`);
  
  if (coveragePercentage < 80) {
    console.log('\n⚠️ API endpoint coverage is below 80%. Consider adding more tests.');
  } else {
    console.log('\n✅ API endpoint coverage is good (>= 80%).');
  }
}

// Run the analyzer
analyzeApiEndpointCoverage();
