#!/bin/sh

# Start <PERSON><PERSON> in the background
minio server /data --console-address ':9001' &
MINIO_PID=$!

# Wait for Min<PERSON> to be ready
echo "Waiting for Min<PERSON> to start..."
for i in $(seq 1 30); do
  if curl -s -f -o /dev/null http://localhost:9000/minio/health/live; then
    echo "<PERSON><PERSON> is ready!"
    break
  fi
  echo "Waiting for Min<PERSON> to start... ($i/30)"
  sleep 2
done

# MinIO client (mc) is already included in the minio/minio image
echo "Using pre-installed MinIO client..."

# Get credentials from environment variables or use defaults
MINIO_ROOT_USER=${MINIO_ROOT_USER:-minioadmin}
MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD:-minioadmin}

echo "Using MinIO credentials: $MINIO_ROOT_USER / $MINIO_ROOT_PASSWORD"

# Configure MinIO client
echo "Configuring MinIO client..."
/usr/bin/mc alias set local-minio http://localhost:9000 "$MINIO_ROOT_USER" "$MINIO_ROOT_PASSWORD"

# Verify the connection
echo "Verifying connection to MinIO..."
/usr/bin/mc admin info local-minio

# Create buckets
echo "Creating buckets..."
/usr/bin/mc mb --ignore-existing local-minio/workspace-audio
/usr/bin/mc mb --ignore-existing local-minio/rag-origin-files-local
/usr/bin/mc mb --ignore-existing local-minio/rag-files-local
/usr/bin/mc mb --ignore-existing local-minio/audio-transcript-files
/usr/bin/mc mb --ignore-existing local-minio/local-audio
/usr/bin/mc mb --ignore-existing local-minio/private-temporary-uploads

# Set bucket policies to public read-write for local development
echo "Setting bucket policies..."
/usr/bin/mc anonymous set public local-minio/workspace-audio
/usr/bin/mc anonymous set public local-minio/rag-origin-files-local
/usr/bin/mc anonymous set public local-minio/rag-files-local
/usr/bin/mc anonymous set public local-minio/audio-transcript-files
/usr/bin/mc anonymous set public local-minio/local-audio
/usr/bin/mc anonymous set public local-minio/private-temporary-uploads

# Configure CORS for all buckets
echo "Configuring CORS for all buckets..."

# Get the path to the script's directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Apply CORS configuration to each bucket
# This loop runs the cors admin command for each bucket
for bucket in workspace-audio rag-origin-files-local rag-files-local audio-transcript-files local-audio private-temporary-uploads; do
  echo "Setting CORS policy for bucket: $bucket"
  /usr/bin/mc admin bucket cors set local-minio/$bucket "$SCRIPT_DIR/minio-cors-config.json" || {
    echo "Warning: Failed to set CORS for $bucket using admin command"
    # Try alternate method if admin command fails
    echo "Trying alternate method for $bucket..."
<<<<<<< HEAD
    
=======

>>>>>>> WA-170_MCP
    # Create a JSON policy for public access with CORS
    cat > /tmp/policy.json << EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {"AWS": ["*"]},
      "Action": ["s3:GetObject", "s3:PutObject", "s3:DeleteObject"],
      "Resource": ["arn:aws:s3:::$bucket/*"]
    }
  ]
}
EOF
    # Apply policy with mc policy command
    /usr/bin/mc policy set-json /tmp/policy.json local-minio/$bucket
  }
done

# Get the current date for directory structure
YEAR=$(date +%Y)
MONTH=$(date +%m)
DAY=$(date +%d)

# Create organized directory structure in workspace-audio bucket
echo "Creating directory structure in workspace-audio bucket..."

# Create common directory structure for all whitelabel IDs
<<<<<<< HEAD
# Default whitelabel ID used in local development
WHITELABEL_ID="682415a03d653676ebe89b06"

# Create base directories
/usr/bin/mc cp /dev/null local-minio/workspace-audio/.keep
/usr/bin/mc cp /dev/null local-minio/workspace-audio/${WHITELABEL_ID}/.keep
/usr/bin/mc cp /dev/null local-minio/workspace-audio/${WHITELABEL_ID}/audio/.keep
/usr/bin/mc cp /dev/null local-minio/workspace-audio/${WHITELABEL_ID}/original/.keep

# Create year/month/day structure for audio files
echo "Creating year/month/day structure for audio files..."
/usr/bin/mc cp /dev/null local-minio/workspace-audio/${WHITELABEL_ID}/audio/${YEAR}/.keep
/usr/bin/mc cp /dev/null local-minio/workspace-audio/${WHITELABEL_ID}/audio/${YEAR}/${MONTH}/.keep
/usr/bin/mc cp /dev/null local-minio/workspace-audio/${WHITELABEL_ID}/audio/${YEAR}/${MONTH}/${DAY}/.keep

# Create year/month/day structure for original files
echo "Creating year/month/day structure for original files..."
/usr/bin/mc cp /dev/null local-minio/workspace-audio/${WHITELABEL_ID}/original/${YEAR}/.keep
/usr/bin/mc cp /dev/null local-minio/workspace-audio/${WHITELABEL_ID}/original/${YEAR}/${MONTH}/.keep
/usr/bin/mc cp /dev/null local-minio/workspace-audio/${WHITELABEL_ID}/original/${YEAR}/${MONTH}/${DAY}/.keep

# Create additional directories in audio-transcript-files
echo "Creating directory structure in audio-transcript-files bucket..."
/usr/bin/mc cp /dev/null local-minio/audio-transcript-files/.keep
/usr/bin/mc cp /dev/null local-minio/audio-transcript-files/${WHITELABEL_ID}/.keep
/usr/bin/mc cp /dev/null local-minio/audio-transcript-files/${WHITELABEL_ID}/${YEAR}/.keep
/usr/bin/mc cp /dev/null local-minio/audio-transcript-files/${WHITELABEL_ID}/${YEAR}/${MONTH}/.keep
/usr/bin/mc cp /dev/null local-minio/audio-transcript-files/${WHITELABEL_ID}/${YEAR}/${MONTH}/${DAY}/.keep

# Create test files to verify visibility
echo "Creating test files to verify MinIO visibility..."
echo "This is a test file for audio" > /tmp/test-audio.txt
echo "This is a test file for original" > /tmp/test-original.txt

# Upload test files to verify visibility
/usr/bin/mc cp /tmp/test-audio.txt local-minio/workspace-audio/${WHITELABEL_ID}/audio/${YEAR}/${MONTH}/${DAY}/test-audio.txt
/usr/bin/mc cp /tmp/test-original.txt local-minio/workspace-audio/${WHITELABEL_ID}/original/${YEAR}/${MONTH}/${DAY}/test-original.txt

# Display objects to verify creation
echo "Verifying objects in workspace-audio bucket:"
/usr/bin/mc ls --recursive local-minio/workspace-audio
=======
# Use environment variable or skip if not provided
WHITELABEL_ID="${DEFAULT_WHITELABEL_ID:-}"
if [ -z "$WHITELABEL_ID" ]; then
  echo "⚠️ DEFAULT_WHITELABEL_ID environment variable not set, skipping whitelabel-specific directory creation"
else
  echo "📁 Creating directories for whitelabel ID: $WHITELABEL_ID"
fi

# Create base directories
/usr/bin/mc cp /dev/null local-minio/workspace-audio/.keep

if [ -n "$WHITELABEL_ID" ]; then
  /usr/bin/mc cp /dev/null local-minio/workspace-audio/${WHITELABEL_ID}/.keep
  /usr/bin/mc cp /dev/null local-minio/workspace-audio/${WHITELABEL_ID}/audio/.keep
  /usr/bin/mc cp /dev/null local-minio/workspace-audio/${WHITELABEL_ID}/original/.keep

  # Create year/month/day structure for audio files
  echo "Creating year/month/day structure for audio files..."
  /usr/bin/mc cp /dev/null local-minio/workspace-audio/${WHITELABEL_ID}/audio/${YEAR}/.keep
  /usr/bin/mc cp /dev/null local-minio/workspace-audio/${WHITELABEL_ID}/audio/${YEAR}/${MONTH}/.keep
  /usr/bin/mc cp /dev/null local-minio/workspace-audio/${WHITELABEL_ID}/audio/${YEAR}/${MONTH}/${DAY}/.keep

  # Create year/month/day structure for original files
  echo "Creating year/month/day structure for original files..."
  /usr/bin/mc cp /dev/null local-minio/workspace-audio/${WHITELABEL_ID}/original/${YEAR}/.keep
  /usr/bin/mc cp /dev/null local-minio/workspace-audio/${WHITELABEL_ID}/original/${YEAR}/${MONTH}/.keep
  /usr/bin/mc cp /dev/null local-minio/workspace-audio/${WHITELABEL_ID}/original/${YEAR}/${MONTH}/${DAY}/.keep

  # Create additional directories in audio-transcript-files
  echo "Creating directory structure in audio-transcript-files bucket..."
  /usr/bin/mc cp /dev/null local-minio/audio-transcript-files/.keep
  /usr/bin/mc cp /dev/null local-minio/audio-transcript-files/${WHITELABEL_ID}/.keep
  /usr/bin/mc cp /dev/null local-minio/audio-transcript-files/${WHITELABEL_ID}/${YEAR}/.keep
  /usr/bin/mc cp /dev/null local-minio/audio-transcript-files/${WHITELABEL_ID}/${YEAR}/${MONTH}/.keep
  /usr/bin/mc cp /dev/null local-minio/audio-transcript-files/${WHITELABEL_ID}/${YEAR}/${MONTH}/${DAY}/.keep

  # Create test files to verify visibility
  echo "Creating test files to verify MinIO visibility..."
  echo "This is a test file for audio" > /tmp/test-audio.txt
  echo "This is a test file for original" > /tmp/test-original.txt

  # Upload test files to verify visibility
  /usr/bin/mc cp /tmp/test-audio.txt local-minio/workspace-audio/${WHITELABEL_ID}/audio/${YEAR}/${MONTH}/${DAY}/test-audio.txt
  /usr/bin/mc cp /tmp/test-original.txt local-minio/workspace-audio/${WHITELABEL_ID}/original/${YEAR}/${MONTH}/${DAY}/test-original.txt
fi

# Display objects to verify creation
# echo "Verifying objects in workspace-audio bucket:"
# /usr/bin/mc ls --recursive local-minio/workspace-audio
>>>>>>> WA-170_MCP

echo "MinIO setup completed successfully!"

# Wait for MinIO to exit
wait $MINIO_PID
