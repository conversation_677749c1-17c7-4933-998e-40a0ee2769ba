#!/bin/bash

# Test script for MCP tools
# This script tests all available MCP tools

set -e

echo "🧪 Testing Divinci MCP Tools"
echo "============================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Server URL
SERVER_URL="http://localhost:8793/sse"

# Function to test MCP tool
test_tool() {
    local tool_name=$1
    local params=$2
    local description=$3
    
    echo -e "${BLUE}Testing $tool_name: $description${NC}"
    
    local request="{\"jsonrpc\":\"2.0\",\"id\":$(date +%s),\"method\":\"tools/call\",\"params\":{\"name\":\"$tool_name\",\"arguments\":$params}}"
    
    echo "Request: $request"
    echo "Response:"
    
    response=$(curl -s -X POST "$SERVER_URL" \
        -H "Content-Type: application/json" \
        -d "$request")
    
    echo "$response" | jq -r '.result.content[0].text' 2>/dev/null || echo "$response"
    echo ""
}

# Test 1: List all available tools
echo -e "${BLUE}📋 Listing all available tools${NC}"
curl -s -X POST "$SERVER_URL" \
    -H "Content-Type: application/json" \
    -d '{"jsonrpc":"2.0","id":1,"method":"tools/list","params":{}}' | \
    jq -r '.result.tools[] | "- \(.name): \(.description)"'
echo ""

# Test 2: Get user profile
test_tool "get_user_profile" "{}" "Get current user profile"

# Test 3: Create a chat
test_tool "create_chat" '{"title":"Test Chat","model":"gpt-4"}' "Create a new chat with title and model"

# Test 4: Send a message
test_tool "send_message" '{"chatId":"chat_123","message":"Hello, how are you today?"}' "Send a message to a chat"

# Test 5: List chats
test_tool "list_chats" '{"limit":5}' "List user chats with limit"

# Test 6: Create chat without parameters
test_tool "create_chat" '{}' "Create a chat without parameters"

# Test 7: List all chats
test_tool "list_chats" '{}' "List all chats without limit"

echo -e "${GREEN}✅ All tools tested successfully!${NC}"
echo ""
echo "🎯 Next Steps:"
echo "1. Try the MCP Inspector: http://localhost:6274"
echo "2. Connect to: http://localhost:8793/sse"
echo "3. Test tools interactively"
echo ""
echo "🚀 For Claude Desktop integration:"
echo 'Add this to your Claude Desktop config:'
echo '{'
echo '  "mcpServers": {'
echo '    "divinci": {'
echo '      "command": "npx",'
echo '      "args": ["mcp-remote", "http://localhost:8793/sse"]'
echo '    }'
echo '  }'
echo '}'
