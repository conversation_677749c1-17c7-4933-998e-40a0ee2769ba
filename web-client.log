
> @divinci-ai/web-client@0.3.0 start /workspaces/server/workspace/clients/web
> node server.js

🐞LOG_DEBUG MODE:  1
🐞LOG_DEBUG MODE:  1
🔎 CURRENT ENVIRONMENT: dev
🔎 CORS FULL ORIGINS (raw): api.slack.com,dev-46tiys6hnb6vbg17-cd-hljzhimsn7o28fvq.edge.tenants.us.auth0.com,dev-46tiys6hnb6vbg17.us.auth0.com,local-chunks-vectorized,127.0.0.1:,effective-invention-xjwwp64g36r4j-8080.app.github.dev,effective-invention-xjwwp64g36r4j-8081.app.github.dev,effective-invention-xjwwp64g36r4j-8082.app.github.dev,effective-invention-xjwwp64g36r4j-8083.app.github.dev,effective-invention-xjwwp64g36r4j-9080.app.github.dev
🔎 CORS FULL ORIGINS (cleaned): [
  'api.slack.com',
  'dev-46tiys6hnb6vbg17-cd-hljzhimsn7o28fvq.edge.tenants.us.auth0.com',
  'dev-46tiys6hnb6vbg17.us.auth0.com',
  'local-chunks-vectorized',
  '127.0.0.1:',
  'effective-invention-xjwwp64g36r4j-8080.app.github.dev',
  'effective-invention-xjwwp64g36r4j-8081.app.github.dev',
  'effective-invention-xjwwp64g36r4j-8082.app.github.dev',
  'effective-invention-xjwwp64g36r4j-8083.app.github.dev',
  'effective-invention-xjwwp64g36r4j-9080.app.github.dev'
]
🌐 Web CORS_PORTS:  [
  8080, 8081, 8082,
  8083, 8084, 9080,
  9081, 8787, 8788,
  8789, 8790, 9000
]
Server is running on port 8080
GET / 200 1727 - 4.103 ms {"accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8","host":"localhost:8080","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9","referer":"https://effective-invention-xjwwp64g36r4j.github.dev/","x-request-id":"acf37d0c3d23483282996bb71e5faad4","x-real-ip":"*************","x-forwarded-port":"443","x-forwarded-scheme":"https","x-original-uri":"/","x-scheme":"https","sec-ch-ua":"\"Brave\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","sec-gpc":"1","sec-fetch-site":"same-site","sec-fetch-mode":"navigate","sec-fetch-user":"?1","sec-fetch-dest":"document","priority":"u=0, i","x-original-proto":"https","cookie":"_dd_s=isExpired=1","x-forwarded-proto":"https","x-forwarded-host":"effective-invention-xjwwp64g36r4j-8080.app.github.dev","x-forwarded-for":"*************","proxy-connection":"Keep-Alive"}
GET /hidden.build.js 200 51136629 - 1.767 ms {"accept":"*/*","host":"localhost:8080","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9","if-modified-since":"Sat, 14 Jun 2025 22:35:33 GMT","if-none-match":"W/\"30c4875-197709579d0\"","referer":"https://effective-invention-xjwwp64g36r4j-8080.app.github.dev/","x-request-id":"cfa6fd9d268d09ab13c969cfcd8a194c","x-real-ip":"*************","x-forwarded-port":"443","x-forwarded-scheme":"https","x-original-uri":"/hidden.build.js","x-scheme":"https","sec-ch-ua-platform":"\"macOS\"","sec-ch-ua":"\"Brave\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","sec-ch-ua-mobile":"?0","sec-gpc":"1","sec-fetch-site":"same-origin","sec-fetch-mode":"no-cors","sec-fetch-dest":"script","x-original-proto":"https","cookie":"_dd_s=isExpired=1","x-forwarded-proto":"https","x-forwarded-host":"effective-invention-xjwwp64g36r4j-8080.app.github.dev","x-forwarded-for":"*************","proxy-connection":"Keep-Alive"}
GET /img/divinci_logo.png 304 - - 0.777 ms {"accept":"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8","host":"localhost:8080","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9","if-modified-since":"Sat, 14 Jun 2025 13:22:48 GMT","if-none-match":"W/\"167e5-1976e9b6a8e\"","referer":"https://effective-invention-xjwwp64g36r4j-8080.app.github.dev/","x-request-id":"c3dcaa6585a8f0a79b8da906de143acc","x-real-ip":"*************","x-forwarded-port":"443","x-forwarded-scheme":"https","x-original-uri":"/img/divinci_logo.png","x-scheme":"https","sec-ch-ua-platform":"\"macOS\"","sec-ch-ua":"\"Brave\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","sec-ch-ua-mobile":"?0","sec-gpc":"1","sec-fetch-site":"same-origin","sec-fetch-mode":"no-cors","sec-fetch-dest":"image","priority":"i","x-original-proto":"https","cookie":"_dd_s=rum=2&id=bb3b6a99-bec9-45bb-8d74-86188cf7d762&created=1749961763785&expire=1749962663786","x-forwarded-proto":"https","x-forwarded-host":"effective-invention-xjwwp64g36r4j-8080.app.github.dev","x-forwarded-for":"*************","proxy-connection":"Keep-Alive"}
GET / 200 1727 - 0.716 ms {"host":"localhost:8080","user-agent":"curl/7.88.1","accept":"*/*"}
GET /ai-chat/trending 200 1727 - 1.723 ms {"accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8","host":"localhost:8080","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9","cache-control":"max-age=0","x-request-id":"16391e2889dd61c7e0ebfd82e69f2eab","x-real-ip":"*************","x-forwarded-port":"443","x-forwarded-scheme":"https","x-original-uri":"/ai-chat/trending","x-scheme":"https","sec-ch-ua":"\"Brave\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","sec-gpc":"1","sec-fetch-site":"same-origin","sec-fetch-mode":"navigate","sec-fetch-user":"?1","sec-fetch-dest":"document","priority":"u=0, i","x-original-proto":"https","cookie":"_dd_s=rum=2&id=bb3b6a99-bec9-45bb-8d74-86188cf7d762&created=1749961763785&expire=1749962977138","x-forwarded-proto":"https","x-forwarded-host":"effective-invention-xjwwp64g36r4j-8080.app.github.dev","x-forwarded-for":"*************","proxy-connection":"Keep-Alive"}
GET /hidden.build.js 304 - - 0.479 ms {"accept":"*/*","host":"localhost:8080","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9","if-modified-since":"Sun, 15 Jun 2025 04:04:21 GMT","if-none-match":"W/\"30c4875-19771c28111\"","referer":"https://effective-invention-xjwwp64g36r4j-8080.app.github.dev/ai-chat/trending","x-request-id":"cce5f118073c1e4aadf1d3dac3382951","x-real-ip":"*************","x-forwarded-port":"443","x-forwarded-scheme":"https","x-original-uri":"/hidden.build.js","x-scheme":"https","sec-ch-ua-platform":"\"macOS\"","sec-ch-ua":"\"Brave\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","sec-ch-ua-mobile":"?0","sec-gpc":"1","sec-fetch-site":"same-origin","sec-fetch-mode":"no-cors","sec-fetch-dest":"script","x-original-proto":"https","cookie":"_dd_s=rum=2&id=bb3b6a99-bec9-45bb-8d74-86188cf7d762&created=1749961763785&expire=1749962977138","x-forwarded-proto":"https","x-forwarded-host":"effective-invention-xjwwp64g36r4j-8080.app.github.dev","x-forwarded-for":"*************","proxy-connection":"Keep-Alive"}
GET /?id=7abdc636-90ab-4cb8-a15a-016a5a42f82d&vscodeBrowserReqId=1749962078332 200 1727 - 0.563 ms {"accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8","host":"localhost:8080","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.6","referer":"https://16rnqehp3hvudojv6eogua2qgf49p0rfcgm6o6gnqbgv5h6ms0s9.assets.github.dev/","x-request-id":"30cb27a8d9d48c83283f674d7f79be0c","x-real-ip":"*************","x-forwarded-port":"443","x-forwarded-scheme":"https","x-original-uri":"/?id=7abdc636-90ab-4cb8-a15a-016a5a42f82d&vscodeBrowserReqId=1749962078332","x-scheme":"https","sec-ch-ua":"\"Brave\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","sec-gpc":"1","sec-fetch-site":"same-site","sec-fetch-mode":"navigate","sec-fetch-user":"?1","sec-fetch-dest":"iframe","priority":"u=0, i","x-original-proto":"https","cookie":"_dd_s=rum=2&id=bb3b6a99-bec9-45bb-8d74-86188cf7d762&created=1749961763785&expire=1749962977138","x-forwarded-proto":"https","x-forwarded-host":"effective-invention-xjwwp64g36r4j-8080.app.github.dev","x-forwarded-for":"*************","proxy-connection":"Keep-Alive"}
GET /hidden.build.js 304 - - 0.480 ms {"accept":"*/*","host":"localhost:8080","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.6","if-modified-since":"Sun, 15 Jun 2025 04:04:21 GMT","if-none-match":"W/\"30c4875-19771c28111\"","referer":"https://effective-invention-xjwwp64g36r4j-8080.app.github.dev/?id=7abdc636-90ab-4cb8-a15a-016a5a42f82d&vscodeBrowserReqId=1749962078332","x-request-id":"d6803576671f047acc6284f31b10cec8","x-real-ip":"*************","x-forwarded-port":"443","x-forwarded-scheme":"https","x-original-uri":"/hidden.build.js","x-scheme":"https","sec-ch-ua-platform":"\"macOS\"","sec-ch-ua":"\"Brave\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","sec-ch-ua-mobile":"?0","sec-gpc":"1","sec-fetch-site":"same-origin","sec-fetch-mode":"no-cors","sec-fetch-dest":"script","x-original-proto":"https","cookie":"_dd_s=rum=2&id=bb3b6a99-bec9-45bb-8d74-86188cf7d762&created=1749961763785&expire=1749962977138","x-forwarded-proto":"https","x-forwarded-host":"effective-invention-xjwwp64g36r4j-8080.app.github.dev","x-forwarded-for":"*************","proxy-connection":"Keep-Alive"}
GET /img/divinci_logo.png 304 - - 0.756 ms {"accept":"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8","host":"localhost:8080","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9","if-modified-since":"Sat, 14 Jun 2025 13:22:48 GMT","if-none-match":"W/\"167e5-1976e9b6a8e\"","referer":"https://effective-invention-xjwwp64g36r4j-8080.app.github.dev/ai-chat/trending","x-request-id":"deaef3c0506be1b02f42e5164052f94a","x-real-ip":"*************","x-forwarded-port":"443","x-forwarded-scheme":"https","x-original-uri":"/img/divinci_logo.png","x-scheme":"https","sec-ch-ua-platform":"\"macOS\"","sec-ch-ua":"\"Brave\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","sec-ch-ua-mobile":"?0","sec-gpc":"1","sec-fetch-site":"same-origin","sec-fetch-mode":"no-cors","sec-fetch-dest":"image","priority":"i","x-original-proto":"https","cookie":"_dd_s=rum=2&id=bb3b6a99-bec9-45bb-8d74-86188cf7d762&created=1749961763785&expire=1749962979520","x-forwarded-proto":"https","x-forwarded-host":"effective-invention-xjwwp64g36r4j-8080.app.github.dev","x-forwarded-for":"*************","proxy-connection":"Keep-Alive"}
GET /favicon.ico 304 - - 0.424 ms {"accept":"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8","host":"localhost:8080","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9","if-modified-since":"Sat, 14 Jun 2025 13:22:48 GMT","if-none-match":"W/\"3c2e-1976e9b6a8e\"","referer":"https://effective-invention-xjwwp64g36r4j-8080.app.github.dev/ai-chat/trending","x-request-id":"e1c3d7c7b281be64546763e363e4586e","x-real-ip":"*************","x-forwarded-port":"443","x-forwarded-scheme":"https","x-original-uri":"/favicon.ico","x-scheme":"https","sec-ch-ua-platform":"\"macOS\"","sec-ch-ua":"\"Brave\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","sec-ch-ua-mobile":"?0","sec-gpc":"1","sec-fetch-site":"same-origin","sec-fetch-mode":"no-cors","sec-fetch-dest":"image","priority":"u=1, i","x-original-proto":"https","cookie":"_dd_s=rum=2&id=bb3b6a99-bec9-45bb-8d74-86188cf7d762&created=1749961763785&expire=1749962979520","x-forwarded-proto":"https","x-forwarded-host":"effective-invention-xjwwp64g36r4j-8080.app.github.dev","x-forwarded-for":"*************","proxy-connection":"Keep-Alive"}
