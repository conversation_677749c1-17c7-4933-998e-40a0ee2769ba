import React, { useEffect, useState } from "react";

import { StateInputProps } from "../../../../../../../../util/react/input";
import { BodyType } from "./types";
import { useAuth0Fetch } from "../../../../../../../../globals/auth0-user";

import { DiarizeTool } from "../../../Form/DiarizeTool";
import { TranscribeTool } from "../../../Form/TranscribeTool";
import { URLTextareaInput } from "../../../Form/MediaInput/URLTextareaInput";
import {
  loadAudioToolPreferences,
  saveAudioToolPreferences,
  StorageOption
} from "../../../utils/toolPreferences";

<<<<<<< HEAD
=======
// Simplified configuration interface for open-source pyannote
interface DiarizeConfig {
  tool: string;
  numSpeakers?: 1 | 2;
  showAdvanced: boolean;
}

>>>>>>> WA-170_MCP
export function Form({ value, onChange }: StateInputProps<BodyType>) {
  const [preferencesLoaded, setPreferencesLoaded] = useState(false);
  const [savePreferences, setSavePreferences] = useState(true);
  const [storageOption] = useState<StorageOption>('both');
  const [servicesAvailable, setServicesAvailable] = useState(true);
  const [urls, setUrls] = useState<string[]>(value.urls || []);
  const auth0Fetch = useAuth0Fetch();

<<<<<<< HEAD
=======
  // Initialize simplified configuration with defaults
  const initializeDiarizeConfig = (): DiarizeConfig => ({
    tool: value.diarizerTool || '',
    numSpeakers: undefined,
    showAdvanced: false,
  });

  // Get current configuration or initialize defaults
  const diarizeConfig = initializeDiarizeConfig();

>>>>>>> WA-170_MCP
  // Load saved preferences when component mounts
  useEffect(() => {
    const loadPreferences = async () => {
      try {
        // Try to load from both local and server storage
        const prefs = await loadAudioToolPreferences('both', auth0Fetch);

        // Only apply preferences if they exist and we haven't already set values
        if (!preferencesLoaded && (prefs.diarizerTool || prefs.transcriberTool)) {
          onChange(currentValue => ({
            ...currentValue,
            diarizerTool: prefs.diarizerTool || currentValue.diarizerTool,
            transcriberTool: prefs.transcriberTool || currentValue.transcriberTool
          }));
        }

        setPreferencesLoaded(true);
      } catch (error) {
        console.error("Error loading audio tool preferences:", error);
        setPreferencesLoaded(true);
      }
    };

    loadPreferences();
  }, [onChange, auth0Fetch]);

  // Save preferences when they change
  useEffect(() => {
    // Only save if preferences are loaded and the user has selected tools
    if (preferencesLoaded && savePreferences && value.diarizerTool && value.transcriberTool) {
      saveAudioToolPreferences(
        {
          diarizerTool: value.diarizerTool,
          transcriberTool: value.transcriberTool
        },
        storageOption,
        auth0Fetch
      ).catch(error => {
        console.error("Error saving audio tool preferences:", error);
      });
    }
  }, [value.diarizerTool, value.transcriberTool, preferencesLoaded, savePreferences, storageOption, auth0Fetch]);

<<<<<<< HEAD
  const handleDiarizerChange = (newValue: string) => {
    onChange(value => ({ ...value, diarizerTool: newValue }));
=======
  // Enhanced handlers for new configuration structure
  const handleDiarizeConfigChange = (newConfig: DiarizeConfig) => {
    onChange(value => ({
      ...value,
      diarizerTool: newConfig.tool, // Maintain backward compatibility
    }));
  };

  // Legacy handler for backward compatibility
  const handleDiarizerChange = (newValue: string) => {
    const updatedConfig = { ...diarizeConfig, tool: newValue };
    handleDiarizeConfigChange(updatedConfig);
>>>>>>> WA-170_MCP
  };

  const handleTranscriberChange = (newValue: string) => {
    onChange(value => ({ ...value, transcriberTool: newValue }));
  };

  const toggleSavePreferences = () => {
    setSavePreferences(!savePreferences);
  };

  // We'll get service status from the DiarizeTool component
  useEffect(() => {
    // Listen for messages from DiarizeTool about service status
    const handleMessage = (event: MessageEvent) => {
      if (event.data && event.data.type === 'SERVICE_STATUS_CHANGE') {
        setServicesAvailable(event.data.available);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  // Handle URL changes
  const handleUrlsChange = (newUrls: string[]) => {
    setUrls(newUrls);
    onChange({
      ...value,
      urls: newUrls,
      url: newUrls[0] || "" // Keep the first URL in the single URL field for backward compatibility
    });
  };

  return (
    <div className="container">
      <div className="card">
        <div className="card-content">
          <h1 className="title is-3 mb-5">Upload from URLs</h1>

          <div className="content">
            <div className="mb-6">
              <URLTextareaInput
                value={value.urls || []}
                onChange={handleUrlsChange}
              />
              {!servicesAvailable && (
                <p className="help is-danger mt-2">
                  Some required services are unavailable. URL processing may not work correctly.
                </p>
              )}
            </div>

            <div className="columns">
              <div className="column">
                <div className="box">
                  <h3 className="subtitle is-4 mb-4">
                    <span className="icon-text">
                      <span className="icon">
                        <i className="fas fa-users"></i>
                      </span>
                      <span>Speaker Detection</span>
                    </span>
                  </h3>
                  <DiarizeTool
<<<<<<< HEAD
                    value={value.diarizerTool}
                    onChange={handleDiarizerChange}
=======
                    value={diarizeConfig}
                    onChange={handleDiarizeConfigChange}
>>>>>>> WA-170_MCP
                  />
                </div>
              </div>

              <div className="column">
                <div className="box">
                  <h3 className="subtitle is-4 mb-4">
                    <span className="icon-text">
                      <span className="icon">
                        <i className="fas fa-microphone"></i>
                      </span>
                      <span>Speech Recognition</span>
                    </span>
                  </h3>
                  <TranscribeTool
                    value={value.transcriberTool}
                    onChange={handleTranscriberChange}
                  />
                </div>
              </div>
            </div>

            <div className="field mt-4">
              <div className="control">
                <label className="checkbox">
                  <input
                    type="checkbox"
                    checked={savePreferences}
                    onChange={toggleSavePreferences}
                    className="mr-2"
                  />
                  Remember my tool selections for next time
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}