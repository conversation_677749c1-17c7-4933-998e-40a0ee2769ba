import { describe, it, expect, vi, beforeEach } from "vitest";
import { createStorageClient } from "../../src/utils/storage-client";
import { generateAuthorizationHeader } from "../../src/utils/aws-sig-v4";

// Mock the aws-sig-v4 module
vi.mock("../../src/utils/aws-sig-v4", ()=>({
  generateAuthorizationHeader: vi.fn().mockResolvedValue({
    "Authorization": "AWS4-HMAC-SHA256 Credential=test",
    "x-amz-date": "20230101T000000Z"
  })
}));

describe("storage-client", ()=>{
  // Mock console.log to avoid cluttering test output
  beforeEach(()=>{
    vi.spyOn(console, "log").mockImplementation(()=>{});
    vi.spyOn(console, "warn").mockImplementation(()=>{});
    vi.spyOn(console, "error").mockImplementation(()=>{});
  });

  describe("createStorageClient", ()=>{
    it("should create R2StorageClient for production environment", async ()=>{
      // Mock R2 bucket
      const mockR2 = {
        get: vi.fn().mockResolvedValue({ body: "test-data" }),
        put: vi.fn().mockResolvedValue({ key: "test-key" }),
        delete: vi.fn().mockResolvedValue(undefined),
        list: vi.fn().mockResolvedValue({ objects: [] })
      };

      const env = {
        ENVIRONMENT: "production",
        R2_BUCKET_URL: "https://r2.example.com",
        R2: mockR2
      };

      const client = createStorageClient(env);

      // Test get method
      const getResult = await client.get("test-key");
      expect(mockR2.get).toHaveBeenCalledWith("test-key");
      expect(getResult).toEqual({ body: "test-data" });

      // Test put method
      const putResult = await client.put("test-key", "test-data");
      expect(mockR2.put).toHaveBeenCalledWith("test-key", "test-data", undefined);
      expect(putResult).toEqual({ key: "test-key" });

      // Test delete method
      await client.delete("test-key");
      expect(mockR2.delete).toHaveBeenCalledWith("test-key");

      // Test list method
      const listResult = await client.list();
      expect(mockR2.list).toHaveBeenCalledWith(undefined);
      expect(listResult).toEqual({ objects: [] });
    });

    it("should create MinioStorageClient for local environment", async ()=>{
      // Mock fetch for MinIO client
      global.fetch = vi.fn().mockImplementation((url, options)=>{
        const urlString = url.toString();
        console.log(`Mocked fetch called with URL: ${urlString}, method: ${options?.method || "GET"}`);

        // HEAD request to check if bucket exists - handle any bucket name
<<<<<<< HEAD
        if((urlString.includes("test-bucket") || urlString.includes("rag-origin-files-local")) && options?.method === "HEAD") {
=======
        if((urlString.includes("test-bucket") || urlString.includes("rag-files-local")) && options?.method === "HEAD") {
>>>>>>> WA-170_MCP
          return Promise.resolve({
            ok: true,
            status: 200,
            url: urlString,
            text: ()=>Promise.resolve("OK")
          });
        }

        // PUT request to create bucket - handle any bucket name
<<<<<<< HEAD
        if((urlString.includes("test-bucket") || urlString.includes("rag-origin-files-local")) && options?.method === "PUT") {
=======
        if((urlString.includes("test-bucket") || urlString.includes("rag-files-local")) && options?.method === "PUT") {
>>>>>>> WA-170_MCP
          return Promise.resolve({
            ok: true,
            status: 200,
            url: urlString,
            text: ()=>Promise.resolve("OK")
          });
        }

        // HEAD request to check if object exists
        if(urlString.includes("test-key") && options?.method === "HEAD") {
          return Promise.resolve({
            ok: false,
            status: 404,
            url: urlString,
            text: ()=>Promise.resolve("Not Found")
          });
        }

        // GET request to retrieve object
        if(urlString.includes("test-key") && (!options?.method || options.method === "GET") && !urlString.includes("list-type=2")) {
<<<<<<< HEAD
          const headers = new Headers({
            "content-type": "application/octet-stream"
          });

          // Ensure headers has forEach method
          if(!headers.forEach) {
            headers.forEach = function(callback){
              this.entries().forEach(([key, value])=>callback(value, key));
            };
          }
=======
          const headers = {
            get: (name) => {
              const lowerName = name.toLowerCase();
              if (lowerName === 'content-type') return 'application/octet-stream';
              return null;
            },
            has: (name) => {
              const lowerName = name.toLowerCase();
              return lowerName === 'content-type';
            },
            forEach: function(callback) {
              callback('application/octet-stream', 'content-type');
            },
            entries: function*() {
              yield ['content-type', 'application/octet-stream'];
            }
          };
>>>>>>> WA-170_MCP

          return Promise.resolve({
            ok: true,
            status: 200,
            url: urlString,
            body: new ReadableStream({
              start(controller){
                controller.enqueue(new TextEncoder().encode("test-data"));
                controller.close();
              }
            }),
            text: ()=>Promise.resolve("test-data"),
            json: ()=>Promise.resolve({ data: "test-data" }),
            blob: ()=>Promise.resolve(new Blob(["test-data"], { type: "application/octet-stream" })),
            arrayBuffer: ()=>Promise.resolve(new TextEncoder().encode("test-data").buffer),
            headers: headers,
            clone: function(){
              return this;
            }
          });
        }

        // PUT request to upload object
        if(urlString.includes("test-key") && options?.method === "PUT") {
<<<<<<< HEAD
          const headers = new Headers({
            "content-type": "application/json",
            "etag": "\"test-etag\""
          });

          // Ensure headers has forEach method
          if(!headers.forEach) {
            headers.forEach = function(callback){
              this.entries().forEach(([key, value])=>callback(value, key));
            };
          }
=======
          const headers = {
            get: (name) => {
              const lowerName = name.toLowerCase();
              if (lowerName === 'etag') return '"test-etag"';
              if (lowerName === 'content-type') return 'application/json';
              return null;
            },
            has: (name) => {
              const lowerName = name.toLowerCase();
              return lowerName === 'etag' || lowerName === 'content-type';
            },
            forEach: function(callback) {
              callback('"test-etag"', 'etag');
              callback('application/json', 'content-type');
            },
            entries: function*() {
              yield ['etag', '"test-etag"'];
              yield ['content-type', 'application/json'];
            }
          };
>>>>>>> WA-170_MCP

          return Promise.resolve({
            ok: true,
            status: 200,
            url: urlString,
            text: ()=>Promise.resolve("Success"),
            json: ()=>Promise.resolve({ key: "test-key" }),
            blob: ()=>Promise.resolve(new Blob(["Success"], { type: "text/plain" })),
            arrayBuffer: ()=>Promise.resolve(new TextEncoder().encode("Success").buffer),
            headers: headers,
            clone: function(){
              return this;
            }
          });
        }

        // DELETE request to delete object
        if(urlString.includes("test-key") && options?.method === "DELETE") {
<<<<<<< HEAD
          const headers = new Headers({
            "content-type": "text/plain"
          });

          // Ensure headers has forEach method
          if(!headers.forEach) {
            headers.forEach = function(callback){
              this.entries().forEach(([key, value])=>callback(value, key));
            };
          }
=======
          const headers = {
            get: (name) => {
              const lowerName = name.toLowerCase();
              if (lowerName === 'content-type') return 'text/plain';
              return null;
            },
            has: (name) => {
              const lowerName = name.toLowerCase();
              return lowerName === 'content-type';
            },
            forEach: function(callback) {
              callback('text/plain', 'content-type');
            },
            entries: function*() {
              yield ['content-type', 'text/plain'];
            }
          };
>>>>>>> WA-170_MCP

          return Promise.resolve({
            ok: true,
            status: 204,
            url: urlString,
            text: ()=>Promise.resolve(""),
            json: ()=>Promise.resolve({}),
            blob: ()=>Promise.resolve(new Blob([""], { type: "text/plain" })),
            arrayBuffer: ()=>Promise.resolve(new ArrayBuffer(0)),
            headers: headers,
            clone: function(){
              return this;
            }
          });
        }

        // GET request to list objects
        if(urlString.includes("list-type=2") && (!options?.method || options.method === "GET")) {
<<<<<<< HEAD
          const headers = new Headers({
            "content-type": "application/xml"
          });

          // Ensure headers has forEach method
          if(!headers.forEach) {
            headers.forEach = function(callback){
              this.entries().forEach(([key, value])=>callback(value, key));
            };
          }
=======
          const headers = {
            get: (name) => {
              const lowerName = name.toLowerCase();
              if (lowerName === 'content-type') return 'application/xml';
              return null;
            },
            has: (name) => {
              const lowerName = name.toLowerCase();
              return lowerName === 'content-type';
            },
            forEach: function(callback) {
              callback('application/xml', 'content-type');
            },
            entries: function*() {
              yield ['content-type', 'application/xml'];
            }
          };
>>>>>>> WA-170_MCP

          const xmlResponse = "<ListBucketResult><Contents><Key>test-key</Key></Contents></ListBucketResult>";

          return Promise.resolve({
            ok: true,
            status: 200,
            url: urlString,
            text: ()=>Promise.resolve(xmlResponse),
            json: ()=>Promise.resolve({ objects: [{ key: "test-key" }] }),
            blob: ()=>Promise.resolve(new Blob([xmlResponse], { type: "application/xml" })),
            arrayBuffer: ()=>Promise.resolve(new TextEncoder().encode(xmlResponse).buffer),
            headers: headers,
            clone: function(){
              return this;
            }
          });
        }

        // Fallback for any other requests
        console.log(`Using fallback response for: ${urlString}, method: ${options?.method || "GET"}`);
<<<<<<< HEAD
        const headers = new Headers({
          "content-type": "application/octet-stream"
        });

        // Ensure headers has forEach method
        if(!headers.forEach) {
          headers.forEach = function(callback){
            this.entries().forEach(([key, value])=>callback(value, key));
          };
        }
=======
        console.log(`URL includes test-key: ${urlString.includes("test-key")}`);
        console.log(`Method is PUT: ${options?.method === "PUT"}`);
        const headers = {
          get: (name) => {
            const lowerName = name.toLowerCase();
            if (lowerName === 'content-type') return 'application/octet-stream';
            if (lowerName === 'etag') return '"fallback-etag"';
            return null;
          },
          has: (name) => {
            const lowerName = name.toLowerCase();
            return lowerName === 'content-type' || lowerName === 'etag';
          },
          forEach: function(callback) {
            callback('application/octet-stream', 'content-type');
            callback('"fallback-etag"', 'etag');
          },
          entries: function*() {
            yield ['content-type', 'application/octet-stream'];
            yield ['etag', '"fallback-etag"'];
          }
        };
>>>>>>> WA-170_MCP

        return Promise.resolve({
          ok: true,
          status: 200,
          url: urlString,
          text: ()=>Promise.resolve("Fallback response"),
          json: ()=>Promise.resolve({ success: true }),
          blob: ()=>Promise.resolve(new Blob(["fallback-data"], { type: "application/octet-stream" })),
          arrayBuffer: ()=>Promise.resolve(new TextEncoder().encode("fallback-data").buffer),
          body: new ReadableStream({
            start(controller){
              controller.enqueue(new TextEncoder().encode("fallback-data"));
              controller.close();
            }
          }),
          headers: headers,
          clone: function(){
            return this;
          }
        });
      });

      const env = {
        ENVIRONMENT: "local",
        R2_BUCKET_URL: "http://local-minio:9000",
        R2_ACCESS_KEY_ID: "test-access-key",
        R2_SECRET_ACCESS_KEY: "test-secret-key",
        BUCKET_NAME: "test-bucket"
      };

      const client = createStorageClient(env);

      // Test get method
      const getResult = await client.get("test-key");
      expect(fetch).toHaveBeenCalled();
      expect(generateAuthorizationHeader).toHaveBeenCalled();
      expect(getResult).toBeDefined();

<<<<<<< HEAD
      // Reset mocks
      vi.clearAllMocks();
=======
      // Reset mocks but keep the fetch mock
      generateAuthorizationHeader.mockClear();
>>>>>>> WA-170_MCP

      // Test put method
      const putResult = await client.put("test-key", "test-data");
      expect(fetch).toHaveBeenCalled();
      expect(generateAuthorizationHeader).toHaveBeenCalled();
      expect(putResult).toBeDefined();

<<<<<<< HEAD
      // Reset mocks
      vi.clearAllMocks();
=======
      // Reset mocks but keep the fetch mock
      generateAuthorizationHeader.mockClear();
>>>>>>> WA-170_MCP

      // Test delete method
      await client.delete("test-key");
      expect(fetch).toHaveBeenCalled();
      expect(generateAuthorizationHeader).toHaveBeenCalled();

<<<<<<< HEAD
      // Reset mocks
      vi.clearAllMocks();
=======
      // Reset mocks but keep the fetch mock
      generateAuthorizationHeader.mockClear();
>>>>>>> WA-170_MCP

      // Test list method
      const listResult = await client.list();
      expect(fetch).toHaveBeenCalled();
      expect(generateAuthorizationHeader).toHaveBeenCalled();
      expect(listResult).toBeDefined();
    });
  });
});
