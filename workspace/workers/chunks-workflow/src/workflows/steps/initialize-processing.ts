import { WorkflowStep } from "cloudflare:workers";
import { StepContext, R2ValidationResult, InitialSetup } from "./types";

/**
 * Initializes processing session
 *
<<<<<<< HEAD
 * IMPORTANT: This function has been refactored to always use the original object key
 * without trying to find similar files or use special handling for LOCAL MODE.
 * This ensures consistent behavior between local and production environments.
=======
 * IMPORTANT: This function constructs the correct file URL based on environment
 * and storage configuration, matching the working implementation.
>>>>>>> WA-170_MCP
 */
export async function initializeProcessing(
  step: WorkflowStep,
  context: StepContext,
  r2Result: R2ValidationResult,
  initialSetup: InitialSetup,
  processorType: string,
  processor: any
): Promise<any>{
  return await step.do("initializeProcessing", async ()=>{
    // Construct the file URL using the original object key
    let fileUrl: string;

<<<<<<< HEAD
    // Encode the object key for URL usage
    const encodedKey = r2Result.objectKey.split("/").map(segment => encodeURIComponent(segment)).join("/");

    // IMPORTANT: In BARE METAL MODE, we should use the same URL construction logic
    // for both local and production environments. The only difference should be
    // the base URL, which is provided by the environment configuration.
    //
    // The R2_BUCKET_URL environment variable should be set appropriately for each
    // environment, pointing to either the local MinIO server or the production R2 bucket.
    fileUrl = `${context.env.R2_BUCKET_URL}/${encodedKey}`;
    console.log(`🔄 Constructed file URL: ${fileUrl}`);

    console.log(`🔄 R2 Bucket URL from env: ${context.env.R2_BUCKET_URL}`);
=======
    // Handle timestamp prefix for MinIO compatibility
    // When files are uploaded to MinIO, they often get timestamp prefixes added
    // We need to find the actual file that exists in MinIO
    let adjustedObjectKey = r2Result.objectKey;

    // Check if we need to handle timestamp prefixes (for local MinIO)
    const environment = context.env.ENVIRONMENT || "local";
    const forceR2Storage = context.env.FORCE_R2_STORAGE === "true";
    const isLocalMode = environment === "local" || environment === "development";

    // No need for complex file search anymore since we're now uploading with the correct timestamp
    // The upload step now handles timestamp prefixes correctly and updates r2Result.objectKey
    console.log(`🔍 [FILE PROCESSING] Using objectKey from upload: ${r2Result.objectKey}`);

    // Encode the object key for URL usage
    const encodedKey = adjustedObjectKey.split("/").map(segment => encodeURIComponent(segment)).join("/");

    console.log(`🔄 Environment: ${environment}, FORCE_R2_STORAGE: ${forceR2Storage}`);
    console.log(`🔄 R2 Bucket URL from env: ${context.env.R2_BUCKET_URL}`);

    // Construct file URL based on environment and storage configuration
    if (isLocalMode && !forceR2Storage) {
      // For local development with MinIO, construct the MinIO URL
      // Use host.docker.internal for container-to-host communication
      const bucketName = "rag-files-local";
      fileUrl = `http://host.docker.internal:9000/${bucketName}/${encodedKey}`;
      console.log(`🔄 [LOCAL MODE] Using MinIO URL: ${fileUrl}`);
    } else if (forceR2Storage && isLocalMode) {
      // When forcing R2 storage in local mode, use direct R2 endpoint
      console.log(`🔄 [FORCE_R2_STORAGE] File is in Cloudflare R2, using direct R2 endpoint`);

      const bucketName = "workspace-audio";  // Use workspace-audio bucket for testing
      const r2Endpoint = 'https://14a6fa23390363382f378b5bd4a0f849.r2.cloudflarestorage.com';
      fileUrl = `${r2Endpoint}/${bucketName}/${encodedKey}`;
      console.log(`🔄 [FORCE_R2_STORAGE] Generated R2 direct URL: ${fileUrl}`);
    } else {
      // For production, use the R2 bucket URL from environment
      const bucketUrl = context.env.R2_BUCKET_URL;
      if (!bucketUrl) {
        throw new Error("R2_BUCKET_URL environment variable is not set for production environment");
      }
      fileUrl = `${bucketUrl}/${encodedKey}`;
      console.log(`🔄 [PRODUCTION] Using R2 URL: ${fileUrl}`);
    }

>>>>>>> WA-170_MCP
    console.log(`🔄 Initializing processing for file: ${fileUrl}`, {
      processor: processorType,
      config: initialSetup.normalizedConfig
    });

    // Initialize the processing session with the file URL
    return await processor.initializeSession(
      fileUrl,
      initialSetup.normalizedConfig
    );
  });
}
