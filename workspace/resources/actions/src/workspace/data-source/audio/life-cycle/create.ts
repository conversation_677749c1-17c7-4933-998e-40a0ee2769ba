
import {
  replaceParams, fetchBody, handleFetch,
  checkUsePresigned, handleEmptyFetch, PresignedCheck,
  PresignedResult
} from "@divinci-ai/utils";

import { DATA_SOURCE_AUDIO_ROOT } from "../paths";
import { AudioTranscriptDoc } from "@divinci-ai/models";
import { pollAudioTranscriptStatus } from "./polling";


export async function dataSourceAudioCreateFile(
  fetcher: typeof fetch, params: { whitelabelId: string },
  body: {
    mediaFile: File, diarizerTool: string, transcriberTool: string,
    // Enhanced configuration options
    diarizeConfig?: {
      tool: string;
      confidence: {
        enabled: boolean;
        threshold: number;
        includeInProcessing: boolean;
      };
      numSpeakers?: 1 | 2;
      showAdvanced: boolean;
    };
    voiceprintConfig?: {
      enabled: boolean;
      existingVoiceprints: Array<{
        voiceprintId: string;
        speakerLabel: string;
        voiceprintData: string;
      }>;
      autoCreateFromHighConfidence: boolean;
    };
  },
  presigned: PresignedCheck = "check-size",
  onStatusUpdate?: (status: any) => void
){
  if(!checkUsePresigned(body.mediaFile.size, presigned)){
    return dataSourceAudioCreateFileDirect(fetcher, params, body, onStatusUpdate);
  }
  return dataSourceAudioCreateFilePresigned(fetcher, params, body, onStatusUpdate);
}

export async function dataSourceAudioCreateURL(
  fetcher: typeof fetch, params: { whitelabelId: string },
  body: {
    url: string, diarizerTool: string, transcriberTool: string,
  }
){
  return await handleFetch(fetcher(
    replaceParams(`${DATA_SOURCE_AUDIO_ROOT}/url`, params),
    fetchBody("POST", body)
  )) as AudioTranscriptDoc;
}

async function dataSourceAudioCreateFileDirect(
  fetcher: typeof fetch, params: { whitelabelId: string },
  body: {
    mediaFile: File, diarizerTool: string, transcriberTool: string,
<<<<<<< HEAD
=======
    diarizeConfig?: {
      tool: string;
      confidence: {
        enabled: boolean;
        threshold: number;
        includeInProcessing: boolean;
      };
      numSpeakers?: 1 | 2;
      showAdvanced: boolean;
    };
    voiceprintConfig?: {
      enabled: boolean;
      existingVoiceprints: Array<{
        voiceprintId: string;
        speakerLabel: string;
        voiceprintData: string;
      }>;
      autoCreateFromHighConfidence: boolean;
    };
>>>>>>> WA-170_MCP
  },
  onStatusUpdate?: (status: any) => void
){
  const formbody = new FormData();
  formbody.append("mediaFile", body.mediaFile);
  formbody.append("diarizerTool", body.diarizerTool);
  formbody.append("transcriberTool", body.transcriberTool);

<<<<<<< HEAD
=======
  // Add confidence settings if available
  if (body.diarizeConfig?.confidence?.enabled) {
    formbody.append("confidenceEnabled", "true");
    formbody.append("confidenceThreshold", body.diarizeConfig.confidence.threshold.toString());
    formbody.append("confidenceIncludeInProcessing", body.diarizeConfig.confidence.includeInProcessing.toString());
  }

  // Add voiceprint settings if available
  if (body.voiceprintConfig?.enabled) {
    formbody.append("voiceprintsEnabled", "true");
    formbody.append("voiceprintsAutoCreate", body.voiceprintConfig.autoCreateFromHighConfidence.toString());

    // Add existing voiceprints for identification if available
    if (body.voiceprintConfig.existingVoiceprints && body.voiceprintConfig.existingVoiceprints.length > 0) {
      formbody.append("voiceprintsData", JSON.stringify(body.voiceprintConfig.existingVoiceprints));
      console.log("🎯 [IDENTIFICATION] Sending voiceprints for identification:", body.voiceprintConfig.existingVoiceprints.length);
    }
  }

>>>>>>> WA-170_MCP
  // Start the transcription job
  const response = await handleFetch(fetcher(
    replaceParams(`${DATA_SOURCE_AUDIO_ROOT}/file`, params),
    { method: "POST", body: formbody }
  )) as { id: string, status: string, statusUrl: string, message: string };

  // If the response already contains the complete document, return it
  if ('_id' in response) {
    return response as unknown as AudioTranscriptDoc;
  }

  // Poll for status updates until the job is complete
  return await pollAudioTranscriptStatus(
    fetcher,
    replaceParams(response.statusUrl, params),
    { onStatusUpdate }
  );
}

export async function dataSourceAudioCreateFilePresigned(
  fetcher: typeof fetch, params: { whitelabelId: string },
  body: {
    mediaFile: File, diarizerTool: string, transcriberTool: string,
<<<<<<< HEAD
=======
    diarizeConfig?: {
      tool: string;
      confidence: {
        enabled: boolean;
        threshold: number;
        includeInProcessing: boolean;
      };
      numSpeakers?: 1 | 2;
      showAdvanced: boolean;
    };
    voiceprintConfig?: {
      enabled: boolean;
      existingVoiceprints: Array<{
        voiceprintId: string;
        speakerLabel: string;
        voiceprintData: string;
      }>;
      autoCreateFromHighConfidence: boolean;
    };
>>>>>>> WA-170_MCP
  },
  onStatusUpdate?: (status: any) => void
){
  const prefetchedInfo = await handleFetch(fetcher(
    replaceParams(`${DATA_SOURCE_AUDIO_ROOT}/presigned/prepare`, params),
    fetchBody("POST", {
      filename: body.mediaFile.name,
      byteSize: body.mediaFile.size,
    })
  )) as PresignedResult;

  await handleEmptyFetch(fetch(prefetchedInfo.url, {
    method: "PUT",
    headers: { "Content-Type": body.mediaFile.type },
    body: body.mediaFile,
  }));

  // Start the transcription job
<<<<<<< HEAD
  const response = await handleFetch(fetcher(
    replaceParams(`${DATA_SOURCE_AUDIO_ROOT}/presigned/finalized`, params),
    fetchBody("POST", {
      mediaFile: JSON.stringify(prefetchedInfo),
      diarizerTool: body.diarizerTool,
      transcriberTool: body.transcriberTool,
    })
=======
  const requestBody: any = {
    mediaFile: JSON.stringify(prefetchedInfo),
    diarizerTool: body.diarizerTool,
    transcriberTool: body.transcriberTool,
  };

  // Add confidence settings if available
  if (body.diarizeConfig?.confidence?.enabled) {
    requestBody.confidenceEnabled = "true";
    requestBody.confidenceThreshold = body.diarizeConfig.confidence.threshold.toString();
    requestBody.confidenceIncludeInProcessing = body.diarizeConfig.confidence.includeInProcessing.toString();
  }

  // Add voiceprint settings if available
  if (body.voiceprintConfig?.enabled) {
    requestBody.voiceprintsEnabled = "true";
    requestBody.voiceprintsAutoCreate = body.voiceprintConfig.autoCreateFromHighConfidence.toString();

    // Add existing voiceprints for identification if available
    if (body.voiceprintConfig.existingVoiceprints && body.voiceprintConfig.existingVoiceprints.length > 0) {
      requestBody.voiceprintsData = JSON.stringify(body.voiceprintConfig.existingVoiceprints);
      console.log("🎯 [IDENTIFICATION] Sending voiceprints for identification (presigned):", body.voiceprintConfig.existingVoiceprints.length);
    }
  }

  const response = await handleFetch(fetcher(
    replaceParams(`${DATA_SOURCE_AUDIO_ROOT}/presigned/finalized`, params),
    fetchBody("POST", requestBody)
>>>>>>> WA-170_MCP
  ));

  // If the response already contains the complete document, return it
  if (response && typeof response === 'object' && '_id' in response) {
    return response as unknown as AudioTranscriptDoc;
  }

  // Poll for status updates until the job is complete
  return await pollAudioTranscriptStatus(
    fetcher,
    replaceParams((response as any).statusUrl, params),
    { onStatusUpdate }
  );
}
