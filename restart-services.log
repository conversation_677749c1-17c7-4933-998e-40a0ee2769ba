🔄 Restarting all services...
🚀 Starting Codespace services...
🔍 Environment check:
   CODESPACE_NAME: effective-invention-xjwwp64g36r4j
   Current directory: /workspaces/server

🌐 Updating endpoint hostnames for Codespace: effective-invention-xjwwp64g36r4j
  📝 Updating endpoints.shared.env
    Before: API_HOST=effective-invention-xjwwp64g36r4j-9080.app.github.dev
    Before: API_LIVE_HOST=effective-invention-xjwwp64g36r4j-8082.app.github.dev
    After:  API_HOST=effective-invention-xjwwp64g36r4j-9080.app.github.dev
    After:  API_LIVE_HOST=effective-invention-xjwwp64g36r4j-8082.app.github.dev
    ✅ Updated hostnames in endpoints.shared.env
  📝 Updating web-client-dev.env
    Before: API_HOST=effective-invention-xjwwp64g36r4j-9080.app.github.dev
    Before: API_LIVE_HOST=effective-invention-xjwwp64g36r4j-8082.app.github.dev
    After:  API_HOST=effective-invention-xjwwp64g36r4j-9080.app.github.dev
    After:  API_LIVE_HOST=effective-invention-xjwwp64g36r4j-8082.app.github.dev
    ✅ Updated hostnames in web-client-dev.env
🌐 Updating CORS configuration for Codespace: effective-invention-xjwwp64g36r4j
✅ Updated CORS configuration with current Codespace origins
workspace/resources/models               |  WARN  The field "pnpm.neverBuiltDependencies" was found in /workspaces/server/workspace/resources/models/package.json. This will not take effect. You should configure "pnpm.neverBuiltDependencies" at the root of the workspace instead.
workspace/resources/server-globals       |  WARN  The field "pnpm.neverBuiltDependencies" was found in /workspaces/server/workspace/resources/server-globals/package.json. This will not take effect. You should configure "pnpm.neverBuiltDependencies" at the root of the workspace instead.
workspace/resources/server-permissions   |  WARN  The field "pnpm.neverBuiltDependencies" was found in /workspaces/server/workspace/resources/server-permissions/package.json. This will not take effect. You should configure "pnpm.neverBuiltDependencies" at the root of the workspace instead.
workspace/resources/server-tools         |  WARN  The field "pnpm.neverBuiltDependencies" was found in /workspaces/server/workspace/resources/server-tools/package.json. This will not take effect. You should configure "pnpm.neverBuiltDependencies" at the root of the workspace instead.
workspace/resources/utils                |  WARN  The field "pnpm.neverBuiltDependencies" was found in /workspaces/server/workspace/resources/utils/package.json. This will not take effect. You should configure "pnpm.neverBuiltDependencies" at the root of the workspace instead.
Scope: all 20 workspace projects
Progress: resolved 1, reused 0, downloaded 0, added 0
workspace/servers/shared                 |  WARN  deprecated rimraf@3.0.2

   ╭──────────────────────────────────────────╮
   │                                          │
   │   Update available! 10.11.0 → 10.12.1.   │
   │   Changelog: https://pnpm.io/v/10.12.1   │
   │     To update, run: pnpm add -g pnpm     │
   │                                          │
   ╰──────────────────────────────────────────╯

 WARN  deprecated eslint@8.57.1: This version is no longer supported. Please see https://eslint.org/version-support for other options.
workspace/clients/embed                  |  WARN  deprecated @types/dompurify@3.2.0
Progress: resolved 66, reused 0, downloaded 0, added 0
workspace/resources/server-globals       |  WARN  deprecated @types/redis@4.0.11
workspace/resources/server-globals       |  WARN  deprecated multer@1.4.5-lts.2
workspace/servers/public-api-live        |  WARN  deprecated @types/cookie@1.0.0
Progress: resolved 176, reused 1, downloaded 0, added 0
Progress: resolved 379, reused 1, downloaded 0, added 0
Progress: resolved 646, reused 1, downloaded 0, added 0
Progress: resolved 893, reused 1, downloaded 0, added 0
Progress: resolved 1229, reused 1, downloaded 0, added 0
Progress: resolved 1398, reused 1, downloaded 0, added 0
Progress: resolved 1797, reused 1, downloaded 0, added 0
Progress: resolved 2111, reused 1, downloaded 0, added 0
 WARN  14 deprecated subdependencies found: @humanwhocodes/config-array@0.13.0, @humanwhocodes/object-schema@2.0.3, @types/long@5.0.0, abab@2.0.6, domexception@4.0.0, glob@7.2.3, google-p12-pem@4.0.1, inflight@1.0.6, lodash.get@4.4.2, node-domexception@1.0.0, read-package-json@7.0.1, rimraf@2.7.1, superagent@8.1.2, trim@0.0.1

Progress: resolved 2115, reused 30, downloaded 0, added 0
Packages: +5 -80
++++----------------------------------------------------------------------------
Progress: resolved 2115, reused 86, downloaded 0, added 5
Progress: resolved 2115, reused 86, downloaded 0, added 5, done
workspace/resources/utils prepare$ rimraf ./dist && tsc
workspace/servers/shared prepare$ rimraf ./dist && tsc
. prepare$ husky
. prepare: Done
workspace/resources/utils prepare: Done
workspace/servers/shared prepare: Done
workspace/resources/models prepare$ rimraf ./dist && tsc
workspace/resources/server-utils prepare$ rimraf ./dist && tsc
workspace/resources/models prepare: Done
workspace/resources/server-utils prepare: Done
workspace/clients/embed prepare$ rimraf ./dist && tsc --project tsconfig.json
workspace/resources/actions prepare$ rimraf ./dist && tsc
workspace/resources/tools prepare$ rimraf ./dist && tsc
workspace/resources/server-globals prepare$ rimraf ./dist && tsc
workspace/resources/actions prepare: Done
workspace/resources/tools prepare: Done
workspace/clients/embed prepare: Done
workspace/resources/server-globals prepare: Done
workspace/resources/mtls prepare$ npm run build
workspace/clients/web prepare$ rimraf ./dist && tsc --skipLibCheck
workspace/resources/server-tools prepare$ rimraf ./dist && tsc --skipLibCheck
workspace/resources/mtls prepare: > @divinci-ai/mtls@0.1.0 build
workspace/resources/mtls prepare: > tsc
workspace/resources/mtls prepare: npm notice
workspace/resources/mtls prepare: npm notice New major version of npm available! 10.9.2 -> 11.4.2
workspace/resources/mtls prepare: npm notice Changelog: https://github.com/npm/cli/releases/tag/v11.4.2
workspace/resources/mtls prepare: npm notice To update run: npm install -g npm@11.4.2
workspace/resources/mtls prepare: npm notice
workspace/resources/mtls prepare: Done
workspace/resources/server-tools prepare: Done
workspace/clients/web prepare: Done
workspace/resources/server-models prepare$ rimraf ./dist && tsc --skipLibCheck
workspace/clients/tests prepare$ rimraf ./dist && tsc && node scripts/copy-html.js || echo 'TypeScript errors ignored'
workspace/clients/tests prepare: Copied src/globals/auth0/index.html to dist/globals/auth0/index.html
workspace/clients/tests prepare: Copied src/globals/stripe-subscription/index.html to dist/globals/stripe-subscription/index.html
workspace/clients/tests prepare: Copied src/globals/stripe-payment-method/index.html to dist/globals/stripe-payment-method/index.html
workspace/clients/tests prepare: Done
workspace/resources/server-models prepare: Done
workspace/servers/test-api prepare$ rimraf ./dist && tsc || echo 'ℹ️ TypeScript errors ignored'
workspace/resources/server-permissions prepare$ rimraf ./dist && tsc --skipLibCheck
workspace/servers/public-api-webhook prepare$ rimraf ./dist && tsc
workspace/resources/server-permissions prepare: Done
workspace/servers/public-api-webhook prepare: Done
workspace/servers/test-api prepare: Done
workspace/servers/public-api-live prepare$ rimraf ./dist && tsc
workspace/servers/public-api prepare$ rimraf ./dist && tsc
workspace/servers/public-api-live prepare: Done
workspace/servers/public-api prepare: Done
Done in 1m 6.4s using pnpm v10.11.0
[public-api] Building...

> @divinci-ai/public-api@0.3.0 build /workspaces/server/workspace/servers/public-api
> tsc

[public-api] Starting...
[public-api-live] Building...

> @divinci-ai/public-api-live@0.3.0 build /workspaces/server/workspace/servers/public-api-live
> tsc

[public-api-live] Starting...
[public-api-webhook] Building...

> @divinci-ai/public-api-webhook@0.3.0 build /workspaces/server/workspace/servers/public-api-webhook
> tsc

[public-api-webhook] Starting...
[web-client] Building...

> @divinci-ai/web-client@0.3.0 build /workspaces/server/workspace/clients/web
> webpack --mode development --config webpack.config.js

🐞LOG_DEBUG MODE:  1
set __env: /workspaces/server/workspace/clients/web/env
