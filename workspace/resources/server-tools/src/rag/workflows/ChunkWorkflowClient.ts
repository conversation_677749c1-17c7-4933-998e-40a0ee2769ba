import { requireEnvVar } from "@divinci-ai/server-utils";
import { JSON_Object } from "@divinci-ai/utils";
import { RagVectorTextChunksStatus } from "@divinci-ai/models";
import { R2FileLocation } from "../types";

/**
 * Configuration options for the ChunkWorkflowClient
 */
export interface ChunkWorkflowClientConfig {
  /** Auth0 token for authenticated requests */
<<<<<<< HEAD
  auth0Token: string;
  /** Whitelabel ID for the request */
  whitelabelId: string;
  /** RAG ID (optional) to associate the file with */
  ragId?: string;
  /** RAG name (optional) for the vectorization config */
  ragName?: string;
  /** Custom API endpoint URL (optional) */
  apiUrl?: string;
  /** Cloudflare account ID (optional if set in env) */
  cloudflareAccountId?: string;
  /** Cloudflare API token (optional if set in env) */
  cloudflareApiToken?: string;
  /** Request timeout in milliseconds */
  timeoutMs?: number;
=======
  auth0Token: string,
  /** Whitelabel ID for the request */
  whitelabelId: string,
  /** RAG ID (optional) to associate the file with */
  ragVectors?: Array<string>,
  /** RAG name (optional) for the vectorization config */
  ragName?: string,
  /** Custom API endpoint URL (optional) */
  apiUrl?: string,
  /** Cloudflare account ID (optional if set in env) */
  cloudflareAccountId?: string,
  /** Cloudflare API token (optional if set in env) */
  cloudflareApiToken?: string,
  /** Request timeout in milliseconds */
  timeoutMs?: number,
>>>>>>> WA-170_MCP
}

/**
 * Status response from the workflow API
 */
export interface WorkflowStatusResponse {
<<<<<<< HEAD
  status: string;
  state: {
    name: string;
    startTime: string;
    endTime?: string;
  };
  metadata?: {
    [key: string]: any;
  };
  error?: {
    message: string;
    code?: string;
  };
=======
  status: string,
  state: {
    name: string,
    startTime: string,
    endTime?: string,
  },
  metadata?: {
    [key: string]: any,
  },
  error?: {
    message: string,
    code?: string,
  },
>>>>>>> WA-170_MCP
}

/**
 * Response from submitting a document to the workflow
 */
export interface SubmitDocumentResponse {
<<<<<<< HEAD
  success: boolean;
  workflowId?: string;
  objectKey?: string;
  id?: string;
  error?: string;
=======
  success: boolean,
  workflowId?: string,
  objectKey?: string,
  id?: string,
  error?: string,
>>>>>>> WA-170_MCP
}

/**
 * Processor configuration for document processing
 */
export interface ProcessorConfig {
  /** Semantic chunking enabled flag */
<<<<<<< HEAD
  semantic_chunking?: boolean;
  /** Embeddings provider (cloudflare, ollama, none) */
  embeddings_provider?: "cloudflare" | "ollama" | "none";
  /** Minimum tokens per chunk */
  minTokens?: number;
  /** Maximum tokens per chunk */
  maxTokens?: number;
  /** Relevance threshold for filtering chunks */
  relevanceThreshold?: number;
  /** Skip de-junking process flag */
  skipDeJunk?: boolean;
  /** Disable streaming flag */
  disableStreaming?: boolean;
  /** Unstructured processor specific settings */
  unstructured?: {
    /** Chunking strategy to use */
    chunkingStrategy?: "by_title" | "by_similarity" | "by_page" | "by_character";
    /** Maximum characters per chunk */
    maxCharacters?: number;
    /** Similarity threshold for chunk merging */
    similarityThreshold?: number;
    /** Include original elements flag */
    includeOriginalElements?: boolean;
    /** Multi-page sections flag */
    multipageSections?: boolean;
  };
=======
  semantic_chunking?: boolean,
  /** Embeddings provider (cloudflare, ollama, none) */
  embeddings_provider?: "cloudflare" | "ollama" | "none",
  /** Minimum tokens per chunk */
  minTokens?: number,
  /** Maximum tokens per chunk */
  maxTokens?: number,
  /** Relevance threshold for filtering chunks */
  relevanceThreshold?: number,
  /** Skip de-junking process flag */
  skipDeJunk?: boolean,
  /** Disable streaming flag */
  disableStreaming?: boolean,
  /** Unstructured processor specific settings */
  unstructured?: {
    /** Chunking strategy to use */
    chunkingStrategy?: "by_title" | "by_similarity" | "by_page" | "by_character",
    /** Maximum characters per chunk */
    maxCharacters?: number,
    /** Similarity threshold for chunk merging */
    similarityThreshold?: number,
    /** Include original elements flag */
    includeOriginalElements?: boolean,
    /** Multi-page sections flag */
    multipageSections?: boolean,
  },
>>>>>>> WA-170_MCP
}

/**
 * Client for interacting with the Cloudflare Chunks Workflow API
 */
export class ChunkWorkflowClient {
  private apiUrl: string;
  private cloudflareAccountId: string;
  private cloudflareApiToken: string;
  private auth0Token: string;
  private whitelabelId: string;
<<<<<<< HEAD
  private ragId?: string;
=======
  private ragVectors: Array<string>;
>>>>>>> WA-170_MCP
  private ragName?: string;
  private timeoutMs: number;

  /**
   * Create a new ChunkWorkflowClient
   *
   * @param config Client configuration options
   */
<<<<<<< HEAD
  constructor(config: ChunkWorkflowClientConfig) {
=======
  constructor(config: ChunkWorkflowClientConfig){
>>>>>>> WA-170_MCP
    this.apiUrl = config.apiUrl || "";
    this.cloudflareAccountId = config.cloudflareAccountId || requireEnvVar("CLOUDFLARE_ACCOUNT_ID");
    this.cloudflareApiToken = config.cloudflareApiToken || requireEnvVar("CLOUDFLARE_API_TOKEN");
    this.auth0Token = config.auth0Token;
    this.whitelabelId = config.whitelabelId;
<<<<<<< HEAD
    this.ragId = config.ragId;
=======
    this.ragVectors = config.ragVectors || [];
>>>>>>> WA-170_MCP
    this.ragName = config.ragName || "default";
    this.timeoutMs = config.timeoutMs || 30000; // Default 30s timeout

    // Log configuration for debugging
    console.log(`ChunkWorkflowClient initialized with:`, {
      apiUrl: this.apiUrl,
      whitelabelId: this.whitelabelId,
<<<<<<< HEAD
      ragId: this.ragId,
=======
      ragVectors: this.ragVectors,
>>>>>>> WA-170_MCP
      ragName: this.ragName,
      timeoutMs: this.timeoutMs
    });
  }

  /**
   * Get the API URL used by this client
   *
   * @returns The API URL
   */
<<<<<<< HEAD
  public getApiUrl(): string {
=======
  public getApiUrl(): string{
>>>>>>> WA-170_MCP
    return this.apiUrl;
  }

  /**
<<<<<<< HEAD
=======
   * Securely check if a URL is for local development
   * Uses strict validation to prevent URL spoofing attacks
   *
   * @param url The URL to check
   * @returns True if this is a valid local development URL
   */
  private isLocalDevelopmentUrl(url?: string): boolean{
    if(!url) return false;

    try {
      const parsedUrl = new URL(url);

      // Only allow specific local development patterns
      const validLocalPatterns = [
        // Localhost with various ports
        /^localhost(:\d+)?$/,
        // 127.0.0.1 with various ports
        /^127\.0\.0\.1(:\d+)?$/,
        // Docker service names (exact match)
        /^local-chunks-vectorized(:\d+)?$/,
        /^http:\/\/local-chunks-vectorized(:\d+)?$/
      ];

      const hostname = parsedUrl.hostname;
      const host = parsedUrl.host; // includes port

      // Check if hostname or host matches any valid pattern
      return validLocalPatterns.some(pattern=>pattern.test(hostname) || pattern.test(host) || pattern.test(url)
      );
    }catch(error) {
      // If URL parsing fails, it's not a valid local URL
      return false;
    }
  }

  /**
>>>>>>> WA-170_MCP
   * Submit a document to the Chunks Workflow
   *
   * @param fileLocation R2 file location information
   * @param processor Processor to use (unstructured or openparse)
   * @param processorConfig Processor configuration options
   * @param title Optional title for the document
   * @param description Optional description for the document
<<<<<<< HEAD
=======
   * @param target Optional target for the document (defaults to mongodb/rag-vector/{whitelabelId})
>>>>>>> WA-170_MCP
   * @returns Promise with submission response
   */
  public async submitDocument(
    fileLocation: R2FileLocation,
    processor: "unstructured" | "openparse",
    processorConfig?: ProcessorConfig,
    title?: string,
    description?: string,
<<<<<<< HEAD
  ): Promise<SubmitDocumentResponse> {
    const fileId = fileLocation.objectKey.split("/").pop() || "";
    
=======
    target?: string,
  ): Promise<SubmitDocumentResponse>{
    const fileId = fileLocation.objectKey.split("/").pop() || "";

>>>>>>> WA-170_MCP
    // For local development, ensure we're using the Docker service name
    const localUrl = "http://local-chunks-vectorized:8791";
    const endpoint = `${this.apiUrl ? this.apiUrl : localUrl}/api/process`;

<<<<<<< HEAD
=======
    // Use provided target or default to mongodb/rag-vector/{whitelabelId}
    const documentTarget = target || `mongodb/rag-vector/${this.whitelabelId}`;

>>>>>>> WA-170_MCP
    // Log detailed information about the request
    console.log(`🚀 Submitting document to chunks workflow:`, {
      endpoint,
      apiUrl: this.apiUrl || localUrl,
      fileId,
      processor,
      fileName: fileLocation.originalName,
      objectKey: fileLocation.objectKey,
<<<<<<< HEAD
      bucket: fileLocation.bucket
=======
      bucket: fileLocation.bucket,
      target: documentTarget
>>>>>>> WA-170_MCP
    });

    // Combine the vectorization config
    const vectorizeConfig = {
      accountId: this.cloudflareAccountId,
      apiToken: this.cloudflareApiToken,
      ragName: this.ragName,
<<<<<<< HEAD
      ragId: this.ragId || "",
=======
      ragVectors: this.ragVectors,
>>>>>>> WA-170_MCP
      whitelabelId: this.whitelabelId,
      auth0Token: this.auth0Token
    };

    // Prepare the request payload
    const payload = {
      files: [{
        fileId,
<<<<<<< HEAD
        target: fileLocation.bucket,
=======
        target: documentTarget,
>>>>>>> WA-170_MCP
        fileName: fileLocation.originalName,
        bucket: fileLocation.bucket,
        objectKey: fileLocation.objectKey,
        processor,
        processorConfig,
        title,
        description
      }],
      vectorizeConfig
    };

<<<<<<< HEAD
    // Special auth token for local development
    const token = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'local' 
      ? 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************.3zCjFdW8PKxj-2aTJO-M8_0FVeBoVdF8mWfJOZqQz7M'
      : this.auth0Token;

    try {
      // Make the request with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeoutMs);
=======
    // Special auth token for local development only
    // Use more secure URL validation to prevent spoofing
    const isLocalDevelopment = process.env.NODE_ENV === "development" &&
                              this.isLocalDevelopmentUrl(this.apiUrl);
    const token = isLocalDevelopment
      ? "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************.3zCjFdW8PKxj-2aTJO-M8_0FVeBoVdF8mWfJOZqQz7M"
      : this.auth0Token;

    console.log(`🔑 Using auth token for ${isLocalDevelopment ? "local development" : "production/staging"} (first 10 chars): ${token.substring(0, 10)}...`);

    try {
      // Make the request with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(()=>controller.abort(), this.timeoutMs);
>>>>>>> WA-170_MCP

      console.log(`📡 Sending request to chunks workflow API:`, {
        method: "POST",
        url: endpoint,
        timeout: this.timeoutMs
      });

      const response = await fetch(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        },
        body: JSON.stringify(payload),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      console.log(`📡 Chunks workflow API response:`, {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      });

<<<<<<< HEAD
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: "Failed to parse error response" }));
=======
      if(!response.ok) {
        const errorData = await response.json().catch(()=>({ error: "Failed to parse error response" }));
>>>>>>> WA-170_MCP
        console.error("❌ Failed to submit document to workflow:", errorData);
        return {
          success: false,
          error: errorData.error || `HTTP error: ${response.status} ${response.statusText}`
        };
      }

      const data = await response.json();
      console.log(`✅ Document submitted to workflow successfully:`, {
        success: data.success,
        workflowId: data.workflowId,
        objectKey: data.objectKey,
        id: data.id
      });

      return {
        success: data.success,
        workflowId: data.workflowId,
        objectKey: data.objectKey,
        id: data.id
      };
<<<<<<< HEAD
    } catch (error) {
=======
    }catch(error) {
>>>>>>> WA-170_MCP
      console.error("❌ Error submitting document to workflow:", {
        error: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : undefined
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      };
    }
  }

  /**
   * Submit a document using the stream endpoint
   *
   * @param fileBlob File blob to upload
   * @param fileLocation File location metadata
   * @param processor Processor to use
   * @param processorConfig Processor configuration
   * @returns Promise with submission response
   */
  public async submitDocumentStream(
    fileBlob: Blob,
    fileLocation: R2FileLocation,
    processor: "unstructured" | "openparse",
    processorConfig?: ProcessorConfig,
<<<<<<< HEAD
  ): Promise<SubmitDocumentResponse> {
    const endpoint = `${this.apiUrl}/api/process/stream`;
    const fileId = fileLocation.objectKey.split("/").pop() || "";

    // Special auth token for local development
    const token = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'local' 
      ? 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************.3zCjFdW8PKxj-2aTJO-M8_0FVeBoVdF8mWfJOZqQz7M'
=======
  ): Promise<SubmitDocumentResponse>{
    const endpoint = `${this.apiUrl}/api/process/stream`;
    const fileId = fileLocation.objectKey.split("/").pop() || "";

    // Special auth token for local development only
    // Use more secure URL validation to prevent spoofing
    const isLocalDevelopment = process.env.NODE_ENV === "development" &&
                              this.isLocalDevelopmentUrl(this.apiUrl);
    const token = isLocalDevelopment
      ? "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************.3zCjFdW8PKxj-2aTJO-M8_0FVeBoVdF8mWfJOZqQz7M"
>>>>>>> WA-170_MCP
      : this.auth0Token;

    // Vectorize config as JSON string
    const vectorizeConfig = JSON.stringify({
      accountId: this.cloudflareAccountId,
      apiToken: this.cloudflareApiToken,
      ragName: this.ragName,
<<<<<<< HEAD
      ragId: this.ragId || "",
=======
      ragVectors: this.ragVectors,
>>>>>>> WA-170_MCP
      whitelabelId: this.whitelabelId,
      auth0Token: token
    });

    // Processor config as JSON string
    const processorConfigStr = processorConfig ? JSON.stringify(processorConfig) : "";

    try {
      // Prepare form data with the file
      const formData = new FormData();
      formData.append("file", fileBlob, fileLocation.originalName);

      // Set up the request with timeout
      const controller = new AbortController();
<<<<<<< HEAD
      const timeoutId = setTimeout(() => controller.abort(), this.timeoutMs);
=======
      const timeoutId = setTimeout(()=>controller.abort(), this.timeoutMs);
>>>>>>> WA-170_MCP

      // Use any type to bypass TypeScript's type checking for the fetch body
      const fetchOptions: any = {
        method: "POST",
        headers: {
          "x-file-name": fileLocation.originalName,
          "x-file-id": fileId,
<<<<<<< HEAD
          "x-target": fileLocation.bucket,
=======
          "x-bucket": fileLocation.bucket,
          "x-object-key": fileLocation.objectKey,
>>>>>>> WA-170_MCP
          "x-processor": processor,
          "x-processor-config": processorConfigStr,
          "x-vectorize-config": vectorizeConfig,
          "Authorization": `Bearer ${token}`
        },
        body: formData,
        signal: controller.signal
      };

      const response = await fetch(endpoint, fetchOptions);

      clearTimeout(timeoutId);

<<<<<<< HEAD
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: "Failed to parse error response" }));
=======
      if(!response.ok) {
        const errorData = await response.json().catch(()=>({ error: "Failed to parse error response" }));
>>>>>>> WA-170_MCP
        console.error("Failed to submit document stream to workflow:", errorData);
        return {
          success: false,
          error: errorData.error || `HTTP error: ${response.status} ${response.statusText}`
        };
      }

      const data = await response.json();
      return {
        success: data.success,
        workflowId: data.id,
        objectKey: data.objectKey
      };
<<<<<<< HEAD
    } catch (error) {
=======
    }catch(error) {
>>>>>>> WA-170_MCP
      console.error("Error submitting document stream to workflow:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      };
    }
  }

  /**
   * Get the status of a workflow
   *
   * @param workflowId ID of the workflow to check
   * @returns Promise with workflow status
   */
<<<<<<< HEAD
  public async getWorkflowStatus(workflowId: string): Promise<WorkflowStatusResponse | null> {
    // The Cloudflare API endpoint for workflow status
    const endpoint = `https://api.cloudflare.com/client/v4/accounts/${this.cloudflareAccountId}/workers/dispatch/namespaces/CHUNKS_VECTORIZED/instances/${workflowId}`;
    
    // For local development environments, allow fallback
    const isLocalDev = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'local';
    if (isLocalDev && !workflowId.startsWith('wf-')) {
=======
  public async getWorkflowStatus(workflowId: string): Promise<WorkflowStatusResponse | null>{
    // The Cloudflare API endpoint for workflow status
    const endpoint = `https://api.cloudflare.com/client/v4/accounts/${this.cloudflareAccountId}/workers/dispatch/namespaces/CHUNKS_VECTORIZED/instances/${workflowId}`;

    // For local development environments, allow fallback
    const isLocalDev = process.env.NODE_ENV === "development" || process.env.NODE_ENV === "local";
    if(isLocalDev && !workflowId.startsWith("wf-")) {
>>>>>>> WA-170_MCP
      console.log(`⚠️ Skip workflow status check in local development for: ${workflowId}`);
      // Return mock successful status for local development
      return {
        status: "success",
        state: {
          name: "completed",
          startTime: new Date().toISOString(),
          endTime: new Date().toISOString()
        }
      };
    }

    try {
      const controller = new AbortController();
<<<<<<< HEAD
      const timeoutId = setTimeout(() => controller.abort(), this.timeoutMs);
=======
      const timeoutId = setTimeout(()=>controller.abort(), this.timeoutMs);
>>>>>>> WA-170_MCP

      const response = await fetch(endpoint, {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${this.cloudflareApiToken}`,
          "Content-Type": "application/json"
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

<<<<<<< HEAD
      if (!response.ok) {
        console.error(`Failed to get workflow status, HTTP ${response.status}: ${response.statusText}`);
        
        // For local development, return a mock success
        if (isLocalDev) {
=======
      if(!response.ok) {
        console.error(`Failed to get workflow status, HTTP ${response.status}: ${response.statusText}`);

        // For local development, return a mock success
        if(isLocalDev) {
>>>>>>> WA-170_MCP
          console.log("⚠️ Using mock workflow status for local development");
          return {
            status: "success",
            state: {
              name: "completed",
              startTime: new Date().toISOString(),
              endTime: new Date().toISOString()
            }
          };
        }
<<<<<<< HEAD
        
=======

>>>>>>> WA-170_MCP
        return null;
      }

      const data = await response.json();
      return data.result;
<<<<<<< HEAD
    } catch (error) {
      console.error("Error getting workflow status:", error);
      
      // For local development, return a mock success
      if (isLocalDev) {
=======
    }catch(error) {
      console.error("Error getting workflow status:", error);

      // For local development, return a mock success
      if(isLocalDev) {
>>>>>>> WA-170_MCP
        console.log("⚠️ Using mock workflow status for local development after error");
        return {
          status: "success",
          state: {
            name: "completed",
            startTime: new Date().toISOString(),
            endTime: new Date().toISOString()
          }
        };
      }
<<<<<<< HEAD
      
=======

>>>>>>> WA-170_MCP
      return null;
    }
  }

  /**
   * Maps workflow status to document status
   *
   * @param workflowStatus Status from the workflow
   * @returns The corresponding RagVectorTextChunksStatus
   */
<<<<<<< HEAD
  public mapWorkflowStatusToDocStatus(workflowStatus: string): RagVectorTextChunksStatus {
=======
  public mapWorkflowStatusToDocStatus(workflowStatus: string): RagVectorTextChunksStatus{
>>>>>>> WA-170_MCP
    // Map workflow statuses to document statuses
    const statusMap: Record<string, RagVectorTextChunksStatus> = {
      "initializeWorkflow": RagVectorTextChunksStatus.CHUNKING,
      "validateAndGetR2File": RagVectorTextChunksStatus.CHUNKING,
      "uploadToR2": RagVectorTextChunksStatus.CHUNKING,
      "ensureFileRecord": RagVectorTextChunksStatus.CHUNKING,
      "initializeProcessing": RagVectorTextChunksStatus.CHUNKING,
      "processDocumentBatch": RagVectorTextChunksStatus.CHUNKING,
      "filterChunks": RagVectorTextChunksStatus.STORING,
      "storeChunksInD1": RagVectorTextChunksStatus.STORING,
<<<<<<< HEAD
      "findOrCreateVectorIndex": RagVectorTextChunksStatus.STORING,
      "generateAndStoreEmbeddings": RagVectorTextChunksStatus.STORING,
=======
      // Vector operations should use VECTORIZING status if available
      "findOrCreateVectorIndex": (RagVectorTextChunksStatus as any).VECTORIZING || RagVectorTextChunksStatus.STORING,
      "generateAndStoreEmbeddings": (RagVectorTextChunksStatus as any).VECTORIZING || RagVectorTextChunksStatus.STORING,
>>>>>>> WA-170_MCP
      "completed": RagVectorTextChunksStatus.COMPLETED,
      "failed": RagVectorTextChunksStatus.FAILED_TO_CHUNK
    };

    return statusMap[workflowStatus] || RagVectorTextChunksStatus.CHUNKING;
  }

  /**
   * Wait for a workflow to complete with polling
   *
   * @param workflowId ID of the workflow to wait for
   * @param pollIntervalMs Polling interval in milliseconds
   * @param maxWaitTimeMs Maximum time to wait in milliseconds
   * @returns Promise with the final status or null if timeout
   */
  public async waitForWorkflowCompletion(
    workflowId: string,
    pollIntervalMs = 5000,
    maxWaitTimeMs = 600000 // 10 minutes default
<<<<<<< HEAD
  ): Promise<WorkflowStatusResponse | null> {
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTimeMs) {
      const status = await this.getWorkflowStatus(workflowId);

      if (!status) {
        // If we can't get the status, try again
        await new Promise(resolve => setTimeout(resolve, pollIntervalMs));
=======
  ): Promise<WorkflowStatusResponse | null>{
    const startTime = Date.now();

    while(Date.now() - startTime < maxWaitTimeMs) {
      const status = await this.getWorkflowStatus(workflowId);

      if(!status) {
        // If we can't get the status, try again
        await new Promise(resolve=>setTimeout(resolve, pollIntervalMs));
>>>>>>> WA-170_MCP
        continue;
      }

      // Check if workflow is completed or failed
<<<<<<< HEAD
      if (status.state.name === "completed" ||
=======
      if(status.state.name === "completed" ||
>>>>>>> WA-170_MCP
          status.state.name === "failed" ||
          status.error) {
        return status;
      }

      // Wait before checking again
<<<<<<< HEAD
      await new Promise(resolve => setTimeout(resolve, pollIntervalMs));
=======
      await new Promise(resolve=>setTimeout(resolve, pollIntervalMs));
>>>>>>> WA-170_MCP
    }

    // Timeout reached
    console.error(`Workflow ${workflowId} did not complete within the timeout period (${maxWaitTimeMs}ms)`);
    return null;
  }

  /**
   * Get file processing status from the API
   *
   * @param fileId ID of the file to check
   * @returns Promise with the file processing status
   */
<<<<<<< HEAD
  public async getFileProcessingStatus(fileId: string): Promise<{ status: RagVectorTextChunksStatus, failureReason?: string } | null> {
    const endpoint = `${this.apiUrl}/white-label/${this.whitelabelId}/rag-vector/files/${fileId}/status`;
    
    // Special auth token for local development
    const token = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'local' 
      ? 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************.3zCjFdW8PKxj-2aTJO-M8_0FVeBoVdF8mWfJOZqQz7M'
      : this.auth0Token;

    const isLocalDev = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'local';

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeoutMs);
=======
  public async getFileProcessingStatus(fileId: string): Promise<{ status: RagVectorTextChunksStatus, failureReason?: string } | null>{
    const endpoint = `${this.apiUrl}/white-label/${this.whitelabelId}/rag-vector/files/${fileId}/status`;

    // Special auth token for local development only
    // Use more secure URL validation to prevent spoofing
    const isLocalDevelopment = process.env.NODE_ENV === "development" &&
                              this.isLocalDevelopmentUrl(this.apiUrl);
    const token = isLocalDevelopment
      ? "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************.3zCjFdW8PKxj-2aTJO-M8_0FVeBoVdF8mWfJOZqQz7M"
      : this.auth0Token;

    const isLocalDev = process.env.NODE_ENV === "development" || process.env.NODE_ENV === "local";

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(()=>controller.abort(), this.timeoutMs);
>>>>>>> WA-170_MCP

      const response = await fetch(endpoint, {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json"
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

<<<<<<< HEAD
      if (!response.ok) {
        console.error(`Failed to get file processing status, HTTP ${response.status}: ${response.statusText}`);
        
        // For local development, return a mock success
        if (isLocalDev) {
=======
      if(!response.ok) {
        console.error(`Failed to get file processing status, HTTP ${response.status}: ${response.statusText}`);

        // For local development, return a mock success
        if(isLocalDev) {
>>>>>>> WA-170_MCP
          console.log("⚠️ Using mock file processing status for local development");
          return {
            status: RagVectorTextChunksStatus.COMPLETED
          };
        }
<<<<<<< HEAD
        
=======

>>>>>>> WA-170_MCP
        return null;
      }

      const data = await response.json();
      return {
        status: data.status as RagVectorTextChunksStatus,
        failureReason: data.failureReason
      };
<<<<<<< HEAD
    } catch (error) {
      console.error("Error getting file processing status:", error);
      
      // For local development, return a mock success
      if (isLocalDev) {
=======
    }catch(error) {
      console.error("Error getting file processing status:", error);

      // For local development, return a mock success
      if(isLocalDev) {
>>>>>>> WA-170_MCP
        console.log("⚠️ Using mock file processing status for local development after error");
        return {
          status: RagVectorTextChunksStatus.COMPLETED
        };
      }
<<<<<<< HEAD
      
=======

>>>>>>> WA-170_MCP
      return null;
    }
  }
}
