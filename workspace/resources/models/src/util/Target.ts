export type TargetLocation = {
  model: string,
  database: string,
};

export type UnraveledTarget = {
  id: string,
} & TargetLocation;

export type CondensedTarget = string;

export const CONDENSED_TARGET_REGEXP = /[a-zA-Z0-9]+\/[a-zA-Z0-9]+\/[a-zA-Z0-9]+/;

export const CONDENSED_TARGET_TEST = function(value: string): value is CondensedTarget{
  return CONDENSED_TARGET_REGEXP.test(value);
};

export function unravelTarget(value: string): UnraveledTarget{
  // Allow more flexible target formats for audio-RAG process
  if (value.includes('rag-vector')) {
    console.log(`⚙️ Processing RAG vector target format: ${value}`);
    const parts = value.split("/");
<<<<<<< HEAD
    
=======

>>>>>>> WA-170_MCP
    // For mongodb/rag-vector/{id} format
    if (parts.length === 3 && parts[0] === 'mongodb' && parts[1] === 'rag-vector') {
      console.log(`⚙️ Detected standard RAG vector target format`);
      return {
        database: parts[0],
        model: parts[1],
        id: parts[2]
      };
    }
  }
<<<<<<< HEAD
  
  // Try the standard format check
  if(!CONDENSED_TARGET_TEST(value)){
    console.error(`❌ Target value does not match standard format: ${value}`);
    
    // For flexibility, try to extract reasonable values from the non-standard format
    const parts = (value as string).split("/");
    
=======

  // Try the standard format check
  if(!CONDENSED_TARGET_TEST(value)){
    console.error(`❌ Target value does not match standard format: ${value}`);

    // For flexibility, try to extract reasonable values from the non-standard format
    const parts = (value as string).split("/");

>>>>>>> WA-170_MCP
    if (parts.length >= 1) {
      // Default values that will be overridden if found
      let database = "mongodb";
      let model = "rag-vector";
      let id = parts[0]; // Use the first part as ID by default
<<<<<<< HEAD
      
=======

>>>>>>> WA-170_MCP
      if (parts.length >= 3) {
        // If we have enough parts, use them as standard fields
        database = parts[0];
        model = parts[1];
        id = parts[2];
      } else if (parts.length === 2) {
        // If we have two parts, assume model/id
        model = parts[0];
        id = parts[1];
      }
<<<<<<< HEAD
      
      // If the ID is 'rag-vector', it's likely a malformed target
      // So use the next part or a default ID
      if (id === 'rag-vector' && parts.length > 3) {
        id = parts[3] || "682415a03d653676ebe89b06";
      }
      
      console.log(`⚙️ Reconstructed target from parts: ${database}/${model}/${id}`);
      return { database, model, id };
    }
    
    throw new Error("value is not a valid target");
  }
  
=======

      // If the ID is 'rag-vector', it's likely a malformed target
      // So use the next part or throw an error if not present
      if (id === 'rag-vector' && parts.length > 3) {
        if (parts[3]) {
          id = parts[3];
        } else {
          throw new Error("Target ID missing after 'rag-vector' in malformed target string: " + value);
        }
      }

      console.log(`⚙️ Reconstructed target from parts: ${database}/${model}/${id}`);
      return { database, model, id };
    }

    throw new Error("value is not a valid target");
  }

>>>>>>> WA-170_MCP
  const parts = value.split("/");
  if(parts.length !== 3) {
    console.error(`❌ Target should have exactly 3 parts: ${value}`);
    throw new Error("invalid target:" + value);
  }
<<<<<<< HEAD
  
=======

>>>>>>> WA-170_MCP
  return {
    database: parts[0],
    model: parts[1],
    id: parts[2]
  };
}

export function condenseTarget(target: UnraveledTarget): CondensedTarget{
  return `${target.database}/${target.model}/${target.id}`;
}
