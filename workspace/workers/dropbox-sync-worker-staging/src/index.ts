/**
 * Dropbox Sync Worker - Staging Version
 * Simplified version for staging deployment and testing
 */

import { Hono } from 'hono';

// Environment interface
interface Env {
  ENVIRONMENT: string;
  API_HOST: string;
  ALLOWED_ORIGINS: string;
}

// Create Hono app
const app = new Hono<{ Bindings: Env }>();

// Health check endpoint
app.get('/health', (c) => {
  return c.json({
    success: true,
    service: 'dropbox-sync-worker-staging',
    environment: c.env.ENVIRONMENT || 'staging',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    status: 'healthy'
  });
});

// Mock sync job trigger endpoint
app.post('/trigger-sync-job', async (c) => {
  try {
    const payload = await c.req.json();
    
    console.log('🚀 Staging: Received sync job trigger', {
      jobId: payload.jobId,
      whitelabelId: payload.whitelabelId,
      totalFiles: payload.sourceFiles?.length || 0
    });

    // Simulate workflow creation
    const workflowId = `staging-workflow-${payload.jobId}-${Date.now()}`;
    
    return c.json({
      success: true,
      data: {
        workflowId,
        jobId: payload.jobId,
        status: 'running',
        totalFiles: payload.sourceFiles?.length || 0,
        createdAt: new Date().toISOString(),
        message: 'Staging: Sync job triggered successfully (mock)'
      },
      message: 'Sync job started in staging mode'
    });

  } catch (error) {
    console.error('❌ Staging: Failed to trigger sync job:', error);
    
    return c.json({
      success: false,
      error: 'workflow_creation_failed',
      message: error instanceof Error ? error.message : 'Failed to create workflow'
    }, 500);
  }
});

// Mock workflow status endpoint
app.get('/workflow-status/:workflowId', async (c) => {
  const workflowId = c.req.param('workflowId');
  
  return c.json({
    success: true,
    data: {
      workflowId,
      status: 'completed',
      output: {
        success: true,
        processedFiles: 3,
        totalFiles: 3,
        message: 'Staging: Mock workflow completed successfully'
      },
      createdOn: new Date(Date.now() - 300000).toISOString(),
      modifiedOn: new Date().toISOString()
    }
  });
});

// Environment info endpoint
app.get('/info', (c) => {
  return c.json({
    success: true,
    data: {
      environment: c.env.ENVIRONMENT || 'staging',
      apiHost: c.env.API_HOST,
      allowedOrigins: c.env.ALLOWED_ORIGINS,
      timestamp: new Date().toISOString()
    }
  });
});

export default app;
