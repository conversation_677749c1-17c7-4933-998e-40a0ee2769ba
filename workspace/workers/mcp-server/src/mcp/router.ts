/**
 * MCP Router
 * Handles Model Context Protocol communication via Server-Sent Events
 */

import { Context } from 'hono';
import { 
  Server,
  Transport,
  CallToolRequest,
  ListToolsRequest,
  GetPromptRequest,
  ListPromptsRequest
} from '@modelcontextprotocol/sdk/server/index.js';
import { SSEServerTransport } from '@modelcontextprotocol/sdk/server/sse.js';

export class MCPRouter {
  private toolsHandler: any;

  constructor(toolsHandler: any) {
    this.toolsHandler = toolsHandler;
  }

  async handle(c: Context) {
    // Set up SSE headers
    c.header('Content-Type', 'text/event-stream');
    c.header('Cache-Control', 'no-cache');
    c.header('Connection', 'keep-alive');
    c.header('Access-Control-Allow-Origin', '*');
    c.header('Access-Control-Allow-Headers', 'Cache-Control');

    // Create MCP server
    const server = new Server(
      {
        name: 'divinci-ai-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
          prompts: {},
        },
      }
    );

    // Set up tool handlers
    server.setRequestHandler(ListToolsRequest, async () => {
      return {
        tools: await this.toolsHandler.listTools(c.req.user)
      };
    });

    server.setRequestHandler(CallToolRequest, async (request) => {
      return await this.toolsHandler.callTool(request.params.name, request.params.arguments, c.req.user, c.env);
    });

    server.setRequestHandler(ListPromptsRequest, async () => {
      return {
        prompts: await this.toolsHandler.listPrompts(c.req.user)
      };
    });

    server.setRequestHandler(GetPromptRequest, async (request) => {
      return await this.toolsHandler.getPrompt(request.params.name, request.params.arguments, c.req.user);
    });

    // Create SSE transport
    const transport = new SSEServerTransport('/sse', c.res);
    
    // Connect server to transport
    await server.connect(transport);

    // Keep connection alive
    return new Response(transport.stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      },
    });
  }
}
