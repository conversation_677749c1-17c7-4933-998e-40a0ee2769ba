#!/bin/bash

# <PERSON>ript to recreate PR branches with only the specific files from the current branch

# Check if PR number is provided
if [ -z "$1" ]; then
  echo "Usage: $0 <PR_NUMBER>"
  echo "Example: $0 1 (for PR-Split-1-GitHub-Actions)"
  exit 1
fi

PR_NUMBER=$1
ANALYSIS_FILE="pr-analysis/pr${PR_NUMBER}-files.txt"

# Map PR number to branch name
case $PR_NUMBER in
  1)
    PR_BRANCH="PR-Split-1-GitHub-Actions"
    PR_DESCRIPTION="GitHub Actions and CI/CD Improvements"
    ;;
  2)
    PR_BRANCH="PR-Split-2-mTLS-Security"
    PR_DESCRIPTION="mTLS Security Enhancements"
    ;;
  3)
    PR_BRANCH="PR-Split-3-Audio-Processing"
    PR_DESCRIPTION="Audio Processing and Pyannote Integration"
    ;;
  4)
    PR_BRANCH="PR-Split-4-OpenParse-RAG"
    PR_DESCRIPTION="Open-Parse Tool and RAG Workflow"
    ;;
  5)
    PR_BRANCH="PR-Split-5-Docker-Env"
    PR_DESCRIPTION="Docker and Environment Configuration"
    ;;
  6)
    PR_BRANCH="PR-Split-6-Client-Side"
    PR_DESCRIPTION="Client-Side Changes"
    ;;
  *)
    echo "Invalid PR number: $PR_NUMBER"
    exit 1
    ;;
esac

RECREATE_BRANCH="recreate-${PR_BRANCH}"
SOURCE_BRANCH="AS-211_AS-176-Workflow-Polish_2"

echo "Recreating branch for $PR_BRANCH: $PR_DESCRIPTION..."

# Check if analysis file exists
if [ ! -f "$ANALYSIS_FILE" ]; then
  echo "Analysis file not found: $ANALYSIS_FILE"
  exit 1
fi

# Create or checkout recreate branch from develop
if git show-ref --verify --quiet refs/heads/$RECREATE_BRANCH; then
  git checkout $RECREATE_BRANCH
else
  git checkout -b $RECREATE_BRANCH origin/develop
fi

# Get the list of files from the analysis file, skipping the header lines
FILES=$(tail -n +4 $ANALYSIS_FILE)

# Create a temporary directory to store the files
mkdir -p temp_files

# For each file in the list
for FILE in $FILES; do
  # Skip empty lines
  if [ -z "$FILE" ]; then
    continue
  fi
  
  # Skip lines that start with dashes (separator lines)
  if [[ $FILE == -* ]]; then
    continue
  fi
  
  echo "Processing file: $FILE"
  
  # Check if the file exists in the source branch
  if git show origin/$SOURCE_BRANCH:$FILE > /dev/null 2>&1; then
    # Create the directory structure if it doesn't exist
    mkdir -p $(dirname "temp_files/$FILE")
    
    # Copy the file from the source branch to the temporary directory
    git show origin/$SOURCE_BRANCH:$FILE > "temp_files/$FILE"
    
    # Create the directory structure in the target branch if it doesn't exist
    mkdir -p $(dirname "$FILE")
    
    # Copy the file to the target branch
    cp "temp_files/$FILE" "$FILE"
    
    # Add the file to the staging area
    git add "$FILE"
  else
    echo "File not found in $SOURCE_BRANCH: $FILE"
  fi
done

# Commit the changes
git commit -m "Recreate $PR_BRANCH with files from $SOURCE_BRANCH"

# Push the changes
git push -f --no-verify origin $RECREATE_BRANCH:$PR_BRANCH

# Clean up
rm -rf temp_files

echo "Branch $PR_BRANCH recreated successfully with only the specific files from $SOURCE_BRANCH!"
