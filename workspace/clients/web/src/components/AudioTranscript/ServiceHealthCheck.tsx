import React, { useEffect, useState } from 'react';
import { useAuth0Fetch } from "../../globals/auth0-user";
import styles from './ServiceHealthCheck.module.css';

interface ServiceStatus {
  service: string;
  status: 'available' | 'unavailable' | 'checking';
}

interface ServiceHealthCheckProps {
  services: string[];
  onStatusChange?: (statuses: Record<string, ServiceStatus>) => void;
}

/**
 * Component to check the health of audio processing services
 */
const ServiceHealthCheck: React.FC<ServiceHealthCheckProps> = ({ services, onStatusChange }) => {
  const [serviceStatuses, setServiceStatuses] = useState<Record<string, ServiceStatus>>({});
  const auth0Fetch = useAuth0Fetch();

  useEffect(() => {
    const checkServices = async () => {
      const newStatuses: Record<string, ServiceStatus> = {};

      // Initialize services with previous status or 'checking' if first run
      services.forEach(service => {
        const previousStatus = serviceStatuses[service];
        // Only use 'checking' for initial load, otherwise keep previous status
        newStatuses[service] = previousStatus || { service, status: 'checking' };
      });

      // Only update the UI with 'checking' state on initial load
      if (Object.keys(serviceStatuses).length === 0) {
        setServiceStatuses(newStatuses);
      }

      try {
        // Use the system health endpoint which doesn't require a whitelabel ID
        const url = '/system/health';
        console.log('Fetching service health from:', url);
        const response = await auth0Fetch(url);

        if (response.ok) {
<<<<<<< HEAD
          const data = await response.json();
=======
          interface ServiceHealthResponse {
            services?: {
              [key: string]: {
                status: string;
              };
            };
          }
          
          const data = await response.json() as ServiceHealthResponse;
>>>>>>> WA-170_MCP
          console.log('Service health response:', data);

          // Update statuses for all services based on the response
          if (data.services) {
            for (const service of services) {
              if (data.services[service]) {
                const newStatus = data.services[service].status === 'available' ? 'available' : 'unavailable';
                console.log(`Setting ${service} status to:`, newStatus);
                newStatuses[service] = {
                  service,
                  status: newStatus
                };
              } else {
                console.log(`Service ${service} not found in response, marking as unavailable`);
                newStatuses[service] = { service, status: 'unavailable' };
              }
            }
          } else {
            console.warn('No services property found in response:', data);
          }
        } else {
          // If the API call fails, mark all services as unavailable
          services.forEach(service => {
            newStatuses[service] = { service, status: 'unavailable' };
          });
        }
      } catch (error) {
        console.error('Error checking service health:', error);
        // If there's an error, mark all services as unavailable
        services.forEach(service => {
          newStatuses[service] = { service, status: 'unavailable' };
        });
      }

      // Only update statuses that have changed to avoid unnecessary re-renders
      const hasChanges = Object.values(newStatuses).some(newStatus => {
        const currentStatus = serviceStatuses[newStatus.service];
        return !currentStatus || currentStatus.status !== newStatus.status;
      });

      if (hasChanges) {
        console.log('Service statuses changed, updating UI');
        setServiceStatuses(newStatuses);

        // Notify parent component if callback is provided
        if (onStatusChange) {
          onStatusChange(newStatuses);
        }
      } else {
        console.log('No changes in service statuses');
      }
    };

    checkServices();

    // Check services every 60 seconds to reduce unnecessary API calls
    const interval = setInterval(checkServices, 60000);

    return () => clearInterval(interval);
  }, [services, onStatusChange, auth0Fetch]);

  return (
    <div className={styles.serviceHealthCheck}>
      <div className={styles.serviceStatuses}>
        {Object.values(serviceStatuses).map((status) => (
          <div key={status.service} className={styles.serviceStatus}>
            <span className={styles.serviceName}>{status.service}</span>
            <span className={styles.statusIndicator}>
              {status.status === 'checking' && (
                <div className={styles.spinner} title="Checking service status..." />
              )}
              {status.status === 'available' && (
                <div
                  className={styles.statusCircle}
                  style={{ backgroundColor: '#48c774' }}
                  title="Service is available"
                />
              )}
              {status.status === 'unavailable' && (
                <div
                  className={styles.statusCircle}
                  style={{ backgroundColor: '#f14668' }}
                  title="Service is unavailable"
                />
              )}
            </span>
          </div>
        ))}
      </div>

      {Object.values(serviceStatuses).some(s => s.status === 'unavailable') && (
        <div className="notification is-warning">
          <p>
            <strong>Warning:</strong> Some audio processing services are unavailable.
            Audio transcription may not work correctly.
          </p>
        </div>
      )}
    </div>
  );
};

export default ServiceHealthCheck;
