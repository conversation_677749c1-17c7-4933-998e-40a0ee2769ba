/**
 * User Group API
 *
 * This module provides API client functions for working with user groups.
 * It includes both real API calls and mock implementations for testing.
 */

import { ApiClient } from './api-client';
import { getAuthFileForRole, getUserIdFromAuthState, UserRole } from '../auth/auth-utils';
import { randomString } from '../utils/test-utils';

// Flag to use mock implementations for testing
const USE_MOCK = true;

/**
 * User group member
 */
export interface UserGroupMember {
  /** User ID */
  userId: string;
  /** User email */
  email: string;
}

/**
 * User group
 */
export interface UserGroup {
  /** User group ID */
  _id: string;
  /** User group name */
  name: string;
  /** User group slug */
  slug: string;
  /** Owner user ID */
  ownerUser: string;
  /** User group members */
  users: UserGroupMember[];
  /** User group invitations (emails) */
  invitations: string[];
}

// In-memory store for mock user groups
const mockUserGroups = new Map<string, UserGroup>();

/**
 * Create a user group
 * @param role User role
 * @param name User group name
 * @returns A promise that resolves with the created user group
 */
export async function createUserGroup(role: UserRole, name: string): Promise<UserGroup> {
  if (USE_MOCK) {
    return createMockUserGroup(role, name);
  }

  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);

  // Generate a unique slug
  const slug = `${name.toLowerCase().replace(/\s+/g, '-')}-${Date.now().toString(36)}`;

  // Create the user group
  return client.post<UserGroup>('user-group', {
    name,
    slug
  });
}

/**
 * Get a user group by ID
 * @param role User role
 * @param groupId User group ID
 * @returns A promise that resolves with the user group
 */
export async function getUserGroup(role: UserRole, groupId: string): Promise<UserGroup> {
  if (USE_MOCK) {
    return getMockUserGroup(role, groupId);
  }

  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);

  // Get the user group
  return client.get<UserGroup>(`user-group/${groupId}`);
}

/**
 * Get all user groups for the current user
 * @param role User role
 * @returns A promise that resolves with the user groups
 */
export async function getUserGroups(role: UserRole): Promise<UserGroup[]> {
  if (USE_MOCK) {
    return getMockUserGroups(role);
  }

  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);

  // Get the user groups
  return client.get<UserGroup[]>('user-group');
}

/**
 * Delete a user group
 * @param role User role
 * @param groupId User group ID
 * @returns A promise that resolves when the user group is deleted
 */
export async function deleteUserGroup(role: UserRole, groupId: string): Promise<void> {
  if (USE_MOCK) {
    return deleteMockUserGroup(role, groupId);
  }

  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);

  // Delete the user group
  await client.delete(`user-group/${groupId}`);
}

/**
 * Add a user invitation to a group
 * @param role User role
 * @param groupId User group ID
 * @param email Email to invite
<<<<<<< HEAD
 * @returns A promise that resolves with the updated user group
 */
export async function addUserInvitationToGroup(role: UserRole, groupId: string, email: string): Promise<UserGroup> {
=======
 * @returns A promise that resolves when the invitation is sent
 */
export async function addUserInvitationToGroup(role: UserRole, groupId: string, email: string): Promise<void> {
>>>>>>> WA-170_MCP
  if (USE_MOCK) {
    return addMockUserInvitationToGroup(role, groupId, email);
  }

  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);

  // Add the invitation
  await client.post(`user-group/${groupId}/invitation`, { email });
<<<<<<< HEAD

  // Return the updated group
  return getUserGroup(role, groupId);
=======
>>>>>>> WA-170_MCP
}

/**
 * Accept a user group invitation
 * @param role User role
 * @param groupId User group ID
 * @returns A promise that resolves when the invitation is accepted
 */
export async function acceptUserGroupInvitation(role: UserRole, groupId: string): Promise<void> {
  if (USE_MOCK) {
    return acceptMockUserGroupInvitation(role, groupId);
  }

  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);

  // Accept the invitation
  await client.post(`user-group/${groupId}/accept-invitation`, {});
}

/**
 * Remove a user from a group
 * @param role User role
 * @param groupId User group ID
 * @param userId User ID to remove
 * @returns A promise that resolves when the user is removed
 */
export async function removeUserFromGroup(role: UserRole, groupId: string, userId: string): Promise<void> {
  if (USE_MOCK) {
    return removeMockUserFromGroup(role, groupId, userId);
  }

  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);

  // Remove the user
  await client.delete(`user-group/${groupId}/user/${userId}`);
}

/**
 * Update a user group
 * @param role User role
 * @param groupId User group ID
 * @param updates Updates to apply
 * @returns A promise that resolves with the updated user group
 */
export async function updateUserGroup(
  role: UserRole,
  groupId: string,
  updates: Partial<Pick<UserGroup, 'name' | 'slug'>>
): Promise<UserGroup> {
  if (USE_MOCK) {
    return updateMockUserGroup(role, groupId, updates);
  }

  const authFile = getAuthFileForRole(role);
  const client = new ApiClient(undefined, authFile);

  // Update the user group
  return client.put<UserGroup>(`user-group/${groupId}`, updates);
}

// Mock implementations

/**
 * Create a mock user group
 * @param role User role
 * @param name User group name
 * @returns A promise that resolves with the created user group
 */
async function createMockUserGroup(role: UserRole, name: string): Promise<UserGroup> {
  console.log(`[Mock] Creating user group: ${name}`);

  const authFile = getAuthFileForRole(role);
  const userId = getUserIdFromAuthState(authFile) || `mock-${role}-user`;

  // Generate a unique ID and slug
  const id = `mock-group-${randomString()}`;
  const slug = `${name.toLowerCase().replace(/\s+/g, '-')}-${Date.now().toString(36)}`;

  // Create the user group
  const userGroup: UserGroup = {
    _id: id,
    name,
    slug,
    ownerUser: userId,
    users: [
      {
        userId,
        email: `test-${role}@divinci.app`
      }
    ],
    invitations: []
  };

  // Store the user group
  mockUserGroups.set(id, userGroup);

  console.log(`[Mock] Created user group: ${name} (${id})`);

  return userGroup;
}

/**
 * Get a mock user group by ID
 * @param role User role
 * @param groupId User group ID
 * @returns A promise that resolves with the user group
 */
async function getMockUserGroup(role: UserRole, groupId: string): Promise<UserGroup> {
  console.log(`[Mock] Getting user group: ${groupId}`);

  const userGroup = mockUserGroups.get(groupId);

  if (!userGroup) {
    throw new Error(`User group not found: ${groupId}`);
  }

  const authFile = getAuthFileForRole(role);
  const userId = getUserIdFromAuthState(authFile) || `mock-${role}-user`;

  // Check if the user is a member of the group
  const isMember = userGroup.ownerUser === userId || userGroup.users.some(user => user.userId === userId);

  if (!isMember) {
    const error = new Error(`User does not have permission to access group ${groupId}`);
    (error as any).status = 403;
    throw error;
  }

  console.log(`[Mock] Retrieved user group: ${userGroup.name} (${groupId})`);

  return userGroup;
}

/**
 * Get all mock user groups for the current user
 * @param role User role
 * @returns A promise that resolves with the user groups
 */
async function getMockUserGroups(role: UserRole): Promise<UserGroup[]> {
  console.log(`[Mock] Getting all user groups for role: ${role}`);

  const authFile = getAuthFileForRole(role);
  const userId = getUserIdFromAuthState(authFile) || `mock-${role}-user`;

  // Filter user groups by membership
  const userGroups = Array.from(mockUserGroups.values()).filter(group => {
    return group.ownerUser === userId || group.users.some(user => user.userId === userId);
  });

  console.log(`[Mock] Retrieved ${userGroups.length} user groups for role: ${role}`);

  return userGroups;
}

/**
 * Delete a mock user group
 * @param role User role
 * @param groupId User group ID
 * @returns A promise that resolves when the user group is deleted
 */
async function deleteMockUserGroup(role: UserRole, groupId: string): Promise<void> {
  console.log(`[Mock] Deleting user group: ${groupId}`);

  const userGroup = mockUserGroups.get(groupId);

  if (!userGroup) {
    throw new Error(`User group not found: ${groupId}`);
  }

  const authFile = getAuthFileForRole(role);
  const userId = getUserIdFromAuthState(authFile) || `mock-${role}-user`;

  // Check if the user is the owner of the group
  if (userGroup.ownerUser !== userId) {
    const error = new Error(`User does not have permission to delete group ${groupId}`);
    (error as any).status = 403;
    throw error;
  }

  // Delete the user group
  mockUserGroups.delete(groupId);

  console.log(`[Mock] Deleted user group: ${groupId}`);
}

/**
 * Add a mock user invitation to a group
 * @param role User role
 * @param groupId User group ID
 * @param email Email to invite
<<<<<<< HEAD
 * @returns A promise that resolves with the updated user group
 */
async function addMockUserInvitationToGroup(role: UserRole, groupId: string, email: string): Promise<UserGroup> {
=======
 * @returns A promise that resolves when the invitation is sent
 */
async function addMockUserInvitationToGroup(role: UserRole, groupId: string, email: string): Promise<void> {
>>>>>>> WA-170_MCP
  console.log(`[Mock] Adding invitation for user ${email} to group ${groupId}`);

  const userGroup = mockUserGroups.get(groupId);

  if (!userGroup) {
    throw new Error(`User group not found: ${groupId}`);
  }

  const authFile = getAuthFileForRole(role);
  const userId = getUserIdFromAuthState(authFile) || `mock-${role}-user`;

  // Check if the user is the owner of the group
  if (userGroup.ownerUser !== userId) {
    const error = new Error(`User does not have permission to invite users to group ${groupId}`);
    (error as any).status = 403;
    throw error;
  }

  // Check if the user is already a member of the group
  if (userGroup.users.some(user => user.email === email)) {
    console.log(`[Mock] User ${email} is already a member of group ${groupId}`);
<<<<<<< HEAD
    return userGroup;
=======
    return;
>>>>>>> WA-170_MCP
  }

  // Check if the user is already invited
  if (userGroup.invitations.includes(email)) {
    console.log(`[Mock] User ${email} is already invited to group ${groupId}`);
<<<<<<< HEAD
    return userGroup;
=======
    return;
>>>>>>> WA-170_MCP
  }

  // Add the invitation
  userGroup.invitations.push(email);

  console.log(`[Mock] Added invitation for user ${email} to group ${groupId}`);
<<<<<<< HEAD

  return userGroup;
=======
>>>>>>> WA-170_MCP
}

/**
 * Accept a mock user group invitation
 * @param role User role
 * @param groupId User group ID
 * @returns A promise that resolves when the invitation is accepted
 */
async function acceptMockUserGroupInvitation(role: UserRole, groupId: string): Promise<void> {
  console.log(`[Mock] Accepting invitation to group ${groupId}`);

  const userGroup = mockUserGroups.get(groupId);

  if (!userGroup) {
    throw new Error(`User group not found: ${groupId}`);
  }

  const authFile = getAuthFileForRole(role);
  const userId = getUserIdFromAuthState(authFile) || `mock-${role}-user`;
  const email = `test-${role}@divinci.app`;

  // Check if the user is already a member of the group
  if (userGroup.users.some(user => user.userId === userId)) {
    console.log(`[Mock] User is already a member of group ${groupId}`);
    return;
  }

  // Check if the user is invited
  if (!userGroup.invitations.includes(email)) {
    const error = new Error(`User is not invited to group ${groupId}`);
    (error as any).status = 403;
    throw error;
  }

  // Remove the invitation
  userGroup.invitations = userGroup.invitations.filter(invitation => invitation !== email);

  // Add the user to the group
  userGroup.users.push({
    userId,
    email
  });

  console.log(`[Mock] Accepted invitation to group ${groupId}`);
}

/**
 * Remove a mock user from a group
 * @param role User role
 * @param groupId User group ID
 * @param userId User ID to remove
 * @returns A promise that resolves when the user is removed
 */
async function removeMockUserFromGroup(role: UserRole, groupId: string, userId: string): Promise<void> {
  console.log(`[Mock] Removing user ${userId} from group ${groupId}`);

  const userGroup = mockUserGroups.get(groupId);

  if (!userGroup) {
    throw new Error(`User group not found: ${groupId}`);
  }

  const authFile = getAuthFileForRole(role);
  const currentUserId = getUserIdFromAuthState(authFile) || `mock-${role}-user`;

  // Check if the user is the owner of the group
  if (userGroup.ownerUser !== currentUserId) {
    const error = new Error(`User does not have permission to remove users from group ${groupId}`);
    (error as any).status = 403;
    throw error;
  }

  // Check if the user is a member of the group
  if (!userGroup.users.some(user => user.userId === userId)) {
    throw new Error(`User ${userId} is not a member of group ${groupId}`);
  }

  // Remove the user from the group
  userGroup.users = userGroup.users.filter(user => user.userId !== userId);

  console.log(`[Mock] Removed user ${userId} from group ${groupId}`);
}

/**
 * Update a mock user group
 * @param role User role
 * @param groupId User group ID
 * @param updates Updates to apply
 * @returns A promise that resolves with the updated user group
 */
async function updateMockUserGroup(
  role: UserRole,
  groupId: string,
  updates: Partial<Pick<UserGroup, 'name' | 'slug'>>
): Promise<UserGroup> {
  console.log(`[Mock] Updating user group: ${groupId}`);

  const userGroup = mockUserGroups.get(groupId);

  if (!userGroup) {
    throw new Error(`User group not found: ${groupId}`);
  }

  const authFile = getAuthFileForRole(role);
  const userId = getUserIdFromAuthState(authFile) || `mock-${role}-user`;

  // Check if the user is the owner of the group
  if (userGroup.ownerUser !== userId) {
    const error = new Error(`User does not have permission to update group ${groupId}`);
    (error as any).status = 403;
    throw error;
  }

  // Update the user group
  if (updates.name) {
    userGroup.name = updates.name;
  }

  if (updates.slug) {
    userGroup.slug = updates.slug;
  }

  console.log(`[Mock] Updated user group: ${userGroup.name} (${groupId})`);

  return userGroup;
}
