
import { dataSourceAudioCreateFile } from "@divinci-ai/actions";
import { runnerLocalMode } from "./local-mode";
import { ENVIRONMENT } from "../../../../../../../../globals/constants/clients";

<<<<<<< HEAD
export type RawBodyType = Parameters<typeof dataSourceAudioCreateFile>[2];
export type BodyType = Omit<RawBodyType, "mediaFile"> & { mediaFile: File[] | null | File };
=======
// Enhanced diarization configuration
export // Enhanced configuration interface supporting both open-source and official pyannote
interface DiarizeConfig {
  tool: string;
  numSpeakers?: 1 | 2;
  showAdvanced: boolean;
  // Confidence settings (only available for Official Pyannote API)
  confidence?: {
    enabled: boolean;
    threshold: number;
    includeInProcessing: boolean;
  };
}

// Voiceprint reference for identification
export interface VoiceprintReference {
  voiceprintId: string;
  speakerLabel: string;
  voiceprintData: string; // Base64 voiceprint data
}

// Voiceprint configuration
export interface VoiceprintConfig {
  enabled: boolean;
  existingVoiceprints: VoiceprintReference[];
  autoCreateFromHighConfidence: boolean;
  selectedVoiceprintIds: string[]; // IDs of selected voiceprints for identification
}

export type RawBodyType = Parameters<typeof dataSourceAudioCreateFile>[2];
export type BodyType = Omit<RawBodyType, "mediaFile" | "diarizerTool"> & {
  mediaFile: File[] | null | File;
  diarizerTool: string; // Keep backward compatibility
  diarizeConfig?: DiarizeConfig; // New enhanced config
  voiceprintConfig?: VoiceprintConfig; // Voiceprint settings
};
>>>>>>> WA-170_MCP
export type SuccessResultSingle = Awaited<ReturnType<typeof dataSourceAudioCreateFile>>;

// Choose the appropriate runner based on environment
const isLocalMode = ENVIRONMENT === 'local' || ENVIRONMENT === 'development';
export const runner = isLocalMode ? runnerLocalMode : dataSourceAudioCreateFile;
