/**
 * Fixed storage client implementation for handling file storage operations
 * with support for both R2 and MinIO S3 interfaces.
 */

import { Env } from "../types";

/**
 * Interface for a unified storage client that works with both R2 and MinIO
 */
export interface StorageClient {
  get(key: string): Promise<R2ObjectBody | null>,
  put(key: string, value: ReadableStream | ArrayBuffer | ArrayBufferView | string | null | Blob, options?: R2PutOptions): Promise<R2Object>,
  delete(key: string): Promise<void>,
  list(options?: R2ListOptions): Promise<R2Objects>,
}

/**
 * Creates an appropriate storage client based on the environment.
 * Uses MinIO client for local/dev environments and R2 client for production.
<<<<<<< HEAD
 */
export function createFixedStorageClient(env: Env): StorageClient {
  const isLocalEnvironment = env.ENVIRONMENT === "local" || env.ENVIRONMENT === "development";

  if (isLocalEnvironment) {
    console.log(`🔄 [FIXED-STORAGE] Using MinIO S3 client for local environment: ${env.ENVIRONMENT}`);
    console.log(`🔄 [FIXED-STORAGE] Storage URL: ${env.R2_BUCKET_URL}`);
    return new FixedMinioStorageClient(env);
  } else {
    console.log(`🔄 [FIXED-STORAGE] Using Cloudflare R2 client for environment: ${env.ENVIRONMENT}`);
    console.log(`🔄 [FIXED-STORAGE] Storage URL: ${env.R2_BUCKET_URL}`);
    return new FixedR2StorageClient(env);
=======
 * Respects FORCE_R2_STORAGE flag to use R2 even in local environments.
 */
export function createFixedStorageClient(env: Env): StorageClient {
  // Use the EXACT same logic as getWhitelabelVectorR2Instance from server-globals
  const isLocalMode = env.ENVIRONMENT === "local" || env.ENVIRONMENT === "development";
  const forceR2Storage = env.FORCE_R2_STORAGE === "true";

  console.log(`🔄 [API-COMPATIBLE-STORAGE] Environment: ${env.ENVIRONMENT}`);
  console.log(`🔄 [API-COMPATIBLE-STORAGE] Force R2 storage: ${forceR2Storage}`);
  console.log(`🔄 [API-COMPATIBLE-STORAGE] Is local mode: ${isLocalMode}`);

  // ALWAYS use MinIO for local development to avoid SSL handshake issues
  if (isLocalMode && !forceR2Storage) {
    console.log(`🔄 [API-COMPATIBLE-STORAGE] Using MinIO client for local development (avoiding SSL issues)`);
    return new ApiCompatibleMinioStorageClient(env);
  } else {
    // Production mode OR forced R2 mode - use Cloudflare R2 with same credentials as API
    const forcedMsg = (isLocalMode && forceR2Storage) ? ' (forced in local mode)' : '';
    console.log(`🔄 [API-COMPATIBLE-STORAGE] Using Cloudflare R2 client${forcedMsg}`);
    return new ApiCompatibleR2StorageClient(env);
>>>>>>> WA-170_MCP
  }
}

/**
<<<<<<< HEAD
 * R2 storage client implementation
 */
class FixedR2StorageClient implements StorageClient {
  constructor(private env: Env) {}

  async get(key: string): Promise<R2ObjectBody | null> {
    return this.env.R2.get(key);
  }

  async put(key: string, value: ReadableStream | ArrayBuffer | ArrayBufferView | string | null | Blob, options?: R2PutOptions): Promise<R2Object> {
    return this.env.R2.put(key, value, options);
  }

  async delete(key: string): Promise<void> {
    await this.env.R2.delete(key);
  }

  async list(options?: R2ListOptions): Promise<R2Objects> {
    return this.env.R2.list(options);
  }
}

/**
 * MinIO S3 storage client implementation using direct fetch calls
 */
class FixedMinioStorageClient implements StorageClient {
  private baseUrl: string;
  private buckets: string[] = ["rag-origin-files-local", "rag-files-local"];
  private accessKey: string;
  private secretKey: string;

  constructor(private env: Env) {
    // Use the R2_BUCKET_URL from environment if available, or DNS name
    this.baseUrl = this.env.R2_BUCKET_URL || "http://minio.divinci.local:9000";
    this.accessKey = this.env.R2_ACCESS_KEY_ID || "minioadmin";
    this.secretKey = this.env.R2_SECRET_ACCESS_KEY || "minioadmin";

    console.log(`🔄 [FIXED-STORAGE] Using MinIO endpoint: ${this.baseUrl}`);
  }

  /**
   * Gets a file from any of the configured buckets
   * @param key The object key to get
   * @returns The file content as a Response if found, null otherwise
   */
  async get(key: string): Promise<R2ObjectBody | null> {
    console.log(`🔄 [FIXED-STORAGE] Getting object: ${key}`);

    // Extract the whitelabelId from the key if it exists
    let whitelabelId = '';
    if (key.includes('/')) {
      whitelabelId = key.split('/')[0];
    }

    // Paths to try
    const pathsToTry = [
      key, // Original key
    ];

    // If the key has a timestamp prefix, also try just the filename
    if (key.includes('T') && key.includes('Z_')) {
      const parts = key.split('Z_');
      if (parts.length > 1) {
        const filename = parts[1];
        pathsToTry.push(filename);
      }
    }

    // If the key has a whitelabelId prefix, also try just the timestamp_filename part
    if (key.includes('/')) {
      const timestampFilename = key.split('/').pop() || '';
      pathsToTry.push(timestampFilename);
    }

    // Try each bucket
    for (const bucket of this.buckets) {
      // Try each path in the bucket
      for (const path of pathsToTry) {
        try {
          console.log(`🔄 [FIXED-STORAGE] Trying bucket: ${bucket}, path: ${path}`);

          // URLs to try
          const urlsToTry = [
            `${this.baseUrl}/${bucket}/${path}`, // Direct path
          ];

          // If we have a whitelabelId, also try with the whitelabelId folder
          if (whitelabelId && !path.includes('/')) {
            urlsToTry.push(`${this.baseUrl}/${bucket}/${whitelabelId}/${path}`);
          }

          // Try each URL
          for (const url of urlsToTry) {
            console.log(`🔄 [FIXED-STORAGE] Trying URL: ${url}`);

            // Try to get the object
            const response = await fetch(url, {
              headers: {
                'Authorization': `Basic ${btoa(`${this.accessKey}:${this.secretKey}`)}`
              }
            });

            if (response.ok) {
              console.log(`✅ [FIXED-STORAGE] File found at URL: ${url}`);

              // Get the content type and other metadata
              const contentType = response.headers.get('Content-Type') || 'application/octet-stream';
              const contentLength = parseInt(response.headers.get('Content-Length') || '0', 10);
              const etag = response.headers.get('ETag') || '';
              const lastModified = response.headers.get('Last-Modified') || new Date().toUTCString();

              // Create a blob from the content
              const blob = new Blob([], { type: contentType });

              // Create a new R2ObjectBody from the response
              const r2Object = {
                body: response.body,
                bodyUsed: false,
                headers: response.headers,
                ok: true,
                redirected: false,
                status: 200,
                statusText: 'OK',
                type: 'basic',
                url: response.url,
                clone: function() { return this; },
                arrayBuffer: async function() { return new ArrayBuffer(0); },
                blob: async function() { return blob; },
                formData: async function() { return new FormData(); },
                json: async function() { return {}; },
                text: async function() { return ''; },
                size: contentLength,
                key: key,
                httpMetadata: {
                  contentType: contentType
                },
                customMetadata: {}
              } as unknown as R2ObjectBody;

              return r2Object;
            }
          }
        } catch (error: any) {
          console.log(`⚠️ [FIXED-STORAGE] Error checking path ${path} in bucket ${bucket}: ${error.message}`);
        }
      }
    }

    console.log(`❌ [FIXED-STORAGE] File not found in any bucket: ${key}`);
    return null;
  }

  /**
   * Stub implementation for put
   */
  async put(key: string, value: ReadableStream | ArrayBuffer | ArrayBufferView | string | null | Blob, options?: R2PutOptions): Promise<R2Object> {
    console.log(`🔄 [FIXED-STORAGE] Putting object: ${key}`);

    // Create a dummy R2Object to return
    return {
      key,
      version: "",
      size: 0,
      etag: "",
      httpEtag: "",
      uploaded: new Date(),
      httpMetadata: {
        contentType: options?.httpMetadata?.contentType || 'application/octet-stream'
      },
      customMetadata: options?.customMetadata || {},
    } as R2Object;
  }

  /**
   * Stub implementation for delete
   */
  async delete(key: string): Promise<void> {
    console.log(`🔄 [FIXED-STORAGE] Deleting object: ${key}`);
    // No-op
  }

  /**
   * Stub implementation for list
   */
  async list(options?: R2ListOptions): Promise<R2Objects> {
    console.log(`🔄 [FIXED-STORAGE] Listing objects`);

    // Return an empty list
=======
 * API-compatible R2 storage client that uses the same configuration as getWhitelabelVectorR2Instance
 * This ensures chunks are stored in the same R2 endpoint that the API checks
 */
class ApiCompatibleR2StorageClient implements StorageClient {
  private r2Client: any;
  private bucketName: string;

  constructor(private env: Env) {
    console.log(`🔄 [API-COMPATIBLE-R2] Creating R2 client with same config as getWhitelabelVectorR2Instance`);

    // Use the same bucket naming logic as the main storage client
    this.bucketName = this.getBucketName(env.ENVIRONMENT || "local");

    // Use the same credentials as getWhitelabelVectorR2Instance
    console.log(`🔍 [API-COMPATIBLE-R2] Using CLOUDFLARE_WHITELABEL_VECTOR credentials`);
    console.log(`🔍 [API-COMPATIBLE-R2] CLOUDFLARE_WHITELABEL_VECTOR_ACCESS_KEY_ID: ${env.CLOUDFLARE_WHITELABEL_VECTOR_ACCESS_KEY_ID ? 'SET' : 'UNDEFINED'}`);
    console.log(`🔍 [API-COMPATIBLE-R2] CLOUDFLARE_WHITELABEL_VECTOR_ACCESS_KEY_SECRET: ${env.CLOUDFLARE_WHITELABEL_VECTOR_ACCESS_KEY_SECRET ? 'SET' : 'UNDEFINED'}`);
    console.log(`🔍 [API-COMPATIBLE-R2] Target bucket: ${this.bucketName}`);

    // Initialize R2 client - will be set up in async methods
    this.r2Client = null;

    console.log(`✅ [API-COMPATIBLE-R2] R2 client configuration prepared`);
  }

  private getBucketName(environment: string): string {
    switch (environment) {
      case "local":
      case "development":
        return "rag-files-local";
      case "stage":
      case "staging":
        return "rag-origin-files-stage";
      case "production":
      case "prod":
        return "whitelabel-vector-index";
      default:
        return "rag-files-local";
    }
  }

  private async getR2Client() {
    if (!this.r2Client) {
      // Dynamic import to avoid fs issues in Cloudflare Workers
      const { S3Client } = await import('@aws-sdk/client-s3');

      // Use the same credentials as getWhitelabelVectorR2Instance
      const accessKeyId = this.env.CLOUDFLARE_WHITELABEL_VECTOR_ACCESS_KEY_ID;
      const secretAccessKey = this.env.CLOUDFLARE_WHITELABEL_VECTOR_ACCESS_KEY_SECRET;

      console.log(`🔍 [API-COMPATIBLE-R2] Using whitelabel vector credentials:`);
      console.log(`🔍 [API-COMPATIBLE-R2] accessKeyId: ${accessKeyId ? accessKeyId.substring(0, 8) + '...' : 'UNDEFINED'}`);
      console.log(`🔍 [API-COMPATIBLE-R2] secretAccessKey: ${secretAccessKey ? secretAccessKey.substring(0, 8) + '...' : 'UNDEFINED'}`);

      if (!accessKeyId || !secretAccessKey) {
        throw new Error(`Missing whitelabel vector R2 credentials: accessKeyId=${!!accessKeyId}, secretAccessKey=${!!secretAccessKey}`);
      }

      // Use the same endpoint configuration as getWhitelabelVectorR2Instance
      this.r2Client = new S3Client({
        region: 'auto',
        endpoint: 'https://r2.cloudflarestorage.com', // Same as R2_ENDPOINT in server-globals
        credentials: {
          accessKeyId: accessKeyId,
          secretAccessKey: secretAccessKey,
        },
        forcePathStyle: false,
      });

      console.log(`✅ [API-COMPATIBLE-R2] R2 client initialized with whitelabel vector credentials`);
    }
    return this.r2Client;
  }

  async get(key: string): Promise<R2ObjectBody | null> {
    console.log(`🔍 [API-COMPATIBLE-R2] Getting object: ${key} from ${this.bucketName} bucket`);

    try {
      const r2Client = await this.getR2Client();
      const { GetObjectCommand } = await import('@aws-sdk/client-s3');
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });
      const response = await r2Client.send(command);
      console.log(`✅ [API-COMPATIBLE-R2] Successfully retrieved object: ${key}`);
      return response.Body as R2ObjectBody;
    } catch (error: any) {
      if (error.name === 'NoSuchKey') {
        console.log(`❌ [API-COMPATIBLE-R2] Object not found: ${key}`);
        return null;
      }
      console.error(`❌ [API-COMPATIBLE-R2] Error getting object ${key}:`, error);
      throw error;
    }
  }

  async put(key: string, value: ReadableStream | ArrayBuffer | ArrayBufferView | string | null | Blob, options?: R2PutOptions): Promise<R2Object> {
    console.log(`📤 [API-COMPATIBLE-R2] Putting object: ${key} to ${this.bucketName} bucket`);

    try {
      const r2Client = await this.getR2Client();
      const { PutObjectCommand } = await import('@aws-sdk/client-s3');

      // Convert value to appropriate format for Cloudflare Workers
      let body = value;
      let size = 0;

      if (typeof value === 'string') {
        // Use TextEncoder instead of Buffer for Cloudflare Workers
        const encoder = new TextEncoder();
        body = encoder.encode(value);
        size = body.byteLength;
        console.log(`📤 [API-COMPATIBLE-R2] Converted string to Uint8Array, size: ${size} bytes`);
      }

      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        Body: body,
        ContentType: options?.httpMetadata?.contentType || 'application/json',
        Metadata: options?.customMetadata || {},
      });

      const response = await r2Client.send(command);
      console.log(`✅ [API-COMPATIBLE-R2] Successfully stored object: ${key} in ${this.bucketName}`);
      console.log(`✅ [API-COMPATIBLE-R2] ETag: ${response.ETag}`);

      // Return a mock R2Object that matches the interface
      return {
        key: key,
        version: response.VersionId || 'unknown',
        size: size,
        etag: response.ETag || 'unknown',
        httpEtag: response.ETag || 'unknown',
        checksums: {},
        uploaded: new Date(),
        httpMetadata: options?.httpMetadata || {},
        customMetadata: options?.customMetadata || {},
        range: undefined,
      } as R2Object;
    } catch (error) {
      console.error(`❌ [API-COMPATIBLE-R2] Error storing object ${key}:`, error);
      throw error;
    }
  }

  async delete(key: string): Promise<void> {
    console.log(`🗑️ [API-COMPATIBLE-R2] Deleting object: ${key} from ${this.bucketName} bucket`);
    const r2Client = await this.getR2Client();
    const { DeleteObjectCommand } = await import('@aws-sdk/client-s3');
    const command = new DeleteObjectCommand({
      Bucket: this.bucketName,
      Key: key,
    });
    await r2Client.send(command);
    console.log(`✅ [API-COMPATIBLE-R2] Successfully deleted object: ${key}`);
  }

  async list(options?: R2ListOptions): Promise<R2Objects> {
    console.log(`📋 [API-COMPATIBLE-R2] Listing objects from ${this.bucketName} bucket`);
    const r2Client = await this.getR2Client();
    const { ListObjectsV2Command } = await import('@aws-sdk/client-s3');
    const command = new ListObjectsV2Command({
      Bucket: this.bucketName,
      Prefix: options?.prefix,
      MaxKeys: options?.limit,
      ContinuationToken: options?.cursor,
    });

    const response = await r2Client.send(command);

    // Convert S3 response to R2Objects format
    return {
      objects: (response.Contents || []).map(obj => ({
        key: obj.Key || '',
        version: obj.ETag || 'unknown',
        size: obj.Size || 0,
        etag: obj.ETag || 'unknown',
        httpEtag: obj.ETag || 'unknown',
        checksums: {},
        uploaded: obj.LastModified || new Date(),
        httpMetadata: {},
        customMetadata: {},
        range: undefined,
      })),
      truncated: response.IsTruncated || false,
      cursor: response.NextContinuationToken,
      delimitedPrefixes: [],
    } as R2Objects;
  }
}

/**
 * API-compatible MinIO storage client that uses the same configuration as getWhitelabelVectorR2Instance
 */
class ApiCompatibleMinioStorageClient implements StorageClient {
  private baseUrl: string;
  private accessKey: string;
  private secretKey: string;
  private bucketName: string;

  constructor(private env: Env) {
    // Use the EXACT same configuration as getWhitelabelVectorR2Instance for local mode
    this.baseUrl = env.MINIO_ENDPOINT || "http://minio.divinci.local:9000";
    this.accessKey = "minioadmin";
    this.secretKey = "minioadmin";

    // Use the same bucket naming logic as the main storage client
    this.bucketName = this.getBucketName(env.ENVIRONMENT || "local");

    console.log(`🔄 [API-COMPATIBLE-MINIO] Using MinIO endpoint: ${this.baseUrl}`);
    console.log(`🔄 [API-COMPATIBLE-MINIO] Using credentials: minioadmin/minioadmin`);
    console.log(`🔄 [API-COMPATIBLE-MINIO] Target bucket: ${this.bucketName}`);
  }

  private getBucketName(environment: string): string {
    switch (environment) {
      case "local":
      case "development":
        return "rag-files-local";
      case "stage":
      case "staging":
        return "rag-origin-files-stage";
      case "production":
      case "prod":
        return "whitelabel-vector-index";
      default:
        return "rag-files-local";
    }
  }

  async get(key: string): Promise<R2ObjectBody | null> {
    console.log(`🔍 [API-COMPATIBLE-MINIO] Getting object: ${key} from ${this.bucketName} bucket`);

    try {
      // Use the dynamic bucket name
      const getUrl = new URL(`${this.baseUrl}/${this.bucketName}/${key}`);
      console.log(`🔄 [API-COMPATIBLE-MINIO] Trying URL: ${getUrl.toString()}`);

      // Generate AWS signature v4 headers for GET request
      const { generateAuthorizationHeader } = await import('./aws-sig-v4');

      const authHeaders = await generateAuthorizationHeader(
        "GET",
        getUrl,
        this.accessKey,
        this.secretKey,
        null,
        ""
      );

      const response = await fetch(getUrl.toString(), {
        method: 'GET',
        headers: authHeaders,
      });

      if (response.ok) {
        console.log(`✅ [API-COMPATIBLE-MINIO] File found: ${key}`);

        // Get the content type and other metadata
        const contentType = response.headers.get('Content-Type') || 'application/octet-stream';
        const contentLength = parseInt(response.headers.get('Content-Length') || '0', 10);
        const etag = response.headers.get('ETag') || '';

        // Create a blob from the content
        const blob = new Blob([], { type: contentType });

        // Create a new R2ObjectBody from the response
        const r2Object = {
          body: response.body,
          bodyUsed: false,
          headers: response.headers,
          ok: true,
          redirected: false,
          status: 200,
          statusText: 'OK',
          type: 'basic',
          url: response.url,
          clone: function() { return this; },
          arrayBuffer: async function() { return new ArrayBuffer(0); },
          blob: async function() { return blob; },
          formData: async function() { return new FormData(); },
          json: async function() { return {}; },
          text: async function() { return ''; },
          size: contentLength,
          key: key,
          httpMetadata: {
            contentType: contentType
          },
          customMetadata: {}
        } as unknown as R2ObjectBody;

        return r2Object;
      } else if (response.status === 404) {
        console.log(`❌ [API-COMPATIBLE-MINIO] File not found: ${key}`);
        return null;
      } else {
        console.log(`❌ [API-COMPATIBLE-MINIO] Error getting file: ${key} (status: ${response.status})`);
        return null;
      }
    } catch (error: any) {
      console.log(`⚠️ [API-COMPATIBLE-MINIO] Error getting object ${key}: ${error.message}`);
      return null;
    }
  }

  async put(key: string, value: ReadableStream | ArrayBuffer | ArrayBufferView | string | null | Blob, options?: R2PutOptions): Promise<R2Object> {
    console.log(`📤 [API-COMPATIBLE-MINIO] Putting object: ${key} to ${this.bucketName} bucket`);

    try {
      // Convert value to ArrayBuffer for AWS signature
      let bodyData: ArrayBuffer;
      let bodyContent: string;

      if (typeof value === 'string') {
        bodyContent = value;
        const encoder = new TextEncoder();
        bodyData = encoder.encode(value).buffer;
      } else if (value instanceof ArrayBuffer) {
        bodyData = value;
        bodyContent = new TextDecoder().decode(value);
      } else if (value instanceof Blob) {
        bodyContent = await value.text();
        const encoder = new TextEncoder();
        bodyData = encoder.encode(bodyContent).buffer;
      } else {
        bodyContent = JSON.stringify(value);
        const encoder = new TextEncoder();
        bodyData = encoder.encode(bodyContent).buffer;
      }

      const contentType = options?.httpMetadata?.contentType || 'application/json';
      const contentLength = bodyData.byteLength;

      // Use the dynamic bucket name
      const putUrl = new URL(`${this.baseUrl}/${this.bucketName}/${key}`);
      console.log(`📤 [API-COMPATIBLE-MINIO] Storing at URL: ${putUrl.toString()}`);

      // Generate AWS signature v4 headers (import the function from the working MinIO client)
      const { generateAuthorizationHeader } = await import('./aws-sig-v4');

      const authHeaders = await generateAuthorizationHeader(
        "PUT",
        putUrl,
        this.accessKey,
        this.secretKey,
        bodyData,
        contentType
      );

      // Add required headers
      const headers = {
        ...authHeaders,
        "Content-Type": contentType,
        "Content-Length": String(contentLength),
      };

      // Add metadata headers
      if (options?.customMetadata) {
        for (const [k, v] of Object.entries(options.customMetadata)) {
          headers[`x-amz-meta-${k}`] = String(v);
        }
      }

      const response = await fetch(putUrl.toString(), {
        method: 'PUT',
        headers,
        body: new Uint8Array(bodyData),
      });

      if (!response.ok) {
        const errorText = await response.text().catch(() => `Status: ${response.status}`);
        throw new Error(`Failed to store object: ${response.status} ${response.statusText} - ${errorText}`);
      }

      console.log(`✅ [API-COMPATIBLE-MINIO] Successfully stored object: ${key} in ${this.bucketName} bucket`);

      // Return a proper R2Object
      return {
        key,
        version: "",
        size: contentLength,
        etag: response.headers.get('ETag') || "",
        httpEtag: response.headers.get('ETag') || "",
        uploaded: new Date(),
        httpMetadata: {
          contentType: contentType
        },
        customMetadata: options?.customMetadata || {},
      } as R2Object;

    } catch (error) {
      console.error(`❌ [API-COMPATIBLE-MINIO] Error storing object ${key}:`, error);
      throw error;
    }
  }

  async delete(key: string): Promise<void> {
    console.log(`🗑️ [API-COMPATIBLE-MINIO] Deleting object: ${key} from ${this.bucketName} bucket`);
    // No-op for now
  }

  async list(options?: R2ListOptions): Promise<R2Objects> {
    console.log(`📋 [API-COMPATIBLE-MINIO] Listing objects from ${this.bucketName} bucket`);

    // Return an empty list for now
>>>>>>> WA-170_MCP
    return {
      objects: [],
      truncated: false,
      delimitedPrefixes: []
    } as R2Objects;
  }
}
