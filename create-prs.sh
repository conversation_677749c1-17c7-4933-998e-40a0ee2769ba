#!/bin/bash

# Script to create pull requests for each PR branch

# PR 1: GitHub Actions and CI/CD Improvements
echo "Creating PR for PR-Split-1-GitHub-Actions..."
gh pr create \
  --base develop \
  --head PR-Split-1-GitHub-Actions \
  --title "PR-Split-1: GitHub Actions and CI/CD Improvements" \
  --body "## Description
Implements concurrent GitHub Actions, improves self-hosted runners, and enhances build/deploy workflows.

## Key Changes
- Updates GitHub Actions workflows for better concurrency
- Improves self-hosted runner configurations
- Enhances build/deploy workflows

## Notes
This is part of the PR split from the original PR #579 (AS-211_AS-176-Workflow-Polish_2).
This PR contains only the GitHub Actions and CI/CD related changes.

## Files Changed
- Files in .github/ directory
- Runner scripts and configurations
- Workflow-related files

## Merge Order
This PR should be merged second, after PR-Split-5-Docker-Env."

# PR 2: mTLS Implementation and Security
echo "Creating PR for PR-Split-2-mTLS-Security..."
gh pr create \
  --base develop \
  --head PR-Split-2-mTLS-Security \
  --title "PR-Split-2: mTLS Implementation and Security" \
  --body "## Description
Implements server-to-server mTLS, fixes CORS issues, and adds SSL/TLS testing.

## Key Changes
- Adds mTLS implementation for secure server-to-server communication
- Fixes CORS issues
- Adds SSL/TLS testing

## Notes
This is part of the PR split from the original PR #579 (AS-211_AS-176-Workflow-Polish_2).
This PR contains only the mTLS and security-related changes.

## Files Changed
- Files in mtls/ directory
- CORS-related changes in server files
- Security-related configurations

## Merge Order
This PR should be merged third, after PR-Split-1-GitHub-Actions."

# PR 3: Audio Processing and Pyannote Integration
echo "Creating PR for PR-Split-3-Audio-Processing..."
gh pr create \
  --base develop \
  --head PR-Split-3-Audio-Processing \
  --title "PR-Split-3: Audio Processing and Pyannote Integration" \
  --body "## Description
Implements polling for audio processing to fix Cloudflare timeout issues.

## Key Changes
- Adds polling mechanism for audio processing
- Integrates with Pyannote for speaker diarization
- Fixes Cloudflare timeout issues

## Notes
This is part of the PR split from the original PR #579 (AS-211_AS-176-Workflow-Polish_2).
This PR contains only the audio processing and Pyannote integration changes.

## Files Changed
- Audio processing and Pyannote-related files
- Polling implementation for long-running processes

## Merge Order
This PR should be merged fifth, after PR-Split-4-OpenParse-RAG."

# PR 4: Open-Parse Tool and RAG Workflow
echo "Creating PR for PR-Split-4-OpenParse-RAG..."
gh pr create \
  --base develop \
  --head PR-Split-4-OpenParse-RAG \
  --title "PR-Split-4: Open-Parse Tool and RAG Workflow" \
  --body "## Description
Enhances open-parse tool with file URL support and improves RAG workflow.

## Key Changes
- Adds file URL support to open-parse tool
- Improves RAG workflow
- Enhances chunking capabilities

## Notes
This is part of the PR split from the original PR #579 (AS-211_AS-176-Workflow-Polish_2).
This PR contains only the Open-Parse tool and RAG workflow changes.

## Files Changed
- Open-parse and RAG workflow related files
- Chunking implementation files

## Merge Order
This PR should be merged fourth, after PR-Split-2-mTLS-Security."

# PR 5: Docker and Environment Configuration
echo "Creating PR for PR-Split-5-Docker-Env..."
gh pr create \
  --base develop \
  --head PR-Split-5-Docker-Env \
  --title "PR-Split-5: Docker and Environment Configuration" \
  --body "## Description
Fixes Docker build reliability issues and updates environment configurations.

## Key Changes
- Improves Docker build reliability
- Updates environment configurations
- Adds support for GCP and Cloud Run

## Notes
This is part of the PR split from the original PR #579 (AS-211_AS-176-Workflow-Polish_2).
This PR contains only the Docker and environment configuration changes.

## Files Changed
- Docker files
- Environment configuration files

## Merge Order
This PR should be merged first to minimize merge conflicts."

# PR 6: Client-Side Changes
echo "Creating PR for PR-Split-6-Client-Side..."
gh pr create \
  --base develop \
  --head PR-Split-6-Client-Side \
  --title "PR-Split-6: Client-Side Changes" \
  --body "## Description
Updates client-side code to work with the new server features.

## Key Changes
- Updates client-side code to work with new server features
- Enhances UI components
- Improves client-side testing

## Notes
This is part of the PR split from the original PR #579 (AS-211_AS-176-Workflow-Polish_2).
This PR contains only the client-side changes.

## Files Changed
- Files in workspace/clients/ directory

## Merge Order
This PR should be merged last, after all other PRs have been merged."

echo "All pull requests created successfully!"
