import React, { useEffect, useState } from "react";

import { dataSourceAudioList } from "@divinci-ai/actions";
import { replaceParams } from "@divinci-ai/utils";
import { useParams } from "react-router";
import { Link } from "react-router-dom";
import { PATH_WHITELABEL_DATASOURCE_AUDIO_ITEM } from "../../../paths";
import { useAuth0Fetch } from "../../../../../../../../globals/auth0-user";
import { AudioTranscriptDoc, AudioTranscriptPopulated, AudioTranscriptStatus } from "@divinci-ai/models";

// Define a type for the audio transcript item
type AudioTranscriptItem = AudioTranscriptDoc & AudioTranscriptPopulated;

export function TranscriptList({ refreshInterval = 5000, highlightId = "" }) {
  const params = useParams();
  const auth0Fetch = useAuth0Fetch();
  const [transcripts, setTranscripts] = useState<AudioTranscriptItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Function to fetch the list of transcripts
  const fetchTranscripts = async () => {
    try {
      const data = await dataSourceAudioList(auth0Fetch, { whitelabelId: params.whitelabelId as string });
      setTranscripts(data);
      setLoading(false);
    } catch (err) {
      setError(err as Error);
      setLoading(false);
    }
  };

  // Fetch transcripts on mount and at the specified interval
  useEffect(() => {
    fetchTranscripts();
    
    // Set up polling interval
    const intervalId = setInterval(fetchTranscripts, refreshInterval);
    
    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, [params.whitelabelId, refreshInterval]);

  // Function to get status badge class based on process status
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case AudioTranscriptStatus.Completed:
        return "is-success";
      case AudioTranscriptStatus.Failed:
        return "is-danger";
      case AudioTranscriptStatus.Diarization:
      case AudioTranscriptStatus.Transcription:
        return "is-warning";
      default:
        return "is-light";
    }
  };

  if (loading) {
    return (
      <div className="section">
        <div className="container">
          <div className="has-text-centered">
            <span className="icon is-large">
              <i className="fas fa-spinner fa-pulse"></i>
            </span>
            <p>Loading transcripts...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="section">
        <div className="container">
          <div className="notification is-danger">
            <p className="has-text-weight-bold">Error loading transcripts</p>
            <p>{error.message}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="section pt-0">
      <div className="container">
        <div className="table-container">
          <table className="table is-fullwidth is-striped is-hoverable">
            <thead>
              <tr>
                <th>Title</th>
                <th>Source</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {transcripts.length === 0 ? (
                <tr>
                  <td colSpan={4} className="has-text-centered">
                    No audio transcripts found
                  </td>
                </tr>
              ) : (
                transcripts.map((audio) => (
                  <tr key={audio._id} className={audio._id === highlightId ? "is-selected" : ""}>
                    <td>
                      <Link
                        to={replaceParams(
                          PATH_WHITELABEL_DATASOURCE_AUDIO_ITEM,
                          { whitelabelId: params.whitelabelId, audioId: audio._id }
                        )}
                      >
                        {audio.userInfo.title}
                      </Link>
                    </td>
                    <td>{audio.sourceOrigin.sourceType}</td>
                    <td>
                      <span className={`tag ${getStatusBadgeClass(audio.processStatus)}`}>
                        {audio.processStatus}
                      </span>
                    </td>
                    <td>
                      <div className="buttons are-small">
                        <Link
                          className="button is-info"
                          to={replaceParams(
                            PATH_WHITELABEL_DATASOURCE_AUDIO_ITEM,
                            { whitelabelId: params.whitelabelId, audioId: audio._id }
                          )}
                        >
                          <span className="icon">
                            <i className="fas fa-eye"></i>
                          </span>
                          <span>View</span>
                        </Link>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
