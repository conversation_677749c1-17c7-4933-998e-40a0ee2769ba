.selectWidth {
  width: 100%;
}
.selectWithStatus {
  position: relative;
  display: inline-flex;
  align-items: center;
  width: 100%;
}

.statusIndicator {
  position: absolute;
  right: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  z-index: 1;
}

.statusContainer {
  display: flex;
  align-items: center;
  gap: 5px;
}

.statusText {
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

/* Spinner for checking state */
.spinner {
  width: 10px;
  height: 10px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-top-color: #3273dc;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  flex-shrink: 0;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Status circles for available/unavailable states */
.statusCircle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  flex-shrink: 0;
}

/* Disabled option styling */
.disabledOption {
  color: #999;
  font-style: italic;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .spinner {
    border-color: rgba(255, 255, 255, 0.1);
    border-top-color: #3273dc;
  }

  .disabledOption {
    color: #666;
  }

  .statusText {
    color: #e0e0e0;
  }
}
<<<<<<< HEAD
=======

/* Advanced options */
.advancedToggle {
  margin: 16px 0;
}

.advancedButton {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: #3298dc;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 0;
  transition: color 0.2s ease;
}

.advancedButton:hover {
  color: #2980b9;
}

.advancedButton:focus {
  outline: 2px solid #3298dc;
  outline-offset: 2px;
  border-radius: 4px;
}

.advancedOptions {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin-top: 8px;
}

/* Speaker count section */
.speakerCountSection {
  margin-bottom: 24px;
}

.sectionTitle {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.speakerCountOptions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.radioLabel {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  cursor: pointer;
  user-select: none;
  padding: 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.radioLabel:hover {
  background: #f8f9fa;
  border-color: #3298dc;
}

.radioInput {
  margin-top: 2px;
}

.radioText {
  font-weight: 500;
  color: #333;
  min-width: 100px;
}

.radioHelp {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

/* Responsive design */
@media (max-width: 768px) {
  .advancedOptions {
    padding: 16px;
  }

  .speakerCountOptions {
    gap: 8px;
  }

  .radioLabel {
    padding: 8px;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .radioText {
    min-width: auto;
  }
}
>>>>>>> WA-170_MCP
