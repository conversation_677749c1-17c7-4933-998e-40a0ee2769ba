🚀 Starting Codespace services...
🔍 Environment check:
   CODESPACE_NAME: effective-invention-xjwwp64g36r4j
   Current directory: /workspaces/server

🌐 Updating endpoint hostnames for Codespace: effective-invention-xjwwp64g36r4j
  📝 Updating endpoints.shared.env
    Before: API_HOST=effective-invention-xjwwp64g36r4j-9080.app.github.dev
    Before: API_LIVE_HOST=effective-invention-xjwwp64g36r4j-8082.app.github.dev
    After:  API_HOST=effective-invention-xjwwp64g36r4j-9080.app.github.dev
    After:  API_LIVE_HOST=effective-invention-xjwwp64g36r4j-8082.app.github.dev
    ✅ Updated hostnames in endpoints.shared.env
  📝 Updating web-client-dev.env
    Before: API_HOST=effective-invention-xjwwp64g36r4j-9080.app.github.dev
    Before: API_LIVE_HOST=effective-invention-xjwwp64g36r4j-8082.app.github.dev
    After:  API_HOST=effective-invention-xjwwp64g36r4j-9080.app.github.dev
    After:  API_LIVE_HOST=effective-invention-xjwwp64g36r4j-8082.app.github.dev
    ✅ Updated hostnames in web-client-dev.env
🌐 Updating CORS configuration for Codespace: effective-invention-xjwwp64g36r4j
✅ Updated CORS configuration with current Codespace origins
workspace/resources/models               |  WARN  The field "pnpm.neverBuiltDependencies" was found in /workspaces/server/workspace/resources/models/package.json. This will not take effect. You should configure "pnpm.neverBuiltDependencies" at the root of the workspace instead.
workspace/resources/server-globals       |  WARN  The field "pnpm.neverBuiltDependencies" was found in /workspaces/server/workspace/resources/server-globals/package.json. This will not take effect. You should configure "pnpm.neverBuiltDependencies" at the root of the workspace instead.
workspace/resources/server-permissions   |  WARN  The field "pnpm.neverBuiltDependencies" was found in /workspaces/server/workspace/resources/server-permissions/package.json. This will not take effect. You should configure "pnpm.neverBuiltDependencies" at the root of the workspace instead.
workspace/resources/server-tools         |  WARN  The field "pnpm.neverBuiltDependencies" was found in /workspaces/server/workspace/resources/server-tools/package.json. This will not take effect. You should configure "pnpm.neverBuiltDependencies" at the root of the workspace instead.
workspace/resources/utils                |  WARN  The field "pnpm.neverBuiltDependencies" was found in /workspaces/server/workspace/resources/utils/package.json. This will not take effect. You should configure "pnpm.neverBuiltDependencies" at the root of the workspace instead.
Scope: all 20 workspace projects
Progress: resolved 1, reused 0, downloaded 0, added 0
workspace/servers/shared                 |  WARN  deprecated rimraf@3.0.2

   ╭──────────────────────────────────────────╮
   │                                          │
   │   Update available! 10.11.0 → 10.12.1.   │
   │   Changelog: https://pnpm.io/v/10.12.1   │
   │     To update, run: pnpm add -g pnpm     │
   │                                          │
   ╰──────────────────────────────────────────╯

 WARN  deprecated eslint@8.57.1: This version is no longer supported. Please see https://eslint.org/version-support for other options.
workspace/clients/embed                  |  WARN  deprecated @types/dompurify@3.2.0
Progress: resolved 66, reused 0, downloaded 0, added 0
workspace/resources/server-globals       |  WARN  deprecated @types/redis@4.0.11
workspace/resources/server-globals       |  WARN  deprecated multer@1.4.5-lts.2
Progress: resolved 156, reused 1, downloaded 0, added 0
workspace/servers/public-api-live        |  WARN  deprecated @types/cookie@1.0.0
Progress: resolved 308, reused 1, downloaded 0, added 0
Progress: resolved 604, reused 1, downloaded 0, added 0
Progress: resolved 847, reused 1, downloaded 0, added 0
Progress: resolved 1154, reused 1, downloaded 0, added 0
Progress: resolved 1371, reused 1, downloaded 0, added 0
Progress: resolved 1739, reused 1, downloaded 0, added 0
Progress: resolved 2101, reused 1, downloaded 0, added 0
 WARN  14 deprecated subdependencies found: @humanwhocodes/config-array@0.13.0, @humanwhocodes/object-schema@2.0.3, @types/long@5.0.0, abab@2.0.6, domexception@4.0.0, glob@7.2.3, google-p12-pem@4.0.1, inflight@1.0.6, lodash.get@4.4.2, node-domexception@1.0.0, read-package-json@7.0.1, rimraf@2.7.1, superagent@8.1.2, trim@0.0.1

Progress: resolved 2115, reused 30, downloaded 0, added 0
Packages: +12 -81
++++++++++----------------------------------------------------------------------
Progress: resolved 2115, reused 89, downloaded 0, added 7
Progress: resolved 2115, reused 89, downloaded 0, added 12, done
workspace/resources/utils prepare$ rimraf ./dist && tsc
workspace/servers/shared prepare$ rimraf ./dist && tsc
. prepare$ husky
. prepare: Done
workspace/resources/utils prepare: Done
workspace/servers/shared prepare: Done
workspace/resources/models prepare$ rimraf ./dist && tsc
workspace/resources/server-utils prepare$ rimraf ./dist && tsc
workspace/resources/models prepare: Done
workspace/resources/server-utils prepare: Done
workspace/resources/actions prepare$ rimraf ./dist && tsc
workspace/resources/server-globals prepare$ rimraf ./dist && tsc
workspace/resources/tools prepare$ rimraf ./dist && tsc
workspace/clients/embed prepare$ rimraf ./dist && tsc --project tsconfig.json
workspace/resources/actions prepare: Done
workspace/resources/tools prepare: Done
workspace/clients/embed prepare: Done
workspace/resources/server-globals prepare: Done
workspace/resources/mtls prepare$ npm run build
workspace/clients/web prepare$ rimraf ./dist && tsc --skipLibCheck
workspace/resources/server-tools prepare$ rimraf ./dist && tsc --skipLibCheck
workspace/resources/mtls prepare: > @divinci-ai/mtls@0.1.0 build
workspace/resources/mtls prepare: > tsc
workspace/resources/mtls prepare: npm notice
workspace/resources/mtls prepare: npm notice New major version of npm available! 10.9.2 -> 11.4.2
workspace/resources/mtls prepare: npm notice Changelog: https://github.com/npm/cli/releases/tag/v11.4.2
workspace/resources/mtls prepare: npm notice To update run: npm install -g npm@11.4.2
workspace/resources/mtls prepare: npm notice
workspace/resources/mtls prepare: Done
workspace/resources/server-tools prepare: Done
workspace/clients/web prepare: Done
workspace/clients/tests prepare$ rimraf ./dist && tsc && node scripts/copy-html.js || echo 'TypeScript errors ignored'
workspace/resources/server-models prepare$ rimraf ./dist && tsc --skipLibCheck
workspace/clients/tests prepare: Copied src/globals/auth0/index.html to dist/globals/auth0/index.html
workspace/clients/tests prepare: Copied src/globals/stripe-subscription/index.html to dist/globals/stripe-subscription/index.html
workspace/clients/tests prepare: Copied src/globals/stripe-payment-method/index.html to dist/globals/stripe-payment-method/index.html
workspace/clients/tests prepare: Done
workspace/resources/server-models prepare: Done
workspace/servers/public-api-webhook prepare$ rimraf ./dist && tsc
workspace/resources/server-permissions prepare$ rimraf ./dist && tsc --skipLibCheck
workspace/servers/test-api prepare$ rimraf ./dist && tsc || echo 'ℹ️ TypeScript errors ignored'
workspace/resources/server-permissions prepare: Done
workspace/servers/test-api prepare: Done
workspace/servers/public-api-webhook prepare: Done
workspace/servers/public-api prepare$ rimraf ./dist && tsc
workspace/servers/public-api-live prepare$ rimraf ./dist && tsc
workspace/servers/public-api-live prepare: Done
workspace/servers/public-api prepare: Done
Done in 1m 6.8s using pnpm v10.11.0
[public-api] Building...

> @divinci-ai/public-api@0.3.0 build /workspaces/server/workspace/servers/public-api
> tsc

[public-api] Starting...
[public-api-live] Building...

> @divinci-ai/public-api-live@0.3.0 build /workspaces/server/workspace/servers/public-api-live
> tsc

[public-api-live] Starting...
[public-api-webhook] Building...

> @divinci-ai/public-api-webhook@0.3.0 build /workspaces/server/workspace/servers/public-api-webhook
> tsc

[public-api-webhook] Starting...
[web-client] Building...

> @divinci-ai/web-client@0.3.0 build /workspaces/server/workspace/clients/web
> webpack --mode development --config webpack.config.js

🐞LOG_DEBUG MODE:  1
set __env: /workspaces/server/workspace/clients/web/env
assets by status 30.9 KiB [cached] 3 assets
assets by chunk 16.5 MiB (id hint: vendors)
  assets by status 1.24 MiB [emitted]
    asset vendors-node_modules_mermaid_dist_createText-2e5e7dd3_js.hidden.build.js 1.11 MiB [emitted] [compared for emit] (id hint: vendors)
    asset vendors-node_modules_mermaid_dist_sankeyDiagram-04a897e0_js.hidden.build.js 131 KiB [emitted] (id hint: vendors)
  + 30 assets
asset hidden.build.js 48.8 MiB [emitted] [compared for emit] (name: main)
asset node_modules_mermaid_dist_classDiagram-v2-f2320105_js.hidden.build.js 30.1 KiB [compared for emit]
asset node_modules_mermaid_dist_flowDiagram-v2-96b9c2cf_js.hidden.build.js 6.12 KiB [compared for emit]
asset _6876-_31c0-_8290-_c430-_97c6-_cd79.hidden.build.js 2.41 KiB [compared for emit]
asset index.html 1.69 KiB [compared for emit]
asset _4860.hidden.build.js 691 bytes [compared for emit]
asset _411a.hidden.build.js 647 bytes [compared for emit]
orphan modules 1.2 MiB [orphan] 1100 modules
runtime modules 7.46 KiB 13 modules
modules by path ../../ 21.9 MiB (javascript) 30.9 KiB (asset)
  modules by path ../../../node_modules/ 21.6 MiB (javascript) 30.9 KiB (asset)
    javascript modules 21.5 MiB 1997 modules
    json modules 99.9 KiB 8 modules
    asset modules 30.9 KiB (asset) 126 bytes (javascript) 3 modules
  modules by path ../../resources/ 272 KiB 230 modules
modules by path ./ 1.92 MiB 735 modules
modules by mime type image/svg+xml 6.61 KiB
  data:image/svg+xml;base64,PHN2ZyB3aWR0aD0i.. 6.36 KiB [built] [code generated]
  data:image/svg+xml;utf8,<svg xmlns="http.. 252 bytes [built] [code generated]
+ 10 modules
webpack 5.99.9 compiled successfully in 31307 ms
[web-client] Starting...
All services started. Logs:
  public-api:         tail -f /workspaces/server/public-api.log
  public-api-live:    tail -f /workspaces/server/public-api-live.log
  public-api-webhook: tail -f /workspaces/server/public-api-webhook.log
  web-client:         tail -f /workspaces/server/web-client.log
To stop all: kill 34520 34619 34776 35159
❌ Service on port 8080 is not healthy (HTTP 000000)
✅ Service on port 9080 responded (HTTP 200)
✅ Service on port 8081 responded (HTTP 200)
✅ Service on port 8083 responded (HTTP 200)
Some services failed health checks. See logs above.
