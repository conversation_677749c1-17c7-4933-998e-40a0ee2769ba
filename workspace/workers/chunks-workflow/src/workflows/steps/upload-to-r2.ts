import { WorkflowStep } from "cloudflare:workers";
import { StepContext, R2ValidationResult, WorkflowFile } from "./types";

/**
 * Handles file upload if needed
 *
 * IMPORTANT: This function has been refactored to always upload files directly
 * without trying to find similar files or use special handling for LOCAL MODE.
 * This ensures consistent behavior between local and production environments.
 */
export async function uploadToR2(
  step: WorkflowStep,
  context: StepContext,
  r2Result: R2ValidationResult,
  files: WorkflowFile[]
<<<<<<< HEAD
): Promise<{ uploaded: boolean, size?: number, fromR2?: boolean, skipped?: boolean, error?: any }>{
=======
): Promise<{ uploaded: boolean, size?: number, fromR2?: boolean, skipped?: boolean, error?: any, actualObjectKey?: string }>{
>>>>>>> WA-170_MCP
  // Skip upload if file already exists
  if(r2Result.hasExistingFile) {
    console.log(`🔄 [UPLOAD] File already exists, skipping upload: ${r2Result.objectKey}`);
    return { uploaded: false, skipped: true };
  }

  return await step.do("uploadToR2", {
    retries: {
      limit: 3,
      delay: "5 seconds",
      backoff: "exponential"
    },
    timeout: "10 minutes"
  }, async ()=>{
    console.log("🔍 Debug - uploadToR2 files array:", {
      filesLength: files.length,
      file0: files[0] ? {
        file0: files[0]
      } : "undefined"
    });

    // If we have form data, use it to upload the file
    const file = files[0]?.formData?.get("file") as File;
    if(!file) {
      console.log("🔄 No file found in FormData, skipping upload step");
      // Return a dummy result to indicate that we're skipping the upload
      return { uploaded: false, skipped: true };
    }

    try {
      // Get the file content as ArrayBuffer
      const fileBuffer = await file.arrayBuffer();

      // Add some logging
      console.log(`📤 Uploading file to storage: ${r2Result.objectKey}, size: ${fileBuffer.byteLength} bytes, type: ${file.type}`);

<<<<<<< HEAD
      // Always use the original object key for consistency
      const uploadKey = r2Result.objectKey;

      await context.storageClient.put(uploadKey, fileBuffer, {
=======
      // Check if we're in local mode and need to handle timestamp prefixes
      const environment = context.env.ENVIRONMENT || "local";
      const forceR2Storage = context.env.FORCE_R2_STORAGE === "true";
      const isLocalMode = environment === "local" || environment === "development";

      let actualUploadKey = r2Result.objectKey;

      // In local mode with MinIO, add timestamp prefix to match expected behavior
      if (isLocalMode && !forceR2Storage) {
        // Add timestamp prefix to match MinIO behavior
        const timestamp = Date.now();
        const keyParts = r2Result.objectKey.split("/");
        const originalFilename = keyParts[keyParts.length - 1];
        keyParts[keyParts.length - 1] = `${timestamp}-${originalFilename}`;
        actualUploadKey = keyParts.join("/");

        console.log(`🔄 [UPLOAD] Using timestamped key for MinIO: ${actualUploadKey}`);

        // CRITICAL FIX: Update r2Result.objectKey to reflect the actual uploaded filename
        // This ensures subsequent steps use the correct timestamped filename
        r2Result.objectKey = actualUploadKey;
        console.log(`🔄 [UPLOAD] Updated r2Result.objectKey to: ${r2Result.objectKey}`);
      } else {
        console.log(`🔄 [UPLOAD] Using original key for R2: ${actualUploadKey}`);
      }

      await context.storageClient.put(actualUploadKey, fileBuffer, {
>>>>>>> WA-170_MCP
        httpMetadata: {
          contentType: file.type || "application/octet-stream"
        },
        customMetadata: {
          fileName: file.name,
          originalObjectKey: r2Result.objectKey,
<<<<<<< HEAD
=======
          actualObjectKey: actualUploadKey,
>>>>>>> WA-170_MCP
          uploadedAt: new Date().toISOString()
        }
      });

<<<<<<< HEAD
      console.log(`✅ Successfully uploaded file to storage: ${uploadKey}`);

      return { uploaded: true, size: fileBuffer.byteLength };
=======
      console.log(`✅ Successfully uploaded file to storage: ${actualUploadKey}`);

      // Update the r2Result with the actual key that was used
      r2Result.objectKey = actualUploadKey;

      return { uploaded: true, size: fileBuffer.byteLength, actualObjectKey: actualUploadKey };
>>>>>>> WA-170_MCP
    }catch(error) {
      console.error(`❌ Error uploading file to storage: ${error}`);
      throw error;
    }
  });
}
