import { getWhitelabelVectorR2Instance, getAudioR2Instance } from "@divinci-ai/server-globals";

// TEMPORARY TEST: Use Audio R2 client instead of Whitelabel Vector R2 client
// to see if the issue is with the Whitelabel Vector R2 client configuration
export const r2 = getAudioR2Instance();
console.log(`🧪 [TEMP TEST] Using Audio R2 client for chunks files instead of Whitelabel Vector R2 client`);

// Check if we're in local development mode
const isLocalMode = process.env.ENVIRONMENT === "local" ||
                    process.env.NODE_ENV === "development" ||
                    process.env.NODE_ENV === "local";

<<<<<<< HEAD
// Use different bucket names for local and production environments
export const WHITELABEL_VECTOR_INDEX_BUCKET = isLocalMode
  ? "rag-files-local"  // Use the bucket name created in the MinIO entrypoint script
  : "whitelabel-vector-index";  // Use the original bucket name for production
=======
// Check if we should force R2 usage even in local mode
const forceR2Storage = process.env.FORCE_R2_STORAGE === "true";

// Use different bucket names for local and production environments
export const WHITELABEL_VECTOR_INDEX_BUCKET = (isLocalMode && !forceR2Storage)
  ? "rag-files-local"  // Use the bucket name created in the MinIO entrypoint script
  : "workspace-audio";  // TEMPORARY TEST: Use workspace-audio instead of whitelabel-vector-index

console.log(`🧪 [TEMP TEST] Using workspace-audio bucket for chunks files instead of whitelabel-vector-index`);
console.log(`🪣 [API] Bucket selection - isLocalMode: ${isLocalMode}, forceR2Storage: ${forceR2Storage}`);
console.log(`🪣 [API] Selected bucket: ${WHITELABEL_VECTOR_INDEX_BUCKET}`);

console.log(`🪣 Using bucket: ${WHITELABEL_VECTOR_INDEX_BUCKET} for environment: ${process.env.ENVIRONMENT || process.env.NODE_ENV || "production"}, forceR2Storage: ${forceR2Storage}`);
>>>>>>> WA-170_MCP
