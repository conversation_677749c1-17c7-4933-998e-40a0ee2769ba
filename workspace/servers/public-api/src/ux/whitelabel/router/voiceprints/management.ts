import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { DataSourceAudioTranscriptModel } from "@divinci-ai/server-models";
import { checkJobStatus, listJobs } from "@divinci-ai/server-tools";
import { getWhitelabelTarget } from "../data-source/audio-transcript/util/whitelabel";

/**
 * Voiceprint Job Management Endpoints
 * 
 * Provides monitoring and management capabilities for voiceprint jobs
 */

/**
 * Get job statistics
 * GET /white-label/:whitelabelId/voiceprints/stats
 */
export const getJobStatistics: RequestHandler = async (req, res, next) => {
  try {
    console.log("📊 [VOICEPRINT-MGMT] Getting job statistics...");

    // Get whitelabel target for filtering
    const { target } = await getWhitelabelTarget(req);

    // Get audio documents with voiceprints for this whitelabel only
    const audioDocuments = await DataSourceAudioTranscriptModel.find({
      target,
      "voiceprints.0": { $exists: true }
    }).select("voiceprints");

    // Calculate statistics
    let total = 0;
    let succeeded = 0;
    let failed = 0;
    let processing = 0;

    for (const doc of audioDocuments) {
      if (doc.voiceprints) {
        for (const voiceprint of doc.voiceprints) {
          total++;
          switch (voiceprint.status) {
            case "succeeded":
              succeeded++;
              break;
            case "failed":
            case "canceled":
              failed++;
              break;
            case "created":
            case "pending":
            case "running":
              processing++;
              break;
          }
        }
      }
    }

    const stats = {
      total,
      succeeded,
      failed,
      processing,
      polling: 0, // We'll implement this later
      stuck: 0    // We'll implement this later
    };
    
    res.status(200).json({
      success: true,
      data: {
        statistics: stats,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    console.error("❌ [VOICEPRINT-MGMT] Failed to get statistics:", error);
    next(error);
  }
};

/**
 * Get all jobs with optional filtering
 * GET /white-label/:whitelabelId/voiceprints/jobs?status=created&limit=50
 */
export const getAllJobs: RequestHandler = async (req, res, next) => {
  try {
    console.log("📋 [VOICEPRINT-MGMT] Getting all jobs...");

    const { status, limit } = req.query;
    const { target } = await getWhitelabelTarget(req);

    // Get audio documents with voiceprints for this whitelabel only
    const audioDocuments = await DataSourceAudioTranscriptModel.find({
      target,
      "voiceprints.0": { $exists: true }
    }).select("_id voiceprints");

    let jobs: any[] = [];

    for (const doc of audioDocuments) {
      if (doc.voiceprints) {
        for (const voiceprint of doc.voiceprints) {
          jobs.push({
            jobId: voiceprint.jobId,
            audioDocId: doc._id.toString(),
            status: voiceprint.status,
            speakerLabel: voiceprint.speakerLabel,
            name: voiceprint.name,
            timestamp: voiceprint.timestamp,
            isPolling: false // We'll implement polling status later
          });
        }
      }
    }

    // Sort by timestamp (newest first)
    jobs = jobs.sort((a, b) => b.timestamp - a.timestamp);
    
    // Filter by status if provided
    if (status && typeof status === 'string') {
      jobs = jobs.filter(job => job.status === status);
    }
    
    // Limit results if provided
    if (limit && typeof limit === 'string') {
      const limitNum = parseInt(limit);
      if (!isNaN(limitNum) && limitNum > 0) {
        jobs = jobs.slice(0, limitNum);
      }
    }
    
    res.status(200).json({
      success: true,
      data: {
        jobs,
        pagination: {
          total: jobs.length,
          limit: limit ? parseInt(limit as string) : jobs.length,
          offset: 0
        }
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error("❌ [VOICEPRINT-MGMT] Failed to get jobs:", error);
    next(error);
  }
};

/**
 * Get stuck jobs
 * GET /white-label/:whitelabelId/voiceprints/stuck?maxAge=30
 */
export const getStuckJobs: RequestHandler = async (req, res, next) => {
  try {
    console.log("🚑 [VOICEPRINT-MGMT] Getting stuck jobs...");

    const { maxAge } = req.query;
    const maxAgeMinutes = maxAge && typeof maxAge === 'string' ? parseInt(maxAge) : 30;
    const cutoffTime = Date.now() - (maxAgeMinutes * 60 * 1000);
    const { target } = await getWhitelabelTarget(req);

    // Find jobs that are stuck (processing for too long) for this whitelabel only
    const audioDocuments = await DataSourceAudioTranscriptModel.find({
      target,
      "voiceprints": {
        $elemMatch: {
          status: { $in: ["created", "pending", "running"] },
          timestamp: { $lt: cutoffTime }
        }
      }
    }).select("_id voiceprints");

    const stuckJobs: any[] = [];

    for (const doc of audioDocuments) {
      if (doc.voiceprints) {
        for (const voiceprint of doc.voiceprints) {
          if (["created", "pending", "running"].includes(voiceprint.status) &&
              voiceprint.timestamp < cutoffTime) {
            stuckJobs.push({
              jobId: voiceprint.jobId,
              audioDocId: doc._id.toString(),
              status: voiceprint.status,
              speakerLabel: voiceprint.speakerLabel,
              name: voiceprint.name,
              timestamp: voiceprint.timestamp,
              ageMinutes: Math.round((Date.now() - voiceprint.timestamp) / 60000)
            });
          }
        }
      }
    }
    
    res.status(200).json({
      success: true,
      data: {
        stuckJobs,
        count: stuckJobs.length,
        maxAgeMinutes,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    console.error("❌ [VOICEPRINT-MGMT] Failed to get stuck jobs:", error);
    next(error);
  }
};

/**
 * Recover stuck jobs
 * POST /white-label/:whitelabelId/voiceprints/recover
 */
export const recoverStuckJobs: RequestHandler = async (req, res, next) => {
  try {
    console.log("🚑 [VOICEPRINT-MGMT] Starting stuck job recovery...");

    // Find stuck jobs (older than 30 minutes)
    const cutoffTime = Date.now() - (30 * 60 * 1000);

    const audioDocuments = await DataSourceAudioTranscriptModel.find({
      "voiceprints": {
        $elemMatch: {
          status: { $in: ["created", "pending", "running"] },
          timestamp: { $lt: cutoffTime }
        }
      }
    }).select("_id voiceprints");

    let recovered = 0;
    let failed = 0;

    for (const doc of audioDocuments) {
      if (doc.voiceprints) {
        for (const voiceprint of doc.voiceprints) {
          if (["created", "pending", "running"].includes(voiceprint.status) &&
              voiceprint.timestamp < cutoffTime) {

            try {
              // Mark as failed and ready for retry
              // Use a more robust approach to avoid schema casting issues
              const errorObject = {
                message: "Job timed out and was marked for recovery",
                type: "timeout",
                timestamp: Date.now(),
                source: "recovery-service"
              };

              await DataSourceAudioTranscriptModel.updateOne(
                { _id: doc._id, "voiceprints.jobId": voiceprint.jobId },
                {
                  $set: {
                    "voiceprints.$.status": "failed",
                    "voiceprints.$.error.message": errorObject.message,
                    "voiceprints.$.error.type": errorObject.type,
                    "voiceprints.$.error.timestamp": errorObject.timestamp,
                    "voiceprints.$.error.source": errorObject.source,
                    "voiceprints.$.lastUpdated": Date.now()
                  }
                }
              );

              recovered++;
              console.log(`✅ [RECOVERY] Marked stuck job as failed: ${voiceprint.jobId}`);

            } catch (error) {
              failed++;
              console.error(`❌ [RECOVERY] Failed to recover job ${voiceprint.jobId}:`, error);
            }
          }
        }
      }
    }

    const result = {
      totalStuck: recovered + failed,
      recovered,
      failed,
      retried: 0,
      cleaned: 0
    };
    
    res.status(200).json({
      success: true,
      data: {
        ...result,
        message: `Recovery complete: ${result.recovered} recovered, ${result.failed} failed, ${result.retried} retried, ${result.cleaned} cleaned`,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    console.error("❌ [VOICEPRINT-MGMT] Failed to recover stuck jobs:", error);
    next(error);
  }
};

/**
 * Retry a specific job
 * POST /white-label/:whitelabelId/voiceprints/retry/:jobId
 */
export const retryJob: RequestHandler = async (req, res, next) => {
  try {
    const { jobId } = req.params;

    console.log(`🔄 [VOICEPRINT-MGMT] Retrying job: ${jobId}`);

    // For now, return a placeholder response
    // TODO: Implement actual retry logic
    const success = false;
    
    if (success) {
      res.status(200).json({
        success: true,
        data: {
          jobId,
          message: "Job retry started successfully",
          timestamp: new Date().toISOString()
        }
      });
    } else {
      res.status(400).json({
        success: false,
        error: "Failed to retry job",
        jobId,
        timestamp: new Date().toISOString()
      });
    }
    
  } catch (error) {
    console.error("❌ [VOICEPRINT-MGMT] Failed to retry job:", error);
    next(error);
  }
};

/**
 * Get job details
 * GET /white-label/:whitelabelId/voiceprints/job/:jobId
 */
export const getJobDetails: RequestHandler = async (req, res, next) => {
  try {
    const { jobId } = req.params;

    console.log(`🔍 [VOICEPRINT-MGMT] Getting job details: ${jobId}`);

    // Find the job in the database
    const audioDoc = await DataSourceAudioTranscriptModel.findOne({
      "voiceprints.jobId": jobId
    }).select("_id voiceprints");

    let jobDetails = null;
    if (audioDoc && audioDoc.voiceprints) {
      const voiceprint = audioDoc.voiceprints.find((vp: any) => vp.jobId === jobId);
      if (voiceprint) {
        jobDetails = {
          jobId: voiceprint.jobId,
          audioDocId: audioDoc._id.toString(),
          status: voiceprint.status,
          speakerLabel: voiceprint.speakerLabel,
          name: voiceprint.name,
          timestamp: voiceprint.timestamp,
          isPolling: false
        };
      }
    }
    
    if (jobDetails) {
      res.status(200).json({
        success: true,
        data: {
          job: jobDetails,
          timestamp: new Date().toISOString()
        }
      });
    } else {
      res.status(404).json({
        success: false,
        error: "Job not found",
        jobId,
        timestamp: new Date().toISOString()
      });
    }
    
  } catch (error) {
    console.error("❌ [VOICEPRINT-MGMT] Failed to get job details:", error);
    next(error);
  }
};

/**
 * Health check for voiceprint system
 * GET /white-label/:whitelabelId/voiceprints/health
 */
export const getSystemHealth: RequestHandler = async (req, res, next) => {
  try {
    console.log("🏥 [VOICEPRINT-MGMT] Checking system health...");

    const { target } = await getWhitelabelTarget(req);

    // Get statistics using our simplified approach for this whitelabel only
    const audioDocuments = await DataSourceAudioTranscriptModel.find({
      target,
      "voiceprints.0": { $exists: true }
    }).select("voiceprints");

    let total = 0;
    let succeeded = 0;
    let failed = 0;
    let processing = 0;

    for (const doc of audioDocuments) {
      if (doc.voiceprints) {
        for (const voiceprint of doc.voiceprints) {
          total++;
          switch (voiceprint.status) {
            case "succeeded":
              succeeded++;
              break;
            case "failed":
            case "canceled":
              failed++;
              break;
            case "created":
            case "pending":
            case "running":
              processing++;
              break;
          }
        }
      }
    }

    const stats = { total, succeeded, failed, processing, polling: 0, stuck: 0 };
    const stuckJobs: any[] = []; // Simplified for now
    
    const health = {
      totalJobs: stats.total,
      activeJobs: stats.processing,
      systemStatus: "healthy",
      recommendations: [] as string[],
      status: "healthy",
      issues: [] as string[],
      statistics: stats,
      stuckJobsCount: stuckJobs.length
    };
    
    // Check for potential issues and generate recommendations
    if (stuckJobs.length > 5) {
      health.status = "warning";
      health.systemStatus = "warning";
      health.issues.push(`${stuckJobs.length} stuck jobs detected`);
      health.recommendations.push("Consider running recovery to fix stuck jobs");
    }

    if (stats.failed > stats.succeeded * 0.1) { // More than 10% failure rate
      health.status = "warning";
      health.systemStatus = "warning";
      health.issues.push("High failure rate detected");
      health.recommendations.push("Review failed jobs and consider retry operations");
    }

    if (stats.processing > 50) {
      health.status = "warning";
      health.systemStatus = "warning";
      health.issues.push("High number of processing jobs");
      health.recommendations.push("Monitor processing queue for potential bottlenecks");
    }

    if (health.recommendations.length === 0) {
      health.recommendations.push("System is operating normally");
    }
    
    res.status(200).json({
      success: true,
      data: {
        health
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error("❌ [VOICEPRINT-MGMT] Failed to check system health:", error);
    
    res.status(500).json({
      success: false,
      data: {
        health: {
          status: "error",
          issues: ["Failed to check system health"],
          error: error instanceof Error ? error.message : String(error)
        }
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get recovery health status
 * GET /white-label/:whitelabelId/voiceprints/recovery-health
 */
export const getRecoveryHealth: RequestHandler = async (req, res, next) => {
  try {
    console.log("🏥 [VOICEPRINT-MGMT] Checking recovery health...");

    // Find stuck jobs
    const cutoffTime = Date.now() - (30 * 60 * 1000);
    const stuckJobs = await DataSourceAudioTranscriptModel.countDocuments({
      "voiceprints": {
        $elemMatch: {
          status: { $in: ["created", "pending", "running"] },
          timestamp: { $lt: cutoffTime }
        }
      }
    });

    // Find old failed jobs that could be retried
    const retryCutoff = Date.now() - (60 * 60 * 1000); // 1 hour
    const oldFailedJobs = await DataSourceAudioTranscriptModel.countDocuments({
      "voiceprints": {
        $elemMatch: {
          status: "failed",
          timestamp: { $gt: retryCutoff },
          "error.retryCount": { $lt: 3 }
        }
      }
    });

    const health = {
      stuckJobs,
      oldFailedJobs,
      isRecoveryNeeded: stuckJobs > 0 || oldFailedJobs > 0,
      lastRecoveryRun: new Date()
    };

    res.status(200).json({
      success: true,
      data: {
        health,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error("❌ [VOICEPRINT-MGMT] Failed to check recovery health:", error);
    next(error);
  }
};

/**
 * Check Pyannote API status and compare with our database
 * GET /white-label/:whitelabelId/voiceprints/pyannote-status
 */
export const checkPyannoteStatus: RequestHandler = async (req, res, next) => {
  try {
    console.log("🔍 [PYANNOTE-CHECK] Checking Pyannote API status...");

    // Get recent jobs from our database (last 24 hours)
    const cutoffTime = Date.now() - (24 * 60 * 60 * 1000);
    const audioDocuments = await DataSourceAudioTranscriptModel.find({
      "voiceprints": {
        $elemMatch: {
          timestamp: { $gt: cutoffTime }
        }
      }
    }).select("voiceprints");

    const ourJobs: any[] = [];
    for (const doc of audioDocuments) {
      if (doc.voiceprints) {
        for (const voiceprint of doc.voiceprints) {
          if (voiceprint.timestamp > cutoffTime) {
            ourJobs.push({
              jobId: voiceprint.jobId,
              status: voiceprint.status,
              timestamp: voiceprint.timestamp,
              speakerLabel: voiceprint.speakerLabel
            });
          }
        }
      }
    }

    console.log(`📊 Found ${ourJobs.length} recent jobs in our database`);

    // Get jobs from Pyannote API
    let pyannoteJobs: any[] = [];
    let pyannoteError: any = null;

    try {
      const pyannoteResponse = await listJobs({ take: 50 });
      pyannoteJobs = pyannoteResponse.items;
      console.log(`📊 Found ${pyannoteJobs.length} jobs in Pyannote API`);
    } catch (error) {
      console.error("❌ Failed to fetch jobs from Pyannote API:", error);
      pyannoteError = error;
    }

    // Compare our jobs with Pyannote jobs
    const comparison = {
      ourJobsCount: ourJobs.length,
      pyannoteJobsCount: pyannoteJobs.length,
      missingInPyannote: [] as string[],
      statusMismatches: [] as any[],
      pyannoteApiAccessible: !pyannoteError
    };

    // Check which of our jobs are missing in Pyannote
    for (const ourJob of ourJobs) {
      const pyannoteJob = pyannoteJobs.find(pj => pj.id === ourJob.jobId);
      if (!pyannoteJob) {
        comparison.missingInPyannote.push(ourJob.jobId);
      } else if (pyannoteJob.status !== ourJob.status) {
        comparison.statusMismatches.push({
          jobId: ourJob.jobId,
          ourStatus: ourJob.status,
          pyannoteStatus: pyannoteJob.status
        });
      }
    }

    res.status(200).json({
      success: true,
      data: {
        comparison,
        ourJobs: ourJobs.slice(0, 10), // Show first 10 for debugging
        pyannoteJobs: pyannoteJobs.slice(0, 10), // Show first 10 for debugging
        pyannoteError: pyannoteError ? {
          message: pyannoteError.message,
          statusCode: pyannoteError.statusCode
        } : null,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error("❌ [PYANNOTE-CHECK] Failed to check Pyannote status:", error);
    next(error);
  }
};

/**
 * Check specific job status from Pyannote API
 * GET /white-label/:whitelabelId/voiceprints/check-job/:jobId
 */
export const checkSpecificJob: RequestHandler<{ whitelabelId: string; jobId: string }> = async (req, res, next) => {
  try {
    const { jobId } = req.params;
    console.log(`🔍 [JOB-CHECK] Checking job status for: ${jobId}`);

    // Check job status from Pyannote API
    const jobStatus = await checkJobStatus(jobId);

    console.log(`✅ [JOB-CHECK] Job status from Pyannote API:`, jobStatus);

    res.status(200).json({
      success: true,
      data: {
        jobId,
        pyannoteStatus: jobStatus,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    console.error(`❌ [JOB-CHECK] Failed to check job ${req.params.jobId}:`, error);

    res.status(200).json({
      success: false,
      error: {
        message: error.message,
        statusCode: error.statusCode,
        details: error.json
      },
      data: {
        jobId: req.params.jobId,
        timestamp: new Date().toISOString()
      }
    });
  }
};
