/**
 * Authentication Setup for API Tests
 *
 * This file sets up mock authentication for API tests.
 */

import { test as setup } from '@playwright/test';
import { ensureAuthDirExists, getAuthFileForRole, isAuthStateValid } from './auth-utils';
<<<<<<< HEAD
import { getAuth0AccessToken, getAuth0ServiceToken, validateAuth0Token } from './real-auth0';
=======
>>>>>>> WA-170_MCP
import fs from 'fs';

// Ensure the auth directory exists
ensureAuthDirExists();

<<<<<<< HEAD
// Configuration for real vs mock authentication
const USE_REAL_AUTH0 = process.env.USE_REAL_AUTH0 === 'true';

/**
 * Create a real Auth0 auth state file
 * @param authFile The path to the auth file
 * @param email The email to use
 * @param role The user role
 */
async function createRealAuthState(authFile: string, email: string, role: string): Promise<void> {
  try {
    console.log(`[Real Auth0] Getting real token for ${role} user: ${email}`);

    // Get a real Auth0 token
    let token: string;
    let isServiceToken = false;

    if (role === 'admin' || role === 'owner') {
      // Use service token for admin/owner roles
      console.log(`[Real Auth0] Using service token for ${role} role`);
      token = await getAuth0ServiceToken();
      isServiceToken = true;
    } else {
      // For user roles, try user token first, then fall back to service token
      console.log(`[Real Auth0] Attempting user token for ${role} role`);
      try {
        token = await getAuth0AccessToken({ email, password: process.env.AUTH0_TEST_USER_PASSWORD || '(abc123ABC)' });
        isServiceToken = false;
        console.log(`[Real Auth0] Successfully obtained user token for ${role}`);
      } catch (error) {
        console.log(`[Real Auth0] User token failed, using service token for ${role} role`);
        token = await getAuth0ServiceToken();
        isServiceToken = true;
      }
    }

    // Validate the token
    const isValid = await validateAuth0Token(token, isServiceToken);
    if (!isValid) {
      throw new Error(`Failed to validate Auth0 token for ${role}`);
    }

    // Create a storage state with the real token
    const storageState = {
      cookies: [],
      origins: [
        {
          origin: 'http://localhost:8080',
          localStorage: [
            {
              name: `@@auth0spajs@@::${process.env.AUTH0_CLIENT_ID || 'mock-client-id'}::${process.env.AUTH0_AUDIENCE || 'https://api.divinci.app'}::openid profile email`,
              value: JSON.stringify({
                body: {
                  access_token: token.replace('Bearer ', ''),
                  id_token: 'real-id-token',
                  scope: 'openid profile email',
                  expires_in: 86400,
                  token_type: 'Bearer'
                },
                expiresAt: Date.now() + (86400 * 1000) // 24 hours from now
              })
            },
            {
              name: 'auth0.user',
              value: JSON.stringify({
                sub: `auth0|${role}`,
                email: email,
                email_verified: true
              })
            }
          ]
        }
      ]
    };

    // Write the storage state to a file
    fs.writeFileSync(authFile, JSON.stringify(storageState, null, 2));
    console.log(`Real ${role} auth state saved to ${authFile}`);
  } catch (error) {
    console.error(`[Real Auth0] Failed to create real auth state for ${role}:`, error);
    // Fall back to mock auth
    console.log(`[Real Auth0] Falling back to mock auth for ${role}`);
    createMockAuthState(authFile, email, role);
  }
}

=======
>>>>>>> WA-170_MCP
/**
 * Create a mock auth state file
 * @param authFile The path to the auth file
 * @param email The email to use
 * @param role The user role
 */
function createMockAuthState(authFile: string, email: string, role: string): void {
  // Create a mock storage state
  const storageState = {
    cookies: [],
    origins: [
      {
        origin: 'http://localhost:8080',
        localStorage: [
          {
            name: `@@auth0spajs@@::mock-client-id::https://api.divinci.app::openid profile email`,
            value: JSON.stringify({
              body: {
                access_token: `mock-${role}-token`,
                id_token: 'mock-id-token',
                scope: 'openid profile email',
                expires_in: 86400,
                token_type: 'Bearer'
              },
              expiresAt: 9999999999
            })
          },
          {
            name: 'auth0.user',
            value: JSON.stringify({
              sub: `auth0|${role}`,
              email: email,
              email_verified: true
            })
          }
        ]
      }
    ]
  };

  // Write the storage state to a file
  fs.writeFileSync(authFile, JSON.stringify(storageState, null, 2));
  console.log(`Mock ${role} auth state saved to ${authFile}`);
}

// Set up authentication for admin user
setup('authenticate admin for API tests', async () => {
  const adminAuthFile = getAuthFileForRole('admin');

  // Check if the auth file already exists and is valid
  if (isAuthStateValid(adminAuthFile)) {
    console.log(`Admin auth state already exists and is valid: ${adminAuthFile}`);
    return;
  }

<<<<<<< HEAD
  // Create auth state (real or mock based on configuration)
  if (USE_REAL_AUTH0) {
    await createRealAuthState(adminAuthFile, '<EMAIL>', 'admin');
  } else {
    createMockAuthState(adminAuthFile, '<EMAIL>', 'admin');
  }
=======
  // Create a mock auth state
  createMockAuthState(adminAuthFile, '<EMAIL>', 'admin');
>>>>>>> WA-170_MCP
});

// Set up authentication for regular user
setup('authenticate user for API tests', async () => {
  const userAuthFile = getAuthFileForRole('user');

  // Check if the auth file already exists and is valid
  if (isAuthStateValid(userAuthFile)) {
    console.log(`User auth state already exists and is valid: ${userAuthFile}`);
    return;
  }

<<<<<<< HEAD
  // Create auth state (real or mock based on configuration)
  if (USE_REAL_AUTH0) {
    await createRealAuthState(userAuthFile, '<EMAIL>', 'user');
  } else {
    createMockAuthState(userAuthFile, '<EMAIL>', 'user');
  }
=======
  // Create a mock auth state
  createMockAuthState(userAuthFile, '<EMAIL>', 'user');
>>>>>>> WA-170_MCP
});

// Set up authentication for owner user
setup('authenticate owner for API tests', async () => {
  const ownerAuthFile = getAuthFileForRole('owner');

  // Check if the auth file already exists and is valid
  if (isAuthStateValid(ownerAuthFile)) {
    console.log(`Owner auth state already exists and is valid: ${ownerAuthFile}`);
    return;
  }

<<<<<<< HEAD
  // Create auth state (real or mock based on configuration)
  if (USE_REAL_AUTH0) {
    await createRealAuthState(ownerAuthFile, '<EMAIL>', 'owner');
  } else {
    createMockAuthState(ownerAuthFile, '<EMAIL>', 'owner');
  }
=======
  // Create a mock auth state
  createMockAuthState(ownerAuthFile, '<EMAIL>', 'owner');
>>>>>>> WA-170_MCP
});
