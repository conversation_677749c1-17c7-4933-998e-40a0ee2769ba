import React, { useState, useEffect } from 'react';
import { <PERSON>, CardH<PERSON>er, Card<PERSON><PERSON>le, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

/**
 * MCP Settings Management Component
 * 
 * This component allows users to configure and manage their MCP (Model Context Protocol) settings
 * including server configuration, tool preferences, and connection testing.
 */

interface MCPTool {
  name: string;
  description: string;
  enabled: boolean;
  lastUsed?: string;
}

interface MCPSettings {
  serverUrl: string;
  enabled: boolean;
  autoConnect: boolean;
  tools: MCPTool[];
  connectionStatus: 'connected' | 'disconnected' | 'connecting' | 'error';
  lastConnected?: string;
}

export const MCPSettingsManager: React.FC = () => {
  const [settings, setSettings] = useState<MCPSettings>({
    serverUrl: 'http://localhost:8793/sse',
    enabled: true,
    autoConnect: false,
    tools: [],
    connectionStatus: 'disconnected'
  });

  const [testResult, setTestResult] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Load MCP tools on component mount
  useEffect(() => {
    loadMCPTools();
  }, []);

  const loadMCPTools = async () => {
    try {
      const response = await fetch(`${settings.serverUrl}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: 1,
          method: 'tools/list',
          params: {}
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.result && data.result.tools) {
          const tools = data.result.tools.map((tool: any) => ({
            name: tool.name,
            description: tool.description,
            enabled: true
          }));
          setSettings(prev => ({ ...prev, tools, connectionStatus: 'connected' }));
        }
      }
    } catch (error) {
      console.error('Failed to load MCP tools:', error);
      setSettings(prev => ({ ...prev, connectionStatus: 'error' }));
    }
  };

  const testConnection = async () => {
    setIsLoading(true);
    setTestResult(null);

    try {
      // Test health endpoint
      const healthResponse = await fetch(`${settings.serverUrl.replace('/sse', '/health')}`);
      
      if (!healthResponse.ok) {
        throw new Error(`Health check failed: ${healthResponse.status}`);
      }

      const healthData = await healthResponse.json();

      // Test tools listing
      const toolsResponse = await fetch(settings.serverUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: 1,
          method: 'tools/list',
          params: {}
        })
      });

      if (!toolsResponse.ok) {
        throw new Error(`Tools listing failed: ${toolsResponse.status}`);
      }

      const toolsData = await toolsResponse.json();

      setTestResult(`✅ Connection successful! 
Server: ${healthData.service} v${healthData.version}
Tools available: ${toolsData.result.tools.length}
Status: ${healthData.status}`);

      setSettings(prev => ({ 
        ...prev, 
        connectionStatus: 'connected',
        lastConnected: new Date().toISOString()
      }));

    } catch (error) {
      setTestResult(`❌ Connection failed: ${error.message}`);
      setSettings(prev => ({ ...prev, connectionStatus: 'error' }));
    } finally {
      setIsLoading(false);
    }
  };

  const toggleTool = (toolName: string) => {
    setSettings(prev => ({
      ...prev,
      tools: prev.tools.map(tool =>
        tool.name === toolName ? { ...tool, enabled: !tool.enabled } : tool
      )
    }));
  };

  const saveSettings = async () => {
    try {
      // Save to localStorage or send to backend
      localStorage.setItem('mcpSettings', JSON.stringify(settings));
      setTestResult('✅ Settings saved successfully!');
    } catch (error) {
      setTestResult('❌ Failed to save settings');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'bg-green-500';
      case 'connecting': return 'bg-yellow-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🔌 MCP (Model Context Protocol) Settings
            <Badge className={getStatusColor(settings.connectionStatus)}>
              {settings.connectionStatus}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="connection" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="connection">Connection</TabsTrigger>
              <TabsTrigger value="tools">Tools</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
            </TabsList>

            <TabsContent value="connection" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="serverUrl">MCP Server URL</Label>
                  <Input
                    id="serverUrl"
                    value={settings.serverUrl}
                    onChange={(e) => setSettings(prev => ({ ...prev, serverUrl: e.target.value }))}
                    placeholder="http://localhost:8793/sse"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="enabled"
                    checked={settings.enabled}
                    onCheckedChange={(checked) => setSettings(prev => ({ ...prev, enabled: checked }))}
                  />
                  <Label htmlFor="enabled">Enable MCP Integration</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="autoConnect"
                    checked={settings.autoConnect}
                    onCheckedChange={(checked) => setSettings(prev => ({ ...prev, autoConnect: checked }))}
                  />
                  <Label htmlFor="autoConnect">Auto-connect on startup</Label>
                </div>

                <div className="flex gap-2">
                  <Button onClick={testConnection} disabled={isLoading}>
                    {isLoading ? 'Testing...' : 'Test Connection'}
                  </Button>
                  <Button onClick={loadMCPTools} variant="outline">
                    Refresh Tools
                  </Button>
                </div>

                {testResult && (
                  <Alert>
                    <AlertDescription className="whitespace-pre-line">
                      {testResult}
                    </AlertDescription>
                  </Alert>
                )}

                {settings.lastConnected && (
                  <p className="text-sm text-gray-600">
                    Last connected: {new Date(settings.lastConnected).toLocaleString()}
                  </p>
                )}
              </div>
            </TabsContent>

            <TabsContent value="tools" className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold mb-4">Available MCP Tools</h3>
                {settings.tools.length === 0 ? (
                  <p className="text-gray-600">No tools available. Please test connection first.</p>
                ) : (
                  <div className="space-y-3">
                    {settings.tools.map((tool) => (
                      <Card key={tool.name} className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <h4 className="font-medium">{tool.name}</h4>
                            <p className="text-sm text-gray-600">{tool.description}</p>
                            {tool.lastUsed && (
                              <p className="text-xs text-gray-500">
                                Last used: {new Date(tool.lastUsed).toLocaleString()}
                              </p>
                            )}
                          </div>
                          <Switch
                            checked={tool.enabled}
                            onCheckedChange={() => toggleTool(tool.name)}
                          />
                        </div>
                      </Card>
                    ))}
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Advanced Settings</h3>
                
                <div>
                  <Label>Claude Desktop Configuration</Label>
                  <div className="mt-2 p-4 bg-gray-100 rounded-md">
                    <p className="text-sm font-medium mb-2">Add this to your Claude Desktop config:</p>
                    <pre className="text-xs bg-white p-2 rounded border overflow-x-auto">
{`{
  "mcpServers": {
    "divinci": {
      "command": "npx",
      "args": ["mcp-remote", "${settings.serverUrl}"]
    }
  }
}`}
                    </pre>
                  </div>
                </div>

                <div>
                  <Label>MCP Inspector Testing</Label>
                  <div className="mt-2 p-4 bg-gray-100 rounded-md">
                    <p className="text-sm mb-2">Test with MCP Inspector:</p>
                    <code className="text-xs bg-white p-2 rounded border block">
                      npx @modelcontextprotocol/inspector@latest
                    </code>
                    <p className="text-xs text-gray-600 mt-1">
                      Then connect with: npx mcp-remote {settings.serverUrl}
                    </p>
                  </div>
                </div>

                <div>
                  <Label>Connection Statistics</Label>
                  <div className="mt-2 grid grid-cols-2 gap-4">
                    <div className="p-3 bg-gray-50 rounded">
                      <p className="text-sm font-medium">Tools Available</p>
                      <p className="text-2xl font-bold">{settings.tools.length}</p>
                    </div>
                    <div className="p-3 bg-gray-50 rounded">
                      <p className="text-sm font-medium">Enabled Tools</p>
                      <p className="text-2xl font-bold">
                        {settings.tools.filter(t => t.enabled).length}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <div className="mt-6 flex justify-end">
            <Button onClick={saveSettings}>Save Settings</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
