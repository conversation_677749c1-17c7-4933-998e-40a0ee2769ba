import React from "react";
import { Route } from "react-router";

import { relativePath } from "@divinci-ai/utils";

import {
  PATH_WHITELABEL_INDEX,
  PATH_WHITELABEL_SEARCH,
  PATH_WHITELABEL_ITEM,
  PATH_WHIT<PERSON><PERSON><PERSON>_MANAGE_SETTINGS,
  PATH_WH<PERSON><PERSON><PERSON><PERSON>_MANAGE_TRANSCRIPT,
  PATH_WH<PERSON>ELABEL_MANAGE_GRAPH,
  PATH_WH<PERSON><PERSON>ABEL_MANAGE_NOTIFICATION_SETTINGS,
  PATH_WHITELABEL_MANAGE_MCP_SETTINGS,
  PATH_WHITELABEL_THREADS_LIST,
  PATH_WHITELABEL_THREADS_ITEM,
  PATH_WHITELABEL_THREADS_ITEM_GRAPH,
  PATH_WHITELABEL_TRANSCRIPTID,
  PATH_WHITELABEL_VOICEPRINTS,
} from "./paths";

import { <PERSON>LabelIndex, WhiteLabelQuery } from "./root";

import {
  WhiteLabelItemOutlet,
  WhiteLabelSettings,
  WhitelabelTranscript,
  NotificationsSettingsForm,
  WhitelabelRedirectToTranscript,
  MCPSettings,
} from "./Setup";

import { WhitelabelVoiceprints } from "./Setup/Voiceprints";

import { QARoute } from "./Setup/QATesting";

import { ReleaseRoute } from "./Setup/Release";

import { AudioTranscriptRoute } from "./Setup/DataSources/AudioTranscript";
import { DropboxRoute } from "./Setup/DataSources/Dropbox";

import { FineTuneRoute } from "./Setup/ReleaseComponents/FineTune";
import { PromptModeratorRoute } from "./Setup/ReleaseComponents/PromptModerator";
import { MessagePrefixRoute } from "./Setup/ReleaseComponents/MessagePrefix";
import { ThreadPrefixRoute } from "./Setup/ReleaseComponents/ThreadPrefix";
import { RagVectorRoute } from "./Setup/ReleaseComponents/RagVector";
import { WhitelablePermissionRoute } from "./Permissions";

import {
  WhiteLabelThreadOutlet,
  WhitelabelThreadItem,
  WhitelabelThreadList,
} from "./Threads";

import { OutletOrForbidden } from "../Util/ErrorPage/Forbidden";
import { TranscriptGraph } from "../../components/Transcript/views/Graph";

export const WhiteLabelRoute = (
  <Route path={PATH_WHITELABEL_INDEX} element={<OutletOrForbidden />}>
    <Route index element={<WhiteLabelIndex />} />
    <Route
      path={relativePath(PATH_WHITELABEL_INDEX, PATH_WHITELABEL_SEARCH)}
      element={<WhiteLabelQuery />}
    />
    <Route
      path={relativePath(PATH_WHITELABEL_INDEX, PATH_WHITELABEL_ITEM)}
      element={<WhiteLabelItemOutlet />}
    >
      <Route index element={<WhitelabelRedirectToTranscript />} />
      <Route
        path={relativePath(
          PATH_WHITELABEL_ITEM,
          PATH_WHITELABEL_MANAGE_TRANSCRIPT
        )}
        element={<WhitelabelTranscript />}
      />
      <Route
        path={relativePath(PATH_WHITELABEL_ITEM, PATH_WHITELABEL_TRANSCRIPTID)}
        element={<WhitelabelTranscript />}
      />
      <Route
        path={relativePath(
          PATH_WHITELABEL_ITEM,
          PATH_WHITELABEL_MANAGE_SETTINGS
        )}
        element={<WhiteLabelSettings />}
      />
      <Route
        path={relativePath(
          PATH_WHITELABEL_ITEM,
          PATH_WHITELABEL_MANAGE_NOTIFICATION_SETTINGS
        )}
        element={<NotificationsSettingsForm />}
      />
      <Route
        path={relativePath(
          PATH_WHITELABEL_ITEM,
          PATH_WHITELABEL_MANAGE_MCP_SETTINGS
        )}
        element={<MCPSettings />}
      />
      {WhitelablePermissionRoute}
      {ReleaseRoute}

      {AudioTranscriptRoute}
      {DropboxRoute}

      {FineTuneRoute}
      {PromptModeratorRoute}
      {ThreadPrefixRoute}
      {MessagePrefixRoute}
      {RagVectorRoute}

      {QARoute}
      <Route
        path={relativePath(PATH_WHITELABEL_ITEM, PATH_WHITELABEL_MANAGE_GRAPH)}
        element={<TranscriptGraph />}
      />
      <Route
        path={relativePath(PATH_WHITELABEL_ITEM, PATH_WHITELABEL_THREADS_LIST)}
        element={<WhitelabelThreadList />}
      />
      <Route
        path={relativePath(PATH_WHITELABEL_ITEM, PATH_WHITELABEL_THREADS_ITEM)}
        element={<WhiteLabelThreadOutlet />}
      >
        <Route index element={<WhitelabelThreadItem />} />
        <Route
          path={relativePath(
            PATH_WHITELABEL_THREADS_ITEM,
            PATH_WHITELABEL_THREADS_ITEM_GRAPH
          )}
          element={<TranscriptGraph />}
        />
      </Route>
      <Route
        path={relativePath(PATH_WHITELABEL_ITEM, PATH_WHITELABEL_VOICEPRINTS)}
        element={<WhitelabelVoiceprints />}
      />
    </Route>
  </Route>
);
