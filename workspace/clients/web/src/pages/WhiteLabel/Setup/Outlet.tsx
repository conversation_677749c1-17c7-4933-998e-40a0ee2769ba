import React, { useEffect } from "react";
import { Outlet } from "react-router";

import { useParams } from "react-router";

import { useLocalMenu } from "../../../globals/local-menu";

import { White<PERSON>abelProvider, useWhite<PERSON>abel } from "./data/WhiteLabel";
import { OwnPermissionProvider } from "../../../components/Permission/data/OwnPermissionContext";
import { replaceParams } from "../../../util/router";

import {
  PATH_WHITELABEL_MANAGE_NOTIFICATION_SETTINGS,
  PATH_WHITELABEL_MANAGE_QATESTING,
  PATH_WHITELABEL_MANAGE_TRANSCRIPT,
  PATH_WHITELABEL_THREADS_LIST,
  PATH_WHITELABEL_TRANSCRIPTID,
  PATH_WHITELABEL_VOICEPRINTS,
} from "../paths";
import { PATH_WHITELABEL_MANAGE_RELEASE_INDEX } from "./Release/paths";
import { PATH_WHITELABEL_MANAGE_SETTINGS } from "../paths";
import { PATH_WHITELABEL_MANAGE_PERMISSIONS } from "../paths";
import { PATH_WHITELABEL_MANAGE_FINE_TUNE } from "./ReleaseComponents/FineTune/paths";
import { PATH_WHITELABEL_PROMPT_MODERATION } from "./ReleaseComponents/PromptModerator/paths";
import { PATH_WHITELABEL_THREAD_PREFIX } from "./ReleaseComponents/ThreadPrefix/paths";
import { PATH_WHITELABEL_MESSAGE_PREFIX } from "./ReleaseComponents/MessagePrefix/paths";
import { PATH_WHITELABEL_RAG_VECTOR } from "./ReleaseComponents/RagVector/paths";
import { PATH_WHITELABEL_DATASOURCE_AUDIO_ROOT } from "./DataSources/AudioTranscript";
import { PATH_WHITELABEL_DATASOURCE_DROPBOX_ROOT } from "./DataSources/Dropbox";

export function WhiteLabelItemOutlet(){
  const params = useParams();
  console.log("Whitelabel outlet:", params);
  return (
    <OwnPermissionProvider
      apiUrlPrefix={`/white-label/${params.whitelabelId}/permission`}
      defaultPermissions={[]}
    >
      <WhiteLabelProvider>
        <RequireWhiteLabel />
      </WhiteLabelProvider>
    </OwnPermissionProvider>
  );
}

function RequireWhiteLabel(){
  const params = useParams();
  const { whitelabel } = useWhiteLabel();
  const { setMenuRoot, setMenuOptions } = useLocalMenu();

  useEffect(()=>{
    if(!whitelabel) {
      return;
    }
    setMenuRoot({
      title: whitelabel.title,
      linkTo: replaceParams(PATH_WHITELABEL_TRANSCRIPTID, {
        whitelabelId: params.whitelabelId,
        transcriptId: whitelabel.transcriptIds[0],
      }),
    });

    setMenuOptions([
      {
        title: "⚙️ Settings",
        linkTo: replaceParams(PATH_WHITELABEL_MANAGE_SETTINGS, {
          whitelabelId: whitelabel._id,
        }),
      },
      {
        title: "👤 Permissions",
        linkTo: replaceParams(PATH_WHITELABEL_MANAGE_PERMISSIONS, {
          whitelabelId: whitelabel._id,
        }),
      },
      {
        title: "☑️ Releases",
        linkTo: replaceParams(PATH_WHITELABEL_MANAGE_RELEASE_INDEX, {
          whitelabelId: whitelabel._id,
        }),
      },
      {
        title: "📢 Notifications",
        linkTo: replaceParams(PATH_WHITELABEL_MANAGE_NOTIFICATION_SETTINGS, {
          whitelabelId: whitelabel._id,
        }),
      },
      {
        title: "📝 Audio Transcripts",
        linkTo: replaceParams(PATH_WHITELABEL_DATASOURCE_AUDIO_ROOT, {
          whitelabelId: whitelabel._id,
        })
      },
      {
<<<<<<< HEAD
=======
        title: "🎤 Voiceprints",
        linkTo: replaceParams(PATH_WHITELABEL_VOICEPRINTS, {
          whitelabelId: whitelabel._id,
        })
      },
      {
        title: "📦 Dropbox Integration",
        linkTo: replaceParams(PATH_WHITELABEL_DATASOURCE_DROPBOX_ROOT, {
          whitelabelId: whitelabel._id,
        })
      },
      {
>>>>>>> WA-170_MCP
        title: "🎚️ Fine Tune",
        linkTo: replaceParams(PATH_WHITELABEL_MANAGE_FINE_TUNE, {
          whitelabelId: whitelabel._id,
        }),
      },
      {
        title: "🎚️ Prompt Moderation",
        linkTo: replaceParams(PATH_WHITELABEL_PROMPT_MODERATION, {
          whitelabelId: whitelabel._id,
        }),
      },
      {
        title: "🎚️ Thread Prefix",
        linkTo: replaceParams(PATH_WHITELABEL_THREAD_PREFIX, {
          whitelabelId: whitelabel._id,
        }),
      },
      {
        title: "🎚️ Message Prefix",
        linkTo: replaceParams(PATH_WHITELABEL_MESSAGE_PREFIX, {
          whitelabelId: whitelabel._id,
        }),
      },
      {
        title: "🎚️ RAG Vector",
        linkTo: replaceParams(PATH_WHITELABEL_RAG_VECTOR, {
          whitelabelId: whitelabel._id,
        }),
      },
      {
        title: "💬 Transcript",
        linkTo: replaceParams(PATH_WHITELABEL_MANAGE_TRANSCRIPT, {
          whitelabelId: whitelabel._id,
        }),
      },
      {
        title: "📝 Quality Assurance",
        linkTo: replaceParams(PATH_WHITELABEL_MANAGE_QATESTING, {
          whitelabelId: whitelabel._id,
        }),
      },
      {
        title: "📑 Threads",
        linkTo: replaceParams(PATH_WHITELABEL_THREADS_LIST, {
          whitelabelId: whitelabel._id,
        }),
      },
    ]);
    return ()=>{
      setMenuRoot(null);
      setMenuOptions([]);
    };
  }, [whitelabel]);

  console.log("⚪️ Whitelabel: About to render the outlet: ", whitelabel);

  if(!whitelabel) return null;

  return <Outlet />;
}
