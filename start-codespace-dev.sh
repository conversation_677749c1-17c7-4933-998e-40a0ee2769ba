#!/bin/bash
# Start all main services for GitHub Codespaces in development mode
# Each service runs in the background and logs to its own file.

set -e

echo "🚀 Starting Divinci services in development mode for GitHub Codespaces..."

# Load all env files from private-keys/local-fast
set -a
for envfile in /workspaces/server/private-keys/local-fast/*.env; do
  if [ -f "$envfile" ]; then
    echo "Loading environment from: $envfile"
    # shellcheck disable=SC1090
    . "$envfile"
  fi
done
set +a

# Install all dependencies at the workspace root (pnpm workspace install)
echo "📦 Installing dependencies..."
cd /workspaces/server
pnpm install --silent

# Start public-api in development mode
echo "🌐 Starting public-api in development mode..."
cd /workspaces/server/workspace/servers/public-api
export HTTP_PORT=9080
export ENABLE_MTLS=true
export MTLS_CERT_DIR="/workspaces/server/private-keys/local-fast/certs"
export HTTPS=true
export SSL_CERT_PATH="/workspaces/server/private-keys/local-fast/certs/server.crt"
export SSL_KEY_PATH="/workspaces/server/private-keys/local-fast/certs/server.key"
pnpm run start:dev > /workspaces/server/public-api.log 2>&1 &
PUBLIC_API_PID=$!

# Start public-api-live in development mode
echo "📡 Starting public-api-live in development mode..."
cd /workspaces/server/workspace/servers/public-api-live
export HTTP_PORT=8082
export HTTPS=true
export SSL_CERT_PATH="/workspaces/server/private-keys/local-fast/certs/server.crt"
export SSL_KEY_PATH="/workspaces/server/private-keys/local-fast/certs/server.key"
pnpm run start:dev > /workspaces/server/public-api-live.log 2>&1 &
PUBLIC_API_LIVE_PID=$!

# Start public-api-webhook in development mode
echo "🪝 Starting public-api-webhook in development mode..."
cd /workspaces/server/workspace/servers/public-api-webhook
export HTTP_PORT=8083
export HTTPS=true
export SSL_CERT_PATH="/workspaces/server/private-keys/local-fast/certs/server.crt"
export SSL_KEY_PATH="/workspaces/server/private-keys/local-fast/certs/server.key"
pnpm run start:dev > /workspaces/server/public-api-webhook.log 2>&1 &
PUBLIC_API_WEBHOOK_PID=$!

# Start web-client in development mode
echo "💻 Starting web-client in development mode..."
cd /workspaces/server/workspace/clients/web
export HTTP_PORT=8080

# Use Codespace-specific webpack config if in Codespaces
if [ "$CODESPACES" = "true" ]; then
  echo "🌐 Detected Codespace environment - using Codespace-specific webpack config"
  # Use Codespace webpack config with HTTP-only setup
  npx webpack serve --config webpack.codespace.config.js > /workspaces/server/web-client.log 2>&1 &
else
  # Use start:dev:http for local development
  pnpm run start:dev:http > /workspaces/server/web-client.log 2>&1 &
fi
WEB_CLIENT_PID=$!

cd /workspaces/server

echo ""
echo "✅ All services started in development mode!"
echo ""
if [ "$CODESPACES" = "true" ]; then
  # Show Codespace URLs
  CODESPACE_URL_BASE="https://${CODESPACE_NAME:-cuddly-winner-vxvvr4wwhjp6}"
  echo "📊 Codespace Service URLs:"
  echo "  🌐 Web Client:        ${CODESPACE_URL_BASE}-8080.app.github.dev"
  echo "  🔗 Public API:        ${CODESPACE_URL_BASE}-9080.app.github.dev"
  echo "  📡 Public API Live:   ${CODESPACE_URL_BASE}-8082.app.github.dev"
  echo "  🪝 Public API Webhook: ${CODESPACE_URL_BASE}-8083.app.github.dev"
else
  # Show localhost URLs for local development
  echo "📊 Local Service URLs:"
  echo "  🌐 Web Client:        https://localhost:8080"
  echo "  🔗 Public API:        https://localhost:9080"
  echo "  📡 Public API Live:   https://localhost:8082"
  echo "  🪝 Public API Webhook: https://localhost:8083"
fi
echo ""
echo "📋 Logs:"
echo "  🌐 Web Client:        tail -f /workspaces/server/web-client.log"
echo "  🔗 Public API:        tail -f /workspaces/server/public-api.log"
echo "  📡 Public API Live:   tail -f /workspaces/server/public-api-live.log"
echo "  🪝 Public API Webhook: tail -f /workspaces/server/public-api-webhook.log"
echo ""
echo "🛑 To stop all services: kill $PUBLIC_API_PID $PUBLIC_API_LIVE_PID $PUBLIC_API_WEBHOOK_PID $WEB_CLIENT_PID"

# Health check for all services
echo "⏳ Waiting for services to start up..."
sleep 10

echo "🔍 Performing health checks..."
SERVICES=(8080 9080 8082 8083)
ALL_HEALTHY=true

for PORT in "${SERVICES[@]}"; do
  # Try both HTTP and HTTPS since some services might not have SSL configured properly yet
  STATUS_HTTPS=$(curl -s -o /dev/null -w "%{http_code}" -k "https://localhost:$PORT/" 2>/dev/null || echo "000")
  STATUS_HTTP=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:$PORT/" 2>/dev/null || echo "000")
  
  if [ "$STATUS_HTTPS" != "000" ] && [ "$STATUS_HTTPS" != "200" ] && [ "$STATUS_HTTPS" != "404" ]; then
    echo "✅ Service on port $PORT responded via HTTPS (HTTP $STATUS_HTTPS)"
  elif [ "$STATUS_HTTP" != "000" ] && [ "$STATUS_HTTP" != "200" ] && [ "$STATUS_HTTP" != "404" ]; then
    echo "✅ Service on port $PORT responded via HTTP (HTTP $STATUS_HTTP)"
  else
    echo "❌ Service on port $PORT is not responding (HTTPS: $STATUS_HTTPS, HTTP: $STATUS_HTTP)"
    ALL_HEALTHY=false
  fi
done

if [ "$ALL_HEALTHY" = true ]; then
  echo "🎉 All services are healthy and running!"
else
  echo "⚠️  Some services failed health checks. Check the logs above for details."
fi

echo ""
echo "🔥 Development environment is ready! Happy coding! 🌙✨"
