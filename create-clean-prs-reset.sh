#!/bin/bash

# Script to create clean PR branches with only specific files

# Function to create a clean PR branch with only specific files
create_clean_pr() {
  local branch=$1
  local pattern=$2
  local title=$3
  local body=$4

  echo "Creating clean PR for $branch with pattern: $pattern"
  
  # Reset the working directory to a clean state
  git reset --hard
  
  # Create a new branch from develop
  git checkout -b clean-$branch origin/develop
  
  # Get the list of files that match the pattern from the current branch
  FILES=$(git diff --name-only origin/develop origin/AS-211_AS-176-Workflow-Polish_2 | grep -E "$pattern" || true)
  
  if [ -z "$FILES" ]; then
    echo "No files found matching pattern: $pattern"
    git checkout AS-211_AS-176-Workflow-Polish_2
    return 1
  fi
  
  echo "Found $(echo "$FILES" | wc -l) files matching pattern"
  
  # For each file in the list
  for FILE in $FILES; do
    echo "Processing file: $FILE"
    
    # Check if the file exists in the source branch
    if git show origin/AS-211_AS-176-Workflow-Polish_2:$FILE > /dev/null 2>&1; then
      # Create the directory structure if it doesn't exist
      mkdir -p $(dirname "$FILE")
      
      # Copy the file from the source branch
      git show origin/AS-211_AS-176-Workflow-Polish_2:$FILE > "$FILE"
      
      # Add the file to the staging area
      git add "$FILE"
    else
      echo "File not found in AS-211_AS-176-Workflow-Polish_2: $FILE"
    fi
  done
  
  # Commit the changes
  git commit -m "Add files for $branch"
  
  # Push the branch
  git push -f --no-verify origin clean-$branch:$branch
  
  # Create the PR
  gh pr create \
    --base develop \
    --head $branch \
    --title "$title" \
    --body "$body"
  
  # Go back to the original branch
  git checkout AS-211_AS-176-Workflow-Polish_2
}

# PR 1: GitHub Actions and CI/CD Improvements
create_clean_pr \
  "PR-Split-1-GitHub-Actions" \
  "\.github/|runner|action|workflow" \
  "PR-Split-1: GitHub Actions and CI/CD Improvements" \
  "## Description
Implements concurrent GitHub Actions, improves self-hosted runners, and enhances build/deploy workflows.

## Key Changes
- Updates GitHub Actions workflows for better concurrency
- Improves self-hosted runner configurations
- Enhances build/deploy workflows

## Notes
This is part of the PR split from the original PR #579 (AS-211_AS-176-Workflow-Polish_2).
This PR contains only the GitHub Actions and CI/CD related changes.

## Files Changed
- Files in .github/ directory
- Runner scripts and configurations
- Workflow-related files

## Merge Order
This PR should be merged second, after PR-Split-5-Docker-Env."

# PR 2: mTLS Implementation and Security
create_clean_pr \
  "PR-Split-2-mTLS-Security" \
  "mtls/|CORS|security|SSL|TLS" \
  "PR-Split-2: mTLS Implementation and Security" \
  "## Description
Implements server-to-server mTLS, fixes CORS issues, and adds SSL/TLS testing.

## Key Changes
- Adds mTLS implementation for secure server-to-server communication
- Fixes CORS issues
- Adds SSL/TLS testing

## Notes
This is part of the PR split from the original PR #579 (AS-211_AS-176-Workflow-Polish_2).
This PR contains only the mTLS and security-related changes.

## Files Changed
- Files in mtls/ directory
- CORS-related changes in server files
- Security-related configurations

## Merge Order
This PR should be merged third, after PR-Split-1-GitHub-Actions."

# PR 3: Audio Processing and Pyannote Integration
create_clean_pr \
  "PR-Split-3-Audio-Processing" \
  "pyannote|audio|polling|ffmpeg" \
  "PR-Split-3: Audio Processing and Pyannote Integration" \
  "## Description
Implements polling for audio processing to fix Cloudflare timeout issues.

## Key Changes
- Adds polling mechanism for audio processing
- Integrates with Pyannote for speaker diarization
- Fixes Cloudflare timeout issues

## Notes
This is part of the PR split from the original PR #579 (AS-211_AS-176-Workflow-Polish_2).
This PR contains only the audio processing and Pyannote integration changes.

## Files Changed
- Audio processing and Pyannote-related files
- Polling implementation for long-running processes

## Merge Order
This PR should be merged fifth, after PR-Split-4-OpenParse-RAG."

# PR 4: Open-Parse Tool and RAG Workflow
create_clean_pr \
  "PR-Split-4-OpenParse-RAG" \
  "open-parse|RAG|workflow|chunking|markitdown" \
  "PR-Split-4: Open-Parse Tool and RAG Workflow" \
  "## Description
Enhances open-parse tool with file URL support and improves RAG workflow.

## Key Changes
- Adds file URL support to open-parse tool
- Improves RAG workflow
- Enhances chunking capabilities

## Notes
This is part of the PR split from the original PR #579 (AS-211_AS-176-Workflow-Polish_2).
This PR contains only the Open-Parse tool and RAG workflow changes.

## Files Changed
- Open-parse and RAG workflow related files
- Chunking implementation files

## Merge Order
This PR should be merged fourth, after PR-Split-2-mTLS-Security."

# PR 5: Docker and Environment Configuration
create_clean_pr \
  "PR-Split-5-Docker-Env" \
  "docker|Dockerfile|\.env|docker-compose|GCP|Cloud Run|volume" \
  "PR-Split-5: Docker and Environment Configuration" \
  "## Description
Fixes Docker build reliability issues and updates environment configurations.

## Key Changes
- Improves Docker build reliability
- Updates environment configurations
- Adds support for GCP and Cloud Run

## Notes
This is part of the PR split from the original PR #579 (AS-211_AS-176-Workflow-Polish_2).
This PR contains only the Docker and environment configuration changes.

## Files Changed
- Docker files
- Environment configuration files

## Merge Order
This PR should be merged first to minimize merge conflicts."

# PR 6: Client-Side Changes
create_clean_pr \
  "PR-Split-6-Client-Side" \
  "workspace/clients/" \
  "PR-Split-6: Client-Side Changes" \
  "## Description
Updates client-side code to work with the new server features.

## Key Changes
- Updates client-side code to work with new server features
- Enhances UI components
- Improves client-side testing

## Notes
This is part of the PR split from the original PR #579 (AS-211_AS-176-Workflow-Polish_2).
This PR contains only the client-side changes.

## Files Changed
- Files in workspace/clients/ directory

## Merge Order
This PR should be merged last, after all other PRs have been merged."

echo "All pull requests created successfully!"
