/**
 * Get Dropbox connection status
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { DropboxConnectionModel } from "@divinci-ai/server-models";
import { ensureWhitelabel } from "../util/ensure-whitelabel";

export const getDropboxConnectionStatus: RequestHandler = async function(req, res, next){
  console.log("🔍 [DROPBOX-STATUS] Handler called", {
    method: req.method,
    path: req.path,
    originalUrl: req.originalUrl,
    params: req.params,
    hasAuth: !!req.headers.authorization
  });

  try {
    console.log("🔍 [DROPBOX-STATUS] Calling ensureWhitelabel...");
    const { whitelabel, userId } = await ensureWhitelabel(req);
    console.log("🔍 [DROPBOX-STATUS] ensureWhitelabel successful", {
      whitelabelId: whitelabel._id.toString(),
      userId: userId
    });

    // Find active connection for this user
    if(!userId) {
      return res.status(400).json({
        success: false,
        error: "user_id_missing",
        message: "User ID is required"
      });
    }

    const connection = await DropboxConnectionModel.findActiveConnection(
      whitelabel._id?.toString() || "",
      userId
    );

    if(!connection) {
      return res.status(200).json({
        success: true,
        data: {
          connected: false,
          message: "No active Dropbox connection found"
        }
      });
    }

    // Check if connection needs token refresh
    const needsRefresh = connection.needsTokenRefresh();

    res.status(200).json({
      success: true,
      data: {
        connected: true,
        connectionId: connection._id.toString(),
        accountInfo: connection.accountInfo,
        connectionStatus: connection.connectionStatus,
        connectedAt: connection.createdAt,
        lastUsedAt: connection.lastUsedAt,
        needsRefresh,
        connectionAge: connection.getConnectionAge()
      }
    });

  }catch(error) {
    console.error("😨 [DROPBOX-STATUS] Error in status handler:", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      originalUrl: req.originalUrl
    });
    next(error);
  }
};
