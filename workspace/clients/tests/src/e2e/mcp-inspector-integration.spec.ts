import { test, expect, Page } from '@playwright/test';
import { verifyLoggedIn } from '../utils/auth';

/**
 * MCP Inspector Integration E2E Tests
 * 
 * This test suite verifies that the MCP server works correctly with
 * the MCP Inspector tool and can be used for interactive testing.
 */

/**
 * Helper function to start MCP Inspector if not running
 */
async function ensureMCPInspectorRunning(): Promise<string> {
  // For now, we'll assume the inspector is manually started
  // In a real CI/CD environment, you might want to start it programmatically
  const inspectorUrl = 'http://localhost:6274';
  console.log(`Expected MCP Inspector URL: ${inspectorUrl}`);
  return inspectorUrl;
}

/**
 * Helper function to test MCP remote connection
 */
async function testMCPRemoteConnection(page: Page): Promise<boolean> {
  try {
    // Test if mcp-remote can connect to our server
    const response = await page.evaluate(async () => {
      try {
        // Simulate what mcp-remote does - test the SSE endpoint
        const response = await fetch('http://localhost:8793/sse', {
          method: 'GET',
          headers: {
            'Accept': 'text/event-stream',
            'Cache-Control': 'no-cache'
          }
        });
        return {
          ok: response.ok,
          status: response.status,
          headers: Object.fromEntries(response.headers.entries())
        };
      } catch (error) {
        return { ok: false, error: error.message };
      }
    });

    console.log(`MCP Remote connection test: ${response.ok ? 'SUCCESS' : 'FAILED'}`);
    if (response.ok) {
      console.log(`SSE endpoint responding with status: ${response.status}`);
      console.log(`Content-Type: ${response.headers['content-type']}`);
    }
    return response.ok;
  } catch (error) {
    console.error('MCP Remote connection test failed:', error);
    return false;
  }
}

/**
 * Helper function to test MCP protocol initialization
 */
async function testMCPProtocolInit(page: Page): Promise<any> {
  try {
    const response = await page.evaluate(async () => {
      try {
        const response = await fetch('http://localhost:8793/sse', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            jsonrpc: '2.0',
            id: 1,
            method: 'initialize',
            params: {
              protocolVersion: '2024-11-05',
              capabilities: {},
              clientInfo: {
                name: 'e2e-test-client',
                version: '1.0.0'
              }
            }
          })
        });
        return {
          ok: response.ok,
          status: response.status,
          data: await response.json()
        };
      } catch (error) {
        return { ok: false, error: error.message };
      }
    });

    if (response.ok && response.data.result) {
      console.log('MCP Protocol initialization successful');
      console.log(`Protocol version: ${response.data.result.protocolVersion}`);
      console.log(`Server: ${response.data.result.serverInfo.name} v${response.data.result.serverInfo.version}`);
      return response.data.result;
    }
    return null;
  } catch (error) {
    console.error('MCP Protocol initialization failed:', error);
    return null;
  }
}

test.describe('MCP Inspector Integration', () => {
  test.beforeEach(async ({ page }) => {
    test.setTimeout(120000);
    await verifyLoggedIn(page);
  });

  test('MCP Server SSE Endpoint Compatibility', async ({ page }) => {
    console.log('🔌 Testing MCP Server SSE Endpoint Compatibility...');
    
    // Test SSE endpoint responds correctly
    const canConnect = await testMCPRemoteConnection(page);
    expect(canConnect).toBe(true);
    
    console.log('✅ SSE endpoint is compatible with MCP remote connections');
  });

  test('MCP Protocol Initialization', async ({ page }) => {
    console.log('🚀 Testing MCP Protocol Initialization...');
    
    const initResult = await testMCPProtocolInit(page);
    expect(initResult).toBeTruthy();
    expect(initResult.protocolVersion).toBe('2024-11-05');
    expect(initResult.serverInfo.name).toBe('divinci-ai-mcp-server');
    expect(initResult.capabilities).toBeDefined();
    
    console.log('✅ MCP Protocol initialization working correctly');
  });

  test('MCP Inspector Connection Simulation', async ({ page }) => {
    console.log('🔍 Simulating MCP Inspector Connection...');
    
    // Simulate the connection process that MCP Inspector would use
    
    // Step 1: Test server health
    const healthResponse = await page.evaluate(async () => {
      const response = await fetch('http://localhost:8793/health');
      return { ok: response.ok, data: await response.json() };
    });
    expect(healthResponse.ok).toBe(true);
    
    // Step 2: Initialize MCP connection
    const initResult = await testMCPProtocolInit(page);
    expect(initResult).toBeTruthy();
    
    // Step 3: List available tools
    const toolsResponse = await page.evaluate(async () => {
      const response = await fetch('http://localhost:8793/sse', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: 2,
          method: 'tools/list',
          params: {}
        })
      });
      return { ok: response.ok, data: await response.json() };
    });
    expect(toolsResponse.ok).toBe(true);
    expect(toolsResponse.data.result.tools.length).toBeGreaterThan(0);
    
    // Step 4: Test tool execution
    const toolExecResponse = await page.evaluate(async () => {
      const response = await fetch('http://localhost:8793/sse', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: 3,
          method: 'tools/call',
          params: {
            name: 'get_user_profile',
            arguments: {}
          }
        })
      });
      return { ok: response.ok, data: await response.json() };
    });
    expect(toolExecResponse.ok).toBe(true);
    expect(toolExecResponse.data.result.content).toBeDefined();
    
    console.log('✅ MCP Inspector connection simulation successful');
  });

  test('Claude Desktop Configuration Validation', async ({ page }) => {
    console.log('🖥️ Testing Claude Desktop Configuration Compatibility...');
    
    // Test the exact configuration that would be used in Claude Desktop
    const configTest = {
      command: 'npx',
      args: ['mcp-remote', 'http://localhost:8793/sse']
    };
    
    console.log(`Testing Claude Desktop config: ${configTest.command} ${configTest.args.join(' ')}`);
    
    // Verify the server responds to the exact requests Claude Desktop would make
    
    // Test 1: Initial connection
    const connectionTest = await testMCPRemoteConnection(page);
    expect(connectionTest).toBe(true);
    
    // Test 2: Protocol handshake
    const handshakeTest = await testMCPProtocolInit(page);
    expect(handshakeTest).toBeTruthy();
    
    // Test 3: Tool discovery
    const discoveryResponse = await page.evaluate(async () => {
      const response = await fetch('http://localhost:8793/sse', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: 'claude-desktop-discovery',
          method: 'tools/list',
          params: {}
        })
      });
      return { ok: response.ok, data: await response.json() };
    });
    expect(discoveryResponse.ok).toBe(true);
    
    // Test 4: Tool execution with Claude Desktop style request
    const claudeStyleExecution = await page.evaluate(async () => {
      const response = await fetch('http://localhost:8793/sse', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: 'claude-desktop-exec-001',
          method: 'tools/call',
          params: {
            name: 'create_chat',
            arguments: {
              title: 'Claude Desktop Test Chat',
              model: 'claude-3-sonnet'
            }
          }
        })
      });
      return { ok: response.ok, data: await response.json() };
    });
    expect(claudeStyleExecution.ok).toBe(true);
    expect(claudeStyleExecution.data.result.content[0].text).toContain('Claude Desktop Test Chat');
    
    console.log('✅ Claude Desktop configuration compatibility verified');
  });

  test('MCP Server Concurrent Connections', async ({ page }) => {
    console.log('🔄 Testing MCP Server Concurrent Connections...');
    
    // Simulate multiple clients connecting simultaneously
    const connectionPromises = [];
    
    for (let i = 0; i < 3; i++) {
      const promise = page.evaluate(async (clientId) => {
        try {
          // Initialize connection
          const initResponse = await fetch('http://localhost:8793/sse', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              jsonrpc: '2.0',
              id: `client-${clientId}-init`,
              method: 'initialize',
              params: {
                protocolVersion: '2024-11-05',
                capabilities: {},
                clientInfo: {
                  name: `test-client-${clientId}`,
                  version: '1.0.0'
                }
              }
            })
          });
          
          if (!initResponse.ok) return { success: false, error: 'Init failed' };
          
          // Execute a tool
          const toolResponse = await fetch('http://localhost:8793/sse', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              jsonrpc: '2.0',
              id: `client-${clientId}-tool`,
              method: 'tools/call',
              params: {
                name: 'get_user_profile',
                arguments: {}
              }
            })
          });
          
          return { 
            success: toolResponse.ok,
            clientId,
            data: await toolResponse.json()
          };
        } catch (error) {
          return { success: false, clientId, error: error.message };
        }
      }, i);
      
      connectionPromises.push(promise);
    }
    
    const results = await Promise.all(connectionPromises);
    
    // Verify all connections succeeded
    results.forEach((result, index) => {
      expect(result.success).toBe(true);
      console.log(`✅ Client ${index} connected and executed tools successfully`);
    });
    
    console.log('✅ Concurrent connections handled correctly');
  });
});
