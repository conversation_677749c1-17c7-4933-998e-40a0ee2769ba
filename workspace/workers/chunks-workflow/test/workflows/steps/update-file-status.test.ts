import { describe, it, expect, vi, beforeEach } from "vitest";
import { updateFileStatus } from "../../../src/workflows/steps/update-file-status";
import { WorkflowStep } from "cloudflare:workers";
import { StepContext, VectorizeConfig } from "../../../src/workflows/steps/types";

<<<<<<< HEAD
=======
// Mock setTimeout to avoid real delays in tests
vi.stubGlobal('setTimeout', (fn: Function) => {
  fn();
  return 1;
});

>>>>>>> WA-170_MCP
describe("update-file-status", ()=>{
  // Mock console methods to avoid cluttering test output
  beforeEach(()=>{
    vi.spyOn(console, "log").mockImplementation(()=>{});
    vi.spyOn(console, "warn").mockImplementation(()=>{});
    vi.spyOn(console, "error").mockImplementation(()=>{});
  });

  describe("updateFileStatus", ()=>{
    it("should update file status successfully", async ()=>{
      // Mock step
      const step = {
        do: vi.fn().mockImplementation((name, fn)=>fn())
      } as unknown as WorkflowStep;

      // Mock context
      const context = {
        env: {
          ENVIRONMENT: "production",
          API_HOST: "https://api.example.com"
        }
      } as unknown as StepContext;

      // Mock vectorizeConfig
      const vectorizeConfig: VectorizeConfig = {
        whitelabelId: "test-whitelabel",
        auth0Token: "test-token",
        ragId: "test-rag"
      };

      // Mock fetch responses
      global.fetch = vi.fn()
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: ()=>Promise.resolve({ status: "success" })
        });

      await updateFileStatus(step, context, "test-file-id", "COMPLETED", vectorizeConfig);

      expect(step.do).toHaveBeenCalledWith("updateFileStatus_COMPLETED", expect.any(Function));
      // We're only checking if the file exists now, not making a second call
      expect(global.fetch).toHaveBeenCalledTimes(1);

      // Check the fetch call (update status)
      expect(global.fetch).toHaveBeenNthCalledWith(
        1,
<<<<<<< HEAD
        "https://api.example.com/white-label/test-whitelabel/rag-vector/files/test-file-id/status",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": "Bearer test-token"
          },
          body: JSON.stringify({ status: "COMPLETED" })
        }
=======
        expect.stringContaining("/white-label/test-whitelabel/rag-vector/files/"),
        expect.objectContaining({
          method: "POST",
          headers: expect.objectContaining({
            "Content-Type": "application/json",
            "Authorization": "Bearer test-token",
            "X-Retry-Attempt": expect.stringMatching(/\d+\/\d+/),
            "X-Workflow-ID": expect.any(String)
          }),
          body: JSON.stringify({ status: "COMPLETED" })
        })
>>>>>>> WA-170_MCP
      );
    });

    it("should handle file not found gracefully", async ()=>{
      // Mock step
      const step = {
        do: vi.fn().mockImplementation((name, fn)=>fn())
      } as unknown as WorkflowStep;

      // Mock context
      const context = {
        env: {
          ENVIRONMENT: "production",
          API_HOST: "https://api.example.com"
        }
      } as unknown as StepContext;

      // Mock vectorizeConfig
      const vectorizeConfig: VectorizeConfig = {
        whitelabelId: "test-whitelabel",
        auth0Token: "test-token",
        ragId: "test-rag"
      };

      // Mock fetch responses
      global.fetch = vi.fn()
        .mockResolvedValueOnce({
          ok: false,
          status: 404,
          statusText: "Not Found"
        });

      await updateFileStatus(step, context, "test-file-id", "COMPLETED", vectorizeConfig);

      expect(step.do).toHaveBeenCalledWith("updateFileStatus_COMPLETED", expect.any(Function));
<<<<<<< HEAD
      expect(global.fetch).toHaveBeenCalledTimes(1);
      expect(console.warn).toHaveBeenCalledWith("⚠️ Continuing workflow despite status update failure");
=======
      expect(global.fetch).toHaveBeenCalled(); // May retry multiple times
      expect(console.warn).toHaveBeenCalledWith("⚠️ All 5 attempts to update file status failed, continuing workflow anyway");
>>>>>>> WA-170_MCP
    });

    it("should handle empty file ID", async ()=>{
      // Reset fetch mock before this test
      vi.resetAllMocks();

      // Mock console.error to capture the error message
      const consoleErrorSpy = vi.spyOn(console, "error");

      // Mock step
      const step = {
        do: vi.fn().mockImplementation((name, fn)=>fn())
      } as unknown as WorkflowStep;

      // Mock context
      const context = {
        env: {
          ENVIRONMENT: "production",
          API_HOST: "https://api.example.com"
        }
      } as unknown as StepContext;

      // Mock vectorizeConfig
      const vectorizeConfig: VectorizeConfig = {
        whitelabelId: "test-whitelabel",
        auth0Token: "test-token",
        ragId: "test-rag"
      };

      // Create a mock implementation of fetch that will be called if the code doesn't
      // properly handle the empty fileId case
      global.fetch = vi.fn().mockImplementation(()=>{
        // This should never be called with an empty fileId
        throw new Error("fetch should not be called with empty fileId");
      });

      // Call the function with an empty fileId
      await updateFileStatus(step, context, "", "COMPLETED", vectorizeConfig);

      // Verify expectations
      expect(step.do).toHaveBeenCalledWith("updateFileStatus_COMPLETED", expect.any(Function));
<<<<<<< HEAD
      expect(global.fetch).not.toHaveBeenCalled();
      expect(consoleErrorSpy).toHaveBeenCalledWith("❌ Cannot update file status: Invalid file ID (empty)");
=======
      // The implementation generates a fallback ObjectId, so fetch will be called
      expect(global.fetch).toHaveBeenCalled();
      // Should see warning about generating fallback ObjectId
      expect(console.warn).toHaveBeenCalledWith(expect.stringContaining("Generated new ObjectId as fallback"));
>>>>>>> WA-170_MCP
    });

    it("should handle API errors gracefully in production", async ()=>{
      // Mock step
      const step = {
        do: vi.fn().mockImplementation((name, fn)=>fn())
      } as unknown as WorkflowStep;

      // Mock context
      const context = {
        env: {
          ENVIRONMENT: "production",
          API_HOST: "https://api.example.com"
        }
      } as unknown as StepContext;

      // Mock vectorizeConfig
      const vectorizeConfig: VectorizeConfig = {
        whitelabelId: "test-whitelabel",
        auth0Token: "test-token",
        ragId: "test-rag"
      };

      // Mock fetch responses
      global.fetch = vi.fn()
        .mockResolvedValueOnce({
          ok: false,
          status: 500,
          statusText: "Internal Server Error",
          json: ()=>Promise.resolve({ error: "Server error" })
        });

      // Mock console.error to verify it's called
      console.error = vi.fn();

      await expect(updateFileStatus(step, context, "test-file-id", "COMPLETED", vectorizeConfig))
        .resolves.not.toThrow();

      expect(step.do).toHaveBeenCalledWith("updateFileStatus_COMPLETED", expect.any(Function));
<<<<<<< HEAD
      // We're only checking if the file exists now, not making a second call
      expect(global.fetch).toHaveBeenCalledTimes(1);
=======
      // May retry multiple times due to error
      expect(global.fetch).toHaveBeenCalled();
>>>>>>> WA-170_MCP
      expect(console.error).toHaveBeenCalled();
    });
  });
});
