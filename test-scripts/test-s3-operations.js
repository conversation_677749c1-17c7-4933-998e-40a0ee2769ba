// Test script for S3 operations in the chunks-workflow container
// This script tests basic S3 operations (PUT, GET, LIST) using the AWS SDK

console.log('Starting S3 operations test...');

// Import AWS SDK
const { S3 } = require('@aws-sdk/client-s3');

// Configuration from environment variables
const bucketUrl = process.env.R2_BUCKET_URL || 'http://minio.divinci.local:9000';
const accessKey = process.env.R2_ACCESS_KEY_ID || 'hello';
const secretKey = process.env.R2_SECRET_ACCESS_KEY || 'divinci!';
<<<<<<< HEAD
const bucketName = 'rag-origin-files-local';
=======
const bucketName = 'rag-files-local';
// const bucketName = 'rag-origin-files-local';
>>>>>>> WA-170_MCP

console.log(`Using MinIO endpoint: ${bucketUrl}`);
console.log(`Using credentials: ${accessKey} / [hidden]`);
console.log(`Using bucket: ${bucketName}`);

// Create S3 client
const s3Client = new S3({
  endpoint: bucketUrl,
  region: 'auto',
  credentials: {
    accessKeyId: accessKey,
    secretAccessKey: secretKey,
  },
  forcePathStyle: true, // Required for MinIO
});

// Test file data
const testKey = `test-file-${Date.now()}.txt`;
const testContent = `This is a test file created at ${new Date().toISOString()}`;
console.log(`\nTest file: ${testKey}`);

// Function to test PUT operation
async function testPut() {
  console.log('\n--- Testing PUT operation ---');
  try {
    const params = {
      Bucket: bucketName,
      Key: testKey,
      Body: testContent,
      ContentType: 'text/plain',
    };
    
    console.log(`Putting object ${testKey} to bucket ${bucketName}...`);
    const result = await s3Client.putObject(params);
    console.log('PUT operation successful:', result);
    return true;
  } catch (error) {
    console.error('Error in PUT operation:', error);
    return false;
  }
}

// Function to test GET operation
async function testGet() {
  console.log('\n--- Testing GET operation ---');
  try {
    const params = {
      Bucket: bucketName,
      Key: testKey,
    };
    
    console.log(`Getting object ${testKey} from bucket ${bucketName}...`);
    const result = await s3Client.getObject(params);
    
    // Convert the stream to text
    const chunks = [];
    for await (const chunk of result.Body) {
      chunks.push(chunk);
    }
    const content = Buffer.concat(chunks).toString('utf-8');
    
    console.log(`GET operation successful, content: ${content}`);
    console.log(`Expected content: ${testContent}`);
    console.log(`Content matches: ${content === testContent}`);
    
    return content === testContent;
  } catch (error) {
    console.error('Error in GET operation:', error);
    return false;
  }
}

// Function to test LIST operation
async function testList() {
  console.log('\n--- Testing LIST operation ---');
  try {
    const params = {
      Bucket: bucketName,
      MaxKeys: 10,
    };
    
    console.log(`Listing objects in bucket ${bucketName}...`);
    const result = await s3Client.listObjectsV2(params);
    
    console.log(`Found ${result.Contents?.length || 0} objects in bucket`);
    const foundTestFile = result.Contents?.some(item => item.Key === testKey);
    console.log(`Test file found in list: ${foundTestFile}`);
    
    return !!foundTestFile;
  } catch (error) {
    console.error('Error in LIST operation:', error);
    return false;
  }
}

// Function to test DELETE operation
async function testDelete() {
  console.log('\n--- Testing DELETE operation ---');
  try {
    const params = {
      Bucket: bucketName,
      Key: testKey,
    };
    
    console.log(`Deleting object ${testKey} from bucket ${bucketName}...`);
    const result = await s3Client.deleteObject(params);
    console.log('DELETE operation successful:', result);
    
    return true;
  } catch (error) {
    console.error('Error in DELETE operation:', error);
    return false;
  }
}

// Run all tests in sequence
async function runTests() {
  console.log('\n======= Starting S3 Operations Tests =======');
  
  // Create bucket if it doesn't exist
  try {
    console.log(`Checking if bucket ${bucketName} exists...`);
    await s3Client.headBucket({ Bucket: bucketName });
    console.log(`Bucket ${bucketName} exists`);
  } catch (error) {
    console.log(`Bucket ${bucketName} doesn't exist, creating it...`);
    try {
      await s3Client.createBucket({ Bucket: bucketName });
      console.log(`Bucket ${bucketName} created successfully`);
    } catch (createError) {
      console.error('Error creating bucket:', createError);
      return;
    }
  }
  
  // Run the tests
  const putSuccess = await testPut();
  const getSuccess = putSuccess ? await testGet() : false;
  const listSuccess = putSuccess ? await testList() : false;
  const deleteSuccess = putSuccess ? await testDelete() : false;
  
  // Print summary
  console.log('\n======= Test Summary =======');
  console.log(`PUT Test: ${putSuccess ? 'SUCCESS ✅' : 'FAILED ❌'}`);
  console.log(`GET Test: ${getSuccess ? 'SUCCESS ✅' : 'FAILED ❌'}`);
  console.log(`LIST Test: ${listSuccess ? 'SUCCESS ✅' : 'FAILED ❌'}`);
  console.log(`DELETE Test: ${deleteSuccess ? 'SUCCESS ✅' : 'FAILED ❌'}`);
  
  const overallSuccess = putSuccess && getSuccess && listSuccess && deleteSuccess;
  console.log(`\nOverall Test Result: ${overallSuccess ? 'SUCCESS ✅' : 'FAILED ❌'}`);
  
  // Exit with appropriate code
  process.exit(overallSuccess ? 0 : 1);
}

// Run the tests
runTests().catch(error => {
  console.error('Error running tests:', error);
  process.exit(1);
});