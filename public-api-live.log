
> @divinci-ai/public-api-live@0.3.0 start /workspaces/server/workspace/servers/public-api-live
> node dist/index.js

🐞LOG_DEBUG MODE:  1
🐞LOG_DEBUG MODE:  1
🔎 CURRENT ENVIRONMENT: dev | NODE_ENV: development | FORCE_R2_STORAGE: undefined
🔎 CORS FULL ORIGINS (raw): api.slack.com,dev-46tiys6hnb6vbg17-cd-hljzhimsn7o28fvq.edge.tenants.us.auth0.com,dev-46tiys6hnb6vbg17.us.auth0.com,local-chunks-vectorized,127.0.0.1:,obscure-chainsaw-jj4x9rq9gx4qcqw45-8080.app.github.dev,obscure-chainsaw-jj4x9rq9gx4qcqw45-8081.app.github.dev,obscure-chainsaw-jj4x9rq9gx4qcqw45-8082.app.github.dev,obscure-chainsaw-jj4x9rq9gx4qcqw45-8083.app.github.dev,obscure-chainsaw-jj4x9rq9gx4qcqw45-9080.app.github.dev
🔎 CORS FULL ORIGINS (cleaned): [
  'api.slack.com',
  'dev-46tiys6hnb6vbg17-cd-hljzhimsn7o28fvq.edge.tenants.us.auth0.com',
  'dev-46tiys6hnb6vbg17.us.auth0.com',
  'local-chunks-vectorized',
  '127.0.0.1:',
  'obscure-chainsaw-jj4x9rq9gx4qcqw45-8080.app.github.dev',
  'obscure-chainsaw-jj4x9rq9gx4qcqw45-8081.app.github.dev',
  'obscure-chainsaw-jj4x9rq9gx4qcqw45-8082.app.github.dev',
  'obscure-chainsaw-jj4x9rq9gx4qcqw45-8083.app.github.dev',
  'obscure-chainsaw-jj4x9rq9gx4qcqw45-9080.app.github.dev'
]
🔑 [DROPBOX-ENV] Loading Dropbox environment variables...
🔑 [DROPBOX-ENV] Dropbox environment variables loaded successfully {
  clientId: 'snqjeycw...',
  clientSecret: '[PRESENT]',
  redirectUri: 'http://localhost:9080/auth/dropbox/callback'
}
🐞LOG_DEBUG MODE:  1
🐞LOG_DEBUG MODE:  1
🎈 Attempting to connect to Redis in dev environment: redis://default:<EMAIL>:15121
🔄 Creating simple R2 client for fine-tune in local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🔄 Creating simple Audio R2 client for local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🧪 [TEMP TEST] Using Audio R2 client for chunks files instead of Whitelabel Vector R2 client
🧪 [TEMP TEST] Using workspace-audio bucket for chunks files instead of whitelabel-vector-index
🪣 [API] Bucket selection - isLocalMode: true, forceR2Storage: false
🪣 [API] Selected bucket: rag-files-local
🪣 Using bucket: rag-files-local for environment: dev, forceR2Storage: false
🔍 [JOB-MANAGER] Starting monitoring service (every 5 minutes)
🤖 [RECOVERY] Starting auto-recovery (every 10 minutes)
🐞LOG_DEBUG MODE:  1
🐞LOG_DEBUG MODE:  1
Attempting Mongoose Connection.
🎈 MONGO_CONNECTION_URL: mongodb+srv://serverlessinstance0.c4pobzg.mongodb.net/divinci-dev
Attempting Redis Connection
🟢 Connected to Redis in dev environment at 2025-06-17T22:34:40.012Z
✅🌱 Successfully connected to Redis. 
✅🌱 Successfully connected to MongoDB. 
🌐 HTTP server created
node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8082
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at /workspaces/server/workspace/servers/public-api-live/dist/app.js:57:12
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1975:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8082
}

Node.js v22.16.0
 ELIFECYCLE  Command failed with exit code 1.
