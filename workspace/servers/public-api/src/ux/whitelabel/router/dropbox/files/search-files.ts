/**
 * Search files in Dropbox
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { ensureDropboxConnection, handleDropboxError } from "../util/ensure-whitelabel";
import { isMediaFile, processDropboxFile, sortFilesByName } from "@divinci-ai/server-globals";

interface SearchFilesQuery {
  query?: string;
  path?: string;
  mediaOnly?: string;
  maxResults?: string;
  filenameOnly?: string;
}

export const searchDropboxFiles: RequestHandler = async function(req, res, next) {
  try {
    const { connection, dropboxClient } = await ensureDropboxConnection(req);
    const {
      query,
      path,
      mediaOnly = "true",
      maxResults = "100",
      filenameOnly = "false"
    }: SearchFilesQuery = req.query;

    if (!query) {
      return res.status(400).json({
        success: false,
        error: "missing_query",
        message: "Search query is required"
      });
    }

    // Search files in Dropbox
    const searchResponse = await dropboxClient.searchFiles({
      query,
      path,
      max_results: parseInt(maxResults, 10),
      filename_only: filenameOnly === "true"
    });

    // Process search results
    let files = searchResponse.matches
      .map(match => match.metadata)
      .filter(entry => entry[".tag"] === "file")
      .map(file => processDropboxFile(file as any));

    // Filter to media files only if requested
    if (mediaOnly === "true") {
      files = files.filter(file => file.isAudio || file.isVideo);
    }

    // Sort files by name
    files = sortFilesByName(files);

    res.status(200).json({
      success: true,
      data: {
        files,
        totalCount: files.length,
        hasMore: searchResponse.more,
        query,
        filters: {
          path,
          mediaOnly: mediaOnly === "true",
          filenameOnly: filenameOnly === "true",
          maxResults: parseInt(maxResults, 10)
        }
      }
    });

  } catch (error) {
    try {
      await handleDropboxError(error, req.connection);
    } catch (handledError) {
      return next(handledError);
    }
    next(error);
  }
};
