/**
 * Get sync statistics for the whitelabel
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { ensureWhitelabel } from "../util/ensure-whitelabel";
import { DropboxSyncJobModel, DropboxFileMappingModel } from "@divinci-ai/server-models";

export const getDropboxSyncStats: RequestHandler = async function(req, res, next){
  try {
    const { whitelabel } = await ensureWhitelabel(req);

    // Get job statistics
    const jobStats = await DropboxSyncJobModel.getJobStats(whitelabel._id.toString());

    // Get file mapping statistics
    const fileMappingStats = await DropboxFileMappingModel.getSyncStats(whitelabel._id.toString());

    // Get recent activity
    const recentJobs = await DropboxSyncJobModel.find({
      whitelabelId: whitelabel._id.toString()
    })
    .sort({ createdAt: -1 })
    .limit(10)
    .select("status targetType createdAt completedAt progress metadata");

    const recentFiles = await DropboxFileMappingModel.getRecentlySynced(
      whitelabel._id.toString(),
      24, // Last 24 hours
      10  // Limit to 10 files
    );

    res.status(200).json({
      success: true,
      data: {
        jobs: jobStats,
        files: fileMappingStats,
        recentActivity: {
          jobs: recentJobs.map(job=>({
            jobId: job._id.toString(),
            status: job.status,
            targetType: job.targetType,
            createdAt: job.createdAt,
            completedAt: job.completedAt,
            totalFiles: job.progress.totalFiles,
            processedFiles: job.progress.processedFiles,
            failedFiles: job.progress.failedFiles,
            jobName: job.metadata?.jobName
          })),
          files: recentFiles.map(file=>({
            fileId: file._id.toString(),
            fileName: file.fileName,
            fileType: file.fileType,
            syncStatus: file.syncStatus,
            lastSyncAt: file.lastSyncAt,
            internalFileId: file.internalFileId
          }))
        },
        summary: {
          totalJobsCreated: jobStats.total,
          totalFilesProcessed: fileMappingStats.completed,
          totalFilesPending: fileMappingStats.pending + fileMappingStats.outdated,
          totalFilesFailed: fileMappingStats.failed,
          successRate: fileMappingStats.total > 0
            ? Math.round((fileMappingStats.completed / fileMappingStats.total) * 100)
            : 0
        }
      }
    });

  }catch(error) {
    next(error);
  }
};
