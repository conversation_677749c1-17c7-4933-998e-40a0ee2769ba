import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { DataSourceAudioTranscriptModel } from "@divinci-ai/server-models";

/**
 * Speaker Intelligence Endpoints
 * 
 * Advanced AI-powered speaker identification and management.
 * The most intelligent speaker analysis system ever built!
 */

/**
 * Analyze all speakers in a whitelabel for cross-audio matches and unified identities
 * POST /white-label/:whitelabelId/speaker-intelligence/analyze
 */
export const analyzeSpeakers: RequestHandler = async (req, res, next) => {
  try {
    const { whitelabelId } = req.params;

    console.log(`🧠 [SPEAKER-INTELLIGENCE] Starting basic speaker analysis for whitelabel: ${whitelabelId}`);

    // Get all audio documents with voiceprints
    const audioDocuments = await DataSourceAudioTranscriptModel.find({
      whitelabelId,
      "voiceprints": { $exists: true, $ne: [] }
    }).select("_id voiceprints speakerNames userInfo");

    // Basic analysis
    let totalSpeakers = 0;
    let succeededVoiceprints = 0;
    let failedVoiceprints = 0;
    let processingVoiceprints = 0;
    const speakerLabels = new Set<string>();
    const audioFilesWithVoiceprints = audioDocuments.length;

    for (const doc of audioDocuments) {
      if (doc.voiceprints) {
        for (const vp of doc.voiceprints) {
          totalSpeakers++;
          if (vp.speakerLabel) speakerLabels.add(vp.speakerLabel);

          switch (vp.status) {
            case 'succeeded':
              succeededVoiceprints++;
              break;
            case 'failed':
              failedVoiceprints++;
              break;
            case 'created':
            case 'pending':
            case 'running':
              processingVoiceprints++;
              break;
          }
        }
      }
    }

    const analysisResult = {
      totalSpeakers,
      uniqueSpeakerLabels: speakerLabels.size,
      audioFilesWithVoiceprints,
      voiceprintStats: {
        succeeded: succeededVoiceprints,
        failed: failedVoiceprints,
        processing: processingVoiceprints,
        successRate: totalSpeakers > 0 ? Math.round((succeededVoiceprints / totalSpeakers) * 100) : 0
      },
      recommendations: [
        succeededVoiceprints > 0 ? "✅ Voiceprints are being created successfully" : "⚠️ No successful voiceprints found",
        failedVoiceprints > 0 ? `❌ ${failedVoiceprints} voiceprints failed - consider retry` : "✅ No failed voiceprints",
        processingVoiceprints > 0 ? `🔄 ${processingVoiceprints} voiceprints still processing` : "✅ All voiceprints completed",
        "🚀 Phase 5 Speaker Intelligence features coming soon!"
      ]
    };

    res.status(200).json({
      success: true,
      data: {
        analysis: analysisResult,
        summary: {
          message: "Basic speaker analysis complete - Advanced AI matching coming in Phase 5!",
          totalSpeakers,
          uniqueSpeakers: speakerLabels.size,
          successRate: analysisResult.voiceprintStats.successRate
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("❌ [SPEAKER-INTELLIGENCE] Failed to analyze speakers:", error);
    next(error);
  }
};

/**
 * Get cross-audio speaker matches
 * GET /white-label/:whitelabelId/speaker-intelligence/matches
 */
export const getSpeakerMatches: RequestHandler = async (req, res, next) => {
  try {
    const { whitelabelId } = req.params;

    console.log(`🔍 [SPEAKER-INTELLIGENCE] Finding basic speaker patterns for whitelabel: ${whitelabelId}`);

    // Basic pattern matching - find speakers that appear in multiple audio files
    const audioDocuments = await DataSourceAudioTranscriptModel.find({
      whitelabelId,
      "voiceprints": { $exists: true, $ne: [] }
    }).select("_id voiceprints userInfo");

    const speakerMap = new Map<string, any[]>();

    // Group voiceprints by speaker label
    for (const doc of audioDocuments) {
      if (doc.voiceprints) {
        for (const vp of doc.voiceprints) {
          if (vp.speakerLabel && vp.status === 'succeeded') {
            if (!speakerMap.has(vp.speakerLabel)) {
              speakerMap.set(vp.speakerLabel, []);
            }
            speakerMap.get(vp.speakerLabel)!.push({
              audioId: doc._id.toString(),
              audioTitle: doc.userInfo?.title || 'Untitled',
              jobId: vp.jobId,
              timestamp: vp.timestamp
            });
          }
        }
      }
    }

    // Find speakers that appear in multiple files
    const crossAudioSpeakers = Array.from(speakerMap.entries())
      .filter(([_, appearances]) => appearances.length > 1)
      .map(([speakerLabel, appearances]) => ({
        speakerLabel,
        appearanceCount: appearances.length,
        audioFiles: appearances,
        confidence: 85, // Basic confidence for same-label matches
        type: 'label-based-match'
      }));

    res.status(200).json({
      success: true,
      data: {
        matches: crossAudioSpeakers,
        summary: {
          totalSpeakers: speakerMap.size,
          crossAudioSpeakers: crossAudioSpeakers.length,
          message: "Basic label-based matching - AI voice matching coming in Phase 5!"
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("❌ [SPEAKER-INTELLIGENCE] Failed to get speaker matches:", error);
    next(error);
  }
};

/**
 * Get smart name suggestions for a specific speaker
 * GET /white-label/:whitelabelId/speaker-intelligence/suggestions/:audioId/:speakerLabel
 */
export const getSmartSuggestions: RequestHandler = async (req, res, next) => {
  try {
    const { whitelabelId, audioId, speakerLabel } = req.params;

    console.log(`💡 [SPEAKER-INTELLIGENCE] Getting basic suggestions for ${speakerLabel} in ${audioId}`);

    // Basic suggestions based on simple patterns
    const suggestions = [
      {
        name: speakerLabel.replace(/[_-]/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        confidence: 60,
        reasoning: "Cleaned up speaker label",
        category: "pattern"
      },
      {
        name: `Speaker ${speakerLabel.replace(/\D/g, '')}`,
        confidence: 40,
        reasoning: "Numbered speaker format",
        category: "pattern"
      },
      {
        name: "Host",
        confidence: 30,
        reasoning: "Common role suggestion",
        category: "role"
      },
      {
        name: "Guest",
        confidence: 30,
        reasoning: "Common role suggestion",
        category: "role"
      }
    ];

    // Filter out duplicates and invalid suggestions
    const validSuggestions = suggestions.filter(s =>
      s.name !== speakerLabel && s.name.length > 1
    );

    res.status(200).json({
      success: true,
      data: {
        suggestions: validSuggestions,
        speaker: {
          audioId,
          speakerLabel,
          whitelabelId
        },
        summary: {
          totalSuggestions: validSuggestions.length,
          message: "Basic pattern-based suggestions - AI-powered naming coming in Phase 5!",
          categories: [...new Set(validSuggestions.map(s => s.category))]
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("❌ [SPEAKER-INTELLIGENCE] Failed to get smart suggestions:", error);
    next(error);
  }
};

/**
 * Apply unified speaker identity across all audio files
 * POST /white-label/:whitelabelId/speaker-intelligence/apply-identity
 */
export const applyUnifiedIdentity: RequestHandler = async (req, res, next) => {
  try {
    const { whitelabelId } = req.params;
    const { speakerLabel, newName } = req.body || {};

    if (!speakerLabel || !newName) {
      return res.status(400).json({
        success: false,
        error: "Missing required fields: speakerLabel and newName",
        timestamp: new Date().toISOString()
      });
    }

    console.log(`🎯 [SPEAKER-INTELLIGENCE] Applying name "${newName}" to speaker "${speakerLabel}"`);

    // Find all audio files with this speaker label
    const audioDocuments = await DataSourceAudioTranscriptModel.find({
      whitelabelId,
      "voiceprints.speakerLabel": speakerLabel
    });

    let updated = 0;
    const updatedAudioIds: string[] = [];

    for (const doc of audioDocuments) {
      try {
        // Update speaker name in the audio document
        const result = await DataSourceAudioTranscriptModel.updateOne(
          { _id: doc._id },
          {
            $set: {
              [`speakerNames.${speakerLabel}`]: newName
            }
          }
        );

        if (result.modifiedCount > 0) {
          updated++;
          updatedAudioIds.push(doc._id.toString());
        }

      } catch (error) {
        console.error(`❌ Failed to update ${doc._id}:`, error);
      }
    }

    res.status(200).json({
      success: true,
      data: {
        applied: {
          speakerLabel,
          newName,
          updatedAudioFiles: updated,
          audioFileIds: updatedAudioIds
        },
        summary: {
          message: `Successfully applied "${newName}" to ${updated} audio files`,
          affectedFiles: updated
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("❌ [SPEAKER-INTELLIGENCE] Failed to apply unified identity:", error);
    next(error);
  }
};

/**
 * Get detailed information about a specific speaker identity
 * GET /white-label/:whitelabelId/speaker-intelligence/identity/:speakerLabel
 */
export const getIdentityDetails: RequestHandler = async (req, res, next) => {
  try {
    const { whitelabelId, speakerLabel } = req.params;

    console.log(`🔍 [SPEAKER-INTELLIGENCE] Getting details for speaker: ${speakerLabel}`);

    // Find all appearances of this speaker
    const audioDocuments = await DataSourceAudioTranscriptModel.find({
      whitelabelId,
      "voiceprints.speakerLabel": speakerLabel
    }).select("_id voiceprints speakerNames userInfo");

    const appearances: any[] = [];
    let totalVoiceprints = 0;
    let succeededVoiceprints = 0;

    for (const doc of audioDocuments) {
      if (doc.voiceprints) {
        for (const vp of doc.voiceprints) {
          if (vp.speakerLabel === speakerLabel) {
            totalVoiceprints++;
            if (vp.status === 'succeeded') succeededVoiceprints++;

            appearances.push({
              audioId: doc._id.toString(),
              audioTitle: doc.userInfo?.title || 'Untitled',
              jobId: vp.jobId,
              status: vp.status,
              timestamp: vp.timestamp,
              assignedName: doc.speakerNames?.[speakerLabel] || null
            });
          }
        }
      }
    }

    if (appearances.length === 0) {
      return res.status(404).json({
        success: false,
        error: `Speaker not found: ${speakerLabel}`
      });
    }

    const identity = {
      speakerLabel,
      totalAppearances: appearances.length,
      uniqueAudioFiles: new Set(appearances.map(a => a.audioId)).size,
      voiceprintStats: {
        total: totalVoiceprints,
        succeeded: succeededVoiceprints,
        successRate: Math.round((succeededVoiceprints / totalVoiceprints) * 100)
      },
      appearances: appearances.sort((a, b) => b.timestamp - a.timestamp),
      assignedNames: [...new Set(appearances.map(a => a.assignedName).filter(Boolean))],
      timeSpan: {
        firstSeen: new Date(Math.min(...appearances.map(a => a.timestamp))).toISOString(),
        lastSeen: new Date(Math.max(...appearances.map(a => a.timestamp))).toISOString()
      }
    };

    res.status(200).json({
      success: true,
      data: {
        identity,
        summary: {
          message: "Basic speaker identity - Advanced AI analysis coming in Phase 5!"
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("❌ [SPEAKER-INTELLIGENCE] Failed to get identity details:", error);
    next(error);
  }
};

// Helper functions removed - simplified for Phase 5 preview
