
> @divinci-ai/public-api@0.3.0 start /workspaces/server/workspace/servers/public-api
> node dist/index.js

🐞LOG_DEBUG MODE:  1
🔎 CURRENT ENVIRONMENT: dev
🔎 CORS FULL ORIGINS (raw): api.slack.com,dev-46tiys6hnb6vbg17-cd-hljzhimsn7o28fvq.edge.tenants.us.auth0.com,dev-46tiys6hnb6vbg17.us.auth0.com,local-chunks-vectorized,127.0.0.1:,effective-invention-xjwwp64g36r4j-8080.app.github.dev,effective-invention-xjwwp64g36r4j-8081.app.github.dev,effective-invention-xjwwp64g36r4j-8082.app.github.dev,effective-invention-xjwwp64g36r4j-8083.app.github.dev,effective-invention-xjwwp64g36r4j-9080.app.github.dev
🔎 CORS FULL ORIGINS (cleaned): [
  'api.slack.com',
  'dev-46tiys6hnb6vbg17-cd-hljzhimsn7o28fvq.edge.tenants.us.auth0.com',
  'dev-46tiys6hnb6vbg17.us.auth0.com',
  'local-chunks-vectorized',
  '127.0.0.1:',
  'effective-invention-xjwwp64g36r4j-8080.app.github.dev',
  'effective-invention-xjwwp64g36r4j-8081.app.github.dev',
  'effective-invention-xjwwp64g36r4j-8082.app.github.dev',
  'effective-invention-xjwwp64g36r4j-8083.app.github.dev',
  'effective-invention-xjwwp64g36r4j-9080.app.github.dev'
]
🎈 Attempting to connect to Redis in dev environment: redis://default:<EMAIL>:15121
🔄 Creating simple R2 client for fine-tune in local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🔄 Creating simple R2 client for RAG vectors in local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🔄 Creating simple Audio R2 client for local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🔄 Creating simple Public Transcript File R2 client for local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🔧 Local mode detected, using path-style URLs for S3 buckets
🔧 Using S3 endpoint: http://minio.divinci.local:9000
🔧 Using reliable MinIO endpoint: http://minio.divinci.local:9000
🪣 Using bucket name: workspace-audio, isLocalMode: true
🙌 Successful external call to: https://pokeapi.co/api/v2/pokemon/ditto
Attempting Mongoose Connection
🎈 MONGO_CONNECTION_URL: mongodb+srv://serverlessinstance0.c4pobzg.mongodb.net/divinci-dev
Attempting Redis Connection... 
🟢 Connected to Redis in dev environment at 2025-06-15T04:03:36.245Z
✅🌱 Successfully connected to Redis.
✅🌱 Successfully connected to MongoDB.
🔐 mTLS is enabled
🔑 mTLS OPTIONS bypass: enabled
📎 Certificate directory: /workspaces/server/private-keys/local-fast
🔍 Looking for server certificate at /workspaces/server/private-keys/local-fast/server.crt
📄 Certificate content (first 100 chars): -----BEGIN CERTIFICATE-----
MIIDCTCCAfGgAwIBAgIUVES76Ty2CRnyLZu8QlZsKERBaPcwDQYJKoZIhvcNAQEL
BQAwFDE
✅ Loaded server certificate from /workspaces/server/private-keys/local-fast/server.crt
🔍 Looking for server key at /workspaces/server/private-keys/local-fast/server.key
📄 Key content (first 100 chars): -----BEGIN PRIVATE KEY-----
MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC2ikXlFtX4qWKy
avfl+ek
✅ Loaded server key from /workspaces/server/private-keys/local-fast/server.key
🔐 Setting up server-only mTLS (no client certificate verification)
🔐 Set up server-only TLS context that allows all clients
🔐 Creating HTTPS server with mTLS options
🚀 Creating separate HTTP server for OPTIONS requests to bypass mTLS entirely
✅ Server is running on port: 9080
🪪 remote address:  ::ffff:127.0.0.1
🔍 applyCorsHeaders: Checking origin: "undefined"
🔍 applyCorsHeaders: isCloudEnvironment: false
✅ applyCorsHeaders: Allowing request with no origin
✅ TLS connection established for GET /
🪪 remote address:  ::ffff:127.0.0.1
🌐 Setting permissive CORS headers for request without origin
🙅🏻‍♂️🪪 No auth in req:  undefined
🔍 CORS debug: Checking origin: "undefined"
🔍 CORS debug: isCloudEnvironment: false
🔍 CORS debug: Environment: dev
🪪 remote address:  ::ffff:127.0.0.1
🔍 applyCorsHeaders: Checking origin: "undefined"
🔍 applyCorsHeaders: isCloudEnvironment: false
✅ applyCorsHeaders: Allowing request with no origin
✅ TLS connection established for GET /health
🪪 remote address:  ::ffff:127.0.0.1
🌐 Setting permissive CORS headers for request without origin
🙅🏻‍♂️🪪 No auth in req:  undefined
🔍 CORS debug: Checking origin: "undefined"
🔍 CORS debug: isCloudEnvironment: false
🔍 CORS debug: Environment: dev
☹️ HTTP ERR:  /health { statusCode: 404, message: 'not found' } not found undefined
🪪 remote address:  ::ffff:127.0.0.1
🔍 applyCorsHeaders: Checking origin: "undefined"
🔍 applyCorsHeaders: isCloudEnvironment: false
✅ applyCorsHeaders: Allowing request with no origin
✅ TLS connection established for GET /
🪪 remote address:  ::ffff:127.0.0.1
🌐 Setting permissive CORS headers for request without origin
🙅🏻‍♂️🪪 No auth in req:  undefined
🔍 CORS debug: Checking origin: "undefined"
🔍 CORS debug: isCloudEnvironment: false
🔍 CORS debug: Environment: dev
🪪 remote address:  ::ffff:127.0.0.1
