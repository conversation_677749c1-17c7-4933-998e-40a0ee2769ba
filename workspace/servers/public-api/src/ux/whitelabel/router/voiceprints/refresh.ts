import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { DataSourceAudioTranscriptModel } from "@divinci-ai/server-models";
import { HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";
import { createVoiceprint as createVoiceprintAPI, AUDIO_TOOLS, checkJobStatus } from "@divinci-ai/server-tools";
import { getPyannoteApiKey } from "../../../system/pyannote-constants";
import { getWhitelabelTarget } from "../data-source/audio-transcript/util/whitelabel";

/**
 * POST /api/whitelabel/:whitelabelId/voiceprints/:voiceprintId/refresh
 * 
 * - Looks up the voiceprint job in your DB by :voiceprintId
 * - Calls Pyannote API to get job status using tracked Pyannote job ID
 * - Updates the job in your DB
 * - Returns the updated job info
 */
export const refreshVoiceprintJob: RequestHandler = async (req, res, next) => {
  try {
    const { whitelabelId, voiceprintId } = req.params;
    if (!whitelabelId || !voiceprintId)
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Missing whitelabelId or voiceprintId");

    // Get the proper target format using the same utility function as create endpoint
    const { target } = await getWhitelabelTarget(req);

    // Find the audio document containing the voiceprint
    // Search by voiceprint jobId in the subdocuments
    console.log(`🔍 [REFRESH] Looking for voiceprint job: ${voiceprintId} with target: ${target}`);
    const audioDoc = await DataSourceAudioTranscriptModel.findOne({
      "voiceprints.jobId": voiceprintId,
      target: target
    });

    if (!audioDoc) {
      console.log(`❌ [REFRESH] No audio document found for voiceprint job: ${voiceprintId}`);
      res.status(404).json({ error: "Voiceprint audio document not found" });
      return;
    }

    console.log(`✅ [REFRESH] Found audio document: ${audioDoc._id}`);
    

    // Find the correct voiceprint in the array by jobId
    let voiceprint;
    if (Array.isArray(audioDoc.voiceprints)) {
      voiceprint = audioDoc.voiceprints.find(vp => vp.jobId === voiceprintId);
    }
    if (!voiceprint) {
      res.status(404).json({ error: "Voiceprint not found in document" });
      return;
    }
    const pyannoteJobId = voiceprint.jobId;
    if (!pyannoteJobId) {
      res.status(400).json({ error: "No external Pyannote jobId recorded for this voiceprint" });
      return;
    }

    // Fetch status from Pyannote API using the correct generic job status endpoint
    console.log(`🔍 [REFRESH] Fetching job status from Pyannote API for job: ${pyannoteJobId}`);
    
    try {
      const pyannoteData = await checkJobStatus(pyannoteJobId);
      console.log(`✅ [REFRESH] Retrieved job status:`, pyannoteData);

      // Update subdocument with new status and info
      voiceprint.status = pyannoteData.status || voiceprint.status;
      voiceprint.voiceprintData = pyannoteData.output || voiceprint.voiceprintData;
      voiceprint.lastUpdated = Date.now();

            await audioDoc.save();

      res.json({
        _id: audioDoc._id,
        voiceprintId,
        status: voiceprint.status,
        voiceprintData: voiceprint.voiceprintData,
        updatedAt: audioDoc.updatedAt,
        pyannote: pyannoteData
      });
      
    } catch (error: any) {
      console.error(`❌ [REFRESH] Failed to fetch job status:`, error);
      res.status(500).json({ 
        error: "Failed to fetch Pyannote status", 
        details: error.message 
      });
      return;
    }

  } catch (err) {
    next(err);
  }
};