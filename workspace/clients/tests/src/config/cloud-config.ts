/**
 * Cloud Environment Test Configuration
 *
 * This module provides configuration for running E2E tests against
 * different cloud environments (develop, staging, production).
 */

export interface CloudTestConfig {
  environment: string;
  baseURL: string;
  apiURL: string;
  headers: Record<string, string>;
  timeouts: {
    test: number;
    expect: number;
    navigation: number;
  };
  retries: {
    test: number;
    expect: number;
  };
  features: {
    auth0: boolean;
    cloudflareAccess: boolean;
    monitoring: boolean;
  };
}

/**
 * Get cloud test configuration based on environment
 */
export function getCloudTestConfig(): CloudTestConfig {
  const environment = process.env.TARGET_ENVIRONMENT || process.env.TEST_ENV || 'develop';

  const configs: Record<string, CloudTestConfig> = {
    develop: {
      environment: 'develop',
      baseURL: 'https://chat.dev.divinci.app',
      apiURL: 'https://api.dev.divinci.app',
      headers: {
        'CF-Access-Client-Id': process.env.CF_ACCESS_CLIENT_ID_DEV || process.env.CF_ACCESS_CLIENT_ID || '',
        'CF-Access-Client-Secret': process.env.CF_ACCESS_CLIENT_SECRET_DEV || process.env.CF_ACCESS_CLIENT_SECRET || '',
        'User-Agent': 'Divinci-E2E-Tests/1.0 (Development)',
      },
      timeouts: {
        test: 60000,      // 1 minute per test
        expect: 10000,    // 10 seconds for assertions
        navigation: 30000, // 30 seconds for page loads
      },
      retries: {
        test: 2,
        expect: 1,
      },
      features: {
        auth0: true,
        cloudflareAccess: true,
        monitoring: false,
      },
    },

    staging: {
      environment: 'staging',
      baseURL: 'https://chat.stage.divinci.app',
      apiURL: 'https://api.stage.divinci.app',
      headers: {
        'CF-Access-Client-Id': process.env.CF_ACCESS_CLIENT_ID_STAGING || process.env.CF_ACCESS_CLIENT_ID || '',
        'CF-Access-Client-Secret': process.env.CF_ACCESS_CLIENT_SECRET_STAGING || process.env.CF_ACCESS_CLIENT_SECRET || '',
        'User-Agent': 'Divinci-E2E-Tests/1.0 (Staging)',
      },
      timeouts: {
        test: 90000,      // 1.5 minutes per test (staging can be slower)
        expect: 15000,    // 15 seconds for assertions
        navigation: 45000, // 45 seconds for page loads
      },
      retries: {
        test: 3,          // More retries for staging
        expect: 2,
      },
      features: {
        auth0: true,
        cloudflareAccess: true,
        monitoring: true,
      },
    },

    production: {
      environment: 'production',
      baseURL: 'https://chat.divinci.app',
      apiURL: 'https://api.divinci.app',
      headers: {
        'CF-Access-Client-Id': process.env.CF_ACCESS_CLIENT_ID_PROD || process.env.CF_ACCESS_CLIENT_ID || '',
        'CF-Access-Client-Secret': process.env.CF_ACCESS_CLIENT_SECRET_PROD || process.env.CF_ACCESS_CLIENT_SECRET || '',
        'User-Agent': 'Divinci-E2E-Tests/1.0 (Production)',
      },
      timeouts: {
        test: 120000,     // 2 minutes per test (production should be fast but allow for network)
        expect: 20000,    // 20 seconds for assertions
        navigation: 60000, // 1 minute for page loads
      },
      retries: {
        test: 1,          // Fewer retries for production
        expect: 1,
      },
      features: {
        auth0: true,
        cloudflareAccess: true,
        monitoring: true,
      },
    },
  };

  const config = configs[environment];
  if (!config) {
    throw new Error(`Unknown environment: ${environment}. Supported environments: ${Object.keys(configs).join(', ')}`);
  }

  // Validate required environment variables
  validateConfig(config);

  return config;
}

/**
 * Validate that required configuration is present
 */
function validateConfig(config: CloudTestConfig): void {
  const errors: string[] = [];

  // Check for required URLs
  if (!config.baseURL) {
    errors.push('baseURL is required');
  }

  if (!config.apiURL) {
    errors.push('apiURL is required');
  }

  // Check for Cloudflare Access credentials if feature is enabled
  if (config.features.cloudflareAccess) {
    if (!config.headers['CF-Access-Client-Id']) {
      errors.push('CF-Access-Client-Id is required for Cloudflare Access');
    }

    if (!config.headers['CF-Access-Client-Secret']) {
      errors.push('CF-Access-Client-Secret is required for Cloudflare Access');
    }
  }

  // Check for Auth0 configuration if feature is enabled
  if (config.features.auth0) {
    const requiredAuth0Vars = [
      'AUTH0_BASE_URL',
      'AUTH0_CLIENT_ID',
      'AUTH0_AUDIENCE',
    ];

    for (const varName of requiredAuth0Vars) {
      if (!process.env[varName]) {
        errors.push(`${varName} environment variable is required for Auth0`);
      }
    }
  }

  if (errors.length > 0) {
    throw new Error(`Configuration validation failed:\n${errors.map(e => `  - ${e}`).join('\n')}`);
  }
}

/**
 * Get environment-specific test user credentials
 */
export function getTestUserCredentials(environment: string) {
  const envPrefix = environment.toUpperCase();

  return {
    email: process.env[`AUTH0_TEST_USER_EMAIL_${envPrefix}`] || process.env.AUTH0_TEST_USER_EMAIL,
    password: process.env[`AUTH0_TEST_USER_PASSWORD_${envPrefix}`] || process.env.AUTH0_TEST_USER_PASSWORD,
    adminEmail: process.env[`AUTH0_ADMIN_USER_EMAIL_${envPrefix}`] || process.env.AUTH0_ADMIN_USER_EMAIL,
    adminPassword: process.env[`AUTH0_ADMIN_USER_PASSWORD_${envPrefix}`] || process.env.AUTH0_ADMIN_USER_PASSWORD,
  };
}

/**
 * Get service-specific endpoints for the environment
 */
export function getServiceEndpoints(config: CloudTestConfig) {
  return {
    api: {
      health: `${config.apiURL}/`,  // Root endpoint serves as health check
      auth: `${config.apiURL}/auth`,
      chat: `${config.apiURL}/ai-chat`,
      rag: `${config.apiURL}/rag`,
      transcript: `${config.apiURL}/audio-transcript`,
      fineTune: `${config.apiURL}/fine-tune`,
    },
    web: {
      login: `${config.baseURL}/login`,
      dashboard: `${config.baseURL}/dashboard`,
      chat: `${config.baseURL}/chat`,
      rag: `${config.baseURL}/rag`,
      transcript: `${config.baseURL}/transcript`,
      fineTune: `${config.baseURL}/fine-tune`,
    },
  };
}

/**
 * Check if we're running in a cloud environment
 */
export function isCloudEnvironment(): boolean {
  const env = process.env.TARGET_ENVIRONMENT || process.env.TEST_ENV || 'local';
  return env !== 'local';
}

/**
 * Get test tags based on environment and test type
 */
export function getTestTags(testType: 'smoke' | 'integration' | 'performance' | 'security' = 'smoke') {
  const config = getCloudTestConfig();
  const tags = [`@${testType}`, `@${config.environment}`];

  // Add feature-specific tags
  if (config.features.auth0) tags.push('@auth0');
  if (config.features.cloudflareAccess) tags.push('@cloudflare');
  if (config.features.monitoring) tags.push('@monitoring');

  return tags;
}
