#!/usr/bin/env node

/**
 * Quick Fix Script for API E2E Test Authentication Issues
 * 
 * This script automatically fixes the most common authentication configuration
 * issues in the API E2E test files.
 */

const fs = require('fs');
const path = require('path');

const testFiles = [
  'src/tests/message.spec.ts',
  'src/tests/finetune.spec.ts', 
  'src/tests/moderation.spec.ts',
  'src/tests/thread.spec.ts',
  'src/tests/workspace.spec.ts'
];

function fixAuthInFile(filePath) {
  console.log(`Fixing auth issues in: ${filePath}`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`  ⚠️  File not found: ${filePath}`);
    return;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Fix 1: Add missing auth import if not present
  if (!content.includes('getAuthFileForRole')) {
    const importMatch = content.match(/import.*from.*['"]@playwright\/test['"];?\n/);
    if (importMatch) {
      const newImport = `${importMatch[0]}import { getAuthFileForRole } from '../auth/auth-utils';\n`;
      content = content.replace(importMatch[0], newImport);
      modified = true;
      console.log('  ✅ Added auth import');
    }
  }

  // Fix 2: Fix ApiClient constructor calls without auth file
  const apiClientPattern = /new ApiClient\(\s*\)/g;
  if (apiClientPattern.test(content)) {
    content = content.replace(
      /new ApiClient\(\s*\)/g, 
      "new ApiClient(config.apiBaseUrl, getAuthFileForRole('admin'))"
    );
    modified = true;
    console.log('  ✅ Fixed ApiClient constructor');
  }

  // Fix 3: Add config import if ApiClient is used but config is not imported
  if (content.includes('ApiClient') && !content.includes('config')) {
    const authImportMatch = content.match(/import.*getAuthFileForRole.*\n/);
    if (authImportMatch) {
      const newImport = `${authImportMatch[0]}import { config } from '../config/config';\n`;
      content = content.replace(authImportMatch[0], newImport);
      modified = true;
      console.log('  ✅ Added config import');
    }
  }

  // Fix 4: Fix super() calls in extended ApiClient classes
  const superCallPattern = /class.*extends ApiClient.*\{[\s\S]*?constructor\(\).*\{[\s\S]*?super\(\);/g;
  const matches = content.match(superCallPattern);
  if (matches) {
    content = content.replace(
      /super\(\);/g,
      "super(config.apiBaseUrl, getAuthFileForRole('admin'));"
    );
    modified = true;
    console.log('  ✅ Fixed super() calls in ApiClient extensions');
  }

  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log(`  ✅ Successfully updated ${filePath}`);
  } else {
    console.log(`  ℹ️  No changes needed for ${filePath}`);
  }
}

function main() {
  console.log('🔧 Starting API E2E Test Authentication Fix Script\n');

  // Change to the tests directory
  const testsDir = path.resolve(__dirname);
  process.chdir(testsDir);

  testFiles.forEach(fixAuthInFile);

  console.log('\n✅ Authentication fix script completed!');
  console.log('\nNext steps:');
  console.log('1. Run the tests to verify fixes: npx playwright test --project=api-tests');
  console.log('2. Review the API_E2E_IMPROVEMENT_PLAN.md for next steps');
  console.log('3. Consider migrating to real API tests using ai-chat-real-api.spec.ts as a template');
}

if (require.main === module) {
  main();
}

module.exports = { fixAuthInFile };
