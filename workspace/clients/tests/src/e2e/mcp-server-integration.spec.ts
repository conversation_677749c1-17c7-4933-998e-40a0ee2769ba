import { test, expect, Page } from "@playwright/test";
import { verifyLoggedIn } from "../utils/auth";
import { getWhitelabelId } from "../utils/whitelabel";

/**
 * MCP Server Integration E2E Tests
 *
 * This test suite verifies that the Model Context Protocol (MCP) server
 * is properly integrated and functioning with the Divinci AI platform.
 */

/**
 * Helper function to test MCP server health
 */
async function testMCPServerHealth(page: Page): Promise<boolean> {
  try {
    const response = await page.evaluate(async () => {
      try {
        const response = await fetch("http://localhost:8793/health");
        return {
          ok: response.ok,
          status: response.status,
          data: await response.json(),
        };
      } catch (error) {
        return { ok: false, error: error.message };
      }
    });

    console.log(
      `MCP Server health check: ${response.ok ? "HEALTHY" : "UNHEALTHY"}`
    );
    if (response.ok) {
      console.log(`MCP Server info: ${JSON.stringify(response.data)}`);
    }
    return response.ok;
  } catch (error) {
    console.error("MCP Server health check failed:", error);
    return false;
  }
}

/**
 * Helper function to test MCP tool listing
 */
async function testMCPToolListing(page: Page): Promise<any[]> {
  try {
    const response = await page.evaluate(async () => {
      try {
        const response = await fetch("http://localhost:8793/sse", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            jsonrpc: "2.0",
            id: 1,
            method: "tools/list",
            params: {},
          }),
        });
        return {
          ok: response.ok,
          status: response.status,
          data: await response.json(),
        };
      } catch (error) {
        return { ok: false, error: error.message };
      }
    });

    if (response.ok && response.data.result) {
      console.log(`MCP Tools available: ${response.data.result.tools.length}`);
      response.data.result.tools.forEach((tool: any, index: number) => {
        console.log(`  ${index + 1}. ${tool.name}: ${tool.description}`);
      });
      return response.data.result.tools;
    }
    return [];
  } catch (error) {
    console.error("MCP Tool listing failed:", error);
    return [];
  }
}

/**
 * Helper function to test MCP tool execution
 */
async function testMCPToolExecution(
  page: Page,
  toolName: string,
  args: any = {}
): Promise<any> {
  try {
    const response = await page.evaluate(
      async (params) => {
        try {
          const response = await fetch("http://localhost:8793/sse", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              jsonrpc: "2.0",
              id: Date.now(),
              method: "tools/call",
              params: {
                name: params.toolName,
                arguments: params.args,
              },
            }),
          });
          return {
            ok: response.ok,
            status: response.status,
            data: await response.json(),
          };
        } catch (error) {
          return { ok: false, error: error.message };
        }
      },
      { toolName, args }
    );

    if (response.ok && response.data.result) {
      console.log(`MCP Tool '${toolName}' executed successfully`);
      if (response.data.result.content && response.data.result.content[0]) {
        const content = response.data.result.content[0].text;
        console.log(
          `Tool response: ${content.substring(0, 200)}${
            content.length > 200 ? "..." : ""
          }`
        );
      }
      return response.data.result;
    } else {
      console.error(`MCP Tool '${toolName}' execution failed:`, response.data);
      return null;
    }
  } catch (error) {
    console.error(`MCP Tool '${toolName}' execution error:`, error);
    return null;
  }
}

test.describe("MCP Server Integration", () => {
  test.beforeEach(async ({ page }) => {
    // Increase timeout for MCP tests
    test.setTimeout(120000);

    // Verify user is logged in
    await verifyLoggedIn(page);
  });

  test("MCP Server Health Check", async ({ page }) => {
    console.log("🏥 Testing MCP Server Health...");

    const isHealthy = await testMCPServerHealth(page);
    expect(isHealthy).toBe(true);

    console.log("✅ MCP Server is healthy and responding");
  });

  test("MCP Tools Discovery", async ({ page }) => {
    console.log("🔍 Testing MCP Tools Discovery...");

    // First verify server is healthy
    const isHealthy = await testMCPServerHealth(page);
    expect(isHealthy).toBe(true);

    // Test tool listing
    const tools = await testMCPToolListing(page);
    expect(tools.length).toBeGreaterThan(0);

    // Verify expected tools are present
    const expectedTools = [
      "get_user_profile",
      "create_chat",
      "send_message",
      "list_chats",
    ];
    expectedTools.forEach((expectedTool) => {
      const tool = tools.find((t) => t.name === expectedTool);
      expect(tool).toBeDefined();
      console.log(`✅ Found expected tool: ${expectedTool}`);
    });

    console.log("✅ MCP Tools discovery completed successfully");
  });

  test("MCP Tool: Get User Profile", async ({ page }) => {
    console.log("👤 Testing MCP Tool: get_user_profile...");

    const result = await testMCPToolExecution(page, "get_user_profile");
    expect(result).toBeTruthy();
    expect(result.content).toBeDefined();
    expect(result.content[0].text).toContain("User Profile");
    expect(result.content[0].text).toContain("Email:");
    expect(result.content[0].text).toContain("Name:");

    console.log("✅ get_user_profile tool working correctly");
  });

  test("MCP Tool: Create Chat", async ({ page }) => {
    console.log("💬 Testing MCP Tool: create_chat...");

    // Test creating chat with parameters
    const resultWithParams = await testMCPToolExecution(page, "create_chat", {
      title: "E2E Test Chat",
      model: "gpt-4",
    });
    expect(resultWithParams).toBeTruthy();
    expect(resultWithParams.content[0].text).toContain(
      "Created new chat conversation"
    );
    expect(resultWithParams.content[0].text).toContain("E2E Test Chat");

    // Test creating chat without parameters
    const resultWithoutParams = await testMCPToolExecution(page, "create_chat");
    expect(resultWithoutParams).toBeTruthy();
    expect(resultWithoutParams.content[0].text).toContain(
      "Created new chat conversation"
    );

    console.log("✅ create_chat tool working correctly");
  });

  test("MCP Tool: Send Message", async ({ page }) => {
    console.log("📤 Testing MCP Tool: send_message...");

    const result = await testMCPToolExecution(page, "send_message", {
      chatId: "test_chat_123",
      message: "Hello from E2E test!",
    });
    expect(result).toBeTruthy();
    expect(result.content[0].text).toContain("Message sent to chat");
    expect(result.content[0].text).toContain("Hello from E2E test!");
    expect(result.content[0].text).toContain("Mock AI Response");

    console.log("✅ send_message tool working correctly");
  });

  test("MCP Tool: List Chats", async ({ page }) => {
    console.log("📋 Testing MCP Tool: list_chats...");

    // Test with limit
    const resultWithLimit = await testMCPToolExecution(page, "list_chats", {
      limit: 5,
    });
    expect(resultWithLimit).toBeTruthy();
    expect(resultWithLimit.content[0].text).toContain("Found");
    expect(resultWithLimit.content[0].text).toContain("chat conversations");

    // Test without limit
    const resultWithoutLimit = await testMCPToolExecution(page, "list_chats");
    expect(resultWithoutLimit).toBeTruthy();
    expect(resultWithoutLimit.content[0].text).toContain("Found");
    expect(resultWithoutLimit.content[0].text).toContain("chat conversations");

    console.log("✅ list_chats tool working correctly");
  });

  test("MCP Error Handling", async ({ page }) => {
    console.log("⚠️ Testing MCP Error Handling...");

    // Test calling non-existent tool
    const invalidToolResult = await page.evaluate(async () => {
      try {
        const response = await fetch("http://localhost:8793/sse", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            jsonrpc: "2.0",
            id: 1,
            method: "tools/call",
            params: {
              name: "non_existent_tool",
              arguments: {},
            },
          }),
        });
        return await response.json();
      } catch (error) {
        return { error: error.message };
      }
    });

    expect(invalidToolResult.error).toBeDefined();
    expect(invalidToolResult.error.code).toBe(-32603);
    console.log("✅ Error handling for invalid tools working correctly");

    // Test malformed JSON request
    const malformedResult = await page.evaluate(async () => {
      try {
        const response = await fetch("http://localhost:8793/sse", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: "invalid json",
        });
        return {
          status: response.status,
          data: await response.json(),
        };
      } catch (error) {
        return { error: error.message };
      }
    });

    expect(malformedResult.status).toBe(400);
    console.log("✅ Error handling for malformed requests working correctly");
  });

  test("MCP Performance and Load", async ({ page }) => {
    console.log("⚡ Testing MCP Performance and Load...");

    const startTime = Date.now();
    const promises = [];

    // Execute multiple tools concurrently
    for (let i = 0; i < 5; i++) {
      promises.push(testMCPToolExecution(page, "get_user_profile"));
      promises.push(testMCPToolExecution(page, "list_chats", { limit: 3 }));
    }

    const results = await Promise.all(promises);
    const endTime = Date.now();
    const duration = endTime - startTime;

    // Verify all requests succeeded
    results.forEach((result, index) => {
      expect(result).toBeTruthy();
    });

    console.log(
      `✅ Executed ${promises.length} concurrent MCP requests in ${duration}ms`
    );
    console.log(
      `✅ Average response time: ${duration / promises.length}ms per request`
    );

    // Performance should be reasonable (less than 5 seconds for 10 requests)
    expect(duration).toBeLessThan(5000);
  });

  test("MCP Integration with Whitelabel Context", async ({ page }) => {
    console.log("🏷️ Testing MCP Integration with Whitelabel Context...");

    // Get whitelabel for context
    const whitelabelId = await getWhitelabelId(page);
    console.log(`Using whitelabel ID: ${whitelabelId}`);

    // Test that MCP tools work within whitelabel context
    const userProfile = await testMCPToolExecution(page, "get_user_profile");
    expect(userProfile).toBeTruthy();

    // Create a chat within the whitelabel context
    const chatResult = await testMCPToolExecution(page, "create_chat", {
      title: `Whitelabel ${whitelabelId} Test Chat`,
      model: "gpt-4",
    });
    expect(chatResult).toBeTruthy();
    expect(chatResult.content[0].text).toContain(
      `Whitelabel ${whitelabelId} Test Chat`
    );

    console.log("✅ MCP integration with whitelabel context working correctly");
  });
});
