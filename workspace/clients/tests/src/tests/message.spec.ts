/**
 * Message API Tests
 *
 * This file contains tests for the Message API.
 */

import { test, expect } from '@playwright/test';
import { ApiClient } from '../api/api-client';
import { getAuthFileForRole } from '../auth/auth-utils';
import { config } from '../config/config';

// Mock Message API responses
const mockMessageResponse = {
  _id: 'mock-message-123',
  threadId: 'mock-thread-123',
  role: 'user',
  content: 'Hello, AI!',
  metadata: {
    tags: ['test', 'example'],
    category: 'general'
  },
  timestamp: new Date().toISOString()
};

// Mock API client for Message
class MockMessageClient extends ApiClient {
  constructor() {
    super(config.apiBaseUrl, getAuthFileForRole('admin'));
  }

  async get(path: string) {
    if (path === 'message') {
      return [mockMessageResponse];
    } else if (path.startsWith('message/')) {
      return mockMessageResponse;
    } else if (path.includes('thread') && path.includes('messages')) {
      return [mockMessageResponse];
    }
    return Promise.resolve({});
  }

  async post(path: string, data: any) {
    if (path === 'message') {
      return {
        ...mockMessageResponse,
        content: data.content || 'New message',
        threadId: data.threadId || 'mock-thread-123',
        metadata: data.metadata || mockMessageResponse.metadata
      };
    }
    return Promise.resolve({});
  }

  async delete(path: string) {
    if (path.startsWith('message/')) {
      return { success: true };
    }
    return Promise.resolve({});
  }

  async put(path: string, data: any) {
    if (path.startsWith('message/')) {
      return {
        ...mockMessageResponse,
        content: data.content || mockMessageResponse.content,
        metadata: data.metadata || mockMessageResponse.metadata
      };
    }
    return Promise.resolve({});
  }
}

test.describe('Message API', () => {
  let client: MockMessageClient;

  test.beforeEach(() => {
    client = new MockMessageClient();
  });

  test('should create a new message', async () => {
    const message = await client.post('message', {
      threadId: 'mock-thread-123',
      content: 'This is a test message',
      metadata: {
        tags: ['test', 'example'],
        category: 'general'
      }
    });

    expect(message).toBeDefined();
    expect(message._id).toBeDefined();
    expect(message.threadId).toBe('mock-thread-123');
    expect(message.content).toBe('This is a test message');
    expect(message.metadata).toBeDefined();
    expect(message.metadata.tags).toContain('test');
  });

  test('should get all messages', async () => {
    const messages = await client.get('message');

    expect(messages).toBeInstanceOf(Array);
    expect(messages.length).toBeGreaterThan(0);
    expect(messages[0]._id).toBeDefined();
    expect(messages[0].content).toBeDefined();
  });

  test('should get a message by ID', async () => {
    const message = await client.get('message/mock-message-123');

    expect(message).toBeDefined();
    expect(message._id).toBe('mock-message-123');
    expect(message.content).toBe('Hello, AI!');
    expect(message.threadId).toBe('mock-thread-123');
  });

  test('should get all messages for a thread', async () => {
    const messages = await client.get('thread/mock-thread-123/messages');

    expect(messages).toBeInstanceOf(Array);
    expect(messages.length).toBeGreaterThan(0);
    expect(messages[0].threadId).toBe('mock-thread-123');
  });

  test('should update a message', async () => {
    const updatedMessage = await client.put('message/mock-message-123', {
      content: 'Updated message content',
      metadata: {
        tags: ['updated', 'test'],
        category: 'support'
      }
    });

    expect(updatedMessage).toBeDefined();
    expect(updatedMessage.content).toBe('Updated message content');
    expect(updatedMessage.metadata.tags).toContain('updated');
    expect(updatedMessage.metadata.category).toBe('support');
  });

  test('should delete a message', async () => {
    const result = await client.delete('message/mock-message-123');

    expect(result).toBeDefined();
    expect(result.success).toBe(true);
  });
});
