/**
 * Real API Cleanup Utilities
 * 
 * This module provides utilities for cleaning up resources created during real API tests.
 * It ensures that tests don't leave behind data that could interfere with subsequent runs.
 */

import { ApiClient } from '../api/api-client';
import { config } from '../config/config';

/**
 * Resource types that can be cleaned up
 */
export type ResourceType = 'chat' | 'message' | 'user' | 'release';

/**
 * Resource identifier for cleanup tracking
 */
export interface ResourceIdentifier {
  type: ResourceType;
  id: string;
  parentId?: string; // For nested resources like messages in chats
  metadata?: Record<string, any>;
}

/**
 * Cleanup manager for real API resources
 */
export class RealApiCleanupManager {
  private resources: ResourceIdentifier[] = [];
  private apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  /**
   * Track a resource for cleanup
   */
  trackResource(resource: ResourceIdentifier): void {
    console.log(`[Cleanup] Tracking ${resource.type} resource: ${resource.id}`);
    this.resources.push(resource);
  }

  /**
   * Track a chat resource
   */
  trackChat(chatId: string, metadata?: Record<string, any>): void {
    this.trackResource({
      type: 'chat',
      id: chatId,
      metadata
    });
  }

  /**
   * Track a message resource
   */
  trackMessage(messageId: string, chatId: string, metadata?: Record<string, any>): void {
    this.trackResource({
      type: 'message',
      id: messageId,
      parentId: chatId,
      metadata
    });
  }

  /**
   * Get all tracked resources
   */
  getTrackedResources(): ResourceIdentifier[] {
    return [...this.resources];
  }

  /**
   * Clean up all tracked resources
   */
  async cleanupAll(): Promise<void> {
    console.log(`[Cleanup] Starting cleanup of ${this.resources.length} resources`);
    
    const errors: Error[] = [];
    
    // Clean up in reverse order (children before parents)
    const sortedResources = this.sortResourcesForCleanup();
    
    for (const resource of sortedResources) {
      try {
        await this.cleanupResource(resource);
      } catch (error) {
        console.error(`[Cleanup] Failed to cleanup ${resource.type} ${resource.id}:`, error);
        errors.push(error as Error);
      }
    }
    
    // Clear the tracked resources
    this.resources = [];
    
    if (errors.length > 0) {
      console.warn(`[Cleanup] Completed with ${errors.length} errors`);
      // Don't throw - cleanup errors shouldn't fail tests
    } else {
      console.log(`[Cleanup] Successfully cleaned up all resources`);
    }
  }

  /**
   * Sort resources for cleanup (children before parents)
   */
  private sortResourcesForCleanup(): ResourceIdentifier[] {
    const sorted = [...this.resources];
    
    // Sort by dependency order: messages before chats, etc.
    sorted.sort((a, b) => {
      const order = { message: 0, chat: 1, user: 2, release: 3 };
      return order[a.type] - order[b.type];
    });
    
    return sorted;
  }

  /**
   * Clean up a specific resource
   */
  private async cleanupResource(resource: ResourceIdentifier): Promise<void> {
    console.log(`[Cleanup] Cleaning up ${resource.type}: ${resource.id}`);
    
    switch (resource.type) {
      case 'chat':
        await this.cleanupChat(resource);
        break;
      case 'message':
        await this.cleanupMessage(resource);
        break;
      case 'user':
        await this.cleanupUser(resource);
        break;
      case 'release':
        await this.cleanupRelease(resource);
        break;
      default:
        console.warn(`[Cleanup] Unknown resource type: ${resource.type}`);
    }
  }

  /**
   * Clean up a chat resource
   */
  private async cleanupChat(resource: ResourceIdentifier): Promise<void> {
    try {
      // First, try to get the chat to see if it exists
      await this.apiClient.get(`ai-chat/${resource.id}`);
      
      // If it exists, delete it
      await this.apiClient.delete(`ai-chat/${resource.id}`);
      console.log(`[Cleanup] Successfully deleted chat: ${resource.id}`);
    } catch (error: any) {
      if (error.status === 404) {
        console.log(`[Cleanup] Chat ${resource.id} already deleted or doesn't exist`);
      } else {
        throw error;
      }
    }
  }

  /**
   * Clean up a message resource
   */
  private async cleanupMessage(resource: ResourceIdentifier): Promise<void> {
    if (!resource.parentId) {
      console.warn(`[Cleanup] Message ${resource.id} has no parent chat ID, skipping`);
      return;
    }

    try {
      await this.apiClient.delete(`ai-chat/${resource.parentId}/message/${resource.id}`);
      console.log(`[Cleanup] Successfully deleted message: ${resource.id}`);
    } catch (error: any) {
      if (error.status === 404) {
        console.log(`[Cleanup] Message ${resource.id} already deleted or doesn't exist`);
      } else {
        throw error;
      }
    }
  }

  /**
   * Clean up a user resource (placeholder for future implementation)
   */
  private async cleanupUser(resource: ResourceIdentifier): Promise<void> {
    console.log(`[Cleanup] User cleanup not implemented yet: ${resource.id}`);
    // TODO: Implement user cleanup when user management APIs are available
  }

  /**
   * Clean up a release resource (placeholder for future implementation)
   */
  private async cleanupRelease(resource: ResourceIdentifier): Promise<void> {
    console.log(`[Cleanup] Release cleanup not implemented yet: ${resource.id}`);
    // TODO: Implement release cleanup when release management APIs are available
  }

  /**
   * Clean up resources by type
   */
  async cleanupByType(type: ResourceType): Promise<void> {
    const resourcesOfType = this.resources.filter(r => r.type === type);
    console.log(`[Cleanup] Cleaning up ${resourcesOfType.length} ${type} resources`);
    
    for (const resource of resourcesOfType) {
      try {
        await this.cleanupResource(resource);
        // Remove from tracked resources
        this.resources = this.resources.filter(r => r !== resource);
      } catch (error) {
        console.error(`[Cleanup] Failed to cleanup ${type} ${resource.id}:`, error);
      }
    }
  }

  /**
   * Get cleanup statistics
   */
  getCleanupStats(): { total: number; byType: Record<ResourceType, number> } {
    const byType = this.resources.reduce((acc, resource) => {
      acc[resource.type] = (acc[resource.type] || 0) + 1;
      return acc;
    }, {} as Record<ResourceType, number>);

    return {
      total: this.resources.length,
      byType
    };
  }
}

/**
 * Global cleanup manager instance
 */
let globalCleanupManager: RealApiCleanupManager | null = null;

/**
 * Get or create the global cleanup manager
 */
export function getCleanupManager(apiClient?: ApiClient): RealApiCleanupManager {
  if (!globalCleanupManager) {
    if (!apiClient) {
      throw new Error('ApiClient required to create cleanup manager');
    }
    globalCleanupManager = new RealApiCleanupManager(apiClient);
  }
  return globalCleanupManager;
}

/**
 * Reset the global cleanup manager
 */
export function resetCleanupManager(): void {
  globalCleanupManager = null;
}

/**
 * Cleanup helper for test teardown
 */
export async function cleanupTestResources(apiClient: ApiClient): Promise<void> {
  const manager = getCleanupManager(apiClient);
  await manager.cleanupAll();
  resetCleanupManager();
}
