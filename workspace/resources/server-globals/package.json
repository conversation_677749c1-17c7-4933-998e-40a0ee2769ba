{
  "name": "@divinci-ai/server-globals",
  "version": "1.3.12",
  "description": "",
  "main": "dist/index.js",
<<<<<<< HEAD
  "types": "dist/index.d.ts",
  "typings": "dist/index.d.ts",
=======
  "types": "src/index.ts",
  "typings": "src/index.ts",
>>>>>>> WA-170_MCP
  "scripts": {
    "build": "tsc",
    "build:ci": "tsc --project tsconfig.ci.json",
    "build:ignore-errors": "tsc --skipLibCheck --noEmit || echo 'TypeScript errors ignored'",
    "build:ignore-errors:ci": "tsc --skipLibCheck --noEmit --project tsconfig.ci.json || echo 'TypeScript errors ignored'",
    "prepare": "rimraf ./dist && tsc",
    "test": "vitest run",
    "test:watch": "vitest",
    "test:coverage": "vitest run --coverage"
  },
  "author": "",
  "license": "JSON",
  "devDependencies": {
    "@types/busboy": "^1.5.4",
    "@types/cors": "^2.8.17",
    "@types/express": "^4.17.21",
    "@types/jest": "^29.5.12",
    "@types/mime-types": "^2.1.4",
    "@types/multer": "^1.4.11",
    "@types/node": "^22.5.2",
    "@types/node-jose": "^1.1.13",
    "@types/pg": "^8.11.8",
    "@types/readable-stream": "^4.0.15",
    "@types/redis": "^4.0.11",
    "@types/stream-json": "^1.7.7",
    "@types/stream-to-blob": "^2.0.0",
    "@vitest/coverage-v8": "^3.1.1",
    "dotenv": "^16.4.5",
    "glob": "^10.4.1",
    "jest": "^29.7.0",
    "rimraf": "^6.0.1",
    "ts-jest": "^29.2.5",
    "typescript": "^5.8.3",
    "vitest": "^3.1.1"
  },
  "dependencies": {
    "@aws-sdk/client-s3": "^3.750.0",
    "@aws-sdk/lib-storage": "^3.750.0",
    "@aws-sdk/s3-request-presigner": "^3.750.0",
    "@divinci-ai/models": "file:../models",
    "@divinci-ai/server-utils": "file:../server-utils",
    "@divinci-ai/utils": "file:../utils",
    "dropbox": "^10.34.0",
    "@google-cloud/speech": "^6.7.1",
    "@google-cloud/storage": "^6.12.0",
    "@google-cloud/text-to-speech": "^6.0.1",
    "@mendable/firecrawl-js": "^1.11.2",
    "busboy": "^1.6.0",
    "cors": "^2.8.5",
    "express": "^4.19.2",
    "express-oauth2-jwt-bearer": "^1.6.0",
    "form-data": "^4.0.1",
    "jose": "^5.7.0",
    "mime-types": "^2.1.35",
    "mongoose": "^8.7.0",
    "multer": "^1.4.5-lts.1",
    "node-cloudflare-r2": "^0.1.5",
    "node-fetch": "^3.3.2",
    "node-fetch-commonjs": "^3.3.2",
    "openai": "^4.84.1",
    "pg": "^8.14.1",
    "redis": "^4.7.0",
    "stream-json": "^1.8.0",
    "stream-to-blob": "^2.0.1",
    "stripe": "^15.8.0",
    "twilio": "^4.14.0",
    "unstructured-client": "^0.19.0",
    "zod": "^3.23.8"
  },
  "engines": {
    "node": ">=20",
    "pnpm": ">=9"
  },
  "packageManager": "pnpm@10.5.2+sha512.da9dc28cd3ff40d0592188235ab25d3202add8a207afbedc682220e4a0029ffbff4562102b9e6e46b4e3f9e8bd53e6d05de48544b0c57d4b0179e22c76d1199b",
  "pnpm": {
    "neverBuiltDependencies": []
  }
}