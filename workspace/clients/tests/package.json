{
  "name": "@divinci-ai/test-client",
  "version": "0.3.0",
  "private": true,
  "description": "Runs tests on the server",
  "main": "./dist/index.js",
  "typings": "./src/index.ts",
  "scripts": {
    "start": "node dist/index.js",
    "start:all": "CHANGED_FOLDERS=workspace/servers/public-api/src/routes/ai-chat,workspace/servers/public-api/src/routes/rag node dist/index.js",
    "build": "tsc && node scripts/copy-html.js",
    "build:ignore-errors": "tsc || echo 'TypeScript errors ignored'",
    "prepare": "rimraf ./dist && tsc && node scripts/copy-html.js || echo 'TypeScript errors ignored'",
    "prepare:ci": "rimraf ./dist && tsc --skipLibCheck --project tsconfig.ci.json && node scripts/copy-html.js || echo 'TypeScript errors ignored'",
    "start:dev": "ts-node --transpileOnly src/index.ts",
    "start:dev:local": "TEST_ENV=local ts-node --transpileOnly src/index.ts",
    "start:dev-watch": "ts-node-dev --poll --transpile-only --ignore-watch node_modules --files src/index.ts",
    "start:dev-watch:local": "TEST_ENV=local ts-node-dev --poll --transpile-only --ignore-watch node_modules --files src/index.ts",
<<<<<<< HEAD
    "test": "echo \"Running tests for workspace/clients/tests\" && exit 0",
    "test:coverage": "nyc --clean npx playwright test --project=api-tests",
    "test:coverage:report": "nyc report",
    "test:coverage:api": "nyc --clean npx playwright test --project=api-tests",
    "test:coverage:e2e": "nyc --clean npx playwright test --project=e2e-tests",
    "test:coverage:all": "nyc --clean npx playwright test",
    "test:coverage:analyze": "node analyze-coverage.js"
=======
    "test": "echo \"Running tests for workspace/clients/tests\" && exit 0"
>>>>>>> WA-170_MCP
  },
  "author": "Sam Tobia",
  "license": "JSON",
  "dependencies": {
    "@divinci-ai/actions": "file:../../resources/actions",
    "@divinci-ai/models": "file:../../resources/models",
    "@divinci-ai/server-globals": "file:../../resources/server-globals",
    "@divinci-ai/server-tools": "file:../../resources/server-tools",
    "@divinci-ai/server-utils": "file:../../resources/server-utils",
    "@divinci-ai/utils": "file:../../resources/utils",
    "@playwright/test": "^1.44.0",
    "cheerio": "^1.0.0-rc.12",
    "dotenv": "^16.4.5",
    "express": "^4.21.2",
    "express-oauth2-jwt-bearer": "^1.6.0",
    "minimatch": "^10.0.1",
    "playwright": "^1.44.0",
    "tap": "^19.2.2",
    "tap-spec": "^5.0.0",
    "ts-node": "^10.9.2"
  },
  "devDependencies": {
    "@types/cheerio": "^0.22.35",
    "@types/express": "^4.17.21",
    "@types/node": "^22.5.2",
    "@types/pg": "^8.11.8",
    "rimraf": "^6.0.1",
    "ts-node-dev": "^2.0.0",
    "typescript": "^5.8.3"
  },
  "engines": {
    "node": ">=20",
    "pnpm": ">=9"
  },
  "packageManager": "pnpm@10.4.0+sha512.6b849d0787d97f8f4e1f03a9b8ff8f038e79e153d6f11ae539ae7c435ff9e796df6a862c991502695c7f9e8fac8aeafc1ac5a8dab47e36148d183832d886dd52",
  "volta": {
    "node": "22.13.1",
    "pnpm": "10.2.0"
  }
}
