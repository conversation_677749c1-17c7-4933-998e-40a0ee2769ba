/**
 * Download files from Dropbox to R2 storage
 */

// WorkflowStep type (would be imported from cloudflare:workers in real deployment)
type WorkflowStep = any;
import { 
  StepContext, 
  DownloadFilesResult,
  DropboxSyncFile,
  FileDownloadResult,
  DropboxFileProcessingStatus
} from "./types";

export async function downloadFiles(
  step: WorkflowStep,
  context: StepContext,
  sourceFiles: DropboxSyncFile[]
): Promise<DownloadFilesResult> {
  return await step.do("downloadFiles", async () => {
    console.log("📥 Starting file downloads from Dropbox", {
      totalFiles: sourceFiles.length,
      totalBytes: sourceFiles.reduce((sum, f) => sum + f.fileSize, 0)
    });

    const downloadedFiles: (FileDownloadResult & { 
      dropboxFileId: string;
      originalFile: DropboxSyncFile;
    })[] = [];
    
    const failedDownloads: {
      dropboxFileId: string;
      fileName: string;
      error: string;
    }[] = [];

    let totalBytes = 0;

    // Process files in batches to avoid overwhelming the system
    const batchSize = 3; // Process 3 files at a time
    for (let i = 0; i < sourceFiles.length; i += batchSize) {
      const batch = sourceFiles.slice(i, i + batchSize);
      
      console.log(`📥 Processing download batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(sourceFiles.length / batchSize)}`);

      // Process batch in parallel
      const batchPromises = batch.map(async (file) => {
        try {
          console.log(`📥 Downloading file: ${file.fileName}`, {
            dropboxPath: file.dropboxPath,
            fileSize: file.fileSize
          });

          // Update job status to downloading
          await updateFileStatus(context, file.dropboxFileId, DropboxFileProcessingStatus.DOWNLOADING);

          // Download file from Dropbox
          const downloadResult = await downloadFromDropbox(context, file);
          
          // Upload to R2
          const r2Result = await uploadToR2(context, downloadResult, file);

          downloadedFiles.push({
            ...r2Result,
            dropboxFileId: file.dropboxFileId,
            originalFile: file
          });

          totalBytes += file.fileSize;

          console.log(`✅ Successfully downloaded: ${file.fileName}`, {
            objectKey: r2Result.objectKey,
            fileSize: r2Result.fileSize
          });

        } catch (error) {
          console.error(`❌ Failed to download file: ${file.fileName}`, error);
          
          failedDownloads.push({
            dropboxFileId: file.dropboxFileId,
            fileName: file.fileName,
            error: error instanceof Error ? error.message : String(error)
          });

          // Update file status to failed
          await updateFileStatus(
            context, 
            file.dropboxFileId, 
            DropboxFileProcessingStatus.FAILED,
            error instanceof Error ? error.message : String(error)
          );
        }
      });

      // Wait for batch to complete
      await Promise.allSettled(batchPromises);
    }

    console.log("📥 File downloads completed", {
      successful: downloadedFiles.length,
      failed: failedDownloads.length,
      totalBytes
    });

    return {
      downloadedFiles,
      totalBytes,
      failedDownloads
    };
  });
}

/**
 * Download file from Dropbox using access token
 */
async function downloadFromDropbox(
  context: StepContext,
  file: DropboxSyncFile
): Promise<{ content: ArrayBuffer; metadata: any }> {
  const response = await fetch("https://content.dropboxapi.com/2/files/download", {
    method: "POST",
    headers: {
      "Authorization": `Bearer ${context.accessToken}`,
      "Dropbox-API-Arg": JSON.stringify({
        path: file.dropboxPath
      })
    }
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Dropbox download failed: ${response.status} ${errorText}`);
  }

  const content = await response.arrayBuffer();
  
  // Parse metadata from response header
  const metadataHeader = response.headers.get("Dropbox-API-Result");
  const metadata = metadataHeader ? JSON.parse(metadataHeader) : {};

  return { content, metadata };
}

/**
 * Upload file to R2 storage
 */
async function uploadToR2(
  context: StepContext,
  downloadResult: { content: ArrayBuffer; metadata: any },
  file: DropboxSyncFile
): Promise<FileDownloadResult> {
  // Generate object key for R2
  const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
  const objectKey = `dropbox-sync/${context.whitelabelId}/${context.jobId}/${timestamp}-${file.fileName}`;

  // Upload to R2
  await context.env.R2.put(objectKey, downloadResult.content, {
    httpMetadata: {
      contentType: file.mimeType || "application/octet-stream"
    },
    customMetadata: {
      dropboxFileId: file.dropboxFileId,
      dropboxPath: file.dropboxPath,
      originalFileName: file.fileName,
      jobId: context.jobId,
      whitelabelId: context.whitelabelId,
      uploadedAt: new Date().toISOString()
    }
  });

  // Generate R2 URL
  const r2Url = `${context.env.R2_BUCKET_URL}/${objectKey}`;

  return {
    objectKey,
    fileName: file.fileName,
    fileSize: file.fileSize,
    mimeType: file.mimeType || "application/octet-stream",
    downloadedAt: new Date(),
    r2Url
  };
}

/**
 * Update file processing status via API
 */
async function updateFileStatus(
  context: StepContext,
  dropboxFileId: string,
  status: DropboxFileProcessingStatus,
  errorMessage?: string
): Promise<void> {
  try {
    const response = await fetch(`${context.env.API_HOST}/ux/whitelabel/${context.whitelabelId}/dropbox/sync/update-file-status`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${context.auth0Token}`,
        "cloudflare-worker-x-dev-auth": context.env.CLOUDFLARE_WORKER_X_AUTH_DEV || ""
      },
      body: JSON.stringify({
        jobId: context.jobId,
        dropboxFileId,
        status,
        errorMessage,
        timestamp: new Date().toISOString()
      })
    });

    if (!response.ok) {
      console.warn(`Failed to update file status: ${response.status}`);
    }
  } catch (error) {
    console.warn("Failed to update file status:", error);
    // Don't throw - this is a non-critical operation
  }
}
