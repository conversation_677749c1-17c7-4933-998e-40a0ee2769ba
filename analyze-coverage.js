/**
 * Simple script to analyze coverage data
 */

const fs = require('fs');
const path = require('path');

// API test mappings from the original file
const API_TEST_MAPPINGS = [
  {
    folder: "workspace/servers/public-api/src/routes/ai-chat",
    testSuites: ["AI Chats"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/whitelabel",
    testSuites: ["White Label"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/finetune",
    testSuites: ["Fine Tune"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/moderation",
    testSuites: ["Prompt Moderation"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/thread",
    testSuites: ["Thread Prefix"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/message",
    testSuites: ["Message Prefix"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/rag",
    testSuites: ["RAG"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/workspace",
    testSuites: ["Workspace Release"]
  }
];

/**
 * Analyze coverage data and identify gaps
 */
function analyzeCoverage() {
  try {
    // Read coverage data
    const coverageFile = path.join(process.cwd(), '.nyc_output', 'coverage.json');
    if (!fs.existsSync(coverageFile)) {
      console.error('❌ Coverage data not found. Run tests with coverage first.');
      return;
    }

    const coverageData = JSON.parse(fs.readFileSync(coverageFile, 'utf8'));
    
    // Get API routes from the test mapping
    const apiRoutes = API_TEST_MAPPINGS
      .filter(mapping => mapping.folder.includes('workspace/servers/public-api/src/routes'))
      .map(mapping => mapping.folder.split('/').pop());
    
    console.log('📊 API Coverage Analysis');
    console.log('=======================');
    console.log(`Found ${apiRoutes.length} API route categories: ${apiRoutes.join(', ')}`);
    
    // Analyze coverage for each API route
    const routeCoverage = {};
    
    for (const [filePath, coverage] of Object.entries(coverageData)) {
      // Only analyze API route files
      if (!filePath.includes('workspace/servers/public-api/src/routes')) {
        continue;
      }
      
      // Extract route category from path
      const pathParts = filePath.split('/');
      const routeIndex = pathParts.indexOf('routes');
      if (routeIndex === -1 || routeIndex + 1 >= pathParts.length) {
        continue;
      }
      
      const routeCategory = pathParts[routeIndex + 1];
      
      // Calculate coverage percentages
      const statements = Object.values(coverage.s).filter(v => v > 0).length / Object.keys(coverage.s).length * 100;
      const functions = Object.values(coverage.f).filter(v => v > 0).length / Object.keys(coverage.f).length * 100;
      
      // Calculate branch coverage
      let branchTotal = 0;
      let branchCovered = 0;
      for (const branch of Object.values(coverage.b)) {
        branchTotal += branch.length;
        branchCovered += branch.filter(v => v > 0).length;
      }
      const branches = branchTotal > 0 ? (branchCovered / branchTotal * 100) : 100;
      
      // Use statement coverage for lines as well (simplified)
      const lines = statements;
      
      // Create endpoint entry
      const endpoint = {
        path: filePath,
        method: path.basename(filePath, '.ts'),
        coverage: {
          statements,
          branches,
          functions,
          lines
        }
      };
      
      // Add to route coverage
      if (!routeCoverage[routeCategory]) {
        routeCoverage[routeCategory] = [];
      }
      routeCoverage[routeCategory].push(endpoint);
    }
    
    // Print coverage summary by route category
    for (const [category, endpoints] of Object.entries(routeCoverage)) {
      console.log(`\n📁 ${category}`);
      console.log('------------------');
      
      // Calculate average coverage for the category
      const avgStatements = endpoints.reduce((sum, ep) => sum + ep.coverage.statements, 0) / endpoints.length;
      const avgBranches = endpoints.reduce((sum, ep) => sum + ep.coverage.branches, 0) / endpoints.length;
      const avgFunctions = endpoints.reduce((sum, ep) => sum + ep.coverage.functions, 0) / endpoints.length;
      const avgLines = endpoints.reduce((sum, ep) => sum + ep.coverage.lines, 0) / endpoints.length;
      
      console.log(`Average Coverage: ${avgLines.toFixed(2)}% lines, ${avgStatements.toFixed(2)}% statements, ${avgBranches.toFixed(2)}% branches, ${avgFunctions.toFixed(2)}% functions`);
      
      // Sort endpoints by coverage (lowest first)
      endpoints.sort((a, b) => a.coverage.lines - b.coverage.lines);
      
      // Print endpoints with low coverage
      const lowCoverageEndpoints = endpoints.filter(ep => ep.coverage.lines < 80);
      if (lowCoverageEndpoints.length > 0) {
        console.log('\n⚠️ Low Coverage Endpoints:');
        for (const endpoint of lowCoverageEndpoints) {
          console.log(`  - ${endpoint.method}: ${endpoint.coverage.lines.toFixed(2)}% lines, ${endpoint.coverage.functions.toFixed(2)}% functions`);
        }
      }
      
      // Print endpoints with good coverage
      const goodCoverageEndpoints = endpoints.filter(ep => ep.coverage.lines >= 80);
      if (goodCoverageEndpoints.length > 0) {
        console.log('\n✅ Good Coverage Endpoints:');
        for (const endpoint of goodCoverageEndpoints) {
          console.log(`  - ${endpoint.method}: ${endpoint.coverage.lines.toFixed(2)}% lines, ${endpoint.coverage.functions.toFixed(2)}% functions`);
        }
      }
    }
    
    // Check for API routes without any coverage
    const coveredRoutes = Object.keys(routeCoverage);
    const uncoveredRoutes = apiRoutes.filter(route => !coveredRoutes.includes(route));
    
    if (uncoveredRoutes.length > 0) {
      console.log('\n❌ API Routes Without Coverage:');
      for (const route of uncoveredRoutes) {
        console.log(`  - ${route}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Error analyzing coverage:', error);
  }
}

// Run the analyzer
analyzeCoverage();
