#!/usr/bin/env node

/**
 * Test Commit Mapping (Simplified)
 *
 * This script tests the commit mapping functionality without running the actual tests.
 * It verifies that folder changes correctly map to the expected test suites.
 */

const path = require('path');

// Import the mapping function (we'll use require with .js extension)
const mappingPath = path.join(__dirname, 'src', 'api-test-mapping.ts');

// Since we can't directly require TypeScript, let's implement the function here
const API_TEST_MAPPINGS = [
  {
    folder: "workspace/servers/public-api/src/routes/ai-chat",
    testSuites: ["AI Chats"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/whitelabel",
    testSuites: ["White Label"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/finetune",
    testSuites: ["Fine Tune"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/moderation",
    testSuites: ["Prompt Moderation"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/thread",
    testSuites: ["Thread Prefix"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/message",
    testSuites: ["Message Prefix"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/rag",
    testSuites: ["RAG"]
  },
  {
    folder: "workspace/servers/public-api/src/routes/workspace",
    testSuites: ["Workspace Release"]
  },
  {
    folder: "workspace/resources/actions/src/workspace/data-source/audio",
    testSuites: ["Audio Transcript"]
  },
  {
    folder: "workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript",
    testSuites: ["Audio Transcript"]
  },
  {
    folder: "workspace/clients/tests",
    testSuites: ["AI Chats", "White Label", "Fine Tune", "Prompt Moderation", "Thread Prefix", "Message Prefix", "RAG", "Workspace Release", "Audio Transcript"]
  }
];

function getApiTestSuitesForChangedFolders(changedFolders) {
  const testSuites = new Set();

  // Clean up folder paths
  const cleanedFolders = changedFolders.map(folder => {
    let cleaned = folder.trim();
    if ((cleaned.startsWith("'") && cleaned.endsWith("'")) ||
        (cleaned.startsWith('"') && cleaned.endsWith('"'))) {
      cleaned = cleaned.substring(1, cleaned.length - 1);
    }
    return cleaned;
  }).filter(folder => folder.length > 0);

  console.log('🔍 Analyzing changed folders for API tests:', cleanedFolders);

  cleanedFolders.forEach(folder => {
    API_TEST_MAPPINGS.forEach(mapping => {
      // More precise matching: either exact match or folder is a subfolder of mapping.folder
      // This prevents parent folders from matching all their children
      if (folder === mapping.folder ||
          folder.startsWith(mapping.folder + "/")) {
        console.log(`📍 Folder '${folder}' matches API test mapping '${mapping.folder}' -> Adding suites:`, mapping.testSuites);
        mapping.testSuites.forEach(suite => testSuites.add(suite));
      }
    });
  });

  const result = Array.from(testSuites);
  console.log('✅ API test suites to run:', result);
  return result;
}

// Test scenarios
const commitScenarios = [
  {
    name: 'AI Chat API Changes',
    description: 'Developer modifies AI Chat API routes and handlers',
    changedFiles: [
      'workspace/servers/public-api/src/routes/ai-chat/router/chat.ts',
      'workspace/servers/public-api/src/routes/ai-chat/handlers/create.ts',
      'workspace/servers/public-api/src/routes/ai-chat/handlers/update.ts',
      'workspace/servers/public-api/src/routes/ai-chat/middleware/auth.ts'
    ],
    expectedTestSuites: ['AI Chats']
  },
  {
    name: 'White Label API Changes',
    description: 'Developer modifies White Label API functionality',
    changedFiles: [
      'workspace/servers/public-api/src/routes/whitelabel/router/index.ts',
      'workspace/servers/public-api/src/routes/whitelabel/handlers/create.ts'
    ],
    expectedTestSuites: ['White Label']
  },
  {
    name: 'Multiple API Changes',
    description: 'Developer modifies multiple API areas in one commit',
    changedFiles: [
      'workspace/servers/public-api/src/routes/ai-chat/router/chat.ts',
      'workspace/servers/public-api/src/routes/whitelabel/handlers/create.ts',
      'workspace/servers/public-api/src/routes/rag/vector-search.ts',
      'workspace/servers/public-api/src/routes/finetune/training.ts'
    ],
    expectedTestSuites: ['AI Chats', 'White Label', 'RAG', 'Fine Tune']
  },
  {
    name: 'Test Infrastructure Changes',
    description: 'Developer modifies test infrastructure',
    changedFiles: [
      'workspace/clients/tests/src/auth/auth-utils.ts',
      'workspace/clients/tests/src/api/api-client.ts',
      'workspace/clients/tests/src/config/config.ts'
    ],
    expectedTestSuites: ['AI Chats', 'White Label', 'Fine Tune', 'Prompt Moderation', 'Thread Prefix', 'Message Prefix', 'RAG', 'Workspace Release', 'Audio Transcript']
  },
  {
    name: 'Frontend Only Changes',
    description: 'Developer modifies only frontend code',
    changedFiles: [
      'workspace/clients/web/src/components/Chat/ChatInput.tsx',
      'workspace/clients/web/src/pages/Dashboard/Dashboard.tsx',
      'workspace/clients/web/src/styles/global.css'
    ],
    expectedTestSuites: []
  },
  {
    name: 'Audio Transcript Changes',
    description: 'Developer modifies audio transcript functionality',
    changedFiles: [
      'workspace/resources/actions/src/workspace/data-source/audio/transcribe.ts',
      'workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/AudioUpload.tsx'
    ],
    expectedTestSuites: ['Audio Transcript']
  }
];

/**
 * Extract folders from changed files
 */
function extractFoldersFromFiles(changedFiles) {
  const folders = new Set();

  changedFiles.forEach(file => {
    const parts = file.split('/');

    // Build folder paths from most specific to least specific
    for (let i = parts.length - 1; i >= 1; i--) {
      const folder = parts.slice(0, i).join('/');
      folders.add(folder);
    }
  });

  return Array.from(folders);
}

/**
 * Simulate a commit scenario
 */
function simulateCommitScenario(scenario) {
  console.log(`\n${'='.repeat(80)}`);
  console.log(`📝 Scenario: ${scenario.name}`);
  console.log(`📄 Description: ${scenario.description}`);
  console.log(`📁 Changed files (${scenario.changedFiles.length}):`);
  scenario.changedFiles.forEach(file => console.log(`   - ${file}`));

  // Extract folders from changed files
  const allFolders = extractFoldersFromFiles(scenario.changedFiles);
  console.log(`\n📂 Extracted folders (${allFolders.length}):`);
  allFolders.forEach(folder => console.log(`   - ${folder}`));

  // Get test suites using the mapping function
  const testSuites = getApiTestSuitesForChangedFolders(allFolders);
  console.log(`\n🎯 Expected test suites: ${scenario.expectedTestSuites.join(', ') || 'None'}`);
  console.log(`🎯 Actual test suites: ${testSuites.join(', ') || 'None'}`);

  // Verify mapping is correct
  const mappingCorrect = JSON.stringify(testSuites.sort()) === JSON.stringify(scenario.expectedTestSuites.sort());
  if (mappingCorrect) {
    console.log('✅ Test mapping is correct');
  } else {
    console.log('❌ Test mapping is incorrect');
    console.log(`   Expected: [${scenario.expectedTestSuites.sort().join(', ')}]`);
    console.log(`   Actual: [${testSuites.sort().join(', ')}]`);
  }

  return {
    success: mappingCorrect,
    testSuites,
    expected: scenario.expectedTestSuites
  };
}

/**
 * Main execution
 */
function main() {
  console.log('🚀 Testing Commit to Test Suite Mapping');
  console.log('=' .repeat(80));

  const results = [];

  // Run each scenario
  for (const scenario of commitScenarios) {
    const result = simulateCommitScenario(scenario);
    results.push({
      scenario: scenario.name,
      ...result
    });
  }

  // Summary
  console.log(`\n${'='.repeat(80)}`);
  console.log('📊 SUMMARY');
  console.log('=' .repeat(80));

  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;

  console.log(`\n✅ Successful mappings: ${successCount}/${totalCount}`);
  console.log(`❌ Failed mappings: ${totalCount - successCount}/${totalCount}`);

  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    console.log(`   ${status} ${result.scenario}`);
    if (!result.success) {
      console.log(`      Expected: [${result.expected.sort().join(', ')}]`);
      console.log(`      Actual: [${result.testSuites.sort().join(', ')}]`);
    }
  });

  // Overall result
  if (successCount === totalCount) {
    console.log('\n🎉 All commit mapping scenarios passed! Test mapping is working correctly.');
    return true;
  } else {
    console.log('\n💥 Some commit mapping scenarios failed. Please check the test mapping configuration.');
    return false;
  }
}

// Run if called directly
if (require.main === module) {
  const success = main();
  process.exit(success ? 0 : 1);
}

module.exports = {
  simulateCommitScenario,
  extractFoldersFromFiles,
  getApiTestSuitesForChangedFolders
};
