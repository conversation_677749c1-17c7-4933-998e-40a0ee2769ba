import { Page } from '@playwright/test';

/**
 * Verify that the user is logged in
 * This is a simple check that can be used at the start of tests
 * The actual login is handled by the storage state in the Playwright config
 * @param page The Playwright page
 */
export async function verifyLoggedIn(page: Page): Promise<void> {
  console.log('Verifying user is logged in');

  // Navigate to the home page if not already there
  if (!page.url().includes(process.env.WEB_URL || 'http://localhost:8080')) {
    console.log('Navigating to home page');
    await page.goto(process.env.WEB_URL || 'http://localhost:8080');
    await page.waitForLoadState('networkidle');
  }

  // Check if the login button is visible
  const loginButton = page.locator('button.button.is-text:has-text("Login")');
  const loginButtonCount = await loginButton.count();

  if (loginButtonCount > 0) {
    console.log('Login button detected, user is not logged in. Performing login...');

    // Click the login button
    await loginButton.click();

    // Wait for the login form to appear
    await page.waitForSelector('input[name="username"]');

    // Fill in login credentials
    console.log('Filling in login credentials');
    await page.fill('input[name="username"]', '<EMAIL>');
    await page.fill('input[name="password"]', '(abc123ABC)');

    // Click the login button
    console.log('Clicking submit button');
    await page.click('button[type="submit"]');

    // Wait for login to complete
    console.log('Waiting for login to complete');
    await page.waitForTimeout(3000);
    await page.waitForLoadState('networkidle');

    // Navigate back to home page
    await page.goto(process.env.WEB_URL || 'http://localhost:8080');
    await page.waitForLoadState('networkidle');

    console.log('Login completed');
  } else {
    // Check if we're logged in - look for any user menu or profile indicator
    console.log('Login button not found, checking for organization menu or user menu');
    try {
      const organizationMenu = page.locator('a.navbar-link[href="/user-group"]:has-text("Organization"), .user-menu, .profile-menu, .navbar-item.has-dropdown').first();
      await organizationMenu.waitFor({ state: 'visible', timeout: 5000 });
      console.log('Organization menu detected, login confirmed');
    } catch (error) {
      console.log('Could not detect organization menu, assuming logged in based on absence of login button');
    }
  }
}

/**
 * Legacy login function for backward compatibility
 * This function now just verifies that the user is logged in
 * The actual login is handled by the storage state in the Playwright config
 * @param page The Playwright page
 */
export async function login(page: Page): Promise<void> {
  console.log('Using storage state for authentication instead of actual login');
  await verifyLoggedIn(page);
}
<<<<<<< HEAD
=======

/**
 * Login and navigate to a whitelabel, returning the whitelabel ID
 * @param page The Playwright page
 * @returns Object containing the whitelabel ID
 */
export async function loginAndNavigateToWhitelabel(page: Page): Promise<{ whitelabelId: string }> {
  console.log('Logging in and navigating to whitelabel');

  // First ensure we're logged in
  await verifyLoggedIn(page);

  // Navigate to the whitelabel page using the proven approach
  console.log('Navigating to whitelabel page');
  await page.goto(`${process.env.WEB_URL || 'http://localhost:8080'}/white-label`);
  await page.waitForLoadState('networkidle');

  // Check if we're on the whitelabel list page or the create whitelabel page
  const createHeader = page.locator('h1:has-text("Create A New WhiteLabel Language Model")');
  const listHeader = page.locator('h1:has-text("Whitelabel")');

  // Wait for either header to be visible
  await Promise.race([
    createHeader.waitFor({ state: 'visible', timeout: 5000 }).catch(() => {}),
    listHeader.waitFor({ state: 'visible', timeout: 5000 }).catch(() => {})
  ]);

  // Check if we're already on the create whitelabel page
  if (await createHeader.count() > 0) {
    console.log('Already on create whitelabel page, proceeding with creation');
  } else {
    // Check if there are any existing whitelabels
    const existingWhitelabels = await page.locator('.whitelabel-ai-card').count();
    console.log(`Found ${existingWhitelabels} existing whitelabels`);

    if (existingWhitelabels > 0) {
      // Use the first existing whitelabel
      console.log('Using existing whitelabel');
      const whitelabelHref = await page.locator('.whitelabel-ai-card .clickable-text').first().getAttribute('href');
      if (!whitelabelHref) {
        throw new Error('Could not find whitelabel href');
      }
      const whitelabelId = whitelabelHref.split('/').pop() || '';
      if (!whitelabelId) {
        throw new Error('Could not extract whitelabel ID from href');
      }
      console.log(`Using existing whitelabel ID: ${whitelabelId}`);
      return { whitelabelId };
    } else {
      // No whitelabels found, need to create one
      console.log('No whitelabels found. Creating a new whitelabel for testing.');

      // Take a screenshot to see what's on the page
      await page.screenshot({ path: 'whitelabel-page.png' });

      // Try different selectors for the Create Whitelabel button
      const createWhitelabelButton = page.locator('text=Create Whitelabel, button:has-text("Create Whitelabel"), .create-whitelabel-button, button.is-primary');

      if (await createWhitelabelButton.count() > 0) {
        console.log('Found Create Whitelabel button, clicking it');
        await createWhitelabelButton.first().click();
      } else {
        console.log('Create Whitelabel button not found, trying to navigate directly to create form');
        await page.goto(`${process.env.WEB_URL || 'http://localhost:8080'}/white-label/create`);
      }

      // Wait for the create whitelabel form to load
      await page.waitForLoadState('networkidle');

      // Wait for the create header to be visible
      await createHeader.waitFor({ state: 'visible', timeout: 5000 }).catch(() => {
        console.log('Warning: Create whitelabel header not found after clicking Create Whitelabel button');
      });
    }
  }

  // If we reach here, we need to create a whitelabel
  // Generate a unique name for the whitelabel
  const timestamp = new Date().getTime();
  const whitelabelName = `Test Whitelabel ${timestamp}`;

  // Fill out the whitelabel form
  console.log('Filling out the whitelabel form');

  // Fill in the title field
  console.log('Filling in title field');
  await page.fill('input.input[required]', whitelabelName);

  // Fill in the description field
  console.log('Filling in description field');
  await page.fill('textarea#description', 'This is a test whitelabel created for E2E testing');

  // Check the terms and conditions checkbox
  await page.check('input[type="checkbox"]');

  // Take a screenshot of the filled form
  await page.screenshot({ path: 'filled-whitelabel-form.png' });
  console.log('Saved screenshot of filled whitelabel form');

  // Click the Create AI button
  console.log('Clicking Create AI button');
  await page.click('button.create-whitelabel-button');

  // Wait for the whitelabel to be created and redirected
  await page.waitForLoadState('networkidle');

  // Wait a moment for the server to process the whitelabel creation
  console.log('Waiting briefly for server to process whitelabel creation');
  await page.waitForTimeout(3000);

  // Navigate back to the whitelabel page
  console.log('Navigating back to whitelabel list page');
  await page.goto(`${process.env.WEB_URL || 'http://localhost:8080'}/white-label`);
  await page.waitForLoadState('networkidle');

  // Get the first whitelabel ID
  const whitelabelHref = await page.locator('.whitelabel-ai-card .clickable-text').first().getAttribute('href');
  if (!whitelabelHref) {
    throw new Error('Could not find whitelabel href after creation');
  }
  const whitelabelId = whitelabelHref.split('/').pop() || '';
  if (!whitelabelId) {
    throw new Error('Could not extract whitelabel ID from href after creation');
  }

  console.log(`Created new whitelabel with ID: ${whitelabelId}`);
  return { whitelabelId };
}
>>>>>>> WA-170_MCP
