import { WorkflowStep } from "cloudflare:workers";
import { StepContext, R2Validation<PERSON><PERSON><PERSON>, FileRecord, FileRecordPayload, InitialSetup, VectorizeConfig } from "./types";

/**
 * Creates or gets file record
 *
 * IMPORTANT: This function has been refactored to always create a new file record
 * without trying to find similar file records. This ensures consistent behavior
 * between local and production environments.
 */
export async function ensureFileRecord(
  step: WorkflowStep,
  context: StepContext,
  initialSetup: InitialSetup,
  r2Result: R2ValidationResult,
  vectorizeConfig: VectorizeConfig
): Promise<FileRecord>{
  return await step.do("ensureFileRecord", async (): Promise<FileRecord>=>{
    console.log("🔍 Checking for existing file record:", {
      whitelabelId: vectorizeConfig.whitelabelId,
      objectKey: r2Result.objectKey
    });

    // Check if file record already exists with the exact object key
    let existingRecord = await getFileRecord(
      context,
      vectorizeConfig.whitelabelId,
      r2Result.objectKey,
      vectorizeConfig.auth0Token
    );

    console.log("📝 Existing record check result:", {
      found: !!existingRecord,
      recordDetails: existingRecord ? {
        _id: existingRecord._id,
        fileKey: existingRecord.fileKey,
        status: existingRecord.status
      } : "none"
    });

    // If file record exists, return it
    if(existingRecord) {
      // Ensure existingRecord._id is a string in the proper MongoDB ObjectId format
      let recordId = existingRecord._id;

      // Check if the ID is an object and needs to be converted to a string
      if (typeof recordId === 'object' && recordId !== null) {
        if (recordId.toString && typeof recordId.toString === 'function') {
          // Handle MongoDB ObjectId objects
          recordId = recordId.toString();
          console.log(`✅ [FILE RECORD] Converted ObjectId to string: ${recordId}`);
        } else if (recordId._id && typeof recordId._id === 'string') {
          // Handle nested _id objects
          recordId = recordId._id;
          console.log(`✅ [FILE RECORD] Extracted nested _id: ${recordId}`);
        }
      }

      // Check if the ID is a valid MongoDB ObjectId format
      const isValidObjectId = typeof recordId === 'string' && /^[0-9a-fA-F]{24}$/.test(recordId);
      if (!isValidObjectId) {
        console.warn(`⚠️ [FILE RECORD] Invalid ObjectId format detected: ${recordId}`);
        // Try to find a valid ObjectId in the string
        if (typeof recordId === 'string' && recordId.match(/[0-9a-fA-F]{24}/)) {
          recordId = recordId.match(/([0-9a-fA-F]{24})/)[1];
          console.log(`✅ [FILE RECORD] Extracted valid ObjectId from string: ${recordId}`);
        }
      }

      // Create a record object with the expected structure
      const record = {
        success: true,
        data: {
          _id: recordId,
          fileKey: existingRecord.fileKey,
          status: existingRecord.status
        },
        status: existingRecord.status
      } as FileRecord;

      console.log("📄 Returning existing file record:", {
        _id: record.data._id,
        objectKey: record.data.fileKey,
        status: record.status
      });

      return record;
    }

    // Always use the original object key for consistency
    const objectKey = r2Result.objectKey;

    const payload: FileRecordPayload = {
      bucket: "default",
      objectKey: objectKey,
      originalName: r2Result.fileName,
      chunkingTool: initialSetup.fileInfo.processor,
      title: initialSetup.fileInfo.title || r2Result.fileName,
      description: initialSetup.fileInfo.description
    };

    console.log("📤 Creating new file record with payload:", {
      ...payload,
      objectKey: payload.objectKey,
      objectKeyLength: payload.objectKey.length,
      fileName: payload.originalName
    });

    const fileRecord = await createFileRecord(
      context,
      vectorizeConfig.whitelabelId,
      payload,
      vectorizeConfig.auth0Token
    );

    console.log("✅ File record creation response:", {
      success: !!fileRecord?.data?._id,
      recordDetails: fileRecord?.data ? {
        _id: fileRecord.data._id,
        fileKey: fileRecord.data.fileKey,
        status: fileRecord.data.status
      } : "none"
    });

    if(!fileRecord?.data?._id) {
      throw new Error("Failed to create file record");
    }

    // Update fileInfo with the file ID from the created record
    initialSetup.fileInfo.fileId = fileRecord.data._id;

    // Validate that the fileId is a valid MongoDB ObjectId
    console.log(`🔍 [FILE RECORD] Validating fileId format: ${initialSetup.fileInfo.fileId}`);
    const isValidObjectId = typeof initialSetup.fileInfo.fileId === 'string' &&
                           /^[0-9a-fA-F]{24}$/.test(initialSetup.fileInfo.fileId);

    if (!isValidObjectId) {
      console.warn(`⚠️ [FILE RECORD] Invalid ObjectId format detected: ${initialSetup.fileInfo.fileId}`);

      // If the ID is in an object with an "_id" field, extract that
      if (typeof fileRecord.data._id === 'object' && fileRecord.data._id !== null) {
        if (fileRecord.data._id.toString && typeof fileRecord.data._id.toString === 'function') {
          initialSetup.fileInfo.fileId = fileRecord.data._id.toString();
          console.log(`✅ [FILE RECORD] Converted ObjectId to string: ${initialSetup.fileInfo.fileId}`);
        } else if (fileRecord.data._id._id && typeof fileRecord.data._id._id === 'string') {
          initialSetup.fileInfo.fileId = fileRecord.data._id._id;
          console.log(`✅ [FILE RECORD] Extracted nested _id: ${initialSetup.fileInfo.fileId}`);
        }
      }
    }

    console.log("✅ File record created and assigned:", {
      fileId: initialSetup.fileInfo.fileId,
      objectKey: r2Result.objectKey,
      status: "file to chunks",
    });

    return fileRecord;
  });
}

/**
 * Gets file record
 */
async function getFileRecord(
  context: StepContext,
  whitelabelId: string,
  objectKey: string,
  auth0Token: string
): Promise<any>{
  try {
    const url = `${context.env.API_HOST}/white-label/${whitelabelId}/rag-vector/files/get-record?objectKey=${encodeURIComponent(objectKey)}`;
    console.log("🔍 Attempting to get file record from:", url);

    const response = await makeRequest(context, url, "GET", undefined, auth0Token);

    console.log("📡 Get file record response status:", response.status);

    if(!response.ok) {
      console.warn("❌ Failed to get file record. Status:", response.status);
      let errorText: string = "";
      try {
        errorText = await response.text();
        console.warn("Error response text:", errorText);

        // Try to parse the error as JSON for more details
        try {
          const errorJson = JSON.parse(errorText);
          console.warn("Error response JSON:", errorJson);
        }catch(parseError) {
          console.warn("Error response is not valid JSON");
        }
      }catch(textError) {
        console.warn("Could not get error text:", textError);
      }

      // Return null to indicate no record was found
      return null;
    }

    const record = await response.json() as { status: string, data?: any };
    console.log("✅ Got file record:", record);

    return record?.data || null;
  }catch(error) {
    console.error("💥 Error in getFileRecord:", error);
    return null;
  }
}

/**
 * Creates file record
 */
async function createFileRecord(
  context: StepContext,
  whitelabelId: string,
  fileData: FileRecordPayload,
  auth0Token: string
): Promise<any>{
  const url = `${context.env.API_HOST}/white-label/${whitelabelId}/rag-vector/files/create-record`;

  console.log("📤 Attempting to create file record:", {
    url,
    whitelabelId,
    objectKey: fileData.objectKey
  });

  try {
    const response = await makeRequest(
      context,
      url,
      "POST",
      {
        bucket: fileData.bucket,
        objectKey: fileData.objectKey,  // Changed from fileKey to objectKey to match API expectations
        originalName: fileData.originalName,
        chunkingTool: fileData.chunkingTool,
        title: fileData.title,
        // Only include description if it's defined
        ...(fileData.description ? { description: fileData.description } : {})
      },
      auth0Token
    );

    if(!response.ok) {
      console.error("❌ Failed to create file record. Status:", response.status);
      let errorText: string = "Unknown error";
      try {
        errorText = await response.text();
        console.error("Error response text:", errorText);

        // Try to parse the error as JSON for more details
        try {
          const errorJson = JSON.parse(errorText);
          console.error("Error response JSON:", errorJson);
        }catch(parseError) {
          console.error("Error response is not valid JSON");
        }
      }catch(textError) {
        console.error("Could not get error text:", textError);
      }

      throw new Error(`Failed to create file record: ${errorText}`);
    }

    const result = await response.json();
    console.log("✅ File record created successfully:", result);
    return result;
  }catch(error) {
    console.error("💥 Error in createFileRecord:", {
      error,
      whitelabelId,
      objectKey: fileData.objectKey
    });
    throw error;
  }
}

/**
 * Makes API request
 */
async function makeRequest(
  context: StepContext,
  url: string,
  method: string,
  body?: any,
  auth0Token?: string
): Promise<Response>{
  const headers: Record<string, string> = {
    "Content-Type": "application/json"
  };

  // Add authorization header if token is provided
  if(auth0Token) {
    headers["Authorization"] = `Bearer ${auth0Token}`;
    console.log(`🔑 Using auth0Token (first 10 chars): ${auth0Token.substring(0, 10)}...`);
  } else {
    console.warn(`⚠️ No auth0Token provided for request to ${url}`);
  }

  // In BARE METAL MODE, we still need to authenticate with the API.
  // In production, this is handled by Cloudflare's authentication system.
<<<<<<< HEAD
  // In local development, we use a special header to simulate this authentication.
  if(context.env.ENVIRONMENT === 'local' || context.env.ENVIRONMENT === 'development') {
=======
  // In local/dev/stage environments, we use a special header to authenticate.
  if(context.env.ENVIRONMENT === 'local' || context.env.ENVIRONMENT === 'development' || context.env.ENVIRONMENT === 'stage') {
>>>>>>> WA-170_MCP
    // Use the auth token from the environment variable if available
    if (context.env.CLOUDFLARE_WORKER_X_AUTH_DEV) {
      headers["cloudflare-worker-x-dev-auth"] = context.env.CLOUDFLARE_WORKER_X_AUTH_DEV;
      headers["x-worker-local-dev"] = "true";
      console.log(`🔑 Added Cloudflare Worker dev auth headers from environment variable`);
    } else {
      console.warn(`⚠️ No CLOUDFLARE_WORKER_X_AUTH_DEV environment variable found. Authentication may fail.`);
    }
  }

  let bodyToSend: string | undefined;
  if(body) {
    bodyToSend = JSON.stringify(body);
  }

  // Log the request details (with sensitive info redacted)
  console.log(`📡 API Request [${method}] ${url}:`, {
    headers: {
      ...headers,
      "Authorization": headers["Authorization"] ? "Bearer [REDACTED]" : undefined,
      "cloudflare-worker-x-dev-auth": headers["cloudflare-worker-x-dev-auth"] ? "[REDACTED]" : undefined
    },
    body: bodyToSend ? JSON.parse(bodyToSend) : undefined,
    bodyLength: bodyToSend ? bodyToSend.length : 0,
    url: url,
    method: method
  });

  // Add debug logging for the request
  console.log(`🔍 DEBUG - Request details:`, {
    method,
    url,
    hasAuthHeader: !!headers["Authorization"],
    hasDevAuthHeader: !!headers["cloudflare-worker-x-dev-auth"],
    envHasDevAuth: !!context.env.CLOUDFLARE_WORKER_X_AUTH_DEV,
    bodySize: bodyToSend ? bodyToSend.length : 0
  });

  try {
    const response = await fetch(url, {
      method,
      headers,
      body: bodyToSend
    });

    // Log response status and headers
    console.log(`📡 API Response [${method}] ${url}:`, {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries())
    });

    return response;
  } catch (error) {
    console.error(`💥 API Request Error [${method}] ${url}:`, error);
    throw error;
  }
}
