name: divinci-test-workflow

networks:
  test-network:
    driver: bridge

services:
  test-chunks-workflow:
    image: node:22-alpine
    volumes:
      - ../workspace/workers/chunks-workflow:/app
    working_dir: /app
    command: |
      sh -c "npm install --no-save aws-sdk express cors && \
             node -e \"
             const express = require('express');
             const cors = require('cors');
             const AWS = require('aws-sdk');
             const app = express();
             
             app.use(cors());
             app.use(express.json());
             
             // Configure S3 client to connect to MinIO
             const s3 = new AWS.S3({
               endpoint: 'http://test-minio:9000',
               s3ForcePathStyle: true,
               signatureVersion: 'v4',
               accessKeyId: 'hello',
               secretAccessKey: 'divinci!'
             });
             
             // Define test endpoint
             app.get('/health', (req, res) => {
               res.json({ status: 'ok', message: 'Test service running' });
             });
             
             // Define endpoint to test MinIO connection
             app.get('/test-minio', async (req, res) => {
               try {
<<<<<<< HEAD
                 const bucketParams = { Bucket: 'rag-origin-files-local' };
=======
                 const bucketParams = { Bucket: 'rag-files-local' };
>>>>>>> WA-170_MCP
                 const data = await s3.listObjects(bucketParams).promise();
                 res.json({ 
                   success: true, 
                   message: 'MinIO connection successful', 
                   objects: data.Contents.slice(0, 5).map(obj => ({ 
                     key: obj.Key, 
                     size: obj.Size,
                     lastModified: obj.LastModified
                   }))
                 });
               } catch (err) {
                 res.status(500).json({ success: false, error: err.message });
               }
             });
             
             // Define specific endpoint to test ObjectId validation
             app.post('/test-object-id', (req, res) => {
               try {
                 const { fileId } = req.body;
                 
                 if (!fileId) {
                   return res.status(400).json({ success: false, message: 'No fileId provided' });
                 }
                 
                 // Convert to string if it's not already
                 const strFileId = String(fileId);
                 
                 // Check if the fileId is in a valid MongoDB ObjectId format
                 const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(strFileId);
                 let sanitizedId = strFileId;
                 let message = '';
                 
                 if (!isValidObjectId) {
                   message = 'Invalid ObjectId format detected';
                   
                   // If it contains special characters like '/', attempt to extract a valid ID portion
                   if (strFileId.includes('/')) {
                     const parts = strFileId.split('/');
                     // Check each part for a valid ObjectId
                     for (const part of parts) {
                       if (/^[0-9a-fA-F]{24}$/.test(part)) {
                         sanitizedId = part;
                         message = 'Extracted valid ObjectId from path';
                         break;
                       }
                     }
                   }
                   // Also try to extract a valid ObjectId pattern from any string
                   else if (strFileId.match(/[0-9a-fA-F]{24}/)) {
                     const match = strFileId.match(/([0-9a-fA-F]{24})/);
                     if (match && match[1]) {
                       sanitizedId = match[1];
                       message = 'Extracted valid ObjectId from string';
                     }
                   }
                   
                   // Hard-coded fallback as last resort
                   if (!/^[0-9a-fA-F]{24}$/.test(sanitizedId)) {
                     sanitizedId = '682415a03d653676ebe89b06';
                     message = 'Using hard-coded fallback ID';
                   }
                 } else {
                   message = 'Valid ObjectId format detected';
                 }
                 
                 res.json({ 
                   success: true, 
                   original: fileId,
                   sanitized: sanitizedId,
                   isValid: /^[0-9a-fA-F]{24}$/.test(sanitizedId),
                   message
                 });
               } catch (err) {
                 res.status(500).json({ success: false, error: err.message });
               }
             });
             
             // Start server
             const PORT = 3000;
             app.listen(PORT, '0.0.0.0', () => {
               console.log('Test server running on port ' + PORT);
             });
             \""
    ports:
      - "3001:3000"
    networks:
      - test-network
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "localhost:127.0.0.1"
    depends_on:
      - test-minio

  test-minio:
    image: minio/minio
    ports:
      - "9100:9000"
      - "9101:9001"
    networks:
      - test-network
    environment:
      MINIO_ROOT_USER: hello
      MINIO_ROOT_PASSWORD: divinci!
    command: server /data --console-address ":9001"
    volumes:
      - test-minio-data:/data

volumes:
  test-minio-data: