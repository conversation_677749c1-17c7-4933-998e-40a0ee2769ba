import { condenseTarget, WHITE_LABEL_LOCATION, RagVectorFileProps, RagVectorTextChunkProps } from "@divinci-ai/models";
import { WhiteLabelModel, RagVectorFileModel, IRagVectorFileMethods } from "@divinci-ai/server-models";
import { getParam, HTTP_ERRORS } from "@divinci-ai/server-utils";
import { IncomingMessage } from "http";
import { Document } from "mongoose";

type MongooseSelect = Array<string> | Record<string, 0 | 1> | string;
// Define the expected return type for the file with proper typing
type RagVectorFileDocument = Document<unknown, object, RagVectorFileProps> & Omit<RagVectorFileProps & RagVectorTextChunkProps, keyof IRagVectorFileMethods> & IRagVectorFileMethods;

// Simple helper function to pass through document instances or null values
<<<<<<< HEAD
function ensureDocument<T>(doc: T): T {
=======
function ensureDocument<T>(doc: T): T{
>>>>>>> WA-170_MCP
  return doc;
}

export async function ensureValidFile(req: IncomingMessage, select: MongooseSelect = { chunks: 0 }): Promise<{
  whitelabel: Document<unknown, any, any>,
<<<<<<< HEAD
  file: RagVectorFileDocument
}> {
=======
  file: RagVectorFileDocument,
}>{
>>>>>>> WA-170_MCP
  console.log(`🔍 [ENSURE VALID FILE] Request URL: ${req.url}`);
  const whitelabelId = getParam(req, "whitelabelId");
  const fileId = getParam(req, "fileId");
  console.log(`🔍 [ENSURE VALID FILE] whitelabelId: ${whitelabelId}, fileId: ${fileId}`);

  // Validate that fileId is a valid MongoDB ObjectId (24 hex characters)
<<<<<<< HEAD
  if (typeof fileId !== 'string' || !/^[0-9a-fA-F]{24}$/.test(fileId)) {
=======
  if(typeof fileId !== "string" || !/^[0-9a-fA-F]{24}$/.test(fileId)) {
>>>>>>> WA-170_MCP
    console.error(`❌ [ENSURE VALID FILE] Invalid fileId format: ${fileId}`);
    throw HTTP_ERRORS.BAD_FORM;
  }

  const target = condenseTarget({
    ...WHITE_LABEL_LOCATION,
    id: whitelabelId,
  });
  console.log(`🔍 [ENSURE VALID FILE] target: ${JSON.stringify(target)}`);

  console.log(`🔍 [ENSURE VALID FILE] Looking up file in database...`);

  // Create a query for the file that checks both _id and fileId fields
  const fileQuery = {
    target,
    $or: [
      { _id: fileId },
      { fileId: fileId }
    ]
  };

  console.log(`🔍 [ENSURE VALID FILE] Query:`, JSON.stringify(fileQuery));

  // Fallback query in case we need it
  const fallbackQuery = {
    target,
    $or: [
<<<<<<< HEAD
      { originalFilename: { $regex: new RegExp(`${fileId}`, 'i') } },
      { fileKey: { $regex: new RegExp(`${fileId}`, 'i') } },
      { rawFileKey: { $regex: new RegExp(`${fileId}`, 'i') } }
=======
      { originalFilename: { $regex: new RegExp(`${fileId}`, "i") } },
      { fileKey: { $regex: new RegExp(`${fileId}`, "i") } },
      { rawFileKey: { $regex: new RegExp(`${fileId}`, "i") } }
>>>>>>> WA-170_MCP
    ]
  };
  console.log(`🔍 [ENSURE VALID FILE] Fallback query prepared if needed.`);

  // Consume the request body using busboy and log the results for debugging
  const startTime = Date.now();
<<<<<<< HEAD
  let whitelabel = ensureDocument(await WhiteLabelModel.findById(whitelabelId).select("_id"));
  let file: any = ensureDocument(await RagVectorFileModel.findOne(fileQuery).select(select));

  let endTime = Date.now();
  console.log(`🔍 [ENSURE VALID FILE] whitelabel found: ${whitelabel !== null}, file found: ${file !== null} (took ${endTime - startTime}ms)`);

  // Try fallback query if file not found but whitelabel exists
  if (whitelabel !== null && file === null) {
=======
  const whitelabel = ensureDocument(await WhiteLabelModel.findById(whitelabelId).select("_id"));
  let file: any = ensureDocument(await RagVectorFileModel.findOne(fileQuery).select(select));

  const endTime = Date.now();
  console.log(`🔍 [ENSURE VALID FILE] whitelabel found: ${whitelabel !== null}, file found: ${file !== null} (took ${endTime - startTime}ms)`);

  // Try fallback query if file not found but whitelabel exists
  if(whitelabel !== null && file === null) {
>>>>>>> WA-170_MCP
    console.log(`🔍 [ENSURE VALID FILE] File not found with primary query, trying fallback query...`);
    const fallbackStartTime = Date.now();
    // Simple direct query for the fallback case
    file = await RagVectorFileModel.findOne(fallbackQuery).select(select) as unknown as RagVectorFileDocument;
    const fallbackEndTime = Date.now();
    console.log(`🔍 [ENSURE VALID FILE] Fallback query result: file found: ${file !== null} (took ${fallbackEndTime - fallbackStartTime}ms)`);

    // If we found a file with the fallback query, update its fileId field if needed
<<<<<<< HEAD
    if (file !== null && !file.fileId) {
=======
    if(file !== null && !file.fileId) {
>>>>>>> WA-170_MCP
      try {
        console.log(`🔍 [ENSURE VALID FILE] File found with fallback, but missing fileId field. Updating with: ${fileId}`);
        // Update the file record to add the fileId field for future compatibility
        await RagVectorFileModel.findByIdAndUpdate(file._id, { fileId: fileId });
        // Also update the in-memory file object
        file.fileId = fileId;
<<<<<<< HEAD
      } catch (updateError) {
        if (updateError && typeof updateError === "object" && "message" in updateError) {
=======
      }catch(updateError) {
        if(updateError && typeof updateError === "object" && "message" in updateError) {
>>>>>>> WA-170_MCP
          console.warn(`⚠️ [ENSURE VALID FILE] Error updating fileId: ${(updateError as any).message}`);
        } else {
          console.warn(`⚠️ [ENSURE VALID FILE] Error updating fileId:`, updateError);
        }
      }
    }
  }

  // As a last resort, try to find any recently created file for this whitelabel
<<<<<<< HEAD
  if (whitelabel !== null && file === null) {
=======
  if(whitelabel !== null && file === null) {
>>>>>>> WA-170_MCP
    console.log(`🔍 [ENSURE VALID FILE] File not found with fallback query, trying recency search...`);
    const recencyStartTime = Date.now();
    // Look for the most recently uploaded file for this whitelabel
    file = await RagVectorFileModel.findOne({ target })
      .sort({ uploadTimestamp: -1 })
      .select(select) as unknown as RagVectorFileDocument;

    const recencyEndTime = Date.now();
    console.log(`🔍 [ENSURE VALID FILE] Recency search result: file found: ${file !== null} (took ${recencyEndTime - recencyStartTime}ms)`);

    // If we found a file with the recency search, update its fileId field if needed
<<<<<<< HEAD
    if (file !== null && !file.fileId) {
=======
    if(file !== null && !file.fileId) {
>>>>>>> WA-170_MCP
      try {
        console.log(`🔍 [ENSURE VALID FILE] File found with recency search, but missing fileId field. Updating with: ${fileId}`);
        // Update the file record to add the fileId field for future compatibility
        await RagVectorFileModel.findByIdAndUpdate(file._id, { fileId: fileId });
        // Also update the in-memory file object
        file.fileId = fileId;
<<<<<<< HEAD
      } catch (updateError) {
        if (updateError && typeof updateError === "object" && "message" in updateError) {
=======
      }catch(updateError) {
        if(updateError && typeof updateError === "object" && "message" in updateError) {
>>>>>>> WA-170_MCP
          console.warn(`⚠️ [ENSURE VALID FILE] Error updating fileId: ${(updateError as any).message}`);
        } else {
          console.warn(`⚠️ [ENSURE VALID FILE] Error updating fileId:`, updateError);
        }
      }
    }
  }

  if(whitelabel === null || file === null){
    console.error(`❌ [ENSURE VALID FILE] File or whitelabel not found: whitelabel=${whitelabel !== null}, file=${file !== null}`);
    throw HTTP_ERRORS.NOT_FOUND;
  }

  console.log(`✅ [ENSURE VALID FILE] File found successfully: ${fileId}`);
  return { whitelabel, file };
}

export async function ensureWhitelabel(req: IncomingMessage): Promise<{
  whitelabel: Document<unknown, object, any>,
<<<<<<< HEAD
  target: string
}> {
=======
  target: string,
}>{
>>>>>>> WA-170_MCP
  const whitelabelId = getParam(req, "whitelabelId");

  const target = condenseTarget({
    ...WHITE_LABEL_LOCATION,
    id: whitelabelId,
  });

  // Fetch the whitelabel document
  const whitelabel = ensureDocument(await WhiteLabelModel.findById(whitelabelId).select("_id"));

  if(whitelabel === null){
    throw HTTP_ERRORS.NOT_FOUND;
  }

  return { whitelabel, target };
}
