#!/bin/bash
# Demo environment variables for testing the E2E framework
# These are dummy values for demonstration purposes

export TARGET_ENVIRONMENT=develop
export TEST_ENV=develop
export NODE_ENV=develop

# Demo Auth0 Configuration (dummy values)
export AUTH0_BASE_URL=https://demo-divinci.us.auth0.com
export AUTH0_CLIENT_ID=demo_client_id_12345
export AUTH0_AUDIENCE=https://api.dev.divinci.app
export AUTH0_S2S_CLIENT_ID=demo_s2s_client_id_67890
export AUTH0_S2S_CLIENT_SECRET=demo_s2s_secret_abcdef

# Demo test user credentials
export AUTH0_TEST_USER_EMAIL=<EMAIL>
export AUTH0_TEST_USER_PASSWORD=demo_password_123
export AUTH0_ADMIN_USER_EMAIL=<EMAIL>
export AUTH0_ADMIN_USER_PASSWORD=demo_admin_password_456

# Demo Cloudflare Access credentials
export CF_ACCESS_CLIENT_ID=demo_cf_client_id_xyz789
export CF_ACCESS_CLIENT_SECRET=demo_cf_secret_abc123
export CF_ACCESS_CLIENT_ID_DEV=demo_cf_client_id_xyz789
export CF_ACCESS_CLIENT_SECRET_DEV=demo_cf_secret_abc123

# Test configuration
export MAX_WAIT_TIME=300
export TEST_TIMEOUT=60000
export EXPECT_TIMEOUT=10000
export NAVIGATION_TIMEOUT=30000

echo "🎭 Demo environment variables loaded for E2E testing framework demonstration"
echo "🌍 Target Environment: $TARGET_ENVIRONMENT"
echo "🔗 API URL: https://api.dev.divinci.app"
echo "🔗 Web URL: https://chat.dev.divinci.app"
echo ""
echo "⚠️  Note: These are dummy credentials for testing the framework only"
echo "📝 To use real environments, configure actual credentials in private-keys/[env]/test.env"
