/**
 * Thread API Tests
 *
 * This file contains tests for the Thread API.
 */

import { test, expect } from '@playwright/test';
import { ApiClient } from '../api/api-client';
import { getAuthFileForRole } from '../auth/auth-utils';
import { config } from '../config/config';

// Mock Thread API responses
const mockThreadResponse = {
  _id: 'mock-thread-123',
  title: 'Test Thread',
  messages: [
    {
      role: 'user',
      content: 'Hello, AI!',
      timestamp: new Date().toISOString()
    },
    {
      role: 'assistant',
      content: 'Hello! How can I help you today?',
      timestamp: new Date().toISOString()
    }
  ],
  metadata: {
    tags: ['test', 'example'],
    category: 'general'
  },
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
};

// Mock API client for Thread
class MockThreadClient extends ApiClient {
  constructor() {
    super(config.apiBaseUrl, getAuthFileForRole('admin'));
  }

  async get(path: string) {
    if (path === 'thread') {
      return [mockThreadResponse];
    } else if (path.startsWith('thread/')) {
      return mockThreadResponse;
    }
    return Promise.resolve({});
  }

  async post(path: string, data: any) {
    if (path === 'thread') {
      return {
        ...mockThreadResponse,
        title: data.title || 'New Thread',
        metadata: data.metadata || mockThreadResponse.metadata
      };
    } else if (path.includes('thread') && path.includes('message')) {
      return {
        ...mockThreadResponse,
        messages: [
          ...mockThreadResponse.messages,
          {
            role: 'user',
            content: data.content,
            timestamp: new Date().toISOString()
          },
          {
            role: 'assistant',
            content: 'I received your message: ' + data.content,
            timestamp: new Date().toISOString()
          }
        ]
      };
    }
    return Promise.resolve({});
  }

  async delete(path: string) {
    if (path.startsWith('thread/')) {
      return { success: true };
    }
    return Promise.resolve({});
  }

  async put(path: string, data: any) {
    if (path.startsWith('thread/')) {
      return {
        ...mockThreadResponse,
        title: data.title || mockThreadResponse.title,
        metadata: data.metadata || mockThreadResponse.metadata,
        updatedAt: new Date().toISOString()
      };
    }
    return Promise.resolve({});
  }
}

test.describe('Thread API', () => {
  let client: MockThreadClient;

  test.beforeEach(() => {
    client = new MockThreadClient();
  });

  // Error condition tests
  test.describe('Error Conditions', () => {
    test('should handle invalid input when creating a thread', async () => {
      // Override the post method to simulate an error
      const originalPost = client.post;
      client.post = async (path, data) => {
        if (path === 'thread' && (!data || !data.title)) {
          throw new Error('Invalid input: title is required');
        }
        return originalPost.call(client, path, data);
      };

      // Try to create a thread with missing title
      let error: Error | null = null;
      try {
        await client.post('thread', {});
      } catch (e) {
        error = e as Error;
      }

      // Verify that an error was thrown
      expect(error).not.toBeNull();
      expect(error?.message).toContain('title is required');

      // Restore the original post method
      client.post = originalPost;
    });

    test('should handle not found error when getting a non-existent thread', async () => {
      // Override the get method to simulate a not found error
      const originalGet = client.get;
      client.get = async (path) => {
        if (path === 'thread/non-existent-id') {
          throw new Error('Thread not found: non-existent-id');
        }
        return originalGet.call(client, path);
      };

      // Try to get a non-existent thread
      let error: Error | null = null;
      try {
        await client.get('thread/non-existent-id');
      } catch (e) {
        error = e as Error;
      }

      // Verify that an error was thrown
      expect(error).not.toBeNull();
      expect(error?.message).toContain('not found');

      // Restore the original get method
      client.get = originalGet;
    });

    test('should handle unauthorized access when accessing a thread', async () => {
      // Override the get method to simulate an unauthorized error
      const originalGet = client.get;
      client.get = async (path) => {
        if (path === 'thread/unauthorized-id') {
          throw new Error('Unauthorized: You do not have permission to access this thread');
        }
        return originalGet.call(client, path);
      };

      // Try to access an unauthorized thread
      let error: Error | null = null;
      try {
        await client.get('thread/unauthorized-id');
      } catch (e) {
        error = e as Error;
      }

      // Verify that an error was thrown
      expect(error).not.toBeNull();
      expect(error?.message).toContain('Unauthorized');

      // Restore the original get method
      client.get = originalGet;
    });

    test('should handle invalid input when adding a message to a thread', async () => {
      // Override the post method to simulate an error
      const originalPost = client.post;
      client.post = async (path, data) => {
        if (path.includes('message') && (!data || !data.content)) {
          throw new Error('Invalid input: content is required');
        }
        return originalPost.call(client, path, data);
      };

      // Try to add a message with missing content
      let error: Error | null = null;
      try {
        await client.post('thread/mock-thread-123/message', {});
      } catch (e) {
        error = e as Error;
      }

      // Verify that an error was thrown
      expect(error).not.toBeNull();
      expect(error?.message).toContain('content is required');

      // Restore the original post method
      client.post = originalPost;
    });

    test('should handle invalid input when updating a thread', async () => {
      // Override the put method to simulate an error
      const originalPut = client.put;
      client.put = async (path, data) => {
        if (path.startsWith('thread/') && (!data || !data.title)) {
          throw new Error('Invalid input: title is required');
        }
        return originalPut.call(client, path, data);
      };

      // Try to update a thread with missing title
      let error: Error | null = null;
      try {
        await client.put('thread/mock-thread-123', {});
      } catch (e) {
        error = e as Error;
      }

      // Verify that an error was thrown
      expect(error).not.toBeNull();
      expect(error?.message).toContain('title is required');

      // Restore the original put method
      client.put = originalPut;
    });

    test('should handle not found error when deleting a non-existent thread', async () => {
      // Override the delete method to simulate a not found error
      const originalDelete = client.delete;
      client.delete = async (path) => {
        if (path === 'thread/non-existent-id') {
          throw new Error('Thread not found: non-existent-id');
        }
        return originalDelete.call(client, path);
      };

      // Try to delete a non-existent thread
      let error: Error | null = null;
      try {
        await client.delete('thread/non-existent-id');
      } catch (e) {
        error = e as Error;
      }

      // Verify that an error was thrown
      expect(error).not.toBeNull();
      expect(error?.message).toContain('not found');

      // Restore the original delete method
      client.delete = originalDelete;
    });
  });

  test('should create a new thread', async () => {
    const thread = await client.post('thread', {
      title: 'Test Thread',
      metadata: {
        tags: ['test', 'example'],
        category: 'general'
      }
    });

    expect(thread).toBeDefined();
    expect(thread._id).toBeDefined();
    expect(thread.title).toBe('Test Thread');
    expect(thread.messages).toBeInstanceOf(Array);
    expect(thread.metadata).toBeDefined();
    expect(thread.metadata.tags).toContain('test');
  });

  test('should get all threads', async () => {
    const threads = await client.get('thread');

    expect(threads).toBeInstanceOf(Array);
    expect(threads.length).toBeGreaterThan(0);
    expect(threads[0]._id).toBeDefined();
    expect(threads[0].title).toBeDefined();
  });

  test('should get a thread by ID', async () => {
    const thread = await client.get('thread/mock-thread-123');

    expect(thread).toBeDefined();
    expect(thread._id).toBe('mock-thread-123');
    expect(thread.title).toBe('Test Thread');
    expect(thread.messages).toBeInstanceOf(Array);
  });

  test('should add a message to a thread', async () => {
    const updatedThread = await client.post('thread/mock-thread-123/message', {
      content: 'This is a test message'
    });

    expect(updatedThread).toBeDefined();
    expect(updatedThread.messages).toBeInstanceOf(Array);

    // Find the last user message
    const userMessages = updatedThread.messages.filter(m => m.role === 'user');
    const lastUserMessage = userMessages[userMessages.length - 1];

    expect(lastUserMessage).toBeDefined();
    expect(lastUserMessage.content).toBe('This is a test message');

    // Check for assistant response
    const assistantMessages = updatedThread.messages.filter(m => m.role === 'assistant');
    const lastAssistantMessage = assistantMessages[assistantMessages.length - 1];

    expect(lastAssistantMessage).toBeDefined();
    expect(lastAssistantMessage.content).toContain('This is a test message');
  });

  test('should update a thread', async () => {
    const updatedThread = await client.put('thread/mock-thread-123', {
      title: 'Updated Thread Title',
      metadata: {
        tags: ['updated', 'test'],
        category: 'support'
      }
    });

    expect(updatedThread).toBeDefined();
    expect(updatedThread.title).toBe('Updated Thread Title');
    expect(updatedThread.metadata.tags).toContain('updated');
    expect(updatedThread.metadata.category).toBe('support');
  });

  test('should delete a thread', async () => {
    const result = await client.delete('thread/mock-thread-123');

    expect(result).toBeDefined();
    expect(result.success).toBe(true);
  });
});
