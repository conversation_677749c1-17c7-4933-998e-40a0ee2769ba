import { WorkflowStep } from "cloudflare:workers";
import { StorageClient } from "../../utils/fixed-storage-client";
import { Env } from "../../types";

// Common types used across steps
export interface StepContext {
  env: Env,
  storageClient: StorageClient,
}

export interface WorkflowFile {
  fileId: string,
  target: string,
  fileName: string,
  bucket: string,
  objectKey: string,
  processor: string,
  processorConfig: any,
  formData?: FormData,
  title?: string,
  description?: string,
}

export interface VectorizeConfig {
  whitelabelId: string,
<<<<<<< HEAD
  ragId?: string,
=======
  ragVectors?: Array<string>,
  ragId?: string, // Backward compatibility for old workflow approach
>>>>>>> WA-170_MCP
  auth0Token: string,
}

export interface R2ValidationResult {
  objectKey: string,
  fileName: string,
  hasExistingFile: boolean,
  pendingDirectUpload?: boolean,
  size: number,
  mimeType: string,
  customMetadata: Record<string, string>,
}

export interface FileInfo {
  fileId: string,
  processor: string,
  objectKey: string,
  fileName: string,
  title?: string,
  description?: string,
  target: string,
  bucket: string,
<<<<<<< HEAD
  hasFormData?: boolean
=======
  hasFormData?: boolean,
>>>>>>> WA-170_MCP
  // Removed similarFileKey and exactFileKey to ensure consistent behavior
}

export interface WorkflowMetadata {
  workflowId: string,
  fileId: string,
  target?: string, // Make target optional to support different database schemas
  processor: string,
  processorConfig: any,
  steps: {
    initializeWorkflow: {
      startTime: string,
      endTime: string,
    },
    deJunk: {
      chunksBeforeDeJunk?: number,
      chunksAfterDeJunk?: number,
      skipped?: boolean,
    },
    [key: string]: any,
  },
  status: string,
  error?: string,
}

export interface InitialSetup {
  normalizedConfig: any,
  metadata: WorkflowMetadata,
  fileInfo: FileInfo,
}

export interface FileRecord {
  success: boolean,
  data: {
    _id: string,
    fileKey: string,
    status: string,
  },
  status: string,
}

export interface FileRecordPayload {
  bucket: string,
  objectKey: string,
  originalName: string,
  chunkingTool: string,
  title?: string,
  description?: string,
}

export interface FileRecordResponse {
  success: boolean,
  data: {
    _id: string,
    fileKey: string,
    status: string,
  },
}

export interface ChunkData {
  id: string,
  text: string,
  metadata: {
    whiteLabelId: string,
    originalName: string,
    vectorId: string,
    fileObjectKey: string,
    tokenCount: number,
    [key: string]: any,
  },
}
