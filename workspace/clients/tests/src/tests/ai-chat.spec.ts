/**
 * AI Chat Tests
 *
 * This file contains tests for the AI Chat API.
 */

import { test, expect } from '@playwright/test';
import { ApiClient } from '../api/api-client';
import { getAuthFileForRole } from '../auth/auth-utils';

// Mock AI Chat API responses
const mockChatResponse = {
  _id: 'mock-chat-123',
  title: 'Test Chat',
  messages: [
    {
      role: 'user',
      content: 'Hello, AI!',
      timestamp: new Date().toISOString()
    },
    {
      role: 'assistant',
      content: 'Hello! How can I help you today?',
      timestamp: new Date().toISOString()
    }
  ],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
};

// Mock API client for AI Chat
class MockAiChatClient extends ApiClient {
  constructor() {
    super('http://localhost:3000/api', getAuthFileForRole('admin'));
  }

  async get(path: string) {
    if (path === 'ai-chat') {
      return [mockChatResponse];
    } else if (path.startsWith('ai-chat/')) {
      return mockChatResponse;
    }
    return Promise.resolve({});
  }

  async post(path: string, data: any) {
    if (path === 'ai-chat') {
      return {
        ...mockChatResponse,
        title: data.title || 'New Chat'
      };
    } else if (path.includes('ai-chat') && path.includes('message')) {
      return {
        ...mockChatResponse,
        messages: [
          ...mockChatResponse.messages,
          {
            role: 'user',
            content: data.content,
            timestamp: new Date().toISOString()
          },
          {
            role: 'assistant',
            content: 'I received your message: ' + data.content,
            timestamp: new Date().toISOString()
          }
        ]
      };
    }
    return Promise.resolve({});
  }

  async delete(path: string) {
    if (path.startsWith('ai-chat/')) {
      return { success: true };
    }
    return Promise.resolve({});
  }
}

test.describe('AI Chat API', () => {
  let client: MockAiChatClient;

  test.beforeEach(() => {
    client = new MockAiChatClient();
  });

  // Edge case tests
  test.describe('Edge Cases', () => {
    test('should handle very long chat titles', async () => {
      // Create a very long title (1000 characters)
      const longTitle = 'Very long title '.repeat(50);

      // Override the post method to handle long titles
      const originalPost = client.post;
      client.post = async (path, data) => {
        if (path === 'ai-chat' && data.title && data.title.length > 500) {
          // Truncate the title to 500 characters in the response
          return {
            ...mockChatResponse,
            title: data.title.substring(0, 500) + '...'
          };
        }
        return originalPost.call(client, path, data);
      };

      // Create a chat with a very long title
      const chat = await client.post('ai-chat', { title: longTitle });

      // Verify that the title was truncated
      expect(chat).toBeDefined();
      expect(chat.title.length).toBeLessThanOrEqual(504); // 500 + '...'
      expect(chat.title).toContain('...');

      // Restore the original post method
      client.post = originalPost;
    });

    test('should handle special characters in chat titles', async () => {
      // Create a title with special characters
      const specialTitle = '!@#$%^&*()_+{}[]|\\:;"\'<>,.?/~`';

      // Create a chat with special characters in the title
      const chat = await client.post('ai-chat', { title: specialTitle });

      // Verify that the special characters were preserved
      expect(chat).toBeDefined();
      expect(chat.title).toBe(specialTitle);
    });

    test('should handle emoji characters in chat titles and messages', async () => {
      // Create a title with emoji characters
      const emojiTitle = '😀 Chat with emojis 🚀';

      // Override the post method to handle emoji
      const originalPost = client.post;
      client.post = async (path, data) => {
        if (path === 'ai-chat') {
          return {
            ...mockChatResponse,
            title: data.title
          };
        } else if (path.includes('message')) {
          return {
            ...mockChatResponse,
            title: emojiTitle,
            messages: [
              ...mockChatResponse.messages,
              {
                role: 'user',
                content: data.content,
                timestamp: new Date().toISOString()
              },
              {
                role: 'assistant',
                content: 'I received your message with emojis: ' + data.content,
                timestamp: new Date().toISOString()
              }
            ]
          };
        }
        return originalPost.call(client, path, data);
      };

      // Create a chat with emoji in the title
      const chat = await client.post('ai-chat', { title: emojiTitle });

      // Verify that the emoji were preserved in the title
      expect(chat).toBeDefined();
      expect(chat.title).toBe(emojiTitle);

      // Add a message with emoji
      const emojiMessage = 'Hello! 👋 How are you doing today? 🌞';
      const updatedChat = await client.post('ai-chat/mock-chat-123/message', {
        content: emojiMessage
      });

      // Verify that the emoji were preserved in the message
      expect(updatedChat).toBeDefined();
      expect(updatedChat.messages).toBeInstanceOf(Array);

      // Find the last user message
      const userMessages = updatedChat.messages.filter(m => m.role === 'user');
      const lastUserMessage = userMessages[userMessages.length - 1];

      expect(lastUserMessage).toBeDefined();
      expect(lastUserMessage.content).toBe(emojiMessage);

      // Restore the original post method
      client.post = originalPost;
    });

    test('should handle HTML tags in messages', async () => {
      // Create a message with HTML tags
      const htmlMessage = '<h1>Hello</h1><p>This is a <strong>test</strong> message with <a href="https://example.com">HTML tags</a>.</p>';

      // Override the post method to handle HTML
      const originalPost = client.post;
      client.post = async (path, data) => {
        if (path.includes('message')) {
          // Sanitize HTML in the response
          const sanitizedContent = data.content.replace(/</g, '&lt;').replace(/>/g, '&gt;');
          return {
            ...mockChatResponse,
            messages: [
              ...mockChatResponse.messages,
              {
                role: 'user',
                content: data.content, // Original content with HTML
                timestamp: new Date().toISOString()
              },
              {
                role: 'assistant',
                content: 'I received your message with HTML: ' + sanitizedContent,
                timestamp: new Date().toISOString()
              }
            ]
          };
        }
        return originalPost.call(client, path, data);
      };

      // Add a message with HTML tags
      const updatedChat = await client.post('ai-chat/mock-chat-123/message', {
        content: htmlMessage
      });

      // Verify that the HTML was preserved in the message
      expect(updatedChat).toBeDefined();
      expect(updatedChat.messages).toBeInstanceOf(Array);

      // Find the last user message
      const userMessages = updatedChat.messages.filter(m => m.role === 'user');
      const lastUserMessage = userMessages[userMessages.length - 1];

      expect(lastUserMessage).toBeDefined();
      expect(lastUserMessage.content).toBe(htmlMessage);

      // Find the last assistant message
      const assistantMessages = updatedChat.messages.filter(m => m.role === 'assistant');
      const lastAssistantMessage = assistantMessages[assistantMessages.length - 1];

      expect(lastAssistantMessage).toBeDefined();
      expect(lastAssistantMessage.content).toContain('&lt;h1&gt;');
      expect(lastAssistantMessage.content).toContain('&gt;');

      // Restore the original post method
      client.post = originalPost;
    });

    test('should handle very large message content', async () => {
      // Create a very large message (50KB)
      const largeMessage = 'This is a very large message. '.repeat(2000);

      // Override the post method to handle large messages
      const originalPost = client.post;
      client.post = async (path, data) => {
        if (path.includes('message') && data.content && data.content.length > 10000) {
          // Truncate the message in the response
          const truncatedContent = data.content.substring(0, 10000) + '... (truncated)';
          return {
            ...mockChatResponse,
            messages: [
              ...mockChatResponse.messages,
              {
                role: 'user',
                content: truncatedContent,
                timestamp: new Date().toISOString()
              },
              {
                role: 'assistant',
                content: 'I received your very large message. It was truncated for display.',
                timestamp: new Date().toISOString()
              }
            ]
          };
        }
        return originalPost.call(client, path, data);
      };

      // Add a very large message
      const updatedChat = await client.post('ai-chat/mock-chat-123/message', {
        content: largeMessage
      });

      // Verify that the message was truncated
      expect(updatedChat).toBeDefined();
      expect(updatedChat.messages).toBeInstanceOf(Array);

      // Find the last user message
      const userMessages = updatedChat.messages.filter(m => m.role === 'user');
      const lastUserMessage = userMessages[userMessages.length - 1];

      expect(lastUserMessage).toBeDefined();
      expect(lastUserMessage.content.length).toBeLessThanOrEqual(10020); // 10000 + '... (truncated)'
      expect(lastUserMessage.content).toContain('... (truncated)');

      // Restore the original post method
      client.post = originalPost;
    });
  });

  // Error condition tests
  test.describe('Error Conditions', () => {
    test('should handle invalid input when creating a chat', async () => {
      // Override the post method to simulate an error
      const originalPost = client.post;
      client.post = async (path, data) => {
        if (path === 'ai-chat' && (!data || !data.title)) {
          throw new Error('Invalid input: title is required');
        }
        return originalPost.call(client, path, data);
      };

      // Try to create a chat with missing title
      let error: Error | null = null;
      try {
        await client.post('ai-chat', {});
      } catch (e) {
        error = e as Error;
      }

      // Verify that an error was thrown
      expect(error).not.toBeNull();
      expect(error?.message).toContain('title is required');

      // Restore the original post method
      client.post = originalPost;
    });

    test('should handle not found error when getting a non-existent chat', async () => {
      // Override the get method to simulate a not found error
      const originalGet = client.get;
      client.get = async (path) => {
        if (path === 'ai-chat/non-existent-id') {
          throw new Error('Chat not found: non-existent-id');
        }
        return originalGet.call(client, path);
      };

      // Try to get a non-existent chat
      let error: Error | null = null;
      try {
        await client.get('ai-chat/non-existent-id');
      } catch (e) {
        error = e as Error;
      }

      // Verify that an error was thrown
      expect(error).not.toBeNull();
      expect(error?.message).toContain('not found');

      // Restore the original get method
      client.get = originalGet;
    });

    test('should handle unauthorized access when accessing a chat', async () => {
      // Override the get method to simulate an unauthorized error
      const originalGet = client.get;
      client.get = async (path) => {
        if (path === 'ai-chat/unauthorized-id') {
          throw new Error('Unauthorized: You do not have permission to access this chat');
        }
        return originalGet.call(client, path);
      };

      // Try to access an unauthorized chat
      let error: Error | null = null;
      try {
        await client.get('ai-chat/unauthorized-id');
      } catch (e) {
        error = e as Error;
      }

      // Verify that an error was thrown
      expect(error).not.toBeNull();
      expect(error?.message).toContain('Unauthorized');

      // Restore the original get method
      client.get = originalGet;
    });

    test('should handle invalid input when adding a message to a chat', async () => {
      // Override the post method to simulate an error
      const originalPost = client.post;
      client.post = async (path, data) => {
        if (path.includes('message') && (!data || !data.content)) {
          throw new Error('Invalid input: content is required');
        }
        return originalPost.call(client, path, data);
      };

      // Try to add a message with missing content
      let error: Error | null = null;
      try {
        await client.post('ai-chat/mock-chat-123/message', {});
      } catch (e) {
        error = e as Error;
      }

      // Verify that an error was thrown
      expect(error).not.toBeNull();
      expect(error?.message).toContain('content is required');

      // Restore the original post method
      client.post = originalPost;
    });

    test('should handle server error when API fails', async () => {
      // Override the post method to simulate a server error
      const originalPost = client.post;
      client.post = async (path, data) => {
        if (path === 'ai-chat/server-error') {
          throw new Error('Internal Server Error: Something went wrong');
        }
        return originalPost.call(client, path, data);
      };

      // Try to make a request that triggers a server error
      let error: Error | null = null;
      try {
        await client.post('ai-chat/server-error', { content: 'This will fail' });
      } catch (e) {
        error = e as Error;
      }

      // Verify that an error was thrown
      expect(error).not.toBeNull();
      expect(error?.message).toContain('Internal Server Error');

      // Restore the original post method
      client.post = originalPost;
    });
  });

  test('should create a new chat', async () => {
    const chat = await client.post('ai-chat', { title: 'Test Chat' });

    expect(chat).toBeDefined();
    expect(chat._id).toBeDefined();
    expect(chat.title).toBe('Test Chat');
    expect(chat.messages).toBeInstanceOf(Array);
  });

  test('should get all chats', async () => {
    const chats = await client.get('ai-chat');

    expect(chats).toBeInstanceOf(Array);
    expect(chats.length).toBeGreaterThan(0);
    expect(chats[0]._id).toBeDefined();
    expect(chats[0].title).toBeDefined();
  });

  test('should get a chat by ID', async () => {
    const chat = await client.get('ai-chat/mock-chat-123');

    expect(chat).toBeDefined();
    expect(chat._id).toBe('mock-chat-123');
    expect(chat.title).toBe('Test Chat');
    expect(chat.messages).toBeInstanceOf(Array);
  });

  test('should add a message to a chat', async () => {
    const updatedChat = await client.post('ai-chat/mock-chat-123/message', {
      content: 'This is a test message'
    });

    expect(updatedChat).toBeDefined();
    expect(updatedChat.messages).toBeInstanceOf(Array);

    // Find the last user message
    const userMessages = updatedChat.messages.filter(m => m.role === 'user');
    const lastUserMessage = userMessages[userMessages.length - 1];

    expect(lastUserMessage).toBeDefined();
    expect(lastUserMessage.content).toBe('This is a test message');

    // Check for assistant response
    const assistantMessages = updatedChat.messages.filter(m => m.role === 'assistant');
    const lastAssistantMessage = assistantMessages[assistantMessages.length - 1];

    expect(lastAssistantMessage).toBeDefined();
    expect(lastAssistantMessage.content).toContain('This is a test message');
  });

  test('should delete a chat', async () => {
    const result = await client.delete('ai-chat/mock-chat-123');

    expect(result).toBeDefined();
    expect(result.success).toBe(true);
  });
});
