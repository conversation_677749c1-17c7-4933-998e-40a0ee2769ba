/**
 * Initiate Dropbox OAuth2 authentication flow
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { dropboxOAuth } from "@divinci-ai/server-globals";
import { ensureWhitelabel } from "../util/ensure-whitelabel";

interface InitiateAuthRequest {
  returnUrl?: string,
  metadata?: any,
}

export const initiateDropboxAuth: RequestHandler = async function(req, res, next){
  try {
    console.log("🔗 [INITIATE-AUTH] Request received:", {
      body: req.body,
      headers: req.headers["content-type"],
      method: req.method
    });

    const { whitelabel, userId } = await ensureWhitelabel(req);
    const { returnUrl, metadata }: InitiateAuthRequest = req.body || {};

    // Prepare additional data to store with the OAuth state
    const stateData = {
      whitelabelId: whitelabel._id.toString(),
      userId: userId,
      returnUrl,
      metadata
    };

    // Start OAuth flow
    const { authUrl, state } = dropboxOAuth.initiateAuth(stateData);

    res.status(200).json({
      success: true,
      data: {
        authUrl,
        state,
        message: "OAuth flow initiated. Redirect user to authUrl."
      }
    });

  }catch(error) {
    next(error);
  }
};
