/**
 * Dropbox Sync Worker
 * 
 * Cloudflare Worker for orchestrating Dropbox file sync operations.
 * Provides HTTP endpoints for triggering sync jobs and managing workflows.
 */

import { Hono } from "hono";
import { cors } from "hono/cors";
import { logger } from "hono/logger";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";

import { Env, DropboxSyncWorkflowEvent, DropboxSyncJobStatus } from "./types";
import { DropboxSyncWorkflow } from "./workflows/dropbox-sync-workflow";

// Create Hono app
const app = new Hono<{ Bindings: Env }>();

// Middleware
app.use("*", logger());
app.use("*", cors({
  origin: (origin, c) => {
    const allowedOrigins = c.env.ALLOWED_ORIGINS?.split(",") || [];
    return allowedOrigins.includes(origin) ? origin : null;
  },
  allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowHeaders: ["Content-Type", "Authorization", "cloudflare-worker-x-dev-auth"],
  credentials: true
}));

// Health check endpoint
app.get("/health", (c) => {
  return c.json({
    success: true,
    service: "dropbox-sync-worker",
    environment: c.env.ENVIRONMENT,
    timestamp: new Date().toISOString()
  });
});

// Validation schemas
const TriggerSyncJobSchema = z.object({
  jobId: z.string().min(1),
  whitelabelId: z.string().min(1),
  connectionId: z.string().min(1),
  userId: z.string().min(1),
  targetType: z.enum(["rag", "fine-tuning"]),
  sourceFiles: z.array(z.object({
    dropboxPath: z.string(),
    dropboxFileId: z.string(),
    fileName: z.string(),
    fileSize: z.number(),
    isAudio: z.boolean(),
    isVideo: z.boolean(),
    extension: z.string(),
    mimeType: z.string().optional()
  })).min(1),
  processingConfig: z.object({
    chunkingTool: z.string().optional(),
    chunkingConfig: z.any().optional(),
    ragId: z.string().optional(),
    fineTuningDatasetId: z.string().optional(),
    transcriptionTool: z.string().optional(),
    diarizationTool: z.string().optional(),
    skipExistingFiles: z.boolean().optional(),
    maxFileSize: z.number().optional()
  }),
  accessToken: z.string().min(1), // Dropbox access token
  auth0Token: z.string().min(1),  // API authentication token
  metadata: z.object({
    jobName: z.string().optional(),
    description: z.string().optional(),
    priority: z.number().optional(),
    tags: z.array(z.string()).optional()
  }).optional()
});

// Trigger sync job endpoint
app.post("/trigger-sync-job", 
  zValidator("json", TriggerSyncJobSchema),
  async (c) => {
    try {
      const payload = c.req.valid("json");
      
      console.log("🚀 Triggering Dropbox sync job", {
        jobId: payload.jobId,
        whitelabelId: payload.whitelabelId,
        totalFiles: payload.sourceFiles.length,
        targetType: payload.targetType
      });

      // Validate target type specific requirements
      if (payload.targetType === "rag" && !payload.processingConfig.ragId) {
        return c.json({
          success: false,
          error: "missing_rag_id",
          message: "RAG ID is required for RAG target type"
        }, 400);
      }

      if (payload.targetType === "fine-tuning" && !payload.processingConfig.fineTuningDatasetId) {
        return c.json({
          success: false,
          error: "missing_dataset_id", 
          message: "Fine-tuning dataset ID is required for fine-tuning target type"
        }, 400);
      }

      // Generate unique instance ID for this workflow
      const instanceId = `dropbox-sync-${payload.jobId}-${Date.now()}`;

      // Prepare workflow event
      const workflowEvent: DropboxSyncWorkflowEvent = {
        ...payload,
        timestamp: new Date().toISOString(),
        instanceId
      };

      // Create and start workflow
      const workflowInstance = await c.env.DROPBOX_SYNC_WORKFLOW.create({
        id: instanceId,
        params: workflowEvent
      });

      console.log("✅ Dropbox sync workflow created", {
        workflowId: workflowInstance.id,
        jobId: payload.jobId
      });

      return c.json({
        success: true,
        data: {
          workflowId: workflowInstance.id,
          jobId: payload.jobId,
          status: "running",
          totalFiles: payload.sourceFiles.length,
          createdAt: new Date().toISOString()
        },
        message: "Dropbox sync job started successfully"
      });

    } catch (error) {
      console.error("❌ Failed to trigger sync job:", error);
      
      return c.json({
        success: false,
        error: "workflow_creation_failed",
        message: error instanceof Error ? error.message : "Failed to create workflow"
      }, 500);
    }
  }
);

// Get workflow status endpoint
app.get("/workflow-status/:workflowId", async (c) => {
  try {
    const workflowId = c.req.param("workflowId");
    
    console.log("📊 Getting workflow status", { workflowId });

    // Get workflow instance
    const workflowInstance = await c.env.DROPBOX_SYNC_WORKFLOW.get(workflowId);
    
    if (!workflowInstance) {
      return c.json({
        success: false,
        error: "workflow_not_found",
        message: "Workflow not found"
      }, 404);
    }

    const status = await workflowInstance.status();
    
    return c.json({
      success: true,
      data: {
        workflowId,
        status: status.status,
        output: status.output,
        error: status.error,
        createdOn: status.createdOn,
        modifiedOn: status.modifiedOn
      }
    });

  } catch (error) {
    console.error("❌ Failed to get workflow status:", error);
    
    return c.json({
      success: false,
      error: "status_check_failed",
      message: error instanceof Error ? error.message : "Failed to check workflow status"
    }, 500);
  }
});

// Cancel workflow endpoint
app.post("/cancel-workflow/:workflowId", async (c) => {
  try {
    const workflowId = c.req.param("workflowId");
    
    console.log("🛑 Cancelling workflow", { workflowId });

    // Get workflow instance
    const workflowInstance = await c.env.DROPBOX_SYNC_WORKFLOW.get(workflowId);
    
    if (!workflowInstance) {
      return c.json({
        success: false,
        error: "workflow_not_found",
        message: "Workflow not found"
      }, 404);
    }

    // Terminate the workflow
    await workflowInstance.terminate();
    
    console.log("✅ Workflow cancelled", { workflowId });

    return c.json({
      success: true,
      data: {
        workflowId,
        status: "cancelled",
        cancelledAt: new Date().toISOString()
      },
      message: "Workflow cancelled successfully"
    });

  } catch (error) {
    console.error("❌ Failed to cancel workflow:", error);
    
    return c.json({
      success: false,
      error: "cancellation_failed",
      message: error instanceof Error ? error.message : "Failed to cancel workflow"
    }, 500);
  }
});

// List active workflows endpoint
app.get("/workflows", async (c) => {
  try {
    console.log("📋 Listing active workflows");

    // Note: This is a simplified implementation
    // In a real scenario, you might want to track workflows in a database
    return c.json({
      success: true,
      data: {
        workflows: [],
        message: "Workflow listing not fully implemented yet"
      }
    });

  } catch (error) {
    console.error("❌ Failed to list workflows:", error);
    
    return c.json({
      success: false,
      error: "listing_failed",
      message: error instanceof Error ? error.message : "Failed to list workflows"
    }, 500);
  }
});

// Error handling
app.onError((err, c) => {
  console.error("🚨 Unhandled error:", err);
  
  return c.json({
    success: false,
    error: "internal_server_error",
    message: "An unexpected error occurred"
  }, 500);
});

// 404 handler
app.notFound((c) => {
  return c.json({
    success: false,
    error: "not_found",
    message: "Endpoint not found"
  }, 404);
});

// Export the workflow class for Cloudflare Workers
export { DropboxSyncWorkflow };

// Export the default handler
export default app;
