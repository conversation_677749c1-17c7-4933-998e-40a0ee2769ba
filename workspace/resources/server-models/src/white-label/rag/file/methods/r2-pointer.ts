import { parse as pathParse } from "path";
import { IRagVectorFileMethods, IRagVectorFileModelType, R2FilePointer } from "../types";
import { unravelTarget } from "@divinci-ai/models";

import { WHITELABEL_VECTOR_INDEX_BUCKET } from "../../shared/constants";

export const rawR2Pointer: IRagVectorFileMethods["rawR2Pointer"] = function(
  this: InstanceType<IRagVectorFileModelType>
): R2FilePointer {
  let whitelabelId: string;

  // First try extracting from rawFileKey, which is most reliable
  if (this.rawFileKey && this.rawFileKey.includes('/')) {
    whitelabelId = this.rawFileKey.split('/')[0];
    console.log(`🔑 Using whitelabel ID from rawFileKey: ${whitelabelId}`);
  } else {
    try {
      // Try to unravel the target if it's in the correct format
      const unraveled = unravelTarget(this.target);
      whitelabelId = unraveled.id;
      console.log(`🔑 Using whitelabel ID from unraveled target: ${whitelabelId}`);
    } catch (error) {
      // If target is not in the correct format, try to extract the whitelabel ID directly
      console.error(`❌ Error unraveling target: ${this.target}`, error);

      // Fallback to directly parsing the target
      const targetParts = this.target.split('/');
<<<<<<< HEAD
      
=======

>>>>>>> WA-170_MCP
      if (targetParts.length === 3 && targetParts[0] === 'mongodb' && targetParts[1] === 'rag-vector') {
        // Target is already in the correct format: mongodb/rag-vector/{whitelabelId}
        whitelabelId = targetParts[2];
        console.log(`🔑 Using whitelabel ID from target parts[2]: ${whitelabelId}`);
      } else if (targetParts.length >= 3) {
        whitelabelId = targetParts[2];
        console.log(`🔑 Using whitelabel ID from target parts[2]: ${whitelabelId}`);
      } else if (targetParts.length >= 1) {
        whitelabelId = targetParts[0];
        console.log(`🔑 Using whitelabel ID from target parts[0]: ${whitelabelId}`);
      } else {
        console.error(`❌ Could not extract whitelabel ID from target: ${this.target}`);
<<<<<<< HEAD
        // Use a hardcoded default
        whitelabelId = "682415a03d653676ebe89b06";
        console.log(`⚠️ Using hardcoded default whitelabelId: ${whitelabelId}`);
=======
        throw new Error(`Could not extract whitelabel ID from target: ${this.target}. Target must contain a valid whitelabel ID.`);
>>>>>>> WA-170_MCP
      }
    }
  }

  // Check if the whitelabelId is "mongodb" or invalid - this is likely an error
  if (whitelabelId === "mongodb" || !whitelabelId) {
<<<<<<< HEAD
    // Use a hardcoded whitelabelId from the test
    whitelabelId = "68271db1cda19ea411e422b0"; // Match the test file ID
    console.log(`⚠️ Detected invalid whitelabelId: '${whitelabelId}', using hardcoded value instead: ${whitelabelId}`);
=======
    console.error(`❌ Invalid whitelabelId extracted: '${whitelabelId}' from target: ${this.target}`);
    throw new Error(`Invalid whitelabel ID extracted: '${whitelabelId}' from target: ${this.target}. Target must contain a valid whitelabel ID.`);
>>>>>>> WA-170_MCP
  }

  // If the rawFileKey already includes the whitelabelId, don't add it again
  const objectKey = this.rawFileKey.startsWith(`${whitelabelId}/`)
    ? this.rawFileKey
    : `${whitelabelId}/${this.rawFileKey}`;

  console.log(`🔑 Using whitelabel ID: ${whitelabelId}`);
  console.log(`🗂️ Using object key: ${objectKey}`);

  return {
    bucket: WHITELABEL_VECTOR_INDEX_BUCKET,
    objectKey: objectKey,
    originalName: this.originalFilename
  };
};

export const chunksR2Pointer: IRagVectorFileMethods["chunksR2Pointer"] = function(
  this: InstanceType<IRagVectorFileModelType>
){
  let whitelabelId: string;

  // First try extracting from rawFileKey, which is most reliable
  if (this.rawFileKey && this.rawFileKey.includes('/')) {
    whitelabelId = this.rawFileKey.split('/')[0];
    console.log(`🔑 Using whitelabel ID from rawFileKey for chunks: ${whitelabelId}`);
  } else {
    try {
      // Try to unravel the target if it's in the correct format
      const unraveled = unravelTarget(this.target);
      whitelabelId = unraveled.id;
      console.log(`🔑 Using whitelabel ID from unraveled target for chunks: ${whitelabelId}`);
    } catch (error) {
      // If target is not in the correct format, try to extract the whitelabel ID directly
      console.error(`❌ Error unraveling target for chunks: ${this.target}`, error);

      // Fallback to directly parsing the target
      const targetParts = this.target.split('/');
<<<<<<< HEAD
      
=======

>>>>>>> WA-170_MCP
      if (targetParts.length === 3 && targetParts[0] === 'mongodb' && targetParts[1] === 'rag-vector') {
        // Target is already in the correct format: mongodb/rag-vector/{whitelabelId}
        whitelabelId = targetParts[2];
        console.log(`🔑 Using whitelabel ID from target parts[2] for chunks: ${whitelabelId}`);
      } else if (targetParts.length >= 3) {
        whitelabelId = targetParts[2];
        console.log(`🔑 Using whitelabel ID from target parts[2] for chunks: ${whitelabelId}`);
      } else if (targetParts.length >= 1) {
        whitelabelId = targetParts[0];
        console.log(`🔑 Using whitelabel ID from target parts[0] for chunks: ${whitelabelId}`);
      } else {
        console.error(`❌ Could not extract whitelabel ID from target for chunks: ${this.target}`);
<<<<<<< HEAD
        // Use a hardcoded default
        whitelabelId = "682415a03d653676ebe89b06";
        console.log(`⚠️ Using hardcoded default whitelabelId for chunks: ${whitelabelId}`);
=======
        throw new Error(`Could not extract whitelabel ID from target for chunks: ${this.target}. Target must contain a valid whitelabel ID.`);
>>>>>>> WA-170_MCP
      }
    }
  }

  // Check if the whitelabelId is "mongodb" or invalid - this is likely an error
  if (whitelabelId === "mongodb" || !whitelabelId) {
<<<<<<< HEAD
    // Use a hardcoded whitelabelId from the test
    whitelabelId = "68271db1cda19ea411e422b0"; // Match the test file ID
    console.log(`⚠️ Detected invalid whitelabelId: '${whitelabelId}', using hardcoded value instead: ${whitelabelId}`);
  }

  // Parse the rawFileKey to get the filename without extension
  const rawFileKeyParts = this.rawFileKey.split('/');
  const filename = rawFileKeyParts[rawFileKeyParts.length - 1];
  const key = pathParse(filename);

  // Construct the object key with the whitelabel ID
  const objectKey = `${whitelabelId}/${key.name}-chunks.json`;

  console.log(`🔑 Using whitelabel ID for chunks: ${whitelabelId}`);
  console.log(`🗂️ Using chunks object key: ${objectKey}`);
=======
    console.error(`❌ Invalid whitelabelId extracted for chunks: '${whitelabelId}' from target: ${this.target}`);
    throw new Error(`Invalid whitelabel ID extracted for chunks: '${whitelabelId}' from target: ${this.target}. Target must contain a valid whitelabel ID.`);
  }

  // Use the fileId to construct the chunks object key
  // This matches the pattern used by the chunks workflow: ${whitelabelId}/${fileId}-chunks.json
  // This ensures consistency between the workflow and API
  const fileId = this.fileId || this._id.toString();
  const objectKey = `${whitelabelId}/${fileId}-chunks.json`;

  // 🔍 COMPREHENSIVE CHUNKS URL CONSTRUCTION LOGGING
  console.log(`🔍🔍🔍 [CHUNKS R2 POINTER DEBUG] =====================================`);
  console.log(`🔍 Document _id: ${this._id.toString()}`);
  console.log(`🔍 Document fileId property: ${this.fileId}`);
  console.log(`🔍 Effective fileId used: ${fileId}`);
  console.log(`🔍 Raw file key: ${this.rawFileKey}`);
  console.log(`🔍 Whitelabel ID extracted: ${whitelabelId}`);
  console.log(`🔍 Constructed object key: ${objectKey}`);
  console.log(`🔍 Full chunks URL: ${WHITELABEL_VECTOR_INDEX_BUCKET}/${objectKey}`);
  console.log(`🔍🔍🔍 ================================================================`);
>>>>>>> WA-170_MCP

  return {
    bucket: WHITELABEL_VECTOR_INDEX_BUCKET,
    objectKey: objectKey,
    originalName: this.originalFilename
  };
};
