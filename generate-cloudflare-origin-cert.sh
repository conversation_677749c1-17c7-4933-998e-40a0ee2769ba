#!/bin/bash
# Script to generate a new Cloudflare Origin Certificate

set -e

echo "🔍 Generating a new Cloudflare Origin Certificate..."

# Get Cloudflare API token
<<<<<<< HEAD
CF_API_TOKEN=${1:-"xQI66zSWAOjLnWlyoZne8_CjVgStYXYbH26f3p2c"}
=======
CF_API_TOKEN=${1:-"aR8Ud0AVhRI59O69XX815JmYuPalWdPG1S_nZGf2"}
>>>>>>> WA-170_MCP

# Get zone ID for divinci.app
ZONE_ID=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones?name=divinci.app" \
  -H "Authorization: Bearer $CF_API_TOKEN" \
  -H "Content-Type: application/json" | jq -r '.result[0].id')

if [ -z "$ZONE_ID" ] || [ "$ZONE_ID" == "null" ]; then
  echo "❌ Failed to get zone ID for divinci.app"
  exit 1
fi

echo "✅ Found zone ID: $ZONE_ID"

# Generate a new Cloudflare Origin Certificate
echo "📋 Generating a new Cloudflare Origin Certificate..."
RESPONSE=$(curl -s -X POST "https://api.cloudflare.com/client/v4/certificates" \
  -H "Authorization: Bearer $CF_API_TOKEN" \
  -H "Content-Type: application/json" \
  --data '{
    "hostnames": ["*.stage.divinci.app", "stage.divinci.app"],
    "requested_validity": 5475,
    "request_type": "origin-ecc",
    "csr": null
  }')

# Check if the request was successful
SUCCESS=$(echo "$RESPONSE" | jq -r '.success')
if [ "$SUCCESS" != "true" ]; then
  echo "❌ Failed to generate a new Cloudflare Origin Certificate"
  echo "$RESPONSE" | jq
  exit 1
fi

# Extract the certificate and private key
CERT=$(echo "$RESPONSE" | jq -r '.result.certificate')
KEY=$(echo "$RESPONSE" | jq -r '.result.private_key')

# Save the certificate and private key to files
echo "$CERT" > origin.crt
echo "$KEY" > origin.key

echo "✅ Generated a new Cloudflare Origin Certificate"
echo "📋 Certificate saved to origin.crt"
echo "📋 Private key saved to origin.key"

# Create directory for certificates
CERT_DIR="./private-keys/staging/certs/mtls"
mkdir -p "$CERT_DIR"

# Copy the certificate and private key to the certificate directory
echo "📋 Copying certificate files to $CERT_DIR..."
cp origin.crt "$CERT_DIR/origin.crt"
cp origin.key "$CERT_DIR/origin.key"

# Also save as server.crt and server.key for compatibility
cp origin.crt "$CERT_DIR/server.crt"
cp origin.key "$CERT_DIR/server.key"

echo "✅ Certificate files copied to $CERT_DIR"

# Update GCP secrets
echo "🔐 Updating GCP secrets..."

# Update server-crt secret
if gcloud secrets versions add server-crt --data-file="$CERT_DIR/server.crt" 2>/dev/null; then
  echo "✅ Updated server-crt secret"
else
  echo "❌ Failed to update server-crt secret"
  exit 1
fi

# Update server-key secret
if gcloud secrets versions add server-key --data-file="$CERT_DIR/server.key" 2>/dev/null; then
  echo "✅ Updated server-key secret"
else
  echo "❌ Failed to update server-key secret"
  exit 1
fi

echo "✅ GCP secrets updated successfully"
echo "📋 Next steps:"
echo "1. Restart your GCP Cloud Run service to pick up the new certificate"
echo "2. Run the tests again to verify that the Error 526 issue has been fixed"
