import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { RagVectorFileModel } from "@divinci-ai/server-models";
import { getParam, HTTP_ERRORS_WITH_STACK } from "@divinci-ai/server-utils";
import { condenseTarget, WHITE_LABEL_LOCATION } from "@divinci-ai/models";

export const getRagVectorFileRecord: RequestHandler = async function(req, res, next){
  console.log("🔍 Get file record endpoint hit", {
    method: req.method,
    params: req.params,
    objectKey: req.query.objectKey,
<<<<<<< HEAD
    isLocalDev: process.env.ENVIRONMENT === "local" || process.env.NODE_ENV === "development" || process.env.NODE_ENV === "local",
=======
    isLocalDev: process.env.ENVIRONMENT === "local" || process.env.ENVIRONMENT === "staging" || process.env.NODE_ENV === "development" || process.env.NODE_ENV === "local",
>>>>>>> WA-170_MCP
    hasWorkerDevHeader: req.headers["x-worker-local-dev"] === "true",
    hasWorkerAuthHeader: !!req.headers["cloudflare-worker-x-dev-auth"]
  });

<<<<<<< HEAD
  // For local development, explicitly log auth details
  if (process.env.ENVIRONMENT === "local" || process.env.NODE_ENV === "development" || process.env.NODE_ENV === "local") {
=======
  // For local development and staging, explicitly log auth details
  if (process.env.ENVIRONMENT === "local" || process.env.ENVIRONMENT === "staging" || process.env.NODE_ENV === "development" || process.env.NODE_ENV === "local") {
>>>>>>> WA-170_MCP
    console.log("🔑 Auth check in get record endpoint:", {
      hasAuth: !!(req as any).auth,
      authSub: (req as any).auth?.payload?.sub || "none",
      workerDevHeader: req.headers["x-worker-local-dev"],
      workerAuthHeader: req.headers["cloudflare-worker-x-dev-auth"] ? "present" : "missing"
    });

    // For local development with workflow calls, add auth if missing
    if (!((req as any).auth) && (req.headers["x-worker-local-dev"] === "true" || req.headers["cloudflare-worker-x-dev-auth"])) {
      console.log("🔧 Adding special auth for local workflow development");
      (req as any).auth = {
        payload: {
          sub: "local-dev-workflow-auth",
          iat: Math.floor(Date.now() / 1000),
          exp: Math.floor(Date.now() / 1000) + 3600,
          iss: "https://divinci.us.auth0.com/",
          aud: "https://api.divinci.ai"
        }
      };

      // Ensure we have the right permissions for the whitelabel
      (req as any).permissions = {
        whitelabel: {
          admin: true
        }
      };
    }
  }

  try {
    const whitelabelId = getParam(req, "whitelabelId");
    const objectKey = req.query.objectKey as string;

    if(!objectKey) {
      throw new HTTP_ERRORS_WITH_STACK.BAD_FORM("objectKey query parameter is required");
    }

    const target = condenseTarget({
      ...WHITE_LABEL_LOCATION,
      id: whitelabelId,
    });

    const fileRecord = await RagVectorFileModel.findOne({
      target,
      rawFileKey: objectKey
    });

    if(!fileRecord) {
      return res.json({ status: "success", data: null });
    }

    console.log("✅ File record found:", fileRecord);
    res.json({ status: "success", data: fileRecord });
  }catch(error) {
    console.error("❌ Error getting file record:", error);
    next(error);
  }
};
