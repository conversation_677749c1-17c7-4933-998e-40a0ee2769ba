import { OpenAI } from 'openai';
import { AudioTranscriptDoc, AudioTranscriptPopulated } from '@divinci-ai/models';

export interface SOAPSection {
  content: string;
  confidence: number;
  aiGenerated: boolean;
  generatedAt: string;
}

export interface SOAPNotesData {
  title: string;
  date: string;
  sections: {
    subjective: SOAPSection;
    objective: SOAPSection;
    assessment: SOAPSection;
    plan: SOAPSection;
  };
  metadata: {
    provider: string;
    duration: string;
    confidence: number;
    aiGenerated: boolean;
    modelUsed: string;
    generatedAt: string;
    processingTime: number;
  };
  warnings: string[];
}

export interface SOAPGenerationOptions {
  confidenceThreshold: number;
  providerName?: string;
  customPrompts?: {
    systemPrompt?: string;
    subjectivePrompt?: string;
    objectivePrompt?: string;
    assessmentPrompt?: string;
    planPrompt?: string;
  };
  includeTimestamps: boolean;
  speakerMapping?: Record<string, string>; // Map speaker names to roles (e.g., "Speaker 1" -> "Doctor")
}

export class SOAPGenerationService {
  private openai: OpenAI;
  private defaultPrompts: SOAPGenerationOptions['customPrompts'];

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    this.defaultPrompts = {
      systemPrompt: `You are a medical documentation assistant specializing in creating professional SOAP notes from audio transcripts of patient consultations.

Your task is to analyze the conversation and extract relevant information for each SOAP section:
- SUBJECTIVE: Patient's reported symptoms, concerns, and history
- OBJECTIVE: Observable findings, vital signs, examination results
- ASSESSMENT: Clinical interpretation and diagnosis
- PLAN: Treatment recommendations and follow-up actions

Guidelines:
1. Use professional medical terminology when appropriate
2. Maintain patient confidentiality (use generic terms like "Patient" instead of names)
3. Only include information that is clearly stated or reasonably inferred from the transcript
4. If information is unclear or missing, note this in your response
5. Provide confidence scores (0-100) for each section based on clarity of information
6. Flag any areas where the transcript is unclear or incomplete

Return your response as a JSON object with the specified structure.`,

      subjectivePrompt: `Extract SUBJECTIVE information from the transcript:
- Patient's chief complaint and symptoms
- Patient's description of their condition
- Relevant medical history mentioned
- Patient's concerns and questions
- Duration and characteristics of symptoms

Focus on what the patient reports about their condition.`,

      objectivePrompt: `Extract OBJECTIVE information from the transcript:
- Vital signs mentioned
- Physical examination findings
- Observable patient behavior or appearance
- Test results discussed
- Measurements or clinical observations

Focus on factual, observable information discussed during the consultation.`,

      assessmentPrompt: `Extract ASSESSMENT information from the transcript:
- Healthcare provider's clinical interpretation
- Differential diagnoses mentioned
- Clinical reasoning discussed
- Risk factors identified
- Severity assessments

Focus on the provider's professional evaluation and clinical thinking.`,

      planPrompt: `Extract PLAN information from the transcript:
- Treatment recommendations
- Medications prescribed or discussed
- Follow-up appointments scheduled
- Lifestyle modifications suggested
- Additional tests or referrals ordered
- Patient education provided

Focus on actionable next steps and treatment plans discussed.`
    };
  }

  async generateSOAP(
    audioTranscript: AudioTranscriptDoc & AudioTranscriptPopulated,
    options: SOAPGenerationOptions
  ): Promise<SOAPNotesData> {
    const startTime = Date.now();
    
    try {
      // Prepare transcript text
      const transcriptText = this.prepareTranscriptText(audioTranscript, options);
      
      // Generate SOAP sections
      const soapSections = await this.generateSOAPSections(transcriptText, options);
      
      // Calculate overall confidence
      const overallConfidence = this.calculateOverallConfidence(soapSections);
      
      // Generate warnings for low confidence areas
      const warnings = this.generateWarnings(soapSections, options.confidenceThreshold);
      
      const processingTime = Date.now() - startTime;
      
      return {
        title: `SOAP Notes - ${audioTranscript.userInfo.title}`,
        date: new Date().toLocaleDateString(),
        sections: soapSections,
        metadata: {
          provider: options.providerName || "Healthcare Provider",
          duration: this.formatDuration(audioTranscript),
          confidence: overallConfidence,
          aiGenerated: true,
          modelUsed: "gpt-4o",
          generatedAt: new Date().toISOString(),
          processingTime
        },
        warnings
      };
    } catch (error) {
      throw new Error(`SOAP generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private prepareTranscriptText(
    audioTranscript: AudioTranscriptDoc & AudioTranscriptPopulated,
    options: SOAPGenerationOptions
  ): string {
    const { samples } = audioTranscript;
    const { includeTimestamps, speakerMapping } = options;
    
    let transcriptText = "TRANSCRIPT:\n\n";
    
    // Add metadata
    transcriptText += `Duration: ${this.formatDuration(audioTranscript)}\n`;
    transcriptText += `Speakers: ${this.getSpeakerList(samples, speakerMapping)}\n\n`;
    
    // Add conversation
    transcriptText += "CONVERSATION:\n";
    
    samples.forEach((sample, index) => {
      const speakerName = speakerMapping?.[sample.speaker] || sample.speaker;
      const timestamp = includeTimestamps ? `[${this.formatTime(sample.start)}] ` : '';
      transcriptText += `${timestamp}${speakerName}: ${sample.text}\n`;
    });
    
    return transcriptText;
  }

  private async generateSOAPSections(
    transcriptText: string,
    options: SOAPGenerationOptions
  ): Promise<SOAPNotesData['sections']> {
    const prompts = { ...this.defaultPrompts, ...options.customPrompts };
    
    const completion = await this.openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: prompts.systemPrompt!
        },
        {
          role: "user",
          content: `${transcriptText}

Please analyze this transcript and create SOAP notes. For each section, provide:
1. The content for that section
2. A confidence score (0-100) based on how clear and complete the information is

SUBJECTIVE INSTRUCTIONS:
${prompts.subjectivePrompt}

OBJECTIVE INSTRUCTIONS:
${prompts.objectivePrompt}

ASSESSMENT INSTRUCTIONS:
${prompts.assessmentPrompt}

PLAN INSTRUCTIONS:
${prompts.planPrompt}

Return your response as a JSON object with this structure:
{
  "subjective": {
    "content": "...",
    "confidence": 85
  },
  "objective": {
    "content": "...",
    "confidence": 90
  },
  "assessment": {
    "content": "...",
    "confidence": 75
  },
  "plan": {
    "content": "...",
    "confidence": 80
  }
}`
        }
      ],
      response_format: { type: "json_object" },
      temperature: 0.3,
      max_tokens: 2000
    });

    const response = completion.choices[0]?.message?.content;
    if (!response) {
      throw new Error('No response from OpenAI');
    }

    const parsedResponse = JSON.parse(response);
    const generatedAt = new Date().toISOString();
    
    return {
      subjective: {
        ...parsedResponse.subjective,
        aiGenerated: true,
        generatedAt
      },
      objective: {
        ...parsedResponse.objective,
        aiGenerated: true,
        generatedAt
      },
      assessment: {
        ...parsedResponse.assessment,
        aiGenerated: true,
        generatedAt
      },
      plan: {
        ...parsedResponse.plan,
        aiGenerated: true,
        generatedAt
      }
    };
  }

  private calculateOverallConfidence(sections: SOAPNotesData['sections']): number {
    const confidences = [
      sections.subjective.confidence,
      sections.objective.confidence,
      sections.assessment.confidence,
      sections.plan.confidence
    ];
    
    return Math.round(confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length);
  }

  private generateWarnings(
    sections: SOAPNotesData['sections'],
    confidenceThreshold: number
  ): string[] {
    const warnings: string[] = [];
    
    Object.entries(sections).forEach(([sectionName, section]) => {
      if (section.confidence < confidenceThreshold) {
        warnings.push(
          `${sectionName.toUpperCase()} section has low confidence (${section.confidence}%) - please review carefully`
        );
      }
    });
    
    return warnings;
  }

  private formatDuration(audioTranscript: AudioTranscriptDoc & AudioTranscriptPopulated): string {
    const { samples } = audioTranscript;
    if (samples.length === 0) return "Unknown";
    
    const totalSeconds = samples[samples.length - 1].end;
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = Math.floor(totalSeconds % 60);
    
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  private getSpeakerList(
    samples: any[],
    speakerMapping?: Record<string, string>
  ): string {
    const speakers = [...new Set(samples.map(s => s.speaker))];
    return speakers
      .map(speaker => speakerMapping?.[speaker] || speaker)
      .join(", ");
  }

  private formatTime(seconds: number): string {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }

  // Method to regenerate a specific section
  async regenerateSection(
    transcriptText: string,
    sectionName: keyof SOAPNotesData['sections'],
    options: SOAPGenerationOptions
  ): Promise<SOAPSection> {
    const prompts = { ...this.defaultPrompts, ...options.customPrompts };
    const sectionPrompt = prompts[`${sectionName}Prompt` as keyof typeof prompts];
    
    const completion = await this.openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: prompts.systemPrompt!
        },
        {
          role: "user",
          content: `${transcriptText}

Please focus specifically on the ${sectionName.toUpperCase()} section:
${sectionPrompt}

Return your response as a JSON object with this structure:
{
  "content": "...",
  "confidence": 85
}`
        }
      ],
      response_format: { type: "json_object" },
      temperature: 0.3,
      max_tokens: 500
    });

    const response = completion.choices[0]?.message?.content;
    if (!response) {
      throw new Error('No response from OpenAI');
    }

    const parsedResponse = JSON.parse(response);
    
    return {
      ...parsedResponse,
      aiGenerated: true,
      generatedAt: new Date().toISOString()
    };
  }
}
