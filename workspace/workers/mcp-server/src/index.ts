/**
 * <PERSON><PERSON>ci AI MCP Server
 * Model Context Protocol server providing access to Divinci AI services
 */

import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { OAuthProvider } from './auth/oauth-provider';
import { <PERSON>PRouter } from './mcp/router';
import { DivinciTools<PERSON>andler } from './tools/divinci-tools';

// Environment interface
interface Env {
  // Auth0 Configuration
  AUTH0_DOMAIN: string;
  AUTH0_CLIENT_ID: string;
  AUTH0_CLIENT_SECRET: string;
  
  // Divinci API Configuration
  DIVINCI_API_BASE_URL: string;
  DIVINCI_API_KEY: string;
  
  // Environment
  ENVIRONMENT: string;
  MCP_SERVER_VERSION: string;
  CLOUDFLARE_ACCOUNT_ID: string;
}

// Create Hono app
const app = new Hono<{ Bindings: Env }>();

// CORS middleware
app.use('*', cors({
  origin: ['http://localhost:5173', 'https://playground.ai.cloudflare.com'],
  allowMethods: ['GET', 'POST', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
}));

// Health check endpoint
app.get('/health', (c) => {
  return c.json({
    success: true,
    service: 'divinci-mcp-server',
    environment: c.env.ENVIRONMENT,
    version: c.env.MCP_SERVER_VERSION,
    timestamp: new Date().toISOString(),
    status: 'healthy'
  });
});

// Initialize OAuth provider and MCP router
app.route('/', new OAuthProvider({
  apiRoute: '/sse',
  apiHandler: new MCPRouter(new DivinciToolsHandler()),
  defaultHandler: async (c) => {
    return c.json({
      name: 'Divinci AI MCP Server',
      version: c.env.MCP_SERVER_VERSION,
      description: 'Model Context Protocol server for Divinci AI services',
      endpoints: {
        sse: '/sse',
        authorize: '/authorize',
        token: '/token',
        register: '/register'
      }
    });
  },
  authorizeEndpoint: '/authorize',
  tokenEndpoint: '/token',
  clientRegistrationEndpoint: '/register',
}));

export default app;
