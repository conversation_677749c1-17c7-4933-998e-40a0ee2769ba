/**
 * Dropbox Sync Workflow
 *
 * Main workflow for syncing Dropbox files to the RAG system.
 * Orchestrates the complete pipeline from download to vectorization.
 */

// Note: Cloudflare Workflows types would be imported here in a real deployment
// For now, we'll use basic types
type WorkflowEntrypoint<T, U> = any;
type WorkflowStep = any;
type WorkflowEvent<T> = { params: T };
import {
  Env,
  DropboxSyncWorkflowEvent,
  StepContext,
  DropboxSyncJobStatus
} from "../types";

// Import workflow steps
import { initializeWorkflow } from "../steps/initialize-workflow";
import { downloadFiles } from "../steps/download-files";
import { processAudio } from "../steps/process-audio";
import { triggerChunksWorkflow } from "../steps/trigger-chunks-workflow";
import { updateDatabase } from "../steps/update-database";

export class DropboxSyncWorkflow extends WorkflowEntrypoint<Env, DropboxSyncWorkflowEvent> {
  async run(event: WorkflowEvent<DropboxSyncWorkflowEvent>, step: WorkflowStep): Promise<any> {
    const { params } = event;

    console.log("🚀 Starting Dropbox Sync Workflow", {
      jobId: params.jobId,
      whitelabelId: params.whitelabelId,
      totalFiles: params.sourceFiles.length,
      targetType: params.targetType,
      instanceId: params.instanceId
    });

    // Create step context
    const context: StepContext = {
      env: this.env,
      jobId: params.jobId,
      whitelabelId: params.whitelabelId,
      auth0Token: params.auth0Token,
      accessToken: params.accessToken
    };

    try {
      // Step 1: Initialize workflow and validate inputs
      const initResult = await initializeWorkflow(
        step,
        context,
        params.sourceFiles,
        params.processingConfig,
        params.targetType,
        params.instanceId
      );

      console.log("✅ Workflow initialized", {
        totalFiles: initResult.validatedFiles.length,
        processingConfig: initResult.processingConfig
      });

      // Step 2: Download files from Dropbox to R2
      const downloadResult = await downloadFiles(
        step,
        context,
        initResult.validatedFiles
      );

      console.log("✅ Files downloaded", {
        successful: downloadResult.downloadedFiles.length,
        failed: downloadResult.failedDownloads.length,
        totalBytes: downloadResult.totalBytes
      });

      // Step 3: Process audio (transcription + diarization)
      const audioResult = await processAudio(
        step,
        context,
        downloadResult.downloadedFiles,
        initResult.processingConfig
      );

      console.log("✅ Audio processed", {
        successful: audioResult.processedFiles.length,
        failed: audioResult.failedProcessing.length
      });

      // Step 4: Trigger chunks workflow for RAG integration
      const chunksResult = await triggerChunksWorkflow(
        step,
        context,
        audioResult.processedFiles,
        initResult.processingConfig,
        params.targetType
      );

      console.log("✅ Chunks workflows triggered", {
        successful: chunksResult.triggeredWorkflows.length,
        failed: chunksResult.failedTriggers.length
      });

      // Collect all failed files
      const allFailedFiles = [
        ...downloadResult.failedDownloads,
        ...audioResult.failedProcessing,
        ...chunksResult.failedTriggers
      ];

      // Step 5: Update database with final results
      const dbResult = await updateDatabase(
        step,
        context,
        audioResult.processedFiles,
        chunksResult.triggeredWorkflows,
        allFailedFiles,
        initResult.validatedFiles.length
      );

      console.log("✅ Database updated", {
        updatedSyncJob: dbResult.updatedSyncJob,
        updatedFileMappings: dbResult.updatedFileMappings,
        failedUpdates: dbResult.failedUpdates.length
      });

      // Calculate final results
      const totalFiles = initResult.validatedFiles.length;
      const successfulFiles = audioResult.processedFiles.length;
      const failedFiles = allFailedFiles.length;
      const finalStatus = failedFiles === totalFiles ?
        DropboxSyncJobStatus.FAILED :
        DropboxSyncJobStatus.COMPLETED;

      const workflowResult = {
        success: finalStatus === DropboxSyncJobStatus.COMPLETED,
        jobId: params.jobId,
        workflowId: params.instanceId,
        status: finalStatus,
        summary: {
          totalFiles,
          successfulFiles,
          failedFiles,
          totalBytes: downloadResult.totalBytes,
          processingDuration: Date.now() - new Date(params.timestamp).getTime()
        },
        results: {
          downloads: {
            successful: downloadResult.downloadedFiles.length,
            failed: downloadResult.failedDownloads.length,
            totalBytes: downloadResult.totalBytes
          },
          audioProcessing: {
            successful: audioResult.processedFiles.length,
            failed: audioResult.failedProcessing.length,
            transcriptions: audioResult.processedFiles.filter(f => f.transcriptionId).length,
            diarizations: audioResult.processedFiles.filter(f => f.diarizationId).length
          },
          chunksWorkflows: {
            triggered: chunksResult.triggeredWorkflows.length,
            failed: chunksResult.failedTriggers.length,
            workflowIds: chunksResult.triggeredWorkflows.map(w => w.workflowId)
          },
          database: {
            syncJobUpdated: dbResult.updatedSyncJob,
            fileMappingsUpdated: dbResult.updatedFileMappings,
            updateFailures: dbResult.failedUpdates.length
          }
        },
        errors: [
          ...downloadResult.failedDownloads.map(f => `Download failed: ${f.fileName} - ${f.error}`),
          ...audioResult.failedProcessing.map(f => `Audio processing failed: ${f.fileName} - ${f.error}`),
          ...chunksResult.failedTriggers.map(f => `Chunks workflow failed: ${f.fileName} - ${f.error}`),
          ...dbResult.failedUpdates
        ],
        completedAt: new Date().toISOString()
      };

      console.log("🎉 Dropbox Sync Workflow completed", workflowResult.summary);

      return workflowResult;

    } catch (error) {
      console.error("❌ Dropbox Sync Workflow failed", error);

      // Update job status to failed
      try {
        await this.updateJobStatusOnError(context, error);
      } catch (updateError) {
        console.error("Failed to update job status on error:", updateError);
      }

      const errorResult = {
        success: false,
        jobId: params.jobId,
        workflowId: params.instanceId,
        status: DropboxSyncJobStatus.FAILED,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        failedAt: new Date().toISOString()
      };

      // Re-throw to mark workflow as failed
      throw new Error(`Dropbox sync workflow failed: ${errorResult.error}`);
    }
  }

  /**
   * Update job status when workflow fails
   */
  private async updateJobStatusOnError(context: StepContext, error: any): Promise<void> {
    try {
      const response = await fetch(`${context.env.API_HOST}/ux/whitelabel/${context.whitelabelId}/dropbox/sync/update-job`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${context.auth0Token}`,
          "cloudflare-worker-x-dev-auth": context.env.CLOUDFLARE_WORKER_X_AUTH_DEV || ""
        },
        body: JSON.stringify({
          jobId: context.jobId,
          status: DropboxSyncJobStatus.FAILED,
          errors: [error instanceof Error ? error.message : String(error)],
          completedAt: new Date().toISOString()
        })
      });

      if (!response.ok) {
        console.warn(`Failed to update job status on error: ${response.status}`);
      }
    } catch (updateError) {
      console.warn("Failed to update job status on error:", updateError);
    }
  }
}
