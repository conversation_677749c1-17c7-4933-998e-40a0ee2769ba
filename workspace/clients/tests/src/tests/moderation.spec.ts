/**
 * Moderation API Tests
 *
 * This file contains tests for the Moderation API.
 */

import { test, expect } from '@playwright/test';
import { ApiClient } from '../api/api-client';
import { getAuthFileForRole } from '../auth/auth-utils';
import { config } from '../config/config';

// Mock Moderation API responses
const mockModerationResponse = {
  _id: 'mock-moderation-123',
  content: 'This is a test message',
  result: {
    flagged: false,
    categories: {
      sexual: false,
      hate: false,
      harassment: false,
      'self-harm': false,
      'sexual/minors': false,
      'hate/threatening': false,
      'violence/graphic': false,
      'self-harm/intent': false,
      'self-harm/instructions': false,
      'harassment/threatening': false,
      violence: false
    },
    category_scores: {
      sexual: 0.0001,
      hate: 0.0001,
      harassment: 0.0001,
      'self-harm': 0.0001,
      'sexual/minors': 0.0001,
      'hate/threatening': 0.0001,
      'violence/graphic': 0.0001,
      'self-harm/intent': 0.0001,
      'self-harm/instructions': 0.0001,
      'harassment/threatening': 0.0001,
      violence: 0.0001
    }
  },
  createdAt: new Date().toISOString()
};

// Mock API client for Moderation
class MockModerationClient extends ApiClient {
  constructor() {
    super(config.apiBaseUrl, getAuthFileForRole('admin'));
  }

  async get(path: string) {
    if (path === 'moderation') {
      return [mockModerationResponse];
    } else if (path.startsWith('moderation/')) {
      return mockModerationResponse;
    }
    return super.get(path);
  }

  async post(path: string, data: any) {
    if (path === 'moderation') {
      return {
        ...mockModerationResponse,
        content: data.content || 'Test content'
      };
    }
    return super.post(path, data);
  }
}

test.describe('Moderation API', () => {
  let client: MockModerationClient;

  test.beforeEach(() => {
    client = new MockModerationClient();
  });

  // Edge case tests
  test.describe('Edge Cases', () => {
    test('should handle content with multiple languages', async () => {
      // Create content with multiple languages
      const multilingualContent = 'English text. Español. 中文. Русский. العربية. हिन्दी. 日本語.';

      // Override the post method to handle multilingual content
      const originalPost = client.post;
      client.post = async (path, data) => {
        if (path === 'moderation') {
          return {
            ...mockModerationResponse,
            content: data.content
          };
        }
        return originalPost.call(client, path, data);
      };

      // Moderate multilingual content
      const moderation = await client.post('moderation', {
        content: multilingualContent
      });

      // Verify that the multilingual content was preserved
      expect(moderation).toBeDefined();
      expect(moderation.content).toBe(multilingualContent);

      // Restore the original post method
      client.post = originalPost;
    });

    test('should handle content with special characters and symbols', async () => {
      // Create content with special characters and symbols
      const specialContent = '!@#$%^&*()_+{}[]|\\:;"\'<>,.?/~` Special Characters';

      // Moderate content with special characters
      const moderation = await client.post('moderation', {
        content: specialContent
      });

      // Verify that the special characters were preserved
      expect(moderation).toBeDefined();
      expect(moderation.content).toBe(specialContent);
    });

    test('should handle content with emoji characters', async () => {
      // Create content with emoji characters
      const emojiContent = 'Message with emojis: 😀 🚀 🌍 🎉 👍 🔥 💯 🤖 🧠 💻';

      // Moderate content with emoji
      const moderation = await client.post('moderation', {
        content: emojiContent
      });

      // Verify that the emoji were preserved
      expect(moderation).toBeDefined();
      expect(moderation.content).toBe(emojiContent);
    });

    test('should handle content with code snippets', async () => {
      // Create content with code snippets
      const codeContent = `
        Here's a JavaScript code snippet:

        \`\`\`javascript
        function hello() {
          console.log("Hello, world!");
          return 42;
        }
        \`\`\`

        And here's some HTML:

        \`\`\`html
        <div class="container">
          <h1>Hello, world!</h1>
          <p>This is a <strong>test</strong>.</p>
        </div>
        \`\`\`
      `;

      // Override the post method to handle code content
      const originalPost = client.post;
      client.post = async (path, data) => {
        if (path === 'moderation') {
          return {
            ...mockModerationResponse,
            content: data.content
          };
        }
        return originalPost.call(client, path, data);
      };

      // Moderate content with code snippets
      const moderation = await client.post('moderation', {
        content: codeContent
      });

      // Verify that the code snippets were preserved
      expect(moderation).toBeDefined();
      expect(moderation.content).toBe(codeContent);
      expect(moderation.content).toContain('```javascript');
      expect(moderation.content).toContain('```html');

      // Restore the original post method
      client.post = originalPost;
    });

    test('should handle boundary case of empty content', async () => {
      // Override the post method to handle empty content
      const originalPost = client.post;
      client.post = async (path, data) => {
        if (path === 'moderation' && data.content === '') {
          return {
            ...mockModerationResponse,
            content: '',
            result: {
              ...mockModerationResponse.result,
              flagged: false,
              categories: Object.fromEntries(
                Object.entries(mockModerationResponse.result.categories).map(([k]) => [k, false])
              ),
              category_scores: Object.fromEntries(
                Object.entries(mockModerationResponse.result.category_scores).map(([k]) => [k, 0])
              )
            }
          };
        }
        return originalPost.call(client, path, data);
      };

      // Moderate empty content
      const moderation = await client.post('moderation', {
        content: ''
      });

      // Verify the response for empty content
      expect(moderation).toBeDefined();
      expect(moderation.content).toBe('');
      expect(moderation.result.flagged).toBe(false);

      // All category scores should be 0
      Object.values(moderation.result.category_scores).forEach(score => {
        expect(score).toBe(0);
      });

      // Restore the original post method
      client.post = originalPost;
    });

    test('should handle content with potentially offensive material', async () => {
      // Create content with potentially offensive material
      const offensiveContent = 'This message contains offensive language that should be flagged.';

      // Override the post method to handle offensive content
      const originalPost = client.post;
      client.post = async (path, data) => {
        if (path === 'moderation' && data.content === offensiveContent) {
          return {
            ...mockModerationResponse,
            content: data.content,
            result: {
              ...mockModerationResponse.result,
              flagged: true,
              categories: {
                ...mockModerationResponse.result.categories,
                hate: true
              },
              category_scores: {
                ...mockModerationResponse.result.category_scores,
                hate: 0.92
              }
            }
          };
        }
        return originalPost.call(client, path, data);
      };

      // Moderate potentially offensive content
      const moderation = await client.post('moderation', {
        content: offensiveContent
      });

      // Verify that the content was flagged
      expect(moderation).toBeDefined();
      expect(moderation.content).toBe(offensiveContent);
      expect(moderation.result.flagged).toBe(true);
      expect(moderation.result.categories.hate).toBe(true);
      expect(moderation.result.category_scores.hate).toBeGreaterThan(0.9);

      // Restore the original post method
      client.post = originalPost;
    });
  });

  // Error condition tests
  test.describe('Error Conditions', () => {
    test('should handle invalid input when moderating content', async () => {
      // Override the post method to simulate an error
      const originalPost = client.post;
      client.post = async (path, data) => {
        if (path === 'moderation' && (!data || !data.content)) {
          throw new Error('Invalid input: content is required');
        }
        return originalPost.call(client, path, data);
      };

      // Try to moderate with missing content
      let error: Error | null = null;
      try {
        await client.post('moderation', {});
      } catch (e) {
        error = e as Error;
      }

      // Verify that an error was thrown
      expect(error).not.toBeNull();
      expect(error?.message).toContain('content is required');

      // Restore the original post method
      client.post = originalPost;
    });

    test('should handle not found error when getting a non-existent moderation result', async () => {
      // Override the get method to simulate a not found error
      const originalGet = client.get;
      client.get = async (path) => {
        if (path === 'moderation/non-existent-id') {
          throw new Error('Moderation result not found: non-existent-id');
        }
        return originalGet.call(client, path);
      };

      // Try to get a non-existent moderation result
      let error: Error | null = null;
      try {
        await client.get('moderation/non-existent-id');
      } catch (e) {
        error = e as Error;
      }

      // Verify that an error was thrown
      expect(error).not.toBeNull();
      expect(error?.message).toContain('not found');

      // Restore the original get method
      client.get = originalGet;
    });

    test('should handle rate limit exceeded error', async () => {
      // Override the post method to simulate a rate limit error
      const originalPost = client.post;
      client.post = async (path, data) => {
        if (path === 'moderation' && data.content === 'rate-limit-test') {
          throw new Error('Rate limit exceeded: Too many requests');
        }
        return originalPost.call(client, path, data);
      };

      // Try to make a request that triggers a rate limit error
      let error: Error | null = null;
      try {
        await client.post('moderation', { content: 'rate-limit-test' });
      } catch (e) {
        error = e as Error;
      }

      // Verify that an error was thrown
      expect(error).not.toBeNull();
      expect(error?.message).toContain('Rate limit exceeded');

      // Restore the original post method
      client.post = originalPost;
    });

    test('should handle content too large error', async () => {
      // Create a very large content string
      const largeContent = 'a'.repeat(100000); // 100KB of text

      // Override the post method to simulate a content too large error
      const originalPost = client.post;
      client.post = async (path, data) => {
        if (path === 'moderation' && data.content && data.content.length > 10000) {
          throw new Error('Content too large: Maximum content size is 10KB');
        }
        return originalPost.call(client, path, data);
      };

      // Try to moderate content that is too large
      let error: Error | null = null;
      try {
        await client.post('moderation', { content: largeContent });
      } catch (e) {
        error = e as Error;
      }

      // Verify that an error was thrown
      expect(error).not.toBeNull();
      expect(error?.message).toContain('Content too large');

      // Restore the original post method
      client.post = originalPost;
    });

    test('should handle server error when moderation service is unavailable', async () => {
      // Override the post method to simulate a server error
      const originalPost = client.post;
      client.post = async (path, data) => {
        if (path === 'moderation' && data.content === 'server-error-test') {
          throw new Error('Service Unavailable: Moderation service is currently unavailable');
        }
        return originalPost.call(client, path, data);
      };

      // Try to make a request that triggers a server error
      let error: Error | null = null;
      try {
        await client.post('moderation', { content: 'server-error-test' });
      } catch (e) {
        error = e as Error;
      }

      // Verify that an error was thrown
      expect(error).not.toBeNull();
      expect(error?.message).toContain('Service Unavailable');

      // Restore the original post method
      client.post = originalPost;
    });
  });

  test('should moderate content', async () => {
    const moderation = await client.post('moderation', {
      content: 'This is a test message'
    });

    expect(moderation).toBeDefined();
    expect(moderation._id).toBeDefined();
    expect(moderation.content).toBe('This is a test message');
    expect(moderation.result).toBeDefined();
    expect(moderation.result.flagged).toBe(false);
    expect(moderation.result.categories).toBeDefined();
    expect(moderation.result.category_scores).toBeDefined();
  });

  test('should get all moderation results', async () => {
    const moderations = await client.get('moderation');

    expect(moderations).toBeInstanceOf(Array);
    expect(moderations.length).toBeGreaterThan(0);
    expect(moderations[0]._id).toBeDefined();
    expect(moderations[0].content).toBeDefined();
  });

  test('should get a moderation result by ID', async () => {
    const moderation = await client.get('moderation/mock-moderation-123');

    expect(moderation).toBeDefined();
    expect(moderation._id).toBe('mock-moderation-123');
    expect(moderation.content).toBe('This is a test message');
    expect(moderation.result).toBeDefined();
  });

  test('should flag inappropriate content', async () => {
    // Create a custom response for this test
    const flaggedResponse = {
      ...mockModerationResponse,
      content: 'This is inappropriate content',
      result: {
        ...mockModerationResponse.result,
        flagged: true,
        categories: {
          ...mockModerationResponse.result.categories,
          hate: true
        },
        category_scores: {
          ...mockModerationResponse.result.category_scores,
          hate: 0.92
        }
      }
    };

    // Override the post method for this test
    const originalPost = client.post;
    client.post = async (path, data) => {
      if (path === 'moderation') {
        return flaggedResponse;
      }
      return Promise.resolve({});
    };

    const moderation = await client.post('moderation', {
      content: 'This is inappropriate content'
    });

    expect(moderation).toBeDefined();
    expect(moderation.result.flagged).toBe(true);
    expect(moderation.result.categories.hate).toBe(true);
    expect(moderation.result.category_scores.hate).toBeGreaterThan(0.9);

    // Restore the original post method
    client.post = originalPost;
  });
});
