import { Page } from '@playwright/test';

/**
 * Create a new whitelabel
 *
 * @param page The Playwright page
 * @param name The name of the whitelabel
 * @returns The ID of the created whitelabel
 */
export async function createWhitelabel(page: Page, name: string): Promise<string> {
  console.log(`Creating or finding whitelabel: ${name}`);

  // Navigate to the whitelabel page
  await page.goto(`${process.env.WEB_URL || 'http://localhost:8080'}/white-label`);
  await page.waitForLoadState('networkidle');

  // Check if there are any existing whitelabels
  const whitelabelCards = page.locator('.whitelabel-ai-card');
  const count = await whitelabelCards.count();

  let whitelabelId = null;

  if (count > 0) {
    console.log(`Found ${count} existing whitelabels, using the first one`);

    // Click on the first whitelabel
    await whitelabelCards.first().click();
    await page.waitForLoadState('networkidle');

    // Extract the ID from the URL
    const url = page.url();
    const urlParts = url.split('/');
    const whitelabelIndex = urlParts.findIndex(part => part === 'white-label');
    if (whitelabelIndex !== -1 && whitelabelIndex + 1 < urlParts.length) {
      whitelabelId = urlParts[whitelabelIndex + 1];
      console.log(`Using existing whitelabel with ID: ${whitelabelId}`);
      return whitelabelId;
    }
  } else {
    console.log('No existing whitelabels found, creating a new one');

    // Click the "Create Whitelabel" button
    await page.click('text=Create Whitelabel, button:has-text("Create Whitelabel")');
    await page.waitForLoadState('networkidle');

    // Fill in the whitelabel form
    await page.fill('input.input[type="text"]', name);
    await page.fill('textarea.textarea', 'This is a test whitelabel created by Playwright E2E test');

    // Check the terms and conditions checkbox
    await page.check('input[type="checkbox"]');

    // Set up a listener for the API response to capture the whitelabel ID
    await page.route('**/white-label', async (route, _request) => {
      // Let the request continue
      await route.continue();

      // Wait for the response
      const response = await route.request().response();
      if (response) {
        try {
          const responseBody = await response.json();
          console.log('Whitelabel API Response:', responseBody);

          // Extract the whitelabel ID from the response
          if (responseBody && responseBody._id) {
            whitelabelId = responseBody._id;
            console.log(`Captured whitelabel ID from API response: ${whitelabelId}`);
          }
        } catch (error) {
          console.error('Error parsing whitelabel API response:', error);
        }
      }
    });

    // Submit the form
    await page.click('button.create-whitelabel-button, button:has-text("🌱 Create AI")');

    // Wait for the form submission to complete
    await page.waitForLoadState('networkidle');
  }

  // If we didn't get the whitelabel ID from the API response, try to get it from the URL
  if (!whitelabelId) {
    const url = page.url();
    console.log(`Current URL: ${url}`);

    // Check if we're on the whitelabel details page
    if (url.includes('/white-label/') && !url.includes('/data-source/')) {
      const urlParts = url.split('/');
      // Find the index of 'white-label' in the URL parts
      const whitelabelIndex = urlParts.findIndex(part => part === 'white-label');
      if (whitelabelIndex !== -1 && whitelabelIndex + 1 < urlParts.length) {
        // The whitelabel ID is the part right after 'white-label'
        const potentialId = urlParts[whitelabelIndex + 1];
        // Make sure it's not another route segment
        if (potentialId && potentialId !== 'data-source' && potentialId !== 'white-label') {
          whitelabelId = potentialId;
          console.log(`Extracted whitelabel ID from URL: ${whitelabelId}`);
        }
      }
    }

    // If we still don't have a valid ID, navigate to the whitelabel list page and get the first one
    if (!whitelabelId || whitelabelId === 'white-label') {
      console.log('Could not extract valid whitelabel ID from URL, navigating to whitelabel list');
      await page.goto(`${process.env.WEB_URL || 'http://localhost:8080'}/white-label`);
      await page.waitForLoadState('networkidle');

      // Try to get the whitelabel ID from the API response data
      try {
        // Wait for the whitelabel list to load
        await page.waitForTimeout(2000);

        // Get the whitelabel list from the page
        const whitelabelListJson = await page.evaluate(() => {
          // Look for a script tag with the whitelabel data
          const scriptTags = Array.from(document.querySelectorAll('script'));
          for (const script of scriptTags) {
            const content = script.textContent || '';
            if (content.includes('window.__INITIAL_STATE__')) {
              // Extract the JSON data
              const match = content.match(/window\.__INITIAL_STATE__\s*=\s*({.*});/);
              if (match && match[1]) {
                return match[1];
              }
            }
          }
          return null;
        });

        if (whitelabelListJson) {
          const whitelabelData = JSON.parse(whitelabelListJson);
          if (whitelabelData && whitelabelData.whitelabel && whitelabelData.whitelabel.whitelabels) {
            const whitelabels = whitelabelData.whitelabel.whitelabels;
            if (whitelabels.length > 0) {
              whitelabelId = whitelabels[0].doc._id;
              console.log(`Extracted whitelabel ID from page data: ${whitelabelId}`);
            }
          }
        }
      } catch (error) {
        console.log(`Error extracting whitelabel ID from page data: ${error instanceof Error ? error.message : String(error)}`);
      }

      // If we still don't have a valid ID, try clicking on the first whitelabel card
      if (!whitelabelId || whitelabelId === 'white-label') {
        // Check if there are any whitelabels
        const whitelabelCards = page.locator('.whitelabel-ai-card');
        const count = await whitelabelCards.count();

        if (count > 0) {
<<<<<<< HEAD
          // Click on the first whitelabel
          console.log('Clicking on the first whitelabel');
          await whitelabelCards.first().click();
          await page.waitForLoadState('networkidle');

          // Now extract the ID from the URL
          const newUrl = page.url();
          const newUrlParts = newUrl.split('/');
          const newWhitelabelIndex = newUrlParts.findIndex(part => part === 'white-label');
          if (newWhitelabelIndex !== -1 && newWhitelabelIndex + 1 < newUrlParts.length) {
            whitelabelId = newUrlParts[newWhitelabelIndex + 1];
            console.log(`Extracted whitelabel ID from new URL: ${whitelabelId}`);
=======
          // Extract the whitelabel ID from the first card's link href before clicking
          console.log('Extracting whitelabel ID from the first whitelabel card');

          const firstCard = whitelabelCards.first();
          const clickableLink = firstCard.locator('a.clickable-text');

          if (await clickableLink.count() > 0) {
            const href = await clickableLink.getAttribute('href');
            console.log(`Found whitelabel link href: ${href}`);

            if (href) {
              // Extract ID from href like "/white-label/68433a457e37301276cf3a3c"
              const hrefParts = href.split('/');
              const whitelabelIndex = hrefParts.findIndex(part => part === 'white-label');
              if (whitelabelIndex !== -1 && whitelabelIndex + 1 < hrefParts.length) {
                const potentialId = hrefParts[whitelabelIndex + 1];
                // Validate it looks like a MongoDB ObjectId (24 hex characters)
                if (potentialId && potentialId.length >= 12 && /^[a-f0-9]+$/i.test(potentialId)) {
                  whitelabelId = potentialId;
                  console.log(`Extracted whitelabel ID from href: ${whitelabelId}`);
                } else {
                  console.log(`Invalid whitelabel ID format in href: ${potentialId}`);
                }
              }
            }
          }

          // If we couldn't extract the ID from href, fall back to clicking
          if (!whitelabelId) {
            console.log('Could not extract ID from href, clicking on the first whitelabel');
            await firstCard.click();
            await page.waitForLoadState('networkidle');

            // Now extract the ID from the URL
            const newUrl = page.url();
            console.log(`URL after clicking: ${newUrl}`);
            const newUrlParts = newUrl.split('/');
            const newWhitelabelIndex = newUrlParts.findIndex(part => part === 'white-label');
            if (newWhitelabelIndex !== -1 && newWhitelabelIndex + 1 < newUrlParts.length) {
              const potentialId = newUrlParts[newWhitelabelIndex + 1];
              if (potentialId && potentialId.length >= 12 && /^[a-f0-9]+$/i.test(potentialId)) {
                whitelabelId = potentialId;
                console.log(`Extracted whitelabel ID from URL: ${whitelabelId}`);
              } else {
                console.log(`Invalid whitelabel ID format in URL: ${potentialId}`);
              }
            }
>>>>>>> WA-170_MCP
          }
        }
      }
    }
  }

  // Final check to ensure we have a valid ID
  if (!whitelabelId || whitelabelId === 'white-label') {
    throw new Error('Could not extract a valid whitelabel ID');
  }

  return whitelabelId;
}
<<<<<<< HEAD
=======

/**
 * Get an existing whitelabel ID or create a new one if none exist
 * This is a convenience function that wraps createWhitelabel with a default name
 *
 * @param page The Playwright page
 * @returns The ID of an existing or newly created whitelabel
 */
export async function getWhitelabelId(page: Page): Promise<string> {
  return await createWhitelabel(page, 'E2E Test Whitelabel');
}
>>>>>>> WA-170_MCP
