import { WorkflowStep } from "cloudflare:workers";
import { StepContext, FileInfo, R2ValidationResult, WorkflowFile } from "./types";
import { createFixedStorageClient } from "../../utils/fixed-storage-client";
<<<<<<< HEAD
=======
import { createFixedStorageClient } from "../../utils/fixed-storage-client";
>>>>>>> WA-170_MCP

/**
 * Validates and gets R2 file metadata
 *
 * IMPORTANT: This function has been refactored to ALWAYS treat files as new uploads
 * and not try to find similar files in MinIO/R2. This ensures consistent behavior
 * between local and production environments.
 */
export async function validateAndGetR2File(
  step: WorkflowStep,
  context: StepContext,
  fileInfo: FileInfo,
  files: WorkflowFile[]
): Promise<R2ValidationResult>{
  return await step.do("validateAndGetR2File", async ()=>{
    const { objectKey, fileName } = fileInfo;

    console.log("🔍 Debug - files array:", {
      filesLength: files.length,
      file0: files[0] ? {
        fileId: files[0].fileId,
        hasFormData: !!files[0].formData,
        formDataType: files[0].formData ? typeof files[0].formData : "undefined"
      } : "undefined"
    });

    const formData = files[0]?.formData;

    // Check if the file exists with the exact key
    console.log(`🔄 Checking if file exists with key: ${objectKey}`);
    let existingFile = null;

    // Try with the exact key first
    try {
      existingFile = await context.storageClient.get(objectKey);
      if (existingFile) {
        console.log(`✅ File exists with key: ${objectKey}`);
      } else {
        console.log(`🔄 File does not exist with key: ${objectKey}, will try alternative paths`);
      }
    } catch (error) {
      console.log(`⚠️ Error checking file existence with exact key: ${error.message}`);
    }

    // If file not found with exact key, try with just the filename (without whitelabelId prefix)
    let foundObjectKey = objectKey; // Keep track of which key we found the file with

    if (!existingFile) {
      const filenameOnly = fileName;
      console.log(`🔄 Trying to find file with just filename: ${filenameOnly}`);

      try {
        existingFile = await context.storageClient.get(filenameOnly);
        if (existingFile) {
          console.log(`✅ File exists with filename only: ${filenameOnly}`);
          foundObjectKey = filenameOnly; // Update the object key to the one that worked
        } else {
          console.log(`🔄 File does not exist with filename only: ${filenameOnly}`);
        }
      } catch (error) {
        console.log(`⚠️ Error checking file existence with filename only: ${error.message}`);
      }
    }

    // If still not found, try with just the timestamp_filename part (without whitelabelId prefix)
    if (!existingFile && objectKey.includes('/')) {
      const timestampFilename = objectKey.split('/').pop() || '';
      console.log(`🔄 Trying to find file with timestamp_filename part: ${timestampFilename}`);

      try {
        existingFile = await context.storageClient.get(timestampFilename);
        if (existingFile) {
          console.log(`✅ File exists with timestamp_filename: ${timestampFilename}`);
          foundObjectKey = timestampFilename; // Update the object key to the one that worked
        } else {
          console.log(`🔄 File does not exist with timestamp_filename: ${timestampFilename}`);
        }
      } catch (error) {
        console.log(`⚠️ Error checking file existence with timestamp_filename: ${error.message}`);
      }
    }

<<<<<<< HEAD
    // If still not found, try using our fixed storage client to check both buckets
    if (!existingFile) {
      console.log(`🔄 Trying to find file using fixed storage client`);

      try {
        // Create a fixed storage client
        const fixedStorageClient = createFixedStorageClient(context.env);

        // Try to get the file
        existingFile = await fixedStorageClient.get(objectKey);

        if (existingFile) {
          console.log(`✅ File found using fixed storage client`);
=======
    // If still not found, try to find files with timestamp prefixes
    if (!existingFile && objectKey.includes('/')) {
      console.log(`🔄 Trying to find file with timestamp prefix in directory`);

      try {
        // Extract directory path and original filename
        const keyParts = objectKey.split('/');
        const originalFilename = keyParts[keyParts.length - 1];
        const directoryPath = keyParts.slice(0, -1).join('/');

        console.log(`🔍 Looking for timestamped versions of "${originalFilename}" in directory "${directoryPath}"`);

        // Try to list objects in the directory to find timestamped versions
        const fixedStorageClient = createFixedStorageClient(context.env);

        // Try common timestamp patterns (more efficient approach)
        const now = Date.now();
        const fiveMinutesAgo = now - (5 * 60 * 1000);

        // Try recent timestamps with larger intervals first, then narrow down
        const intervals = [10000, 5000, 1000]; // 10s, 5s, 1s intervals

        for (const interval of intervals) {
          let found = false;
          for (let timestamp = now; timestamp >= fiveMinutesAgo && !found; timestamp -= interval) {
            const timestampedKey = `${directoryPath}/${timestamp}-${originalFilename}`;

            try {
              const testFile = await fixedStorageClient.get(timestampedKey);
              if (testFile) {
                console.log(`✅ Found file with timestamp prefix: ${timestampedKey}`);
                existingFile = testFile;
                foundObjectKey = timestampedKey;
                found = true;
                break;
              }
            } catch (error) {
              // Continue trying other timestamps
            }
          }
          if (found) break;
        }

        if (!existingFile) {
          console.log(`❌ No timestamped versions found for: ${originalFilename}`);
        }
      } catch (error) {
        console.log(`⚠️ Error searching for timestamped files: ${error.message}`);
      }
    }

    // If still not found, try the fixed storage client as a fallback
    if (!existingFile) {
      console.log(`🔄 Trying to find file using fixed storage client`);
      try {
        const fixedStorageClient = createFixedStorageClient(context.env);
        existingFile = await fixedStorageClient.get(objectKey);
        if (existingFile) {
          console.log(`✅ Found file using fixed storage client: ${objectKey}`);
>>>>>>> WA-170_MCP
          foundObjectKey = objectKey;
        } else {
          console.log(`❌ File not found using fixed storage client: ${objectKey}`);
        }
      } catch (error) {
<<<<<<< HEAD
        console.log(`⚠️ Error checking file existence with fixed storage client: ${error.message}`);
      }
    }

=======
        console.log(`⚠️ Error with fixed storage client: ${error.message}`);
      }
    }

    // If still not found, the file doesn't exist with the expected key
    if (!existingFile) {
      console.log(`⚠️ File not found with expected key: ${objectKey}`);
      console.log(`💡 This usually means the file was uploaded with a timestamp prefix`);
      console.log(`💡 Consider implementing proper filename storage in database to avoid this issue`);
    }



>>>>>>> WA-170_MCP
    // If we found the file with a different key, update the fileInfo object
    if (existingFile && foundObjectKey !== objectKey) {
      console.log(`🔄 Updating objectKey from ${objectKey} to ${foundObjectKey}`);
      fileInfo.objectKey = foundObjectKey;
    }

    // If we're in offline mode and the file doesn't exist, log that we'll upload it
    if(!existingFile && formData) {
      console.log("🔄 File not found, will upload it in the next step");
    }

    console.log("1️⃣ step('validateAndGetR2File')::objectKey:", objectKey);
    console.log("1️⃣ step('validateAndGetR2File')::existingFile metadata:", {
      key: existingFile?.key,
      size: existingFile?.size,
      contentType: existingFile?.httpMetadata?.contentType,
      customMetadata: existingFile?.customMetadata
    });

    // Return a result that indicates whether the file exists
<<<<<<< HEAD
    // Use the foundObjectKey which might have been updated if we found the file with a different key
    return {
      objectKey: fileInfo.objectKey, // Use the potentially updated objectKey from fileInfo
=======
    // Use the foundObjectKey which contains the actual key where the file was found
    const finalObjectKey = foundObjectKey || objectKey;

    console.log(`🔄 Final object key for processing: ${finalObjectKey}`);

    return {
      objectKey: finalObjectKey, // Use the actual key where the file was found (with timestamp if applicable)
>>>>>>> WA-170_MCP
      fileName,
      hasExistingFile: !!existingFile,
      // Set pendingDirectUpload flag if no formData and no existing file
      pendingDirectUpload: !existingFile && !formData,
      size: existingFile?.size || 0,
      mimeType: existingFile?.httpMetadata?.contentType || "application/octet-stream",
      customMetadata: existingFile?.customMetadata || {}
    };
  });
}
