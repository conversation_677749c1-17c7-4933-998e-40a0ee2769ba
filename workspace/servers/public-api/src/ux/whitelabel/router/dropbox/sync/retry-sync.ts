/**
 * Retry a failed sync job
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { ensureWhitelabel } from "../util/ensure-whitelabel";
import { DropboxSyncJobModel } from "@divinci-ai/server-models";

interface RetrySyncRequest {
  jobId: string,
}

export const retryDropboxSync: RequestHandler = async function(req, res, next){
  try {
    const { whitelabel, userId } = await ensureWhitelabel(req);
    const { jobId }: RetrySyncRequest = req.body;

    if(!jobId) {
      return res.status(400).json({
        success: false,
        error: "missing_job_id",
        message: "Job ID is required"
      });
    }

    // Find the job
    const job = await DropboxSyncJobModel.findById(jobId);

    if(!job) {
      return res.status(404).json({
        success: false,
        error: "job_not_found",
        message: "Sync job not found"
      });
    }

    // Check if user has access to this job
    if(job.whitelabelId !== whitelabel._id.toString() || job.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: "access_denied",
        message: "Access denied to this sync job"
      });
    }

    // Check if job can be retried
    if(!job.canRetry()) {
      return res.status(400).json({
        success: false,
        error: "job_not_retryable",
        message: `Job cannot be retried. Status: ${job.status}, Retry count: ${job.retryCount}/${job.maxRetries}`
      });
    }

    // Retry the job
    await job.retry();

    res.status(200).json({
      success: true,
      data: {
        jobId: job._id.toString(),
        status: job.status,
        retryCount: job.retryCount,
        maxRetries: job.maxRetries,
        queuePosition: await DropboxSyncJobModel.getQueuePosition(jobId)
      },
      message: "Sync job queued for retry"
    });

  }catch(error) {
    next(error);
  }
};
