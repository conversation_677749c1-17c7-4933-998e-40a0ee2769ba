import React, { useEffect, useState } from "react";
import { InputProps } from "../../../../../../../../util/react/input";
import {
  AUDIOSOURCE_SUPPORTED_AUDIO_EXTENSIONS,
  AUDIOSOURCE_SUPPORTED_VIDEO_EXTENSIONS,
} from "@divinci-ai/models";
<<<<<<< HEAD
=======
import { validateAndConvertUrl, isDropboxFolder } from "../utils/dropboxUtils";
>>>>>>> WA-170_MCP

export function URLTextareaInput({ value, onChange }: InputProps<string[]>) {
  const [textValue, setTextValue] = useState<string>(value.join('\n'));
  const [errors, setErrors] = useState<string[]>([]);
<<<<<<< HEAD
  const [isLoading, setIsLoading] = useState(false);
=======

  const [conversions, setConversions] = useState<string[]>([]);
>>>>>>> WA-170_MCP

  // Get supported file extensions for display
  const supportedExtensions = [...AUDIOSOURCE_SUPPORTED_AUDIO_EXTENSIONS, ...AUDIOSOURCE_SUPPORTED_VIDEO_EXTENSIONS].join(', ');

  // Update the textarea when the value prop changes
  useEffect(() => {
    setTextValue(value.join('\n'));
  }, [value]);

  // Process and validate URLs
  const processUrls = (text: string) => {
    // Split by newlines or commas and trim whitespace
    const urls = text
      .split(/[\n,]/)
      .map(url => url.trim())
      .filter(url => url.length > 0);

    const newErrors: string[] = [];
    const validUrls: string[] = [];
<<<<<<< HEAD

    urls.forEach((url, index) => {
      try {
        if (!URL.canParse(url)) {
          newErrors.push(`Line ${index + 1}: Invalid URL format`);
          return;
        }

        const urlObj = new URL(url);
        const extension = urlObj.pathname.split('.').pop()?.toLowerCase();

        if (!extension) {
          newErrors.push(`Line ${index + 1}: URL must point to a file with an extension`);
          return;
        }

        const isAudioExtension = AUDIOSOURCE_SUPPORTED_AUDIO_EXTENSIONS.has(extension);
        const isVideoExtension = AUDIOSOURCE_SUPPORTED_VIDEO_EXTENSIONS.has(extension);

        if (!isAudioExtension && !isVideoExtension) {
          newErrors.push(`Line ${index + 1}: Unsupported file type. Supported formats: ${supportedExtensions}`);
          return;
        }

        validUrls.push(url);
=======
    const newConversions: string[] = [];

    urls.forEach((url, index) => {
      try {
        const result = validateAndConvertUrl(
          url,
          new Set(AUDIOSOURCE_SUPPORTED_AUDIO_EXTENSIONS),
          new Set(AUDIOSOURCE_SUPPORTED_VIDEO_EXTENSIONS)
        );

        if (!result.isValid) {
          newErrors.push(`Line ${index + 1}: ${result.error}`);
          return;
        }

        // Use the processed URL (which may be converted for Dropbox)
        validUrls.push(result.processedUrl);

        // Track conversions for user feedback
        if (result.isDropboxUrl && result.wasConverted) {
          if (isDropboxFolder(url)) {
            newConversions.push(`Line ${index + 1}: Dropbox folder URL converted to direct download`);
          } else {
            newConversions.push(`Line ${index + 1}: Dropbox file URL converted to direct download`);
          }
        }
>>>>>>> WA-170_MCP
      } catch (e: any) {
        newErrors.push(`Line ${index + 1}: ${e.message}`);
      }
    });

    setErrors(newErrors);
<<<<<<< HEAD
=======
    setConversions(newConversions);
>>>>>>> WA-170_MCP
    return validUrls;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value;
    setTextValue(newText);
<<<<<<< HEAD
    
=======

>>>>>>> WA-170_MCP
    const validUrls = processUrls(newText);
    onChange(validUrls);
  };

  const handleClearInput = () => {
    setTextValue('');
    setErrors([]);
<<<<<<< HEAD
=======
    setConversions([]);
>>>>>>> WA-170_MCP
    onChange([]);
  };

  return (
    <div className="mb-5">
      <h2 className="title is-4">
        <span className="icon-text">
          <span className="icon">
            <i className="fas fa-link"></i>
          </span>
          <span>Audio or Video URLs</span>
        </span>
      </h2>

      <div className="field">
        <label className="label">Enter one URL per line or separate with commas</label>
        <div className="control">
          <textarea
            className={`textarea ${errors.length > 0 ? 'is-danger' : value.length > 0 ? 'is-success' : ''}`}
<<<<<<< HEAD
            placeholder="https://example.com/audio1.mp3&#10;https://example.com/video2.mp4&#10;https://example.com/audio3.wav"
=======
            placeholder="https://example.com/audio1.mp3&#10;https://example.com/video2.mp4&#10;https://www.dropbox.com/s/abc123/audio.mp3?dl=0&#10;https://www.dropbox.com/scl/fo/xyz/folder?dl=0&#10;https://example.com/audio3.wav"
>>>>>>> WA-170_MCP
            value={textValue}
            onChange={handleInputChange}
            rows={5}
          />
        </div>

        {errors.length > 0 ? (
          <div className="notification is-danger mt-3">
            <button className="delete" onClick={() => setErrors([])}></button>
            <p className="has-text-weight-bold mb-2">Please fix the following errors:</p>
            <ul className="ml-4">
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        ) : value.length > 0 ? (
          <p className="help is-success">
            {value.length} valid URL{value.length !== 1 ? 's' : ''} entered
          </p>
        ) : (
          <p className="help">
<<<<<<< HEAD
            Enter URLs to audio or video files, one per line or separated by commas
          </p>
        )}

=======
            Enter URLs to audio or video files, or Dropbox folders containing media files, one per line or separated by commas. Dropbox share URLs are automatically converted to direct download links.
          </p>
        )}

        {conversions.length > 0 && (
          <div className="notification is-info is-light mt-3">
            <p className="has-text-weight-bold mb-2">Dropbox URLs converted:</p>
            <ul className="ml-4">
              {conversions.map((conversion, index) => (
                <li key={index} className="mb-1">
                  <span className="icon is-small mr-1">
                    <i className="fas fa-exchange-alt"></i>
                  </span>
                  {conversion}
                </li>
              ))}
            </ul>
          </div>
        )}

>>>>>>> WA-170_MCP
        <p className="help has-text-grey mt-2">
          Supported formats: {supportedExtensions}
        </p>

<<<<<<< HEAD
        {value.length > 0 && (
          <div className="field mt-3">
            <div className="control">
              <button 
                className="button is-small is-light" 
=======
        <p className="help has-text-grey">
          <span className="icon is-small mr-1">
            <i className="fab fa-dropbox"></i>
          </span>
          Dropbox URLs to files and folders are supported and will be automatically converted to direct download links
        </p>

        {value.length > 0 && (
          <div className="field mt-3">
            <div className="control">
              <button
                className="button is-small is-light"
>>>>>>> WA-170_MCP
                onClick={handleClearInput}
              >
                <span className="icon is-small">
                  <i className="fas fa-times"></i>
                </span>
                <span>Clear All</span>
              </button>
            </div>
          </div>
        )}
      </div>

      {value.length > 0 && (
        <div className="notification is-info is-light mt-4">
          <p className="has-text-weight-bold mb-2">URLs to process ({value.length}):</p>
          <ul className="ml-4">
            {value.slice(0, 5).map((url, index) => (
              <li key={index} className="mb-1">
                <span className="has-text-weight-medium">{url}</span>
              </li>
            ))}
            {value.length > 5 && (
              <li className="has-text-grey">
                ...and {value.length - 5} more
              </li>
            )}
          </ul>
        </div>
      )}
    </div>
  );
}
