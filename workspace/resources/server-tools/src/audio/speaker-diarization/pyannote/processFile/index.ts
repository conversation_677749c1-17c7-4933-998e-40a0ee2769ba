
import { CLOUDFLARE_AUDIO_PUBLIC_URL } from "../constants";
import {
  SpeakerDiarization,
  DiarizationConfig,
  SpeakerSegmentEnhanced,
  ConfidenceData,
  VoiceprintInfo
} from "../../types";

import { startJob, createVoiceprint } from "./startJob";
import { getJob } from "./getJob";
import { delay } from "@divinci-ai/utils";

// Legacy processFile function for backward compatibility
export const processFile: SpeakerDiarization["processFile"] = async function(
  r2Pointer,
<<<<<<< HEAD
){
  const result = await startJob({
    publicAudioUrl: CLOUDFLARE_AUDIO_PUBLIC_URL + "/" + r2Pointer.Key
  });

  // Check if this is a local mode response (which already contains the diarization data)
  if (result.jobId === "local-job" && result.status === "succeeded" && result.output?.diarization) {
    console.log("✅ Local Pyannote processing completed successfully");
    return result.output.diarization;
=======
  config,
): Promise<Array<SpeakerSegmentEnhanced>> {
  // CRITICAL: This is the OFFICIAL Pyannote API service - it should NEVER use local services
  console.log("🔍 OFFICIAL PYANNOTE API: processFile called");
  console.log("🔍 OFFICIAL PYANNOTE API: This should NEVER call local Divinci service");

  // Convert legacy config to new DiarizationConfig format
  const diarizationConfig: DiarizationConfig = {
    languageCode: config.languageCode,
    confidence: (config as any).confidence || false, // Use confidence from config if provided
  };

  console.log("🔧 Confidence setting from config:", (config as any).confidence);
  console.log("🔧 Final diarizationConfig.confidence:", diarizationConfig.confidence);

  // Check if voiceprint creation is enabled
  const createVoiceprints = (config as any).createVoiceprints || false;
  console.log("🎤 Voiceprint creation setting from config:", createVoiceprints);

  // Use publicUrl from config if provided, otherwise construct from constants
  const publicAudioUrl = (config as any).publicUrl ||
                         (CLOUDFLARE_AUDIO_PUBLIC_URL + "/" + r2Pointer.Key);

  console.log("🔗 Official Pyannote API using publicAudioUrl:", publicAudioUrl);

  const result = await startJob({
    publicAudioUrl,
    config: diarizationConfig
  });

  // CRITICAL: Official Pyannote API should NEVER return "local-job"
  // If we get "local-job", it means the wrong service is being called
  if (result.jobId === "local-job") {
    console.error("🚨 CRITICAL ERROR: Official Pyannote API returned 'local-job' - this indicates wrong service is being called!");
    console.error("🚨 This means the system is incorrectly routing to Divinci Pyannote instead of Official Pyannote API");
    throw new Error("Official Pyannote API incorrectly routed to local service. Check service selection logic.");
  }

  // Check if this is a local mode response (which should NOT happen for Official API)
  if (result.jobId === "local-job" && result.status === "succeeded" && result.output?.diarization) {
    console.error("🚨 CRITICAL ERROR: Official Pyannote API should never return local-job response");
    throw new Error("Official Pyannote API incorrectly returned local-job response");
>>>>>>> WA-170_MCP
  }

  // Otherwise, handle the external API response
  const { jobId, status } = result;

  switch(status){
    case "pending":
    case "created":
    case "running":
    case "succeeded":
        break;
    case "canceled":
      throw new Error("Job canceled");
    case "failed":
      throw new Error("Job failed");
  }

  let jobResult = await getJob(jobId);
  while(jobResult.status !== "succeeded"){
    if(jobResult.status === "canceled"){
      throw new Error("Job canceled");
    }
    if(jobResult.status === "failed"){
      throw new Error("Job failed");
    }
    await delay(15 * 1000);
    jobResult = await getJob(jobId);
  }

  // Process the result and add confidence data if available
  const segments = jobResult.output.diarization || [];
  const confidenceData = jobResult.output.confidence;

  // Create voiceprints if enabled and collect results
  const createdVoiceprints: VoiceprintInfo[] = [];
  if (createVoiceprints && segments.length > 0) {
    console.log("🎤 Creating voiceprints for detected speakers...");

    // Get unique speakers from segments
    const uniqueSpeakers = [...new Set(segments.map((seg: any) => seg.speaker))];
    console.log(`🎤 Found ${uniqueSpeakers.length} unique speakers: ${uniqueSpeakers.join(', ')}`);

    // Create voiceprints for each speaker and collect results
    for (const speaker of uniqueSpeakers) {
      try {
        console.log(`🎤 Creating voiceprint for speaker: ${speaker}`);

        // Find a representative segment for this speaker (longest segment)
        const speakerSegments = segments.filter((seg: any) => seg.speaker === speaker);
        const longestSegment = speakerSegments.reduce((longest: any, current: any) =>
          (current.end - current.start) > (longest.end - longest.start) ? current : longest
        );

        // Create voiceprint using the Pyannote API with webhook (disable for localhost)
        const webhookBaseUrl = process.env.WEBHOOK_BASE_URL;
        const isLocalDevelopment = !webhookBaseUrl || webhookBaseUrl.includes('localhost') || webhookBaseUrl.includes('127.0.0.1');
        
        // Build config object - only include webhook if it's defined (no localhost URLs)
        const voiceprintConfig: any = {
          metadata: {
            speakerLabel: speaker,
            name: `Auto-generated voiceprint for ${speaker}`,
          }
        };

        // Only add webhook if it's defined and not localhost (Pyannote API rejects localhost URLs)
        if (!isLocalDevelopment && webhookBaseUrl) {
          // Note: We don't have audioDoc context here, so we can't include it in webhook
          // This is a limitation of this processFile location - webhooks will be disabled
          console.log(`🔄 [PROCESSFILE-AUTO] Webhook disabled (no audioDoc context available for speaker: ${speaker})`);
        } else {
          console.log(`🔄 [PROCESSFILE-AUTO] Webhook disabled for local development (speaker: ${speaker})`);
        }

        const voiceprintResult = await createVoiceprint({
          audioUrl: publicAudioUrl,
          config: voiceprintConfig
        });

        // Update the voiceprint with segment information
        voiceprintResult.createdFrom.start = longestSegment.start;
        voiceprintResult.createdFrom.end = longestSegment.end;
        voiceprintResult.createdFrom.speaker = speaker;

        createdVoiceprints.push(voiceprintResult);
        console.log(`✅ Voiceprint created for ${speaker} with job ID: ${voiceprintResult.jobId}`);
      } catch (error) {
        console.error(`❌ Failed to create voiceprint for ${speaker}:`, error);
        // Continue with other speakers even if one fails
      }
    }

    console.log(`🎤 Successfully created ${createdVoiceprints.length} voiceprints`);
  }

  return segments.map((segment: any, index: number) => {
    const enhancedSegment: SpeakerSegmentEnhanced = {
      ...segment,
    };

    // Add confidence data if available
    if (confidenceData && diarizationConfig.confidence) {
      enhancedSegment.confidence = processConfidenceForSegment(
        segment,
        confidenceData,
        index
      );
    }

    return enhancedSegment;
  });
};

// Enhanced processFile function with full configuration support
export async function processFileEnhanced(
  r2Pointer: Parameters<SpeakerDiarization["processFile"]>[0],
  config: DiarizationConfig
): Promise<Array<SpeakerSegmentEnhanced>> {
  console.log("🔍 OFFICIAL PYANNOTE API: processFileEnhanced called with confidence settings");
  console.log("🔍 OFFICIAL PYANNOTE API: This should NEVER call local Divinci service");

  // Check if voiceprint creation is enabled
  const createVoiceprints = (config as any).createVoiceprints || false;
  console.log("🎤 Voiceprint creation setting from enhanced config:", createVoiceprints);

  // Use publicUrl from config if provided, otherwise construct from constants
  const publicAudioUrl = (config as any).publicUrl ||
                         (CLOUDFLARE_AUDIO_PUBLIC_URL + "/" + r2Pointer.Key);

  console.log("🔗 Official Pyannote API Enhanced using publicAudioUrl:", publicAudioUrl);

  const result = await startJob({
    publicAudioUrl,
    config
  });

  // CRITICAL: Official Pyannote API should NEVER return "local-job"
  if (result.jobId === "local-job") {
    console.error("🚨 CRITICAL ERROR: Official Pyannote API returned 'local-job' in enhanced mode");
    console.error("🚨 This means the system is incorrectly routing to Divinci Pyannote instead of Official Pyannote API");
    throw new Error("Official Pyannote API incorrectly routed to local service in enhanced mode");
  }

  // Handle local mode (which should NOT happen for Official API)
  if (result.jobId === "local-job" && result.status === "succeeded" && result.output?.diarization) {
    console.error("🚨 CRITICAL ERROR: Official Pyannote API should never return local-job response in enhanced mode");
    throw new Error("Official Pyannote API incorrectly returned local-job response in enhanced mode");
  }

  // Handle external API
  const { jobId, status } = result;

  switch(status){
    case "pending":
    case "created":
    case "running":
    case "succeeded":
        break;
    case "canceled":
      throw new Error("Job canceled");
    case "failed":
      throw new Error("Job failed");
  }

  let jobResult = await getJob(jobId);
  while(jobResult.status !== "succeeded"){
    if(jobResult.status === "canceled"){
      throw new Error("Job canceled");
    }
    if(jobResult.status === "failed"){
      throw new Error("Job failed");
    }
    await delay(15 * 1000);
    jobResult = await getJob(jobId);
  }

  // Process the result with confidence data
  const segments = jobResult.output.diarization || [];
  const confidenceData = jobResult.output.confidence;

  // Create voiceprints if enabled
  if (createVoiceprints && segments.length > 0) {
    console.log("🎤 Creating voiceprints for detected speakers (enhanced mode)...");

    // Get unique speakers from segments
    const uniqueSpeakers = [...new Set(segments.map((seg: any) => seg.speaker))];
    console.log(`🎤 Found ${uniqueSpeakers.length} unique speakers: ${uniqueSpeakers.join(', ')}`);

    // Create voiceprints for each speaker (fire and forget - don't wait for completion)
    uniqueSpeakers.forEach(async (speaker: string) => {
      try {
        console.log(`🎤 Creating voiceprint for speaker: ${speaker}`);
        
        // Create voiceprint using the Pyannote API with webhook (disable for localhost)
        const webhookBaseUrl = process.env.WEBHOOK_BASE_URL;
        const isLocalDevelopment = !webhookBaseUrl || webhookBaseUrl.includes('localhost') || webhookBaseUrl.includes('127.0.0.1');
        
        // Build config object - only include webhook if it's defined (no localhost URLs)
        const voiceprintConfig: any = {
          metadata: {
            speakerLabel: speaker,
            name: `Auto-generated voiceprint for ${speaker}`,
          }
        };

        // Only add webhook if it's defined and not localhost (Pyannote API rejects localhost URLs)
        if (!isLocalDevelopment && webhookBaseUrl) {
          // Note: We don't have audioDoc context here, so we can't include it in webhook
          // This is a limitation of this processFile location - webhooks will be disabled
          console.log(`🔄 [PROCESSFILE-ENHANCED-AUTO] Webhook disabled (no audioDoc context available for speaker: ${speaker})`);
        } else {
          console.log(`🔄 [PROCESSFILE-ENHANCED-AUTO] Webhook disabled for local development (speaker: ${speaker})`);
        }
        
        const voiceprintResult = await createVoiceprint({
          audioUrl: publicAudioUrl,
          config: voiceprintConfig
        });
        console.log(`✅ Voiceprint created for ${speaker} with job ID: ${voiceprintResult.jobId}`);
      } catch (error) {
        console.error(`❌ Failed to create voiceprint for ${speaker}:`, error);
      }
    });
  }

  return segments.map((segment: any, index: number) => {
    const enhancedSegment: SpeakerSegmentEnhanced = {
      ...segment,
    };

    // Add confidence data if available and requested
    if (confidenceData && config.confidence) {
      enhancedSegment.confidence = processConfidenceForSegment(
        segment,
        confidenceData,
        index
      );
    }

    return enhancedSegment;
  });
}

// Helper function to process confidence data for a specific segment
function processConfidenceForSegment(
  segment: any,
  confidenceData: any,
  segmentIndex: number
): ConfidenceData | undefined {
  if (!confidenceData || !confidenceData.score || !confidenceData.resolution) {
    return undefined;
  }

  const { score, resolution } = confidenceData;
  const startTime = segment.start;
  const endTime = segment.end;

  // Calculate which confidence scores correspond to this segment
  const startIndex = Math.floor(startTime / resolution);
  const endIndex = Math.ceil(endTime / resolution);

  // Extract scores for this segment
  const segmentScores = score.slice(startIndex, endIndex);

  if (segmentScores.length === 0) {
    return undefined;
  }

  // Normalize scores to 0-1 range if they appear to be in 0-100 range
  const normalizedScores = segmentScores.map((score: number) => {
    // If score is greater than 1, assume it's in 0-100 range and normalize
    return score > 1 ? score / 100 : score;
  });

  // Calculate statistics
  const average = normalizedScores.reduce((sum: number, s: number) => sum + s, 0) / normalizedScores.length;
  const min = Math.min(...normalizedScores);
  const max = Math.max(...normalizedScores);

  return {
    score: normalizedScores,
    resolution,
    average,
    min,
    max,
  };
}

