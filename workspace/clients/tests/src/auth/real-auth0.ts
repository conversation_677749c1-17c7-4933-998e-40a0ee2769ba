/**
 * Real Auth0 Authentication
 *
 * This module provides utilities for getting real Auth0 JWT tokens for API testing.
 * Unlike the mock authentication, this actually communicates with Auth0 to get valid tokens.
 */

import { config } from '../config/config';

/**
 * Auth0 token response
 */
export interface Auth0TokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  scope?: string;
}

/**
 * User credentials for Auth0 authentication
 */
export interface UserCredentials {
  email: string;
  password: string;
}

/**
 * Auth0 configuration from environment
 */
interface Auth0Config {
  domain: string;
  clientId: string;
  clientSecret: string;
  audience: string;
}

/**
 * Get Auth0 configuration from environment variables
 */
function getAuth0Config(): Auth0Config {
  let domain = process.env.AUTH0_BASE_URL || config.auth0.domain;

  // Ensure domain has https:// prefix
  if (!domain.startsWith('http://') && !domain.startsWith('https://')) {
    domain = `https://${domain}`;
  }

  return {
    domain,
    clientId: process.env.AUTH0_CLIENT_ID || config.auth0.clientId,
    clientSecret: process.env.AUTH0_CLIENT_SECRET || '',
    audience: process.env.AUTH0_AUDIENCE || config.auth0.audience
  };
}

/**
 * Get test user credentials from environment
 */
function getTestUserCredentials(): UserCredentials {
  return {
    email: process.env.AUTH0_TEST_USER_EMAIL || '<EMAIL>',
    password: process.env.AUTH0_TEST_USER_PASSWORD || '(abc123ABC)'
  };
}

/**
 * Get an Auth0 access token using the Resource Owner Password Grant
 * This is suitable for testing environments where we control the user credentials
 */
export async function getAuth0AccessToken(credentials?: UserCredentials): Promise<string> {
  const auth0Config = getAuth0Config();
  const userCreds = credentials || getTestUserCredentials();

  console.log(`[Auth0] Getting access token for user: ${userCreds.email}`);
  console.log(`[Auth0] Using domain: ${auth0Config.domain}`);
  console.log(`[Auth0] Using audience: ${auth0Config.audience}`);

  // First, try the Resource Owner Password Grant
  try {
    return await attemptPasswordGrant(auth0Config, userCreds);
  } catch (error) {
    console.warn(`[Auth0] Password grant failed, trying alternative methods:`, error);

    // Fallback: Try using service token with user impersonation
    console.log(`[Auth0] Falling back to service token for user authentication`);
    return await getAuth0ServiceToken();
  }
}

/**
 * Attempt Resource Owner Password Grant
 */
async function attemptPasswordGrant(auth0Config: Auth0Config, userCreds: UserCredentials): Promise<string> {
  const tokenUrl = `${auth0Config.domain}/oauth/token`;

  // Try different grant configurations
  const grantConfigs = [
    // Standard Resource Owner Password Grant
    {
      grant_type: 'password',
      username: userCreds.email,
      password: userCreds.password,
      audience: auth0Config.audience,
      client_id: auth0Config.clientId,
      client_secret: auth0Config.clientSecret,
      scope: 'openid profile email'
    },
    // Alternative without client_secret (for public clients)
    {
      grant_type: 'password',
      username: userCreds.email,
      password: userCreds.password,
      audience: auth0Config.audience,
      client_id: auth0Config.clientId,
      scope: 'openid profile email'
    },
    // Alternative with different scope
    {
      grant_type: 'password',
      username: userCreds.email,
      password: userCreds.password,
      audience: auth0Config.audience,
      client_id: auth0Config.clientId,
      client_secret: auth0Config.clientSecret,
      scope: 'read:chats write:chats'
    }
  ];

  for (let i = 0; i < grantConfigs.length; i++) {
    const requestBody = grantConfigs[i];
    console.log(`[Auth0] Trying password grant configuration ${i + 1}/${grantConfigs.length}`);

    try {
      const response = await fetch(tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (response.ok) {
        const tokenResponse: Auth0TokenResponse = await response.json();
        console.log(`[Auth0] Successfully obtained user access token with config ${i + 1}`);
        console.log(`[Auth0] Token type: ${tokenResponse.token_type}`);
        console.log(`[Auth0] Expires in: ${tokenResponse.expires_in} seconds`);
        return `Bearer ${tokenResponse.access_token}`;
      } else {
        const errorBody = await response.text();
        console.warn(`[Auth0] Config ${i + 1} failed: ${response.status} ${response.statusText} - ${errorBody}`);
      }
    } catch (error) {
      console.warn(`[Auth0] Config ${i + 1} error:`, error);
    }
  }

  throw new Error('All password grant configurations failed');
}

/**
 * Get an Auth0 access token using Client Credentials Grant (for service-to-service)
 * This is useful for admin-level operations
 */
export async function getAuth0ServiceToken(): Promise<string> {
  const auth0Config = getAuth0Config();

  console.log(`[Auth0] Getting service token using client credentials`);
  console.log(`[Auth0] Using domain: ${auth0Config.domain}`);

  // Use S2S credentials if available
  const s2sClientId = process.env.AUTH0_S2S_CLIENT_ID;
  const s2sClientSecret = process.env.AUTH0_S2S_CLIENT_SECRET;
  const s2sAudience = process.env.AUTH0_S2S_AUDIENCE;

  if (!s2sClientId || !s2sClientSecret) {
    throw new Error('Auth0 S2S credentials not configured. Need AUTH0_S2S_CLIENT_ID and AUTH0_S2S_CLIENT_SECRET');
  }

  const tokenUrl = `${auth0Config.domain}/oauth/token`;
  const requestBody = {
    grant_type: 'client_credentials',
    client_id: s2sClientId,
    client_secret: s2sClientSecret,
    audience: auth0Config.audience // Use the main API audience, not S2S audience
  };

  try {
    console.log(`[Auth0] Making service token request to: ${tokenUrl}`);

    const response = await fetch(tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorBody = await response.text();
      console.error(`[Auth0] Service token request failed: ${response.status} ${response.statusText}`);
      console.error(`[Auth0] Error body: ${errorBody}`);
      throw new Error(`Auth0 service token request failed: ${response.status} ${response.statusText} - ${errorBody}`);
    }

    const tokenResponse: Auth0TokenResponse = await response.json();

    console.log(`[Auth0] Successfully obtained service token`);
    console.log(`[Auth0] Token type: ${tokenResponse.token_type}`);
    console.log(`[Auth0] Expires in: ${tokenResponse.expires_in} seconds`);

    return `Bearer ${tokenResponse.access_token}`;
  } catch (error) {
    console.error(`[Auth0] Failed to get service token:`, error);
    throw error;
  }
}

/**
 * Validate an Auth0 token by making a test request
 */
export async function validateAuth0Token(token: string, isServiceToken: boolean = false): Promise<boolean> {
  try {
    const auth0Config = getAuth0Config();

    if (isServiceToken) {
      // For service tokens, we can't use /userinfo, so we'll just check if the token is well-formed
      // and assume it's valid since we just got it from Auth0
      console.log(`[Auth0] Service token appears to be valid (skipping validation)`);
      return true;
    } else {
      // For user tokens, validate against /userinfo
      const userInfoUrl = `${auth0Config.domain}/userinfo`;

      const response = await fetch(userInfoUrl, {
        headers: {
          'Authorization': token,
          'Accept': 'application/json'
        }
      });

      if (response.ok) {
        const userInfo = await response.json();
        console.log(`[Auth0] Token is valid for user: ${userInfo.email || userInfo.sub}`);
        return true;
      } else {
        console.error(`[Auth0] Token validation failed: ${response.status} ${response.statusText}`);
        return false;
      }
    }
  } catch (error) {
    console.error(`[Auth0] Token validation error:`, error);
    return false;
  }
}
