# GitHub Codespaces & Devcontainer Setup Plan

## Project Overview

This document outlines the plan to prepare the Divinci Server repository for GitHub Codespaces and devcontainer support. The goal is to enable developers to quickly spin up a fully functional development environment in the cloud using GitHub Codespaces.

## Repository Analysis

### Current Structure
- **Monorepo**: Uses pnpm workspaces with three main areas:
  - `workspace/clients/*` - Frontend applications
  - `workspace/resources/*` - Shared libraries and utilities
  - `workspace/servers/*` - Backend services
- **Package Manager**: pnpm 10.11.0
- **Node.js Version**: 22.11.0
- **Docker Setup**: Multiple compose files, with `fast-local.yml` being the target for Codespaces
- **Current Branch**: `WA-162_GitHub-Codespaces`

### Key Services in fast-local.yml
- **local-web-client**: Frontend on port 8080
- **local-api**: Backend API on port 9080
- **local-mongodb**: Database on port 27017
- **local-redis**: Cache on port 6379
- **local-minio**: S3-compatible storage on ports 9000/9001

## Implementation Plan

### Phase 1: Core Devcontainer Setup
1. **Create .devcontainer directory structure**
2. **Configure devcontainer.json**
3. **Create custom Dockerfile**
4. **Set up VS Code extensions and settings**

### Phase 2: Docker Compose Integration
1. **Adapt fast-local.yml for Codespaces**
2. **Optimize networking for cloud environment**
3. **Configure proper service dependencies**
4. **Add health checks and startup order**

### Phase 3: Environment Setup
1. **Create post-create setup scripts**
2. **Configure environment variables**
3. **Set up pnpm and dependencies**
4. **Configure development tools**

### Phase 4: Testing and Optimization
1. **Test Codespace creation**
2. **Verify all services start correctly**
3. **Test development workflow**
4. **Optimize performance and startup time**

## Technical Requirements

### Base Container
- **Base Image**: `mcr.microsoft.com/devcontainers/javascript-node:22-bookworm`
- **Package Manager**: pnpm 10.11.0
- **Additional Tools**: Docker CLI, git, curl, vim

### Port Forwarding
- `8080` - Web Client
- `9080` - Public API
- `27017` - MongoDB
- `6379` - Redis
- `9000` - MinIO API
- `9001` - MinIO Console

### VS Code Extensions
- TypeScript and JavaScript Language Features
- ESLint
- Prettier
- Docker
- GitLens
- Thunder Client (API testing)
- Auto Rename Tag
- Bracket Pair Colorizer

### Environment Variables
- `NODE_ENV=development`
- `DEBUG=app`
- `LOG_DEBUG=1`
- MinIO credentials and endpoints
- Cloudflare Worker auth tokens

## File Structure Plan

```
.devcontainer/
├── devcontainer.json          # Main configuration
├── Dockerfile                 # Custom container definition
├── docker-compose.yml         # Services for Codespaces
├── setup.sh                   # Post-create setup script
└── README.md                  # Codespaces-specific documentation
```

## Key Considerations

### Performance Optimization
- Use multi-stage Docker builds
- Optimize layer caching
- Minimize container size
- Use .dockerignore effectively

### Security
- Use non-root user
- Secure environment variable handling
- Proper file permissions
- Network security for services

### Developer Experience
- Fast startup time
- Hot reload for development
- Proper debugging support
- Clear error messages and logging

### Compatibility
- Works with GitHub Codespaces
- Compatible with VS Code Dev Containers extension
- Supports local development workflow
- Cross-platform compatibility

## Success Criteria

1. **Codespace Creation**: New Codespace starts successfully within 5 minutes
2. **Service Health**: All services (web, API, DB, cache, storage) are healthy
3. **Development Workflow**: Can edit code, see changes, run tests
4. **Port Access**: All services accessible via forwarded ports
5. **Dependencies**: All pnpm workspaces install and build successfully
6. **Hot Reload**: Frontend and backend hot reload works correctly

## Rollback Plan

If issues arise:
1. Keep original docker files as backup
2. Use feature flags for gradual rollout
3. Document known issues and workarounds
4. Maintain compatibility with existing local development

## TODO List - Implementation Progress

### Phase 1: Core Devcontainer Setup
- [x] Create `.devcontainer` directory
- [x] Create `devcontainer.json` with base configuration
- [x] Create custom `Dockerfile` with Node.js 22 and required tools
- [x] Configure VS Code extensions list
- [x] Set up workspace settings for TypeScript/ESLint
- [x] Configure port forwarding for all services
- [x] Add environment variables configuration

### Phase 2: Docker Compose Integration
- [x] Copy and adapt `fast-local.yml` to `.devcontainer/docker-compose.yml`
- [x] Optimize service configurations for Codespaces
- [x] Update networking configuration for cloud environment
- [x] Configure service dependencies and startup order
- [x] Add proper health checks for all services
- [x] Test MinIO connectivity and credentials
- [x] Verify MongoDB and Redis connectivity

### Phase 3: Environment Setup
- [x] Create `setup.sh` post-create script
- [x] Configure pnpm installation and workspace setup
- [x] Add dependency installation automation
- [x] Set up environment variable templates
- [x] Configure git settings and hooks
- [x] Add development tool configurations
- [x] Create Codespaces-specific documentation

### Phase 4: Testing and Optimization
- [ ] Test Codespace creation end-to-end
- [ ] Verify all services start and are healthy
- [ ] Test web client accessibility on port 8080
- [ ] Test API accessibility on port 9080
- [ ] Verify hot reload functionality
- [ ] Test pnpm workspace commands
- [ ] Run existing test suites
- [ ] Optimize startup performance
- [ ] Document any known issues or limitations

### Phase 5: Documentation and Finalization
- [ ] Create `.devcontainer/README.md` with usage instructions
- [ ] Update main repository README with Codespaces instructions
- [ ] Add troubleshooting guide
- [ ] Create developer onboarding checklist
- [ ] Test with fresh GitHub account
- [ ] Get team feedback and iterate
- [ ] Merge to develop branch

## Progress Notes

### Completed Items
- [x] Repository analysis and structure understanding
- [x] Created comprehensive implementation plan
- [x] Identified key services and requirements
- [x] Created this tracking document
- [x] **Phase 1**: Core Devcontainer Setup (7/7 tasks)
- [x] **Phase 2**: Docker Compose Integration (7/7 tasks)
- [x] **Phase 3**: Environment Setup (7/7 tasks)

### Current Status
**Phase**: Phase 4 - Testing and Optimization 🧪
**Next**: Test Codespace creation end-to-end
**Blockers**: None
**Notes**: Core implementation complete, ready for testing

### Issues and Decisions Log
- **Decision**: Use `fast-local.yml` as base for Codespaces services
- **Decision**: Target Node.js 22 with pnpm 10.11.0 for consistency
- **Decision**: Use Microsoft's official devcontainer base images
- **Issue**: Need to handle MinIO credentials and networking in cloud environment
- **Issue**: Large monorepo may have slow initial setup - need optimization
- **Issue Found**: Docker Compose build failures in Codespaces environment
- **Decision**: Simplified to image-based devcontainer without Docker Compose
- **Solution**: Use manual development workflow or existing fast-local.yml separately
- **Issue Found**: Container initialization failures - missing shell utilities (cat, sleep)
- **Root Cause**: Docker-in-Docker feature initialization scripts require basic utilities
- **Solution**: Replace docker-in-docker with common-utils feature
- **Decision**: Remove Docker support from devcontainer, focus on Node.js development

## Team Notes
- Remember to test with clean GitHub account to simulate new developer experience
- Consider creating multiple devcontainer configurations for different use cases
- Keep backward compatibility with existing local development workflow
- Document any deviations from local setup for team awareness
