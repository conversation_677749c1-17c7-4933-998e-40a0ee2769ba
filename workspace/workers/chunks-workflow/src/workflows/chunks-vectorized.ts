import { WorkflowEntrypoint, WorkflowEvent, WorkflowStep } from "cloudflare:workers";
import { Env, RagVectorTextChunksStatus } from "../types";
<<<<<<< HEAD
import { createFixedStorageClient } from "../utils/fixed-storage-client";
=======
import { createStorageClient } from "../utils/storage-client";
>>>>>>> WA-170_MCP
import { OpenParseProcessor } from "../processors/openparse";
import { UnstructuredProcessor } from "../processors/unstructured";
import { NonRetryableError } from "cloudflare:workflows";

import {
  StepContext,
  InitialSetup,
  R2ValidationResult,
  ChunkData,
  WorkflowFile,
  VectorizeConfig,
  FileRecord,
  initializeWorkflow,
  ensureD1Table,
  validateAndGetR2File,
  uploadToR2,
  ensureFileRecord,
  initializeProcessing,
  processBatch,
  validateAndFilterChunks,
  storeChunksInD1,
  storeChunksInR2,
<<<<<<< HEAD
=======
  storeEmbeddingsInR2,
>>>>>>> WA-170_MCP
  vectorizeChunks,
  addChunksToFileRecord,
  updateFileStatus,
  linkFileToRag,
  upsertWorkflowMetadata
} from "./steps";

interface ChunksVectorizedEvent {
  files: WorkflowFile[],
  vectorizeConfig: VectorizeConfig,
  timestamp: string | Date,
  instanceId: string,
}

export class ChunksVectorizedWorkflow extends WorkflowEntrypoint<Env, ChunksVectorizedEvent> {
  private storageClient: any;

  constructor(ctx: ExecutionContext, protected readonly env: Env){
    super(ctx, env);
<<<<<<< HEAD
    // Initialize the storage client
    this.storageClient = createFixedStorageClient(env);
  }

  async run(event: WorkflowEvent<ChunksVectorizedEvent>, step: WorkflowStep): Promise<void>{
    const { files, vectorizeConfig } = event.payload;

    // Create context for steps
    const context: StepContext = {
      env: this.env,
      storageClient: this.storageClient
    };

    // Declare variables that need to be accessible in catch/finally blocks
    let initialSetup: InitialSetup | undefined;
    let r2Result: R2ValidationResult | undefined;
    let processor: any;
    let documentChunks: any[] = [];

    try {
      // Step 0: Normalize processor config and initialize metadata
      initialSetup = await initializeWorkflow(step, context, files[0]);

      // Step 1: Ensure D1 table
      await ensureD1Table(step, context, vectorizeConfig.whitelabelId);

      // Step 2: Validate and get R2 file metadata
      r2Result = await validateAndGetR2File(
        step,
        context,
        initialSetup.fileInfo,
        files
      );

      // Step 3: Handle file upload if needed
      if(!r2Result.hasExistingFile) {
        await uploadToR2(step, context, r2Result, files);
      }

      // Step 4: Create or get file record
      const fileRecord: FileRecord = await ensureFileRecord(
        step,
        context,
        initialSetup,
        r2Result,
        vectorizeConfig
      );

=======
    // Initialize the storage client using the working MinIO client
    this.storageClient = createStorageClient(env);
  }

  async run(event: WorkflowEvent<ChunksVectorizedEvent>, step: WorkflowStep): Promise<void>{
    console.log("🚀🚀🚀 CHUNKS VECTORIZED WORKFLOW STARTING 🚀🚀🚀");
    console.log("🚀🚀🚀 THIS IS THE NEW WORKFLOW VERSION 🚀🚀🚀");

    const { files, vectorizeConfig } = event.payload;

    // Create context for steps
    const context: StepContext = {
      env: this.env,
      storageClient: this.storageClient
    };

    // Declare variables that need to be accessible in catch/finally blocks
    let initialSetup: InitialSetup | undefined;
    let r2Result: R2ValidationResult | undefined;
    let processor: any;
    let documentChunks: any[] = [];

    try {
      // Step 0: Normalize processor config and initialize metadata
      initialSetup = await initializeWorkflow(step, context, files[0]);

      // Step 1: Ensure D1 table
      await ensureD1Table(step, context, vectorizeConfig.whitelabelId);

      // Step 2: Validate and get R2 file metadata
      r2Result = await validateAndGetR2File(
        step,
        context,
        initialSetup.fileInfo,
        files
      );

      // Step 3: Handle file upload if needed
      if(!r2Result.hasExistingFile) {
        const uploadResult = await uploadToR2(step, context, r2Result, files);

        // Step 3.5: Update database record with actual object key if it changed
        if(uploadResult.actualObjectKey && uploadResult.actualObjectKey !== initialSetup.fileInfo.objectKey) {
          await step.do("updateDatabaseWithActualObjectKey", {
            retries: {
              limit: 3,
              delay: "2 seconds",
              backoff: "exponential"
            },
            timeout: "30 seconds"
          }, async () => {
            console.log(`🔄 [UPDATE DB] Updating database record with actual object key: ${uploadResult.actualObjectKey}`);

            const updateUrl = `${context.env.API_HOST}/white-label/${vectorizeConfig.whitelabelId}/rag-vector/files/update-object-key`;

            const headers: Record<string, string> = {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${vectorizeConfig.auth0Token}`
            };

            if(context.env.ENVIRONMENT === 'local' || context.env.ENVIRONMENT === 'development' || context.env.ENVIRONMENT === 'stage') {
              if (context.env.CLOUDFLARE_WORKER_X_AUTH_DEV) {
                headers["cloudflare-worker-x-dev-auth"] = context.env.CLOUDFLARE_WORKER_X_AUTH_DEV;
                headers["x-worker-local-dev"] = "true";
              }
            }

            const updatePayload = {
              originalObjectKey: initialSetup.fileInfo.objectKey,
              actualObjectKey: uploadResult.actualObjectKey,
              fileId: initialSetup.fileInfo.fileId
            };

            const updateResponse = await fetch(updateUrl, {
              method: "POST",
              headers,
              body: JSON.stringify(updatePayload)
            });

            if (!updateResponse.ok) {
              const errorText = await updateResponse.text();
              console.warn(`⚠️ [UPDATE DB] Failed to update database with actual object key: ${errorText}`);
              // Don't throw error - this is not critical for workflow success
            } else {
              console.log(`✅ [UPDATE DB] Successfully updated database with actual object key`);
              // Update our local copy of the object key
              initialSetup.fileInfo.objectKey = uploadResult.actualObjectKey;
            }
          });
        }
      }

      // Step 4: Create or get file record
      const fileRecord: FileRecord = await ensureFileRecord(
        step,
        context,
        initialSetup,
        r2Result,
        vectorizeConfig
      );

>>>>>>> WA-170_MCP
      // Add debug logging before processing
      console.log("🔄 Starting document processing with:", {
        fileRecord: {
          id: fileRecord?.data?._id,
          fileKey: fileRecord?.data?.fileKey,
          status: fileRecord?.data?.status
        },
        processorType: initialSetup.fileInfo.processor
      });

      // Initialize processor
      const processorType = initialSetup.fileInfo.processor?.toLowerCase() || "unstructured";
      console.log(`🔄 Initializing processor: ${processorType}`, {
        config: initialSetup.normalizedConfig,
        fileName: initialSetup.fileInfo.fileName
      });

      // IMPORTANT: In BARE METAL MODE, we should not have any special handling for LOCAL MODE.
      // The environment configuration should handle the differences between environments.
      console.log(`🔄 Environment detected: ${this.env.ENVIRONMENT}`);

      processor = processorType === "openparse"
        ? new OpenParseProcessor(
            this.env.OPENPARSE_API_KEY,
            this.env.OPENPARSE_API_URL,
            this.env,
            {
              useStreaming: true
            }
          )
        : new UnstructuredProcessor(
            this.env.UNSTRUCTURED_WORKER_URL,
            { useStreaming: true }
          );

      try {
        // Step 5: Initialize processing session
        await initializeProcessing(
          step,
          context,
          r2Result,
          initialSetup,
          processorType,
          processor
        );

        // Step 6: Process document in batches
        documentChunks = [];
        let batchNumber = 0;

        const hasMoreBatches = true;
        while(hasMoreBatches) {
          // Process batch
          const batch = await processBatch(
            step,
            context,
            r2Result,
            initialSetup,
            processor,
            batchNumber
          );
<<<<<<< HEAD

          if(!batch) {
            console.log(`🏁 No more chunks to process after batch ${batchNumber}`);
            break;
          }

          documentChunks.push(...batch);
          console.log(`📊 Chunks accumulated: ${documentChunks.length} (added ${batch.length})`);

          batchNumber++;
        }
      } finally {
        // Ensure we clean up resources
        console.log(`📊 pre-processor.dispose - cleaning up resources`);
        await processor.dispose();
        console.log(`📊 post-processor.dispose - resources cleaned up`);
      }

      // Add final count logging
      console.log(`📊 Total chunks received: ${documentChunks.length}`);

      // Step 7: Validate and filter chunks
      const filteredChunks: ChunkData[] = await validateAndFilterChunks(
        step,
        context,
        initialSetup,
        documentChunks
      );

      // Step 8: Store chunks in D1
=======

          if(!batch) {
            console.log(`🏁 No more chunks to process after batch ${batchNumber}`);
            break;
          }

          documentChunks.push(...batch);
          console.log(`📊 Chunks accumulated: ${documentChunks.length} (added ${batch.length})`);

          batchNumber++;
        }
      } finally {
        // Ensure we clean up resources
        console.log(`📊 pre-processor.dispose - cleaning up resources`);
        await processor.dispose();
        console.log(`📊 post-processor.dispose - resources cleaned up`);
      }

      // Add final count logging
      console.log(`📊 Total chunks received: ${documentChunks.length}`);

      // Step 7: Validate and filter chunks
      const filteredChunks: ChunkData[] = await validateAndFilterChunks(
        step,
        context,
        initialSetup,
        documentChunks
      );

      // Step 8: Validate/create file record BEFORE storing chunks
      // This ensures we have the correct fileId for chunk storage
      console.log(`🔍 [WORKFLOW] Step 8: Validating/creating file record before chunk storage`);
      await step.do("validateFileRecord", async () => {
        // Check if fileId is valid MongoDB ObjectId
        const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(initialSetup.fileInfo.fileId);

        if (!isValidObjectId) {
          console.log(`🔄 [WORKFLOW] FileId is not valid ObjectId, will validate/create record: ${initialSetup.fileInfo.fileId}`);

          // Try to find existing file record by objectKey
          const getRecordUrl = `${context.env.API_HOST}/white-label/${vectorizeConfig.whitelabelId}/rag-vector/files/get-record?objectKey=${encodeURIComponent(initialSetup.fileInfo.objectKey)}`;

          const headers: Record<string, string> = {
            "Authorization": `Bearer ${vectorizeConfig.auth0Token}`
          };

          if(context.env.ENVIRONMENT === 'local' || context.env.ENVIRONMENT === 'development' || context.env.ENVIRONMENT === 'stage') {
            if (context.env.CLOUDFLARE_WORKER_X_AUTH_DEV) {
              headers["cloudflare-worker-x-dev-auth"] = context.env.CLOUDFLARE_WORKER_X_AUTH_DEV;
              headers["x-worker-local-dev"] = "true";
            }
          }

          const getRecordResponse = await fetch(getRecordUrl, { method: "GET", headers });

          if (getRecordResponse.ok) {
            const recordResult = await getRecordResponse.json() as any;
            if (recordResult?.data?._id) {
              console.log(`✅ [WORKFLOW] Found existing file record: ${recordResult.data._id}`);
              initialSetup.fileInfo.fileId = recordResult.data._id;
            } else {
              console.log(`🔄 [WORKFLOW] No existing file record found, will create new one`);
              // Create new file record
              const createPayload = {
                bucket: initialSetup.fileInfo.bucket || "default",
                objectKey: initialSetup.fileInfo.objectKey,
                originalName: initialSetup.fileInfo.fileName,
                chunkingTool: initialSetup.fileInfo.processor,
                title: initialSetup.fileInfo.title || initialSetup.fileInfo.fileName,
                description: initialSetup.fileInfo.description
              };

              const createUrl = `${context.env.API_HOST}/white-label/${vectorizeConfig.whitelabelId}/rag-vector/files/create-record`;
              const createHeaders = { ...headers, "Content-Type": "application/json" };

              const createResponse = await fetch(createUrl, {
                method: "POST",
                headers: createHeaders,
                body: JSON.stringify(createPayload)
              });

              if (createResponse.ok) {
                const createResult = await createResponse.json() as any;
                if (createResult?.data?._id) {
                  console.log(`✅ [WORKFLOW] Created new file record: ${createResult.data._id}`);
                  initialSetup.fileInfo.fileId = createResult.data._id;
                }
              }
            }
          }
        }

        console.log(`🔍 [WORKFLOW] Final fileId for chunk storage: ${initialSetup.fileInfo.fileId}`);
      });

      // Step 9: Store chunks in D1
>>>>>>> WA-170_MCP
      await storeChunksInD1(
        step,
        context,
        filteredChunks,
<<<<<<< HEAD
        vectorizeConfig.whitelabelId
      );

      // Step 9: Store chunks in R2 as JSON
      await storeChunksInR2(
        step,
        context,
        filteredChunks,
        vectorizeConfig,
        initialSetup
      );

      // Step 10: Vectorize chunks
      await vectorizeChunks(
        step,
        context,
        filteredChunks,
        vectorizeConfig,
        initialSetup
      );

      // Step 11: Set file status to EDITING before adding chunks
=======
        vectorizeConfig.whitelabelId,
        initialSetup.fileInfo.fileId  // Pass fileId for correct ID shortening
      );

      // Step 10: Store chunks in R2 as JSON
      console.log(`🚀🚀🚀 [WORKFLOW] Step 10: About to store chunks in R2`);
      console.log(`🚀🚀🚀 [WORKFLOW] filteredChunks.length: ${filteredChunks.length}`);
      console.log(`🚀🚀🚀 [WORKFLOW] fileId: ${initialSetup.fileInfo.fileId}`);
      console.log(`🚀🚀🚀 [WORKFLOW] whitelabelId: ${vectorizeConfig.whitelabelId}`);

      // TEMPORARY: Skip R2 storage to avoid workflow timeout
      // TODO: Move R2 storage to a separate, faster workflow
      console.log(`⚠️ [WORKFLOW] TEMPORARILY SKIPPING R2 STORAGE TO AVOID TIMEOUT`);
      console.log(`⚠️ [WORKFLOW] This will be moved to a separate workflow`);

      // Store chunks in R2 with a reasonable timeout
      try {
        await step.do("storeChunksInR2", {
          timeout: "30 seconds",  // Increased timeout for R2 storage
          retries: {
            limit: 2,  // Allow 2 retries
            delay: "2 seconds",
            backoff: "exponential"
          }
        }, async ()=>{
          await storeChunksInR2(
            step,
            context,
            filteredChunks,
            vectorizeConfig,
            initialSetup
          );
        });
        console.log(`✅ [WORKFLOW] Step 9: storeChunksInR2 completed successfully`);
      }catch(error) {
        console.error(`❌ [WORKFLOW] Step 9: storeChunksInR2 failed:`, error);
        // Throw the error to fail the workflow if chunks storage fails
        throw error;
      }

      // Step 10: Vectorize chunks and get embeddings
      console.log("🚀🚀🚀 [WORKFLOW] About to call vectorizeChunks");
      console.log("🚀🚀🚀 [WORKFLOW] filteredChunks.length:", filteredChunks.length);
      console.log("🚀🚀🚀 [WORKFLOW] vectorizeConfig.ragVectors:", vectorizeConfig.ragVectors);
      console.log("🚀🚀🚀 [WORKFLOW] initialSetup.fileInfo.fileId:", initialSetup.fileInfo.fileId);
      console.log("🚀🚀🚀 [WORKFLOW] initialSetup.fileInfo.objectKey:", initialSetup.fileInfo.objectKey);

      // TEMPORARY: Skip vectorization to avoid workflow timeout
      // TODO: Move vectorization to a separate, faster workflow
      console.log(`⚠️ [WORKFLOW] TEMPORARILY SKIPPING VECTORIZATION TO AVOID TIMEOUT`);
      console.log(`⚠️ [WORKFLOW] This will be moved to a separate workflow`);

      let embeddings: any[] = [];

      try {
        embeddings = await step.do("vectorizeChunks", {
          timeout: "30 seconds",  // Much shorter timeout
          retries: {
            limit: 1,  // Only 1 retry to avoid timeout
            delay: "2 seconds",
            backoff: "linear"
          }
        }, async ()=>{
          return await vectorizeChunks(
            step,
            context,
            filteredChunks,
            vectorizeConfig,
            initialSetup
          );
        });
        console.log("✅ [WORKFLOW] vectorizeChunks returned embeddings.length:", embeddings.length);
      }catch(error) {
        console.warn(`⚠️ [WORKFLOW] vectorizeChunks failed, but continuing workflow:`, error);
        // Don't throw the error - let the workflow continue
        embeddings = []; // Empty embeddings array
      }

      console.log("🚀🚀🚀 [WORKFLOW] About to call storeEmbeddingsInR2");

      // Step 10.5: Store embeddings in R2 as JSON
      try {
        await step.do("storeEmbeddingsInR2", {
          timeout: "30 seconds",  // Increased timeout for R2 storage
          retries: {
            limit: 2,  // Allow 2 retries
            delay: "2 seconds",
            backoff: "exponential"
          }
        }, async ()=>{
          await storeEmbeddingsInR2(
            step,
            context,
            filteredChunks,
            vectorizeConfig,
            initialSetup,
            embeddings
          );
        });
        console.log("✅ [WORKFLOW] storeEmbeddingsInR2 completed successfully");
      }catch(error) {
        console.error(`❌ [WORKFLOW] storeEmbeddingsInR2 failed:`, error);
        // Throw the error to fail the workflow if embeddings storage fails
        throw error;
      }

      // Step 10.6: CRITICAL FIX - Upload vectors to Cloudflare Vectorize index
      console.log("🚀🚀🚀 [WORKFLOW] About to upload vectors to Cloudflare Vectorize");
      console.log("🚀🚀🚀 [WORKFLOW] This is the MISSING STEP that prevents audio chunks from being searchable!");

      if (embeddings && embeddings.length > 0) {
        try {
          await step.do("uploadVectorsToVectorize", {
            timeout: "60 seconds",  // Longer timeout for Vectorize upload
            retries: {
              limit: 3,  // Allow more retries for this critical step
              delay: "5 seconds",
              backoff: "exponential"
            }
          }, async ()=>{
            // Import VectorizeAPI
            const { VectorizeAPI } = await import("../utils/vectorize-api");

            const vectorizeApi = new VectorizeAPI({
              accountId: context.env.CLOUDFLARE_ACCOUNT_ID,
              apiToken: context.env.CLOUDFLARE_API_TOKEN,
              ragName: "default"
            });

            // CRITICAL FIX: Use RAG vector ID instead of whitelabel ID for index name
            // This ensures we upload to the same index that RAG retrieval searches
            let vectorIndex: string;
            if (vectorizeConfig.ragVectors && vectorizeConfig.ragVectors.length > 0) {
              // Use the first RAG vector ID (this matches the RAG retrieval logic)
              const ragVectorId = vectorizeConfig.ragVectors[0];
              vectorIndex = `vector-index-${ragVectorId}`;
              console.log(`🔄 [VECTORIZE] Using RAG vector index: ${vectorIndex} (from ragVectors[0]: ${ragVectorId})`);
            } else if (vectorizeConfig.ragId) {
              // Fallback to single ragId for backward compatibility
              vectorIndex = `vector-index-${vectorizeConfig.ragId}`;
              console.log(`🔄 [VECTORIZE] Using fallback RAG index: ${vectorIndex} (from ragId: ${vectorizeConfig.ragId})`);
            } else {
              // Final fallback to whitelabel ID (old behavior)
              vectorIndex = `vector-index-${vectorizeConfig.whitelabelId}`;
              console.log(`🔄 [VECTORIZE] Using whitelabel index: ${vectorIndex} (fallback - no RAG ID provided)`);
            }

            // Ensure vector index exists
            const { findOrCreateVectorizeIndex } = await import("../utils/vector-index");
            await findOrCreateVectorizeIndex(
              vectorIndex,
              `🔢 Vectors for files uploaded \n\n whitelabel: ${vectorizeConfig.whitelabelId} \n\n vectorIndex: ${vectorIndex}`,
              context.env.CLOUDFLARE_ACCOUNT_ID,
              context.env.CLOUDFLARE_API_TOKEN,
              context.env.CLOUDFLARE_API_URL || "https://api.cloudflare.com/client/v4"
            );

            // Prepare vectors for upload
            // The embeddings array already contains objects with { id, values, metadata }
            // from the vectorizeChunks function, so we can use them directly
            console.log(`🔍 [VECTORIZE] Embeddings format check:`, {
              embeddingsLength: embeddings.length,
              firstEmbeddingType: typeof embeddings[0],
              firstEmbeddingKeys: embeddings[0] ? Object.keys(embeddings[0]) : 'none',
              hasValues: embeddings[0] ? 'values' in embeddings[0] : false
            });

            const vectorsWithEmbeddings = embeddings.map((embeddingObj, index) => {
              // The embeddings from vectorizeChunks are already in the correct format
              if (!embeddingObj || typeof embeddingObj !== 'object') {
                throw new Error(`Invalid embedding object at index ${index}: ${typeof embeddingObj}`);
              }

              if (!embeddingObj.values || !Array.isArray(embeddingObj.values)) {
                throw new Error(`Invalid embedding values at index ${index}: ${typeof embeddingObj.values}`);
              }

              // CRITICAL FIX: Create hash-based ID to guarantee it fits Cloudflare Vectorize 64-byte limit
              const originalId = embeddingObj.id;

              // Extract chunk number from the original ID
              const chunkMatch = originalId.match(/chunk_(\d+)$/);
              const chunkNumber = chunkMatch ? chunkMatch[1] : '0';

              // Create a hash-based ID that's guaranteed to be under 64 bytes
              // Format: fileId-chunkNumber (e.g., "68513989fc41a0a8832d53f0-0")
              const fileId = initialSetup.fileInfo.fileId;
              const shortId = `${fileId}-${chunkNumber}`;

              console.log(`🔍 [VECTORIZE] ID shortening: "${originalId}" (${originalId.length} bytes) → "${shortId}" (${shortId.length} bytes)`);

              let finalId: string;
              if (shortId.length > 64) {
                // Fallback: Use just the last 8 chars of fileId + chunk number
                const fallbackId = `${fileId.slice(-8)}-${chunkNumber}`;
                console.log(`🔍 [VECTORIZE] Fallback ID: "${fallbackId}" (${fallbackId.length} bytes)`);

                if (fallbackId.length > 64) {
                  throw new Error(`Even fallback ID too long: ${fallbackId.length} bytes (max 64)`);
                }

                finalId = fallbackId;
              } else {
                finalId = shortId;
              }

              // Use the embedding object directly since it already has the correct structure
              return {
                id: finalId,
                values: embeddingObj.values,
                metadata: {
                  ...embeddingObj.metadata,
                  originalId: originalId, // Store original ID in metadata for reference
                  whiteLabelId: vectorizeConfig.whitelabelId,
                  originalName: initialSetup.fileInfo.fileName,
                  fileObjectKey: initialSetup.fileInfo.objectKey
                }
              };
            });

            console.log(`🔄 [VECTORIZE] Uploading ${vectorsWithEmbeddings.length} vectors to index: ${vectorIndex}`);

            // Upload vectors to Cloudflare Vectorize
            await vectorizeApi.upsert(vectorIndex, vectorsWithEmbeddings);

            console.log(`✅ [VECTORIZE] Successfully uploaded ${vectorsWithEmbeddings.length} vectors to Cloudflare Vectorize!`);
            console.log(`✅ [VECTORIZE] Audio chunks are now searchable in RAG retrieval!`);
          });
        } catch(error) {
          console.error(`❌ [WORKFLOW] uploadVectorsToVectorize failed:`, error);
          // This is critical - if vectors aren't uploaded, the chunks won't be searchable
          throw error;
        }
      } else {
        console.warn("⚠️ [WORKFLOW] No embeddings to upload to Vectorize - chunks will not be searchable!");
      }

      // Step 11: Set file status to EDITING before adding chunks
      await updateFileStatus(
        step,
        context,
        initialSetup.fileInfo.fileId,
        RagVectorTextChunksStatus.EDITING,
        vectorizeConfig
      );

      // Step 12: Add chunks to file record
      await addChunksToFileRecord(
        step,
        context,
        filteredChunks,
        vectorizeConfig,
        initialSetup
      );

      // Step 13: Update file status to COMPLETED
      // Use the updateFileStatus function with built-in retry mechanism
      console.log(`🔄 Updating file status to COMPLETED: ${initialSetup.fileInfo.fileId}`);

      // Use the same updateFileStatus function that we used for EDITING
>>>>>>> WA-170_MCP
      await updateFileStatus(
        step,
        context,
        initialSetup.fileInfo.fileId,
<<<<<<< HEAD
        RagVectorTextChunksStatus.EDITING,
        vectorizeConfig
      );

      // Step 12: Add chunks to file record
      await addChunksToFileRecord(
        step,
        context,
        filteredChunks,
        vectorizeConfig,
        initialSetup
      );

      // Step 13: Update file status to COMPLETED
      // Use the updateFileStatus function with built-in retry mechanism
      console.log(`🔄 Updating file status to COMPLETED: ${initialSetup.fileInfo.fileId}`);

      // Use the same updateFileStatus function that we used for EDITING
      await updateFileStatus(
        step,
        context,
        initialSetup.fileInfo.fileId,
        RagVectorTextChunksStatus.COMPLETED,
        vectorizeConfig
      );

      // Step 14: Link file to RAG if ragId is provided
      if(vectorizeConfig.ragId) {
=======
        RagVectorTextChunksStatus.COMPLETED,
        vectorizeConfig
      );

      // Step 14: Link file to RAG if ragVectors or ragId is provided
      console.log(`🚀🚀🚀 [WORKFLOW] Step 14: Checking if ragVectors or ragId is provided`);
      console.log(`🚀🚀🚀 [WORKFLOW] vectorizeConfig.ragVectors:`, vectorizeConfig.ragVectors);
      console.log(`🚀🚀🚀 [WORKFLOW] vectorizeConfig.ragId:`, vectorizeConfig.ragId);
      console.log(`🚀🚀🚀 [WORKFLOW] ragVectors exists:`, !!vectorizeConfig.ragVectors);
      console.log(`🚀🚀🚀 [WORKFLOW] ragId exists:`, !!vectorizeConfig.ragId);

      // APPROACH 1: Use ragVectors array (new approach)
      if(vectorizeConfig.ragVectors?.length > 0) {
        console.log(`🚀🚀🚀 [WORKFLOW] Step 14: Using ragVectors array approach`);
        console.log(`🚀🚀🚀 [WORKFLOW] linkFileToRag parameters:`, {
          ragVectors: vectorizeConfig.ragVectors,
          whitelabelId: vectorizeConfig.whitelabelId,
          fileId: initialSetup.fileInfo.fileId,
          hasAuth0Token: !!vectorizeConfig.auth0Token
        });

        for(const ragId of vectorizeConfig.ragVectors) {
          console.log(`🚀🚀🚀 [WORKFLOW] Linking file to RAG: ${ragId}`);
          await linkFileToRag(
            step,
            context,
            ragId,
            vectorizeConfig.whitelabelId,
            initialSetup.fileInfo.fileId,
            vectorizeConfig.auth0Token
          );
        }
        console.log(`🚀🚀🚀 [WORKFLOW] Step 14: ragVectors linkFileToRag completed successfully`);
      }
      // APPROACH 2: Fallback to single ragId (old approach compatibility)
      else if(vectorizeConfig.ragId) {
        console.log(`🚀🚀🚀 [WORKFLOW] Step 14: Using single ragId fallback approach`);
        console.log(`🚀🚀🚀 [WORKFLOW] Fallback linkFileToRag parameters:`, {
          ragId: vectorizeConfig.ragId,
          whitelabelId: vectorizeConfig.whitelabelId,
          fileId: initialSetup.fileInfo.fileId,
          hasAuth0Token: !!vectorizeConfig.auth0Token
        });

        console.log(`🚀🚀🚀 [WORKFLOW] Linking file to RAG (fallback): ${vectorizeConfig.ragId}`);
>>>>>>> WA-170_MCP
        await linkFileToRag(
          step,
          context,
          vectorizeConfig.ragId,
          vectorizeConfig.whitelabelId,
          initialSetup.fileInfo.fileId,
          vectorizeConfig.auth0Token
        );
<<<<<<< HEAD
      }

      // Update workflow metadata with success status
      initialSetup.metadata.status = RagVectorTextChunksStatus.COMPLETED;
      await upsertWorkflowMetadata(step, context, initialSetup.metadata);

=======
        console.log(`🚀🚀🚀 [WORKFLOW] Step 14: ragId fallback linkFileToRag completed successfully`);
      } else {
        console.log(`🚀🚀🚀 [WORKFLOW] Step 14: Skipping linkFileToRag - no ragVectors or ragId provided`);
      }

      // Step 15: Final verification that all files are accessible
      await step.do("finalVerification", {
        retries: {
          limit: 3,
          delay: "2 seconds",
          backoff: "exponential"
        },
        timeout: "30 seconds"
      }, async ()=>{
        console.log(`🔍 [FINAL VERIFICATION] Ensuring all files are accessible before completing workflow`);

        // Use the same direct R2 client for verification as we used for storage
        console.log(`🔍 [FINAL VERIFICATION] Using direct R2 client for verification (same as storage)`);

        // Verify chunks file exists and is readable
        const chunksObjectKey = `${vectorizeConfig.whitelabelId}/${initialSetup.fileInfo.fileId}-chunks.json`;
        console.log(`🔍 [FINAL VERIFICATION] Checking chunks file: ${chunksObjectKey}`);

        const chunksFile = await this.storageClient.get(chunksObjectKey);
        if(!chunksFile) {
          throw new Error(`Chunks file not accessible: ${chunksObjectKey}`);
        }
        console.log(`✅ [FINAL VERIFICATION] Chunks file verified: ${chunksObjectKey}`);

        // Verify embeddings file exists and is readable
        const embeddingsObjectKey = `${vectorizeConfig.whitelabelId}/${initialSetup.fileInfo.fileId}-cloudflare-v2-embeddings.json`;
        console.log(`🔍 [FINAL VERIFICATION] Checking embeddings file: ${embeddingsObjectKey}`);

        const embeddingsFile = await this.storageClient.get(embeddingsObjectKey);
        if(!embeddingsFile) {
          throw new Error(`Embeddings file not accessible: ${embeddingsObjectKey}`);
        }
        console.log(`✅ [FINAL VERIFICATION] Embeddings file verified: ${embeddingsObjectKey}`);

        console.log(`✅ [FINAL VERIFICATION] All files verified and accessible using direct R2 client`);

        // Add a small delay to ensure any pending writes are complete
        await new Promise(resolve=>setTimeout(resolve, 1000));
        console.log(`✅ [FINAL VERIFICATION] Final verification completed successfully`);
      });

      // Update workflow metadata with success status
      initialSetup.metadata.status = RagVectorTextChunksStatus.COMPLETED;
      await upsertWorkflowMetadata(step, context, initialSetup.metadata);

>>>>>>> WA-170_MCP
      // Clean up processor resources
      if(processor) {
        await step.do("cleanupProcessor", async ()=>{
          try {
            console.log("🧹 Cleaning up processor resources");
            await processor.dispose();
            console.log("✅ Processor resources cleaned up successfully");
          }catch(cleanupError) {
            console.warn(`⚠️ Error cleaning up processor: ${cleanupError.message}`);
            // Don't rethrow - we want to continue with the workflow
          }
        });
      }

    }catch(error) {
      console.error("❌ Workflow error:", error);

      // Update file status to ERROR if we have a file ID
      if(initialSetup?.fileInfo?.fileId) {
        try {
          await updateFileStatus(
            step,
            context,
            initialSetup.fileInfo.fileId,
            RagVectorTextChunksStatus.FAILED_TO_CHUNK,
            vectorizeConfig
          );
        }catch(statusError) {
          console.error("❌ Error updating file status to ERROR:", statusError);
          // Don't rethrow - we want to continue with the cleanup
        }
      }

      // Update workflow metadata with error status
      if(initialSetup?.metadata) {
        try {
          initialSetup.metadata.status = RagVectorTextChunksStatus.FAILED_TO_CHUNK;
          initialSetup.metadata.error = error.message || String(error);
          await upsertWorkflowMetadata(step, context, initialSetup.metadata, error);
        }catch(metadataError) {
          console.error("❌ Error updating workflow metadata:", metadataError);
          // Don't rethrow - we want to continue with the cleanup
        }
      }

      // Clean up processor resources in error case
      if(processor) {
        await step.do("cleanupProcessorOnError", async ()=>{
          try {
            console.log("🧹 Cleaning up processor resources after error");
            await processor.dispose();
            console.log("✅ Processor resources cleaned up successfully after error");
          }catch(cleanupError) {
            console.warn(`⚠️ Error cleaning up processor after error: ${cleanupError.message}`);
            // Don't rethrow - we want to continue with the error handling
          }
        });
      }

      // Rethrow non-retryable errors
      if(error instanceof NonRetryableError) {
        throw error;
      }

      // Rethrow other errors to trigger retry
      throw error;
    }
  }
}
