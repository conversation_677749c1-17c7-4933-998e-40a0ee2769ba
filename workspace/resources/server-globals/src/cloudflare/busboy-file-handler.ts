import mime from "mime-types";
import { S3 as R2, GetObjectCommand, HeadObjectCommand } from "@aws-sdk/client-s3";
import { Upload } from "@aws-sdk/lib-storage";

import { HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";

import { uniqueId } from "@divinci-ai/utils";

import { Readable } from "node:stream";
import { FileInfo } from "busboy";

import { extname as pathExtname } from "node:path";

export interface FileHandler<T> {
  addStream(
    params: { stream: Readable, info: FileInfo, key?: string }
  ): { pointer: T, uploadPromise: Promise<any> },
  getStream(pointer: T): Promise<{ stream: Readable, info: FileInfo }>,
}

export type R2FilePointer = { bucket: string, objectKey: string };
export class R2BusBoyFileHandler implements FileHandler<R2FilePointer> {
  private isLocalMode: boolean;
<<<<<<< HEAD
=======
  private forceR2Storage: boolean;
>>>>>>> WA-170_MCP

  constructor(
    private r2: R2,
    private bucket: string,
    private metadata: Record<string, string>,
    private allowedMimetypes: null | Set<string> = null
  ){
    // Check if we're in local development mode
    this.isLocalMode = process.env.ENVIRONMENT === "local" ||
                       process.env.NODE_ENV === "development" ||
                       process.env.NODE_ENV === "local";
<<<<<<< HEAD
=======
    this.forceR2Storage = process.env.FORCE_R2_STORAGE === "true";
>>>>>>> WA-170_MCP
  }
  addStream({ stream, info, key }: { stream: Readable, info: FileInfo, key?: string }){
    // Diagnostics: always log which path is being taken
    console.log(`[R2BusBoyFileHandler] addStream: isLocalMode=`, this.isLocalMode, ", forceR2Storage=", this.forceR2Storage, ", ENVIRONMENT=", process.env.ENVIRONMENT, ", NODE_ENV=", process.env.NODE_ENV, ", FORCE_R2_STORAGE=", process.env.FORCE_R2_STORAGE);

    // Generate objectKey synchronously so it's available immediately
    const originalName = info.filename;
    const mimeType = mime.lookup(info.filename);
    if(mimeType === false){
      throw new Error("Unknown file mimetype");
    }
    if(this.allowedMimetypes && !this.allowedMimetypes.has(mimeType)){
      throw new Error("Mimetype not allowed");
    }
    const mimeExtension = pathExtname(info.filename);
    if(!mimeExtension || mimeExtension === "."){
      throw new Error("Bad Extension");
    }

    // Use the provided key or generate a new one
    const objectKey = key || `${uniqueId()}${mimeExtension}`;
<<<<<<< HEAD

    // In local mode with MinIO, we need to ensure the bucket exists
    // and use it directly without combining it with the hostname
    const bucketName = this.bucket;

    console.log(`📦 Uploading file to bucket: ${bucketName}, key: ${objectKey}, isLocalMode: ${this.isLocalMode}`);

    try {
      // Create a new client with forcePathStyle set to true for local mode
      const client = this.isLocalMode ?
        new R2({
          endpoint: this.r2.config.endpoint,
          region: this.r2.config.region,
          credentials: this.r2.config.credentials,
          forcePathStyle: true
        }) :
        this.r2;

      const parallelUploads3 = new Upload({
        client: client,
        params: {
          Bucket: bucketName,
          Key: objectKey,
          Body: stream,
          ContentType: info.mimeType,
          Metadata: {
            ...this.metadata,
            originalName,
            encoding: info.encoding
          }
        },
      });

      return { pointer: { bucket: bucketName, objectKey }, uploadPromise: parallelUploads3.done() };
    } catch (error) {
      console.error(`❌ Error creating upload for bucket: ${bucketName}, key: ${objectKey}`, error);
      throw error;
    }
=======
    console.log(`🔑 Generated objectKey: ${objectKey} for file: ${originalName}`);

    // Wrap the async logic in an IIFE to keep the sync signature
    const uploadPromise = (async () => {

      // Ensure bucket exists and log details if in local mode OR forceR2Storage
      if (this.isLocalMode || this.forceR2Storage) {
        try {
          const { ensureBucketExists } = await import("../cloudflare/ensure-bucket-exists.js");
          const client = new R2({
            endpoint: this.r2.config.endpoint,
            region: this.r2.config.region,
            credentials: this.r2.config.credentials,
            forcePathStyle: true
          });
          console.log(`📦 [R2BusBoyFileHandler] About to ensure bucket exists:`, {
            bucket: this.bucket,
            endpoint: this.r2.config.endpoint,
            region: this.r2.config.region,
            credentialsType: typeof this.r2.config.credentials,
            isLocalMode: this.isLocalMode,
            forceR2Storage: this.forceR2Storage
          });
          await ensureBucketExists(client, this.bucket);
        } catch (err) {
          console.warn(`⚠️ Failed to ensure bucket exists: ${this.bucket}`, err);
        }
      }

      // In local mode with MinIO, we need to ensure the bucket exists
      // and use it directly without combining it with the hostname
      const bucketName = this.bucket;
      console.log(`📦 Uploading file to bucket: ${bucketName}, key: ${objectKey}, isLocalMode: ${this.isLocalMode}, forceR2Storage: ${this.forceR2Storage}`);
      // Add more detailed logging for debugging bucket creation/auth issues
      console.log(`[R2BusBoyFileHandler] Credentials:`, JSON.stringify({
        credentialsType: typeof this.r2.config.credentials,
        endpoint: this.r2.config.endpoint,
        region: this.r2.config.region,
        bucket: bucketName
      }, null, 2));

      try {
        // Create a new client with forcePathStyle set to true for local mode
        const client = this.isLocalMode ?
          new R2({
            endpoint: this.r2.config.endpoint,
            region: this.r2.config.region,
            credentials: this.r2.config.credentials,
            forcePathStyle: true
          }) :
          this.r2;

        const parallelUploads3 = new Upload({
          client: client,
          params: {
            Bucket: bucketName,
            Key: objectKey,
            Body: stream,
            ContentType: info.mimeType,
            Metadata: {
              ...this.metadata,
              originalName,
              encoding: info.encoding
            }
          },
        });

        return { pointer: { bucket: bucketName, objectKey }, uploadPromise: parallelUploads3.done() };
      } catch (error) {
        console.error(`❌ Error creating upload for bucket: ${bucketName}, key: ${objectKey}`, error);
        throw error;
      }
    })();

    console.log(`🎯 Returning pointer with objectKey: ${objectKey} for file: ${originalName}`);

    // Return the pointer and uploadPromise as required by the interface
    return { pointer: { bucket: this.bucket, objectKey }, uploadPromise };
>>>>>>> WA-170_MCP
  }
  async getStream(pointer: R2FilePointer){
    console.log(`🔍 Getting stream for bucket: ${pointer.bucket}, key: ${pointer.objectKey}, isLocalMode: ${this.isLocalMode}`);

    // In local mode with MinIO, we need to use the bucket name directly
    const bucketName = pointer.bucket;

    try {
      // Create a new client with forcePathStyle set to true for local mode
      const client = this.isLocalMode ?
        new R2({
          endpoint: this.r2.config.endpoint,
          region: this.r2.config.region,
          credentials: this.r2.config.credentials,
          forcePathStyle: true
        }) :
        this.r2;

      const objectHead = await client.send(new HeadObjectCommand({
        Bucket: bucketName,
        Key: pointer.objectKey
      }));

      const filename = objectHead.Metadata?.originalName;
      const mimeType = objectHead.ContentType;
      const encoding = objectHead.Metadata?.encoding;

      if(typeof mimeType === "undefined" || typeof encoding === "undefined" || typeof filename === "undefined"){
        throw new Error("Could not retrieve file info");
      }

      const info: FileInfo = { filename, mimeType, encoding };

      const { Body } = await client.send(new GetObjectCommand({
        Bucket: bucketName,
        Key: pointer.objectKey
      }));

      if(typeof Body === "undefined"){
        throw new Error("Not Found");
      }

      return { stream: Body as Readable, info };
    } catch (error) {
      console.error(`❌ Error getting stream for bucket: ${bucketName}, key: ${pointer.objectKey}`, error);
      throw error;
    }
  }
  deleteStream(pointer: R2FilePointer){
    console.log(`🗑️ Deleting stream for bucket: ${pointer.bucket}, key: ${pointer.objectKey}, isLocalMode: ${this.isLocalMode}`);

    // In local mode with MinIO, we need to use the bucket name directly
    const bucketName = pointer.bucket;

    try {
      // Create a new client with forcePathStyle set to true for local mode
      const client = this.isLocalMode ?
        new R2({
          endpoint: this.r2.config.endpoint,
          region: this.r2.config.region,
          credentials: this.r2.config.credentials,
          forcePathStyle: true
        }) :
        this.r2;

      return client.deleteObject({
        Bucket: bucketName,
        Key: pointer.objectKey
      });
    } catch (error) {
      console.error(`❌ Error deleting stream for bucket: ${bucketName}, key: ${pointer.objectKey}`, error);
      throw error;
    }
  }
}
