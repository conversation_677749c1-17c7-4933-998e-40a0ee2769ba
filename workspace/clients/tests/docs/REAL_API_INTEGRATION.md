# Real API Integration Testing Guide

This guide documents how to set up and run real API integration tests that connect to actual backend services with real Auth0 authentication.

## 🎯 **Overview**

The real API integration tests provide:
- ✅ **Real Auth0 Authentication** - Actual JWT tokens from Auth0
- ✅ **Real API Server** - Live backend API on `localhost:18081`
- ✅ **Real Database** - Actual MongoDB and Redis connections
- ✅ **Real Business Logic** - Full end-to-end API testing

## 🏗️ **Architecture**

### **Hybrid Testing Approach**
- **Mock API Tests** - Fast, isolated unit tests with mock responses
- **Real API Tests** - Comprehensive integration tests with real services

### **Authentication Strategy**
- **Service Tokens** - Auth0 Client Credentials Grant for admin/owner roles
- **User Tokens** - Auth0 Resource Owner Password Grant for user roles (future)
- **Token Caching** - Persistent auth state files for performance

## 🚀 **Quick Start**

### **1. Prerequisites**
Ensure Docker services are running:
```bash
# Start test API services
cd docker
docker compose -f test-api.yml up -d

# Verify services are healthy
docker ps
curl http://localhost:18081/
```

### **2. Run Real API Tests**
```bash
# Run with real Auth0 authentication
USE_REAL_AUTH0=true npx playwright test --project=api-tests src/tests/ai-chat-real-api.spec.ts

# Run specific test
USE_REAL_AUTH0=true npx playwright test --project=api-tests -g "should create a new chat"
```

### **3. Environment Variables**
The following environment variables are automatically loaded from `private-keys/test-api-local/`:

```bash
# Auth0 Configuration
AUTH0_BASE_URL=https://dev-46tiys6hnb6vbg17.us.auth0.com
AUTH0_CLIENT_ID=waO2KMbVwo3xXu1wqZyUeLA1cTinVpXx
AUTH0_AUDIENCE=chat.divinci.app:8081

# Service-to-Service Auth0
AUTH0_S2S_CLIENT_ID=krlVBet0lsm75UWfoO2MIfmgXkBaFujM
AUTH0_S2S_CLIENT_SECRET=<secret>

# Test User Credentials
AUTH0_TEST_USER_EMAIL=<EMAIL>
AUTH0_TEST_USER_PASSWORD=<password>
```

## 🔧 **Configuration**

### **API Server Setup**
The test API server runs on `localhost:18081` and requires:

1. **Environment Variables** - Loaded from `private-keys/test-api-local/`
2. **Database Connections** - MongoDB and Redis
3. **Workflow API** - Cloudflare Workers endpoint

### **Auth0 Setup**
Real Auth0 authentication uses:

1. **Client Credentials Grant** - For service-level access (admin/owner)
2. **Resource Owner Password Grant** - For user-level access (future)
3. **Correct Audience** - Must match API server expectation

### **Test Configuration**
Key configuration in `workspace/clients/tests/src/config/config.ts`:

```typescript
const defaultConfig: Config = {
  apiBaseUrl: 'http://localhost:18081', // Test API server
  auth0: {
    audience: 'chat.divinci.app:8081' // Must match API server
  }
};
```

## 📝 **API Specifications**

### **Chat Creation**
```typescript
// Request
POST /ai-chat
{
  "title": "Chat Title",
  "releases": [] // Required field
}

// Response
{
  "chat": {
    "_id": "...",
    "title": "Chat Title",
    "ownerUser": "...",
    "releases": [],
    "transcriptId": "...",
    "createdAt": 1234567890,
    "updatedAt": 1234567890
  },
  "transcript": {
    "_id": "...",
    "messages": [],
    "awaitingResponse": false
  }
}
```

### **Chat Listing**
```typescript
// Request
GET /ai-chat

// Response
[
  {
    "_id": "...",
    "title": "Chat Title",
    "ownerUser": "...",
    "releases": [],
    "createdAt": 1234567890,
    "updatedAt": 1234567890
  }
]
```

## 🐛 **Troubleshooting**

### **Common Issues**

#### **401 Unauthorized**
- **Cause**: Auth0 token issues
- **Solution**: Check audience configuration and token generation
- **Debug**: Enable `logApiCalls: true` in config

#### **400 Bad Request - "Unexpected 'aud' value"**
- **Cause**: Token audience doesn't match API expectation
- **Solution**: Ensure `AUTH0_AUDIENCE=chat.divinci.app:8081`

#### **400 Bad Request - "releases is an object but not expecting one"**
- **Cause**: Missing required `releases` field in request
- **Solution**: Include `releases: []` in chat creation requests

#### **API Server Not Responding**
- **Cause**: Docker services not running or unhealthy
- **Solution**: Restart Docker services and check logs

```bash
# Check service status
docker ps
docker logs divinci-test-api-api-1

# Restart services
cd docker
docker compose -f test-api.yml restart
```

### **Debug Mode**
Enable detailed logging:

```typescript
// In config.ts
const defaultConfig: Config = {
  logApiCalls: true, // Enable API request/response logging
};
```

## 🔄 **Development Workflow**

### **1. Mock Tests First**
Always start with mock API tests for rapid development:
```bash
npx playwright test --project=api-tests src/tests/vector.spec.ts
```

### **2. Real API Validation**
Use real API tests to validate integration:
```bash
USE_REAL_AUTH0=true npx playwright test --project=api-tests src/tests/ai-chat-real-api.spec.ts
```

### **3. Continuous Integration**
- **Mock tests** run in CI for speed
- **Real API tests** run in staging/integration environments

## 📊 **Current Status**

### **✅ Working Features**
- ✅ Real Auth0 authentication
- ✅ Chat creation and listing
- ✅ Database persistence
- ✅ Service token generation

### **🔄 In Progress**
- ⚠️ User token authentication (Resource Owner Password Grant)
- ⚠️ Complete test suite coverage
- ⚠️ Error condition testing

### **📈 Future Enhancements**
- 🔮 Real user authentication flow
- 🔮 Multi-tenant testing
- 🔮 Performance testing
- 🔮 Load testing

## 🎯 **Success Metrics**

The real API integration is considered successful when:
- ✅ All authentication flows work
- ✅ All CRUD operations work
- ✅ Error conditions are properly handled
- ✅ Performance is acceptable
- ✅ Tests are reliable and maintainable

---

*Last updated: January 2025*
*Status: ✅ Core functionality working, expanding test coverage*
