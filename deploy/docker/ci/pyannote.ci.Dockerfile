ARG DOCKER_ORG="openai-api-4375643"
ARG TAG=develop
# ARG SKIP_PRIVATE_KEYS=false

# Build stage
FROM python:3.11.11-bookworm AS builder

ARG APP_FOLDER

# Install system dependencies for PyMuPDF
RUN apt-get update && \
    apt-get install -y \
    cmake \
    pkg-config \
    build-essential \
    libprotobuf-dev \
    protobuf-compiler \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Switch to root for permissions
USER root

# Create app directory and set permissions
RUN mkdir -p /home/<USER>/app && \
  mkdir -p /openparse-tmp && \
  addgroup --system python && \
  adduser --system --group python && \
  chown -R python:python /home/<USER>/openparse-tmp

WORKDIR /home/<USER>/

# Bundle app source
COPY ./${APP_FOLDER} ./app/

WORKDIR /home/<USER>/app

# Install build dependencies and compile requirements
RUN pip install --upgrade pip && \
    pip install python-dotenv && \
    pip install gunicorn && \
    # Configure pip to retry downloads and use a longer timeout
    pip config set global.timeout 300 && \
    pip config set global.retries 10 && \
    # Install requirements with retry mechanism
    pip install --no-cache-dir -r requirements.txt || \
    pip install --no-cache-dir -r requirements.txt || \
    pip install --no-cache-dir -r requirements.txt && \
    # Explicitly install pyannote.audio with retry mechanism
    pip install --no-cache-dir pyannote.audio --no-deps && \
    pip install --no-cache-dir torch torchaudio && \
    pip install --no-cache-dir pyannote.audio

# Runtime stage
FROM python:3.11.11-bookworm

ARG DESCRIPTION
ARG ENVIRONMENT
ARG PORT="8085"

# Metadata
LABEL org.opencontainers.image.description=${DESCRIPTION}

# Set up directories and permissions
USER root
RUN mkdir -p /home/<USER>/app && \
    mkdir -p /home/<USER>/app/env && \
    mkdir -p /file-tmp && \
    mkdir -p /etc/ssl/certs && \
    mkdir -p /etc/ssl/private && \
    chmod 755 /etc/ssl/certs && \
    chmod 750 /etc/ssl/private && \
    # Create python user with proper home directory
    addgroup --system python && \
    adduser --system --group python --home /home/<USER>
    # Create cache directories for matplotlib and huggingface
    mkdir -p /home/<USER>/.cache && \
    mkdir -p /home/<USER>/.config && \
    mkdir -p /home/<USER>/.cache/matplotlib && \
    mkdir -p /home/<USER>/.cache/huggingface && \
    # Set proper permissions
    chown -R python:python /home/<USER>/file-tmp && \
    chmod -R 755 /file-tmp

# Install runtime dependencies as root
RUN apt-get update && \
    apt-get install -y software-properties-common python3-launchpadlib apt-transport-https && \
<<<<<<< HEAD
    # Install FFmpeg using the same approach as the local Dockerfile
=======
    # Install FFmpeg using the same approach as the local Dockerfile with PPA for better codec support
    add-apt-repository ppa:savoury1/ffmpeg4 && \
    apt-get update && \
>>>>>>> WA-170_MCP
    apt-get install -y ffmpeg && \
    # Install additional dependencies
    apt-get install -y cmake pkg-config build-essential libprotobuf-dev protobuf-compiler && \
    rm -rf /var/lib/apt/lists/* && \
    pip install --upgrade pip && \
    pip install gunicorn>=23.0.0 && \
    pip install python-dotenv && \
    pip install flask && \
    pip install flask-cors && \
    pip install waitress>=2.1.2 && \
    pip install pyopenssl>=24.0.0

WORKDIR /home/<USER>/

# Copy application files from builder
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /home/<USER>/app ./app/

# Create environment directory with default configuration
RUN mkdir -p ./app/env/ && \
    echo "# Default configuration created during Docker build" > ./app/env/.env.default && \
    chown -R python:python ./app/env/

# Copy environment-specific configuration
COPY ./private-keys/${ENVIRONMENT}/pyannote.env ./app/env/.env
RUN chown python:python ./app/env/.env && chmod 640 ./app/env/.env

# Add a dummy file in the private-keys directory to ensure it exists
COPY ./deploy/docker/ci/service-mtls-entrypoint.sh /private-keys-placeholder.sh

# Copy and set up entrypoint script
COPY ./deploy/docker/ci/service-mtls-entrypoint.sh /service-mtls-entrypoint.sh
RUN chmod +x /service-mtls-entrypoint.sh

WORKDIR /home/<USER>/app

# Switch to python user for runtime
USER python

# Cloud Run specific environment variables
ENV PYTHONUNBUFFERED=1
ENV ENV_FOLDER="/home/<USER>/app/env"
ENV PYTHONPATH=/home/<USER>/app
ENV PORT=${PORT}
ENV THREADS=8
# Set proper home directory and cache directories
ENV HOME=/home/<USER>
ENV MPLCONFIGDIR=/home/<USER>/.cache/matplotlib
ENV HF_HOME=/home/<USER>/.cache/huggingface
ENV XDG_CACHE_HOME=/home/<USER>/.cache

# Expose the port
EXPOSE ${PORT}

# Use the entrypoint script instead of direct command
ENTRYPOINT ["/service-mtls-entrypoint.sh", "pyannote"]