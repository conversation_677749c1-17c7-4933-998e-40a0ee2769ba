import { test as base, expect } from '@playwright/test';
import path from 'path';
import fs from 'fs';

// Define a custom test that doesn't send Cloudflare headers to external services
const test = base.extend({
  context: async ({ browser }, use) => {
    // Create a context without the Cloudflare headers
    const context = await browser.newContext({
      extraHTTPHeaders: {} // Empty headers to override the default ones
    });
    await use(context);
  }
});

/**
 * E2E test for the complete audio transcript flow
 * This test verifies the entire flow from login to whitelabel creation to audio transcript upload
 */
test.describe('Audio Transcript Complete Flow', () => {
  // Define the path to the test zip file
  const zipFilePath = path.resolve(__dirname, '../../test-files/homer-media-zipped.zip');

  // Before running tests, check that the test file exists
  test.beforeAll(() => {
    // Verify that the test file exists
    expect(fs.existsSync(zipFilePath)).toBeTruthy();
  });

  test('should complete the entire audio transcript flow', async ({ page }) => {
    console.log('Starting complete audio transcript flow test');

    // Step 1: Verify Login (using storage state)
    console.log('Step 1: Verify Login');

    // Navigate to the home page
    console.log('Navigating to home page');
    await page.goto('http://localhost:8080');
    await page.waitForLoadState('networkidle');

    // Verify that we're logged in
    const organizationMenu = page.locator('a.navbar-link[href="/user-group"]:has-text("Organization")');
    await expect(organizationMenu).toBeVisible({ timeout: 15000 });
    console.log('Login verified successfully');

    // Step 2: Create Whitelabel
    console.log('Step 2: Create Whitelabel');

    // Hover over Organization menu in the header
    console.log('Hovering over Organization menu in the header');
    await page.hover('a.navbar-link[href="/user-group"]:has-text("Organization")');

    // Wait a moment for the dropdown to appear
    await page.waitForTimeout(1000);

    // Click on Whitelabel option in the dropdown
    console.log('Clicking on Whitelabel option in the dropdown');
    await page.click('a.dropdown-item[href="/white-label"]:has-text("Whitelabel")');

    // Wait for the whitelabel page to load
    await page.waitForLoadState('networkidle');

    // Generate a unique name for the whitelabel
    const timestamp = new Date().getTime();
    const whitelabelName = `Test Whitelabel ${timestamp}`;

    // Click on Create Whitelabel button
    console.log('Clicking on Create Whitelabel button');
    await page.click('text=Create Whitelabel');

    // Wait for the create whitelabel form to load
    await page.waitForLoadState('networkidle');

    // Fill out the whitelabel form
    console.log('Filling out the whitelabel form');
    await page.fill('input.input[type="text"]', whitelabelName);
    await page.fill('textarea#description', 'This is a test whitelabel created by Playwright E2E test');

    // Check the terms and conditions checkbox
    await page.check('input[type="checkbox"]');

    // Click the Create AI button
    console.log('Clicking Create AI button');
    await page.click('button.create-whitelabel-button');

    // Wait for the whitelabel to be created and redirected
    await page.waitForLoadState('networkidle');

    // Verify that the whitelabel was created successfully
    await expect(page.locator('h1:has-text("Whitelabel")')).toBeVisible();

    console.log('Whitelabel created successfully');

    // Step 3: Upload Audio Transcript
    console.log('Step 3: Upload Audio Transcript');

    // Now find and click on the Audio Transcript option in the dropdown menu
    console.log('Looking for the dropdown menu');

    // Hover over the chat-title dropdown trigger
    const dropdownTrigger = page.locator('.dropdown-trigger');
    await dropdownTrigger.hover();
    console.log('Hovering over dropdown trigger');

    // Wait a moment for the dropdown to appear
    await page.waitForTimeout(1000);

    // Click on the Audio Transcripts option in the dropdown
    console.log('Clicking on Audio Transcripts option');
    await page.click('a.dropdown-item:has-text("📝 Audio Transcripts")');
    await page.waitForLoadState('networkidle');

    // Click on Create Tools link
    console.log('Clicking on Create Tools link');
    await page.click('a:has-text("Create Tools")');

    // Wait for the create tools page to load
    await page.waitForLoadState('networkidle');

    // Set the file input to our test zip file
    console.log('Uploading test zip file');
    const fileInput = await page.locator('input[type="file"]');
    await fileInput.setInputFiles(zipFilePath);

    // Wait for the file to be processed and displayed
    await page.waitForTimeout(2000); // Give it a moment to process the file
    console.log('File uploaded, waiting for processing');

    // Select the diarization tool (first option)
    console.log('Selecting diarization tool');
    await page.locator('select[name="diarizerTool"]').selectOption({ index: 0 });

    // Select the transcription tool (first option)
    console.log('Selecting transcription tool');
    await page.locator('select[name="transcriberTool"]').selectOption({ index: 0 });

    // Click the submit button to start the upload
    console.log('Clicking submit button');
    await page.click('button:has-text("Submit")');

    // Wait for the upload to complete and redirect to the processing page
    await page.waitForLoadState('networkidle');
    console.log('Submitted file, waiting for processing');

    // Wait for the processing to complete (this may take some time)
    console.log('Waiting for processing to complete (this may take up to 2 minutes)...');
    await page.waitForSelector('text=Transcript Complete', { timeout: 120000 });
    console.log('Processing completed');

    // Verify that we're on the transcript view page
    await expect(page.locator('h1:has-text("Audio Transcript")')).toBeVisible();

    // Verify that the transcript contains samples
    await page.waitForSelector('.transcript-row');
    const sampleCount = await page.locator('.transcript-row').count();
    expect(sampleCount).toBeGreaterThan(0);
    console.log(`Found ${sampleCount} transcript samples`);

    // Verify that the audio player is present
    await expect(page.locator('.audio-player')).toBeVisible();

    // Verify that the speaker labels are present
    await expect(page.locator('.speaker-label')).toBeVisible();

    // Verify that the transcript text is present
    await expect(page.locator('.transcript-text')).toBeVisible();

    console.log('Complete audio transcript flow test completed successfully');
  });
});
