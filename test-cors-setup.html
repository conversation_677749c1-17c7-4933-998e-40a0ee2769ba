<!DOCTYPE html>
<html>
<head>
    <title>CORS Test</title>
</head>
<body>
    <h1>CORS Setup Test</h1>
    <div id="status">Testing...</div>
    <div id="details"></div>

    <script>
        const statusDiv = document.getElementById('status');
        const detailsDiv = document.getElementById('details');
        
        async function testCors() {
            try {
                statusDiv.innerHTML = 'Testing CORS connection to backend...';
                
                const response = await fetch('https://localhost:9080/', {
                    method: 'GET',
                    credentials: 'include'
                });
                
                const data = await response.json();
                
                statusDiv.innerHTML = '✅ CORS Test Successful!';
                detailsDiv.innerHTML = `
                    <h3>Response Details:</h3>
                    <p><strong>Status:</strong> ${response.status}</p>
                    <p><strong>Data:</strong> ${JSON.stringify(data)}</p>
                    <p><strong>Origin:</strong> ${window.location.origin}</p>
                `;
            } catch (error) {
                statusDiv.innerHTML = '❌ CORS Test Failed';
                detailsDiv.innerHTML = `
                    <h3>Error Details:</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p><strong>Origin:</strong> ${window.location.origin}</p>
                `;
            }
        }
        
        // Run test when page loads
        testCors();
    </script>
</body>
</html>
