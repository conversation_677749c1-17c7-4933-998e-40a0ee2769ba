
> @divinci-ai/public-api@0.3.0 start:dev /workspaces/server/workspace/servers/public-api
> ts-node-dev --poll --transpile-only --ignore-watch node_modules --files src/index.ts

[INFO] 05:58:54 ts-node-dev ver. 2.0.0 (using ts-node ver. 10.9.2, typescript ver. 5.8.3)
🐞LOG_DEBUG MODE:  1
🔎 CURRENT ENVIRONMENT: dev
🔎 CORS FULL ORIGINS (raw): api.slack.com,dev-46tiys6hnb6vbg17-cd-hljzhimsn7o28fvq.edge.tenants.us.auth0.com,dev-46tiys6hnb6vbg17.us.auth0.com,local-chunks-vectorized,127.0.0.1:,sturdy-space-broccoli-g4xpjgv6376q-8080.app.github.dev,sturdy-space-broccoli-g4xpjgv6376q-8081.app.github.dev,sturdy-space-broccoli-g4xpjgv6376q-8082.app.github.dev,sturdy-space-broccoli-g4xpjgv6376q-8083.app.github.dev,sturdy-space-broccoli-g4xpjgv6376q-9080.app.github.dev,https://sturdy-space-broccoli-g4xpjgv6376q-8080.app.github.dev,https://sturdy-space-broccoli-g4xpjgv6376q-9080.app.github.dev
🔎 CORS FULL ORIGINS (cleaned): [
  'api.slack.com',
  'dev-46tiys6hnb6vbg17-cd-hljzhimsn7o28fvq.edge.tenants.us.auth0.com',
  'dev-46tiys6hnb6vbg17.us.auth0.com',
  'local-chunks-vectorized',
  '127.0.0.1:',
  'sturdy-space-broccoli-g4xpjgv6376q-8080.app.github.dev',
  'sturdy-space-broccoli-g4xpjgv6376q-8081.app.github.dev',
  'sturdy-space-broccoli-g4xpjgv6376q-8082.app.github.dev',
  'sturdy-space-broccoli-g4xpjgv6376q-8083.app.github.dev',
  'sturdy-space-broccoli-g4xpjgv6376q-9080.app.github.dev',
  'https://sturdy-space-broccoli-g4xpjgv6376q-8080.app.github.dev',
  'https://sturdy-space-broccoli-g4xpjgv6376q-9080.app.github.dev'
]
🎈 Attempting to connect to Redis in dev environment: redis://default:<EMAIL>:15121
🔄 Creating simple R2 client for fine-tune in local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🔄 Creating simple R2 client for RAG vectors in local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🔄 Creating simple Audio R2 client for local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🔄 Creating simple Public Transcript File R2 client for local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🔧 Local mode detected, using path-style URLs for S3 buckets
🔧 Using S3 endpoint: http://minio.divinci.local:9000
🔧 Using reliable MinIO endpoint: http://minio.divinci.local:9000
🪣 Using bucket name: workspace-audio, isLocalMode: true
🙌 Successful external call to: https://pokeapi.co/api/v2/pokemon/ditto
Attempting Mongoose Connection
🎈 MONGO_CONNECTION_URL: mongodb+srv://serverlessinstance0.c4pobzg.mongodb.net/divinci-dev
Attempting Redis Connection... 
🟢 Connected to Redis in dev environment at 2025-06-06T05:59:00.437Z
✅🌱 Successfully connected to Redis.
✅🌱 Successfully connected to MongoDB.
🔑 mTLS is disabled, using regular HTTP server
✅ Public API Server is running on port: 9080
🪪 remote address:  ::ffff:127.0.0.1
🪪 remote address:  ::ffff:127.0.0.1
🌐 Setting permissive CORS headers for request without origin
🙅🏻‍♂️🪪 No auth in req:  undefined
 ELIFECYCLE  Command failed.
