/**
 * List files in Dropbox (with media file filtering)
 */

import { Request<PERSON><PERSON><PERSON> } from "express";
import { ensureDropboxConnection, handleDropboxError } from "../util/ensure-whitelabel";
import { isMediaFile, processDropboxFile, sortFilesByName, sortFilesByDate, sortFilesBySize } from "@divinci-ai/server-globals";

interface ListFilesQuery {
  path?: string;
  recursive?: string;
  mediaOnly?: string;
  sortBy?: "name" | "date" | "size";
  limit?: string;
  cursor?: string;
}

export const listDropboxFiles: RequestHandler = async function(req, res, next) {
  try {
    const { connection, dropboxClient } = await ensureDropboxConnection(req);
    const { 
      path = "/", 
      recursive = "false", 
      mediaOnly = "true",
      sortBy = "name",
      limit = "1000",
      cursor
    }: ListFilesQuery = req.query;

    let response;
    
    if (cursor) {
      // Continue pagination
      response = await dropboxClient.listFolderContinue(cursor);
    } else {
      // Initial request
      response = await dropboxClient.listFolder({
        path: path === "/" ? "" : path,
        recursive: recursive === "true",
        limit: parseInt(limit, 10),
        include_media_info: true
      });
    }

    // Filter to only include files
    let files = response.entries
      .filter(entry => entry[".tag"] === "file")
      .map(file => processDropboxFile(file as any));

    // Filter to media files only if requested
    if (mediaOnly === "true") {
      files = files.filter(file => file.isAudio || file.isVideo);
    }

    // Sort files
    switch (sortBy) {
      case "date":
        files = sortFilesByDate(files);
        break;
      case "size":
        files = sortFilesBySize(files);
        break;
      case "name":
      default:
        files = sortFilesByName(files);
        break;
    }

    res.status(200).json({
      success: true,
      data: {
        files,
        hasMore: response.has_more,
        cursor: response.cursor,
        totalCount: files.length,
        path: path,
        filters: {
          mediaOnly: mediaOnly === "true",
          recursive: recursive === "true",
          sortBy
        }
      }
    });

  } catch (error) {
    try {
      await handleDropboxError(error, req.connection);
    } catch (handledError) {
      return next(handledError);
    }
    next(error);
  }
};
