#!/bin/bash
# Configure Codespace port forwarding for all services
# This script sets up proper port visibility and labels for development

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔧 Configuring Codespace port forwarding...${NC}"

# Function to check if a port is being forwarded
check_port_forwarding() {
  local port=$1
  local service_name=$2
  
  echo -e "${YELLOW}📡 Configuring port forwarding for $service_name (port $port)...${NC}"
  
  # Note: In VS Code, ports are automatically forwarded when detected
  # The visibility can be changed through the Ports panel UI
  echo "  - Port $port should be automatically forwarded"
  echo "  - Access via: https://sturdy-space-broccoli-g4xpjgv6376q-${port}.app.github.dev/"
  echo "  - To make public: Right-click port in PORTS panel → Port Visibility → Public"
}

echo -e "${YELLOW}🌐 Port Configuration Guide:${NC}"
echo
echo "Your services are running on these ports:"
echo

check_port_forwarding "8080" "Web Client"
check_port_forwarding "9080" "Public API" 
check_port_forwarding "8082" "Public API Live"
check_port_forwarding "8083" "Webhook API"

echo
echo -e "${YELLOW}📋 To configure port visibility in VS Code:${NC}"
echo "1. Open the PORTS panel (View → Other Views → Ports)"
echo "2. Right-click on any port"
echo "3. Select 'Port Visibility' → Choose visibility level:"
echo "   - Private (default): Requires GitHub authentication"
echo "   - Private to Organization: Accessible to org members"
echo "   - Public: Accessible to anyone with the URL"
echo
echo -e "${YELLOW}🔑 For API testing with tools like curl/Postman:${NC}"
echo "- Public ports: No authentication needed"
echo "- Private ports: Add header 'X-Github-Token: \$GITHUB_TOKEN'"
echo "- Get token with: echo \$GITHUB_TOKEN"
echo
echo -e "${GREEN}✅ Port forwarding information provided!${NC}"
echo "All ports should be automatically forwarded when services start."
