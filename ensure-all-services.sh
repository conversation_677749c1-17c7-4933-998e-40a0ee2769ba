#!/bin/bash
# Ensure all required services are running for Codespace

echo "🔍 Checking service status..."

# Check which ports should be running
REQUIRED_PORTS=(8080 8081 8082 8083 9080)
MISSING_PORTS=()

for PORT in "${REQUIRED_PORTS[@]}"; do
  if ! netstat -tlnp 2>/dev/null | grep -q ":$PORT "; then
    MISSING_PORTS+=($PORT)
    echo "❌ Port $PORT is not listening"
  else
    echo "✅ Port $PORT is listening"
  fi
done

if [ ${#MISSING_PORTS[@]} -eq 0 ]; then
  echo "🎉 All services are running!"
  exit 0
fi

echo "🚀 Starting missing services..."

# Start missing services based on port
for PORT in "${MISSING_PORTS[@]}"; do
  case $PORT in
    9080)
      echo "Starting public API on port 9080..."
      cd /workspaces/server
      code --goto .vscode/tasks.json
      echo "Please run: 'Start Public API' task"
      ;;
    8080)
      echo "Starting web client on port 8080..."
      cd /workspaces/server
      code --goto .vscode/tasks.json
      echo "Please run: 'Start Web Client' task"
      ;;
    8082)
      echo "Starting public API live on port 8082..."
      cd /workspaces/server
      code --goto .vscode/tasks.json
      echo "Please run: 'Start Public API Live' task"
      ;;
    8083)
      echo "Starting public API webhook on port 8083..."
      cd /workspaces/server
      code --goto .vscode/tasks.json
      echo "Please run: 'Start Public API Webhook' task"
      ;;
  esac
done
