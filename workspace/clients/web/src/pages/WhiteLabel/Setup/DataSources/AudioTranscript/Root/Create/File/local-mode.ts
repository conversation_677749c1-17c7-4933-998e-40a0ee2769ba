import { BodyType, SuccessResultSingle } from "./types";
import { replaceParams } from "@divinci-ai/utils";

export async function runnerLocalMode(
  auth0Fetch: any,
  params: { whitelabelId: string },
<<<<<<< HEAD
  body: BodyType | { mediaFile: File, diarizerTool: string, transcriberTool: string }
=======
  body: BodyType
>>>>>>> WA-170_MCP
): Promise<SuccessResultSingle> {
  console.log("🔄 Starting local mode audio upload", {
    whitelabelId: params.whitelabelId,
    diarizerTool: body.diarizerTool,
    transcriberTool: body.transcriberTool,
    hasMediaFile: !!body.mediaFile
  });
  
  // Create FormData for the file upload
  const formData = new FormData();
  
  // The mediaFile could be passed directly from memoRunner (as a single File)
  const mediaFile = body.mediaFile;
  
  if (mediaFile) {
    if (Array.isArray(mediaFile)) {
      // Handle array case (from the BodyType)
      if (mediaFile.length > 0) {
        console.log("🔍 Adding file from array:", mediaFile[0].name);
        formData.append("mediaFile", mediaFile[0]);
      } else {
        throw new Error("No media file provided in array");
      }
    } else {
      // Handle single File object case (passed directly from memoRunner)
      console.log("🔍 Adding single file:", mediaFile.name);
      formData.append("mediaFile", mediaFile);
    }
  } else {
    throw new Error("No media file provided");
  }
  
  formData.append("diarizerTool", body.diarizerTool);
  formData.append("transcriberTool", body.transcriberTool);
  formData.append("isLocalMode", "true"); // Flag for backend to use local mode
<<<<<<< HEAD
=======

  // Add confidence settings if available
  if (body.diarizeConfig?.confidence?.enabled) {
    formData.append("confidenceEnabled", "true");
    formData.append("confidenceThreshold", body.diarizeConfig.confidence.threshold.toString());
    formData.append("confidenceIncludeInProcessing", body.diarizeConfig.confidence.includeInProcessing.toString());
    console.log("✅ Added confidence settings to form data:", body.diarizeConfig.confidence);
  }

  // Add voiceprint settings if available
  if (body.voiceprintConfig?.enabled) {
    formData.append("voiceprintsEnabled", "true");
    formData.append("voiceprintsAutoCreate", body.voiceprintConfig.autoCreateFromHighConfidence.toString());

    // Add existing voiceprints for identification if available
    if (body.voiceprintConfig.existingVoiceprints && body.voiceprintConfig.existingVoiceprints.length > 0) {
      formData.append("voiceprintsData", JSON.stringify(body.voiceprintConfig.existingVoiceprints));
      console.log("🎯 [IDENTIFICATION] Added voiceprints for identification (local mode):", body.voiceprintConfig.existingVoiceprints.length);
    }

    console.log("✅ Added voiceprint settings to form data");
  }
>>>>>>> WA-170_MCP
  
  // Don't use custom headers for local mode to avoid CORS issues
  // Instead, we'll use the isLocalMode parameter in the FormData
  console.log("🔄 Sending local mode request to API without custom headers");
  const response = await auth0Fetch(
    replaceParams(`/white-label/${params.whitelabelId}/data-source/audio-transcript/file`, params),
    {
      method: "POST",
      // No custom headers - they cause CORS issues
      body: formData
    }
  );
  
  // Parse the response
  const result = await response.json();
  console.log("🔄 Local mode upload response:", result);
  
  if (!result.id) {
    throw new Error("Upload failed: " + (result.message || "Unknown error"));
  }
  
  // Return the result in the expected format
  return result;
}