{"name": "@divinci-ai/mcp-server", "version": "1.0.0", "private": true, "description": "Model Context Protocol server for Divinci AI", "main": "src/index.ts", "scripts": {"dev": "wrangler dev --env local --ip 0.0.0.0 --port 8793", "deploy": "wrangler deploy", "deploy:staging": "wrangler deploy --env staging", "deploy:production": "wrangler deploy --env production", "test": "vitest run", "test:watch": "vitest", "build": "wrangler deploy --dry-run"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.12.3", "hono": "^4.0.0", "jose": "^5.0.0", "zod": "^3.22.0"}, "devDependencies": {"@cloudflare/workers-types": "^4.20240925.0", "@types/node": "^22.5.2", "typescript": "^5.8.3", "vitest": "^3.1.1", "wrangler": "^3.78.0"}, "engines": {"node": ">=20"}}