/**
 * Test Mapping Validation Tests
 * 
 * This file tests the API test mapping system to ensure that folder changes
 * correctly trigger the appropriate test suites. This is crucial for CI/CD
 * workflows where specific folder changes should run relevant tests.
 */

import { test, expect } from '@playwright/test';
import { getApiTestSuitesForChangedFolders } from '../api-test-mapping';
import fs from 'fs';
import path from 'path';

test.describe('Test Mapping Validation', () => {
  test.describe('Folder to Test Suite Mapping', () => {
    test('should map AI Chat folder changes to AI Chats test suite', () => {
      const changedFolders = ['workspace/servers/public-api/src/routes/ai-chat'];
      const testSuites = getApiTestSuitesForChangedFolders(changedFolders);
      
      expect(testSuites).toContain('AI Chats');
      expect(testSuites.length).toBe(1);
    });

    test('should map White Label folder changes to White Label test suite', () => {
      const changedFolders = ['workspace/servers/public-api/src/routes/whitelabel'];
      const testSuites = getApiTestSuitesForChangedFolders(changedFolders);
      
      expect(testSuites).toContain('White Label');
      expect(testSuites.length).toBe(1);
    });

    test('should map Fine Tune folder changes to Fine Tune test suite', () => {
      const changedFolders = ['workspace/servers/public-api/src/routes/finetune'];
      const testSuites = getApiTestSuitesForChangedFolders(changedFolders);
      
      expect(testSuites).toContain('Fine Tune');
      expect(testSuites.length).toBe(1);
    });

    test('should map Moderation folder changes to Prompt Moderation test suite', () => {
      const changedFolders = ['workspace/servers/public-api/src/routes/moderation'];
      const testSuites = getApiTestSuitesForChangedFolders(changedFolders);
      
      expect(testSuites).toContain('Prompt Moderation');
      expect(testSuites.length).toBe(1);
    });

    test('should map Thread folder changes to Thread Prefix test suite', () => {
      const changedFolders = ['workspace/servers/public-api/src/routes/thread'];
      const testSuites = getApiTestSuitesForChangedFolders(changedFolders);
      
      expect(testSuites).toContain('Thread Prefix');
      expect(testSuites.length).toBe(1);
    });

    test('should map Message folder changes to Message Prefix test suite', () => {
      const changedFolders = ['workspace/servers/public-api/src/routes/message'];
      const testSuites = getApiTestSuitesForChangedFolders(changedFolders);
      
      expect(testSuites).toContain('Message Prefix');
      expect(testSuites.length).toBe(1);
    });

    test('should map RAG folder changes to RAG test suite', () => {
      const changedFolders = ['workspace/servers/public-api/src/routes/rag'];
      const testSuites = getApiTestSuitesForChangedFolders(changedFolders);
      
      expect(testSuites).toContain('RAG');
      expect(testSuites.length).toBe(1);
    });

    test('should map Workspace folder changes to Workspace Release test suite', () => {
      const changedFolders = ['workspace/servers/public-api/src/routes/workspace'];
      const testSuites = getApiTestSuitesForChangedFolders(changedFolders);
      
      expect(testSuites).toContain('Workspace Release');
      expect(testSuites.length).toBe(1);
    });

    test('should map Audio Transcript folder changes to Audio Transcript test suite', () => {
      const changedFolders = ['workspace/resources/actions/src/workspace/data-source/audio'];
      const testSuites = getApiTestSuitesForChangedFolders(changedFolders);
      
      expect(testSuites).toContain('Audio Transcript');
      expect(testSuites.length).toBe(1);
    });
  });

  test.describe('Multiple Folder Changes', () => {
    test('should handle multiple folder changes and return unique test suites', () => {
      const changedFolders = [
        'workspace/servers/public-api/src/routes/ai-chat',
        'workspace/servers/public-api/src/routes/whitelabel',
        'workspace/servers/public-api/src/routes/rag'
      ];
      const testSuites = getApiTestSuitesForChangedFolders(changedFolders);
      
      expect(testSuites).toContain('AI Chats');
      expect(testSuites).toContain('White Label');
      expect(testSuites).toContain('RAG');
      expect(testSuites.length).toBe(3);
    });

    test('should handle duplicate folder changes and return unique test suites', () => {
      const changedFolders = [
        'workspace/servers/public-api/src/routes/ai-chat',
        'workspace/servers/public-api/src/routes/ai-chat/router',
        'workspace/servers/public-api/src/routes/ai-chat/handlers'
      ];
      const testSuites = getApiTestSuitesForChangedFolders(changedFolders);
      
      expect(testSuites).toContain('AI Chats');
      expect(testSuites.length).toBe(1); // Should deduplicate
    });

    test('should handle test folder changes and return all test suites', () => {
      const changedFolders = ['workspace/clients/tests'];
      const testSuites = getApiTestSuitesForChangedFolders(changedFolders);
      
      // Should include all test suites when test folder changes
      expect(testSuites).toContain('AI Chats');
      expect(testSuites).toContain('White Label');
      expect(testSuites).toContain('Fine Tune');
      expect(testSuites).toContain('Prompt Moderation');
      expect(testSuites).toContain('Thread Prefix');
      expect(testSuites).toContain('Message Prefix');
      expect(testSuites).toContain('RAG');
      expect(testSuites).toContain('Workspace Release');
      expect(testSuites).toContain('Audio Transcript');
      expect(testSuites.length).toBe(9);
    });
  });

  test.describe('Edge Cases', () => {
    test('should handle empty folder list', () => {
      const changedFolders: string[] = [];
      const testSuites = getApiTestSuitesForChangedFolders(changedFolders);
      
      expect(testSuites).toEqual([]);
    });

    test('should handle non-matching folders', () => {
      const changedFolders = ['workspace/some/random/folder', 'another/unrelated/path'];
      const testSuites = getApiTestSuitesForChangedFolders(changedFolders);
      
      expect(testSuites).toEqual([]);
    });

    test('should handle folders with quotes and whitespace', () => {
      const changedFolders = [
        '"workspace/servers/public-api/src/routes/ai-chat"',
        "'workspace/servers/public-api/src/routes/whitelabel'",
        '  workspace/servers/public-api/src/routes/rag  '
      ];
      const testSuites = getApiTestSuitesForChangedFolders(changedFolders);
      
      expect(testSuites).toContain('AI Chats');
      expect(testSuites).toContain('White Label');
      expect(testSuites).toContain('RAG');
      expect(testSuites.length).toBe(3);
    });

    test('should handle subfolder changes correctly', () => {
      const changedFolders = [
        'workspace/servers/public-api/src/routes/ai-chat/handlers/create.ts',
        'workspace/servers/public-api/src/routes/whitelabel/middleware/auth.ts'
      ];
      const testSuites = getApiTestSuitesForChangedFolders(changedFolders);
      
      expect(testSuites).toContain('AI Chats');
      expect(testSuites).toContain('White Label');
      expect(testSuites.length).toBe(2);
    });
  });

  test.describe('Test File Existence Validation', () => {
    test('should verify that mapped test suites have corresponding test files', () => {
      const testSuiteToFileMapping = {
        'AI Chats': ['ai-chat.spec.ts', 'ai-chat-real-api.spec.ts', 'ai-chat-real-api-improved.spec.ts'],
        'White Label': ['whitelabel.spec.ts', 'whitelabel-api.spec.ts'],
        'Fine Tune': ['finetune.spec.ts'],
        'Prompt Moderation': ['moderation.spec.ts'],
        'Thread Prefix': ['thread.spec.ts'],
        'Message Prefix': ['message.spec.ts'],
        'RAG': ['vector.spec.ts'],
        'Workspace Release': ['workspace.spec.ts']
      };

      const testsDir = path.join(process.cwd(), 'src', 'tests');

      Object.entries(testSuiteToFileMapping).forEach(([testSuite, expectedFiles]) => {
        expectedFiles.forEach(fileName => {
          const filePath = path.join(testsDir, fileName);
          const fileExists = fs.existsSync(filePath);
          
          expect(fileExists).toBe(true);
          console.log(`✅ Test suite "${testSuite}" -> File "${fileName}" exists`);
        });
      });
    });

    test('should verify test files contain the expected test suite names', () => {
      const testSuiteToFileMapping = {
        'AI Chats': ['ai-chat.spec.ts'],
        'White Label': ['whitelabel.spec.ts'],
        'Fine Tune': ['finetune.spec.ts'],
        'Prompt Moderation': ['moderation.spec.ts'],
        'Thread Prefix': ['thread.spec.ts'],
        'Message Prefix': ['message.spec.ts'],
        'RAG': ['vector.spec.ts'],
        'Workspace Release': ['workspace.spec.ts']
      };

      const testsDir = path.join(process.cwd(), 'src', 'tests');

      Object.entries(testSuiteToFileMapping).forEach(([testSuite, testFiles]) => {
        testFiles.forEach(fileName => {
          const filePath = path.join(testsDir, fileName);
          
          if (fs.existsSync(filePath)) {
            const fileContent = fs.readFileSync(filePath, 'utf8');
            
            // Check if the file contains test.describe with a relevant name
            const hasTestDescribe = fileContent.includes('test.describe(');
            expect(hasTestDescribe).toBe(true);
            
            console.log(`✅ Test file "${fileName}" contains test.describe blocks`);
          }
        });
      });
    });
  });

  test.describe('Real API Integration with Test Mapping', () => {
    test('should verify AI Chats test suite works with real API', async () => {
      // This test verifies that the AI Chats test suite (which maps to ai-chat folder changes)
      // actually works with our real API integration
      
      const changedFolders = ['workspace/servers/public-api/src/routes/ai-chat'];
      const testSuites = getApiTestSuitesForChangedFolders(changedFolders);
      
      expect(testSuites).toContain('AI Chats');
      
      // Verify that we have real API tests for AI Chats
      const testsDir = path.join(process.cwd(), 'src', 'tests');
      const realApiTestFile = path.join(testsDir, 'ai-chat-real-api.spec.ts');
      const improvedRealApiTestFile = path.join(testsDir, 'ai-chat-real-api-improved.spec.ts');
      
      expect(fs.existsSync(realApiTestFile)).toBe(true);
      expect(fs.existsSync(improvedRealApiTestFile)).toBe(true);
      
      console.log('✅ AI Chats test suite has real API integration tests');
    });

    test('should simulate a commit to ai-chat folder and verify test mapping', () => {
      // Simulate a commit that changes files in the ai-chat folder
      const simulatedChangedFiles = [
        'workspace/servers/public-api/src/routes/ai-chat/router/chat.ts',
        'workspace/servers/public-api/src/routes/ai-chat/handlers/create.ts',
        'workspace/servers/public-api/src/routes/ai-chat/middleware/auth.ts'
      ];
      
      // Extract unique folders from changed files
      const changedFolders = [...new Set(simulatedChangedFiles.map(file => {
        const parts = file.split('/');
        // Find the most specific folder that matches our mappings
        for (let i = parts.length - 1; i >= 0; i--) {
          const folder = parts.slice(0, i).join('/');
          if (folder === 'workspace/servers/public-api/src/routes/ai-chat') {
            return folder;
          }
        }
        return null;
      }).filter(Boolean))];
      
      const testSuites = getApiTestSuitesForChangedFolders(changedFolders);
      
      expect(testSuites).toContain('AI Chats');
      expect(testSuites.length).toBe(1);
      
      console.log(`✅ Simulated commit to ai-chat folder correctly triggers AI Chats test suite`);
      console.log(`   Changed files: ${simulatedChangedFiles.join(', ')}`);
      console.log(`   Detected folders: ${changedFolders.join(', ')}`);
      console.log(`   Test suites to run: ${testSuites.join(', ')}`);
    });
  });
});
