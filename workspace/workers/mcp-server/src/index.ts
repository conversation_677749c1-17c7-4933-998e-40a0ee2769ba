/**
 * Divinci AI MCP Server
 * Model Context Protocol server providing access to Divinci AI services
 */

import { Tool } from "@modelcontextprotocol/sdk/types.js";

// Environment interface
interface Env {
  // Auth0 Configuration
  AUTH0_DOMAIN: string;
  AUTH0_CLIENT_ID: string;
  AUTH0_CLIENT_SECRET: string;

  // Divinci API Configuration
  DIVINCI_API_BASE_URL: string;
  DIVINCI_API_KEY: string;

  // Environment
  ENVIRONMENT: string;
  MCP_SERVER_VERSION: string;
  CLOUDFLARE_ACCOUNT_ID: string;
}

// Define available tools
const TOOLS: Tool[] = [
  {
    name: "create_chat",
    description: "Create a new AI chat conversation",
    inputSchema: {
      type: "object",
      properties: {
        title: { type: "string", description: "Optional title for the chat" },
        model: { type: "string", description: "AI model to use (optional)" },
      },
    },
  },
  {
    name: "send_message",
    description: "Send a message to an AI chat conversation",
    inputSchema: {
      type: "object",
      properties: {
        chatId: { type: "string", description: "ID of the chat conversation" },
        message: { type: "string", description: "Message content to send" },
        model: { type: "string", description: "AI model to use (optional)" },
      },
      required: ["chatId", "message"],
    },
  },
  {
    name: "list_chats",
    description: "List user's chat conversations",
    inputSchema: {
      type: "object",
      properties: {
        limit: {
          type: "number",
          description: "Maximum number of chats to return",
        },
        offset: { type: "number", description: "Number of chats to skip" },
      },
    },
  },
  {
    name: "get_user_profile",
    description: "Get current user profile information",
    inputSchema: {
      type: "object",
      properties: {},
    },
  },
];

export default {
  async fetch(request: Request, env: Env): Promise<Response> {
    const url = new URL(request.url);

    // Handle CORS preflight
    if (request.method === "OPTIONS") {
      return new Response(null, {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type, Authorization",
        },
      });
    }

    // Health check endpoint
    if (url.pathname === "/health") {
      return Response.json({
        success: true,
        service: "divinci-mcp-server",
        environment: env.ENVIRONMENT || "development",
        version: env.MCP_SERVER_VERSION || "1.0.0",
        timestamp: new Date().toISOString(),
        status: "healthy",
      });
    }

    // Root endpoint - server info
    if (url.pathname === "/") {
      return Response.json({
        name: "Divinci AI MCP Server",
        version: env.MCP_SERVER_VERSION || "1.0.0",
        description: "Model Context Protocol server for Divinci AI services",
        endpoints: {
          sse: "/sse",
          health: "/health",
        },
      });
    }

    // MCP SSE endpoint - simplified implementation
    if (url.pathname === "/sse") {
      // Handle POST requests (MCP protocol messages)
      if (request.method === "POST") {
        try {
          const body = await request.json();
          console.log("Received MCP request:", JSON.stringify(body, null, 2));

          // Handle different MCP methods
          if (body.method === "tools/list") {
            return Response.json({
              jsonrpc: "2.0",
              id: body.id,
              result: {
                tools: TOOLS,
              },
            });
          }

          if (body.method === "tools/call") {
            const { name, arguments: args } = body.params;
            try {
              const result = await callTool(name, args || {}, env);
              return Response.json({
                jsonrpc: "2.0",
                id: body.id,
                result,
              });
            } catch (error) {
              return Response.json({
                jsonrpc: "2.0",
                id: body.id,
                error: {
                  code: -32603,
                  message:
                    error instanceof Error ? error.message : "Unknown error",
                },
              });
            }
          }

          if (body.method === "initialize") {
            return Response.json({
              jsonrpc: "2.0",
              id: body.id,
              result: {
                protocolVersion: "2024-11-05",
                capabilities: {
                  tools: {},
                },
                serverInfo: {
                  name: "divinci-ai-mcp-server",
                  version: env.MCP_SERVER_VERSION || "1.0.0",
                },
              },
            });
          }

          // Default response for unknown methods
          return Response.json({
            jsonrpc: "2.0",
            id: body.id,
            error: {
              code: -32601,
              message: "Method not found",
            },
          });
        } catch (error) {
          console.error("Error processing MCP request:", error);
          return Response.json(
            {
              jsonrpc: "2.0",
              error: {
                code: -32700,
                message: "Parse error",
              },
            },
            { status: 400 }
          );
        }
      }

      // Handle GET requests (SSE connection)
      if (request.method === "GET") {
        const { readable, writable } = new TransformStream();
        const writer = writable.getWriter();
        const encoder = new TextEncoder();

        // Send initial SSE messages
        const sendSSE = (data: any) => {
          writer.write(encoder.encode("event: message\n"));
          writer.write(encoder.encode(`data: ${JSON.stringify(data)}\n\n`));
        };

        // Initialize connection
        sendSSE({
          jsonrpc: "2.0",
          method: "notifications/initialized",
          params: {
            protocolVersion: "2024-11-05",
            capabilities: {
              tools: {},
            },
            serverInfo: {
              name: "divinci-ai-mcp-server",
              version: env.MCP_SERVER_VERSION || "1.0.0",
            },
          },
        });

        // Keep connection alive with periodic pings
        const keepAlive = setInterval(() => {
          try {
            writer.write(encoder.encode(": ping\n\n"));
          } catch (error) {
            clearInterval(keepAlive);
          }
        }, 30000);

        // Clean up on connection close
        request.signal?.addEventListener("abort", () => {
          clearInterval(keepAlive);
          writer.close();
        });

        return new Response(readable, {
          headers: {
            "Content-Type": "text/event-stream",
            "Cache-Control": "no-cache",
            Connection: "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Content-Type, Authorization",
          },
        });
      }
    }

    return new Response("Not Found", { status: 404 });
  },
};

// Tool implementation functions
async function callTool(name: string, args: any, env: Env) {
  switch (name) {
    case "create_chat":
      return createChat(args, env);
    case "send_message":
      return sendMessage(args, env);
    case "list_chats":
      return listChats(args, env);
    case "get_user_profile":
      return getUserProfile(env);
    default:
      throw new Error(`Unknown tool: ${name}`);
  }
}

async function createChat(args: any, env: Env) {
  // For testing, return a mock response
  const chatId = `chat_${Date.now()}`;

  return {
    content: [
      {
        type: "text",
        text: `Created new chat conversation with ID: ${chatId}${
          args.title ? ` and title: "${args.title}"` : ""
        }`,
      },
    ],
  };
}

async function sendMessage(args: any, env: Env) {
  // For testing, return a mock response
  return {
    content: [
      {
        type: "text",
        text: `Message sent to chat ${args.chatId}: "${args.message}"\n\nMock AI Response: I received your message "${args.message}" and I'm ready to help!`,
      },
    ],
  };
}

async function listChats(args: any, env: Env) {
  // For testing, return mock chats
  const mockChats = [
    {
      id: "chat_1",
      title: "General Discussion",
      createdAt: "2024-01-15T10:30:00Z",
    },
    {
      id: "chat_2",
      title: "Project Planning",
      createdAt: "2024-01-14T15:45:00Z",
    },
    {
      id: "chat_3",
      title: "Technical Questions",
      createdAt: "2024-01-13T09:20:00Z",
    },
  ];

  const limit = args.limit || 10;
  const offset = args.offset || 0;
  const paginatedChats = mockChats.slice(offset, offset + limit);

  return {
    content: [
      {
        type: "text",
        text:
          `Found ${paginatedChats.length} chat conversations:\n` +
          paginatedChats
            .map((chat) => `- ${chat.title} (${chat.id}) - ${chat.createdAt}`)
            .join("\n"),
      },
    ],
  };
}

async function getUserProfile(env: Env) {
  // For testing, return mock user profile
  return {
    content: [
      {
        type: "text",
        text: `User Profile:\n- Email: <EMAIL>\n- Name: Test User\n- ID: user_123\n- Environment: ${
          env.ENVIRONMENT || "development"
        }`,
      },
    ],
  };
}
