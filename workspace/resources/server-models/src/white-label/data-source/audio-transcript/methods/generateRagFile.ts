import { DATA_SOURCE_AUDIO_TRANSCRIPT_MODEL_NAME, unravelTarget } from "@divinci-ai/models";
import {
  IAudioTranscriptMethods,
  IAudioTranscriptModelType,
  AudioTranscriptStatus,
 } from "../types";

import { RagVectorFileModel } from "../../../rag/file";
import { HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";
import { RAWFILE_TO_CHUNKS } from "@divinci-ai/server-tools";

import { audioTranscriptToRagFile } from "@divinci-ai/tools";

import { lookup as mimeTypeLookup } from "mime-types";

export const generateRagFile: IAudioTranscriptMethods["generateRagFile"] = async function(
  this: InstanceType<IAudioTranscriptModelType>, speakers, chunkingTool, ragVectors,
){
  const doc = this;

  RAWFILE_TO_CHUNKS.tryToGetTool(chunkingTool, HTTP_ERRORS_WITH_CONTEXT.BAD_FORM);

  if(speakers.length === 0){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Need at least one speaker");
  }

  if(doc.processStatus !== AudioTranscriptStatus.Completed){
    throw HTTP_ERRORS_WITH_CONTEXT.LOCKED("Can only make files from completed Audio Transcripts");
  }

  // Get the original audio file's content type for reference
  const originalContentType = mimeTypeLookup(doc.audio.Key);
  if(!originalContentType){
    throw HTTP_ERRORS_WITH_CONTEXT.SERVER_ERROR("Audio Transcript has a bad mime type");
  }

  // Always use text/plain for the transcript content
  // This ensures the file is properly processed as text
  const contentType = "text/plain";

  const transcript = (function(){
    try {
      return audioTranscriptToRagFile(doc, speakers);
    }catch(e){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM((e as { message: string }).message);
    }
  })();


  const timestamp = new Date();
  const dateString = `${timestamp.getFullYear()}-${timestamp.getMonth() + 1}-${timestamp.getDate()}`;
  const timeString = `${timestamp.getHours()}:${timestamp.getMinutes()}:${timestamp.getSeconds()}`;

  const infoName = getIdentifier(doc);

  // Create a filename with .txt extension to ensure it's processed as text
  const textFilename = doc.audio.filename.replace(/\.[^/.]+$/, "") + ".txt";

  console.log(`📄 Creating RAG file from audio transcript: ${doc._id}`);
  console.log(`📄 Original filename: ${doc.audio.filename}, using: ${textFilename}`);

  // Extract whitelabel ID from the document target or use the provided one
  const docTarget = doc.target;

  const targetParts = unravelTarget(docTarget);
  const whitelabelId = targetParts.id;

  console.log(`📄 Using whitelabel ID: ${whitelabelId}`);
  console.log(`📄 RAG ID provided: ${ragVectors.join(", ") || "none"}`);

  // If we couldn't get a valid whitelabel ID, throw an error
  if(!whitelabelId || whitelabelId === "default" || whitelabelId === "mongodb") {
    throw HTTP_ERRORS_WITH_CONTEXT.SERVER_ERROR(`Could not determine valid whitelabel ID. Provided: ${whitelabelId}, Target: ${docTarget}`);
  }

  // Format the target properly as "mongodb/rag-vector/{whitelabelId}"
  // Keep the target clean for file storage - don't include query parameters in the target
  const formattedTarget = `mongodb/rag-vector/${whitelabelId}`;
  console.log(`📄 Using formatted target: ${formattedTarget}`);
  console.log(`📄 Using whitelabel ID: ${whitelabelId}`);
  console.log(`📄 RAG ID will be passed via processConfig: ${ragVectors.join(", ") || "none"}`);

  // Create process config for the chunks workflow
  // We'll use the standard DivinciTestProcessConfig and pass ragId through the ChunkWorkflowClient
  const processConfig = null;

  console.log(`📄 Process config: using null (standard workflow behavior)`);
  console.log(`📄 RAG ID will be passed via ChunkWorkflowClient: ${ragVectors.join(", ") || "none"}`);

  const { doc: fileDoc, workResult } = await RagVectorFileModel.addNewFileFromText(
    formattedTarget,
    {
      source: { model: DATA_SOURCE_AUDIO_TRANSCRIPT_MODEL_NAME, _id: doc._id.toString() },
      filename: textFilename,
      mimeType: contentType,
      text: transcript,
    },
    chunkingTool,
    {
      title: `Audio: ${doc.sourceOrigin.sourceType} ${infoName} ${dateString} ${timeString}`,
      description: [
        `The chunks related to the audio from ${doc.sourceOrigin.sourceType} related to ${infoName}.`,
        `This file was created on ${dateString} at ${timeString}.`,
        `Changing these chunks will not effect the original audio transcript`
      ].join("\n"),
    },
    processConfig,
    ragVectors // Pass the ragId to the workflow
  );

  return { fileDoc, work: workResult };
};

function getIdentifier(doc: InstanceType<IAudioTranscriptModelType>){
  switch(doc.sourceOrigin.sourceType){
    case "url": return doc.sourceOrigin.sourceId;
    case "file": return doc.sourceOrigin.info.filename;
    default: {
      throw HTTP_ERRORS_WITH_CONTEXT.SERVER_ERROR("Document Malformed");
    }
  }

}
