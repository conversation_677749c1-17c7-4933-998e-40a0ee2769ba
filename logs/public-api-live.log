
> @divinci-ai/public-api-live@0.3.0 start:dev /workspaces/server/workspace/servers/public-api-live
> ts-node-dev --poll --transpile-only --ignore-watch node_modules --files src/index.ts

[INFO] 05:58:54 ts-node-dev ver. 2.0.0 (using ts-node ver. 10.9.2, typescript ver. 5.8.3)
🐞LOG_DEBUG MODE:  1
🐞LOG_DEBUG MODE:  1
🔎 CURRENT ENVIRONMENT: dev
🔎 CORS FULL ORIGINS (raw): api.slack.com,dev-46tiys6hnb6vbg17-cd-hljzhimsn7o28fvq.edge.tenants.us.auth0.com,dev-46tiys6hnb6vbg17.us.auth0.com,local-chunks-vectorized,127.0.0.1:,sturdy-space-broccoli-g4xpjgv6376q-8080.app.github.dev,sturdy-space-broccoli-g4xpjgv6376q-8081.app.github.dev,sturdy-space-broccoli-g4xpjgv6376q-8082.app.github.dev,sturdy-space-broccoli-g4xpjgv6376q-8083.app.github.dev,sturdy-space-broccoli-g4xpjgv6376q-9080.app.github.dev,https://sturdy-space-broccoli-g4xpjgv6376q-8080.app.github.dev,https://sturdy-space-broccoli-g4xpjgv6376q-9080.app.github.dev
🔎 CORS FULL ORIGINS (cleaned): [
  'api.slack.com',
  'dev-46tiys6hnb6vbg17-cd-hljzhimsn7o28fvq.edge.tenants.us.auth0.com',
  'dev-46tiys6hnb6vbg17.us.auth0.com',
  'local-chunks-vectorized',
  '127.0.0.1:',
  'sturdy-space-broccoli-g4xpjgv6376q-8080.app.github.dev',
  'sturdy-space-broccoli-g4xpjgv6376q-8081.app.github.dev',
  'sturdy-space-broccoli-g4xpjgv6376q-8082.app.github.dev',
  'sturdy-space-broccoli-g4xpjgv6376q-8083.app.github.dev',
  'sturdy-space-broccoli-g4xpjgv6376q-9080.app.github.dev',
  'https://sturdy-space-broccoli-g4xpjgv6376q-8080.app.github.dev',
  'https://sturdy-space-broccoli-g4xpjgv6376q-9080.app.github.dev'
]
🐞LOG_DEBUG MODE:  1
🐞LOG_DEBUG MODE:  1
🎈 Attempting to connect to Redis in dev environment: redis://default:<EMAIL>:15121
🔄 Creating simple R2 client for fine-tune in local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🔄 Creating simple R2 client for RAG vectors in local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🔄 Creating simple Audio R2 client for local development
🔄 Using MinIO endpoint: http://minio.divinci.local:9000
🐞LOG_DEBUG MODE:  1
🐞LOG_DEBUG MODE:  1
Attempting Mongoose Connection.
🎈 MONGO_CONNECTION_URL: mongodb+srv://serverlessinstance0.c4pobzg.mongodb.net/divinci-dev
Attempting Redis Connection
🟢 Connected to Redis in dev environment at 2025-06-06T05:58:58.089Z
✅🌱 Successfully connected to Redis. 
✅🌱 Successfully connected to MongoDB. 
🌐 Creating HTTP server
🚀 Server listening on:   { address: '::', family: 'IPv6', port: 8082 }
🌐 url:  /
🪪 remote address:   ::ffff:127.0.0.1
[CORS-DEBUG] Received GET request from origin: unknown
[CORS-DEBUG] Request URL: http://localhost:8082/
[CORS-DEBUG] CORS Request Headers:
[MTLS-DEBUG] Received GET request from ::ffff:127.0.0.1
[MTLS-DEBUG] Request URL: http://localhost:8082/
[MTLS-DEBUG] mTLS is disabled on the server
[MTLS-DEBUG] Certificate directory: /Users/<USER>/Documents/Divinci/server3/server/private-keys/local/certs/mtls
[MTLS-DEBUG] Server certificate not found at /Users/<USER>/Documents/Divinci/server3/server/private-keys/local/certs/mtls/server.crt
[MTLS-DEBUG] Client certificate CA not found in any of the expected locations
[MTLS-DEBUG] Socket does not support getPeerCertificate - mTLS may not be properly configured
[CORS-DEBUG] Setting response header: Access-Control-Allow-Credentials: true
[CORS-DEBUG] Setting response header: Access-Control-Expose-Headers: x-file-name,x-file-id,x-target,x-processor,x-vectorize-config,x-processor-config,x-debug-client,divinci-organization,cloudflare-worker-x-dev-auth,x-worker-local-dev
[CORS-DEBUG] Response status: 200 
[CORS-DEBUG] CORS Response Headers:
[CORS-DEBUG]   Access-Control-Allow-Origin: Not set
[CORS-DEBUG]   Access-Control-Allow-Methods: Not set
[CORS-DEBUG]   Access-Control-Allow-Headers: Not set
[CORS-DEBUG]   Access-Control-Allow-Credentials: true
[CORS-DEBUG]   Access-Control-Expose-Headers: x-file-name,x-file-id,x-target,x-processor,x-vectorize-config,x-processor-config,x-debug-client,divinci-organization,cloudflare-worker-x-dev-auth,x-worker-local-dev
[CORS-DEBUG]   Access-Control-Max-Age: Not set
GET / 200 48 - 2.093 ms
 ELIFECYCLE  Command failed.
