#!/bin/bash

# Fix Cloudflare Transform Rules for OPTIONS Requests
# This script enables the transform rules to handle OPTIONS requests at the edge

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Set default credentials from cloudflare-rules-analyzer.sh
<<<<<<< HEAD
CF_API_TOKEN="xQI66zSWAOjLnWlyoZne8_CjVgStYXYbH26f3p2c"
=======
CF_API_TOKEN="aR8Ud0AVhRI59O69XX815JmYuPalWdPG1S_nZGf2"
>>>>>>> WA-170_MCP
CF_ZONE_ID="9b26e2c415f36b0f656204133c8ab87c"

# Function to check if env file has the required credentials
check_env_file() {
  local env_file=$1
  if [ -f "$env_file" ]; then
    echo -e "${BLUE}Found $env_file${NC}"
    # Check if file contains the required variables
    if grep -q "CF_API_TOKEN" "$env_file" && grep -q "CF_ZONE_ID" "$env_file"; then
      echo -e "${GREEN}Found Cloudflare credentials in $env_file${NC}"
      source "$env_file"
      return 0
    else
      echo -e "${YELLOW}File $env_file exists but doesn't contain required credentials${NC}"
      return 1
    fi
  fi
  return 1
}

# Function to search for credentials in deploy/scripts/tests directory
check_test_directory() {
  local test_dir="deploy/scripts/tests"
  if [ -d "$test_dir" ]; then
    echo -e "${BLUE}Checking for credentials in $test_dir${NC}"
    
    # Look for cloudflare.env
    if [ -f "$test_dir/cloudflare.env" ]; then
      echo -e "${BLUE}Found $test_dir/cloudflare.env${NC}"
      if grep -q "CF_API_TOKEN" "$test_dir/cloudflare.env" && grep -q "CF_ZONE_ID" "$test_dir/cloudflare.env"; then
        echo -e "${GREEN}Found Cloudflare credentials in $test_dir/cloudflare.env${NC}"
        source "$test_dir/cloudflare.env"
        return 0
      fi
    fi
    
    # Check if there's a file with credentials inside
    for file in "$test_dir"/*.sh; do
      if [ -f "$file" ]; then
        if grep -q "CF_API_TOKEN" "$file" && grep -q "CF_ZONE_ID" "$file"; then
          echo -e "${GREEN}Found potential Cloudflare credentials in $file${NC}"
          # Extract credentials from the file
          export CF_API_TOKEN=$(grep -o 'CF_API_TOKEN="[^"]*"' "$file" | cut -d'"' -f2)
          export CF_ZONE_ID=$(grep -o 'CF_ZONE_ID="[^"]*"' "$file" | cut -d'"' -f2)
          if [ -n "$CF_API_TOKEN" ] && [ -n "$CF_ZONE_ID" ]; then
            echo -e "${GREEN}Extracted credentials from $file${NC}"
            return 0
          fi
        fi
      fi
    done
  fi
  return 1
}

# Try to load Cloudflare API credentials from various locations
if check_env_file "private-keys/staging/cloudflare.env"; then
  # Already sourced in the function
  :
elif check_env_file "private-keys/production/cloudflare.env"; then
  # Already sourced in the function
  :
elif check_env_file "private-keys/local-staging/cloudflare.env"; then
  # Already sourced in the function
  :
elif check_env_file "private-keys/local/cloudflare.env"; then
  # Already sourced in the function
  :
elif check_env_file "private-keys/develop/cloudflare.env"; then
  # Already sourced in the function
  :
elif check_test_directory; then
  # Already set credentials in the function
  :
else
  echo -e "${RED}Cloudflare environment file not found with required credentials!${NC}"
  echo -e "${YELLOW}Please ensure one of the following files exists with CF_API_TOKEN and CF_ZONE_ID:${NC}"
  echo -e "  - private-keys/staging/cloudflare.env"
  echo -e "  - private-keys/production/cloudflare.env" 
  echo -e "  - private-keys/local-staging/cloudflare.env"
  echo -e "  - private-keys/local/cloudflare.env"
  echo -e "  - private-keys/develop/cloudflare.env"
  echo -e "  - deploy/scripts/tests/cloudflare.env"
  
  # Prompt for credentials if not found
  read -p "Would you like to enter Cloudflare credentials manually? (y/n): " ENTER_CREDS
  if [[ "$ENTER_CREDS" == "y" || "$ENTER_CREDS" == "Y" ]]; then
    read -p "Enter your Cloudflare API Token: " CF_API_TOKEN
    read -p "Enter your Cloudflare Zone ID: " CF_ZONE_ID
    echo -e "${GREEN}Credentials entered manually.${NC}"
  else
    echo -e "${RED}Cloudflare credentials not found. Aborting.${NC}"
    exit 1
  fi
fi

# Check if credentials are set
if [ -z "$CF_API_TOKEN" ] || [ -z "$CF_ZONE_ID" ]; then
  echo -e "${RED}Cloudflare credentials not found or empty after checking all locations. Aborting.${NC}"
  exit 1
fi

echo -e "${GREEN}Using Cloudflare credentials:${NC}"
echo -e "  Zone ID: $CF_ZONE_ID"
echo -e "  API Token: ${CF_API_TOKEN:0:4}...${CF_API_TOKEN: -4}"

# Output directory
OUTPUT_DIR="./cloudflare-output"
mkdir -p "$OUTPUT_DIR"

# Function to check and update transform rules
update_transform_rules() {
  echo -e "${BLUE}Checking transform rules...${NC}"
  
  # Get current rulesets
  curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$CF_ZONE_ID/rulesets" \
    -H "Authorization: Bearer $CF_API_TOKEN" \
    -H "Content-Type: application/json" > "$OUTPUT_DIR/transform_rulesets.json"
  
  # Check if response headers ruleset exists
  TRANSFORM_RULESET_ID=$(jq -r '.result[] | select(.phase == "http_response_headers_transform") | .id' "$OUTPUT_DIR/transform_rulesets.json")
  
  if [ -n "$TRANSFORM_RULESET_ID" ]; then
    echo -e "${GREEN}Found existing transform ruleset: $TRANSFORM_RULESET_ID${NC}"
    
    # Get details of the ruleset
    curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$CF_ZONE_ID/rulesets/$TRANSFORM_RULESET_ID" \
      -H "Authorization: Bearer $CF_API_TOKEN" \
      -H "Content-Type: application/json" > "$OUTPUT_DIR/transform_ruleset_details.json"
    
    # Extract existing rules
    RULES_JSON=$(jq -c '.result.rules' "$OUTPUT_DIR/transform_ruleset_details.json")
    
    # Update each rule to enable it
    UPDATED_RULES=$(echo "$RULES_JSON" | jq '[.[] | .enabled = true]')
    
    # Update the ruleset with enabled rules
    echo -e "${BLUE}Updating transform ruleset to enable all rules...${NC}"
    curl -s -X PUT "https://api.cloudflare.com/client/v4/zones/$CF_ZONE_ID/rulesets/$TRANSFORM_RULESET_ID" \
      -H "Authorization: Bearer $CF_API_TOKEN" \
      -H "Content-Type: application/json" \
      --data "{
        \"description\": \"CORS Headers Configuration\",
        \"rules\": $UPDATED_RULES
      }" > "$OUTPUT_DIR/update_transform_ruleset_response.json"
    
    if grep -q '"success":true' "$OUTPUT_DIR/update_transform_ruleset_response.json"; then
      echo -e "${GREEN}Transform ruleset updated successfully!${NC}"
      jq '.result.rules[] | {description: .description, enabled: .enabled}' "$OUTPUT_DIR/update_transform_ruleset_response.json"
    else
      echo -e "${RED}Failed to update transform ruleset!${NC}"
      cat "$OUTPUT_DIR/update_transform_ruleset_response.json"
    fi
  else
    echo -e "${YELLOW}No existing transform ruleset found. Creating new ruleset...${NC}"
    create_transform_ruleset
  fi
}

# Function to create a new transform ruleset
create_transform_ruleset() {
  echo -e "${BLUE}Creating new transform ruleset...${NC}"
  
  curl -s -X POST "https://api.cloudflare.com/client/v4/zones/$CF_ZONE_ID/rulesets" \
    -H "Authorization: Bearer $CF_API_TOKEN" \
    -H "Content-Type: application/json" \
    --data '{
      "name": "CORS Headers Configuration",
      "kind": "zone",
      "phase": "http_response_headers_transform",
      "description": "Add CORS headers to responses",
      "rules": [
        {
          "action": "rewrite_response_headers",
          "action_parameters": {
            "headers": [
              {
                "name": "Access-Control-Allow-Origin",
                "operation": "set",
                "expression": "http.request.headers.origin"
              },
              {
                "name": "Access-Control-Allow-Methods",
                "operation": "set",
                "value": "GET, POST, PUT, DELETE, OPTIONS, PATCH"
              },
              {
                "name": "Access-Control-Allow-Headers",
                "operation": "set",
                "value": "Content-Type, Authorization, Origin, X-Debug-Client, CF-Access-Client-Id, CF-Access-Client-Secret, divinci-organization"
              },
              {
                "name": "Access-Control-Allow-Credentials",
                "operation": "set",
                "value": "true"
              },
              {
                "name": "Access-Control-Max-Age",
                "operation": "set",
                "value": "86400"
              }
            ]
          },
          "expression": "(http.request.method eq \"OPTIONS\")",
          "description": "Add CORS Headers for OPTIONS Requests",
          "enabled": true
        },
        {
          "action": "rewrite_response_headers",
          "action_parameters": {
            "headers": [
              {
                "name": "Access-Control-Allow-Origin",
                "operation": "set",
                "expression": "http.request.headers.origin"
              },
              {
                "name": "Access-Control-Allow-Credentials",
                "operation": "set",
                "value": "true"
              }
            ]
          },
          "expression": "true",
          "description": "Add CORS Headers for Regular Requests",
          "enabled": true
        }
      ]
    }' > "$OUTPUT_DIR/create_transform_ruleset_response.json"
  
  if grep -q '"success":true' "$OUTPUT_DIR/create_transform_ruleset_response.json"; then
    echo -e "${GREEN}Transform ruleset created successfully!${NC}"
  else
    echo -e "${RED}Failed to create transform ruleset!${NC}"
    cat "$OUTPUT_DIR/create_transform_ruleset_response.json"
  fi
}

# Function to check and update firewall rules
update_firewall_rules() {
  echo -e "${BLUE}Checking firewall rules...${NC}"
  
  # Get current firewall rules
  curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$CF_ZONE_ID/firewall/rules" \
    -H "Authorization: Bearer $CF_API_TOKEN" \
    -H "Content-Type: application/json" > "$OUTPUT_DIR/firewall_rules.json"
  
  # Check for mTLS rule
  MTLS_RULE_ID=$(jq -r '.result[] | select(.description | contains("mTLS")) | .id' "$OUTPUT_DIR/firewall_rules.json")
  
  if [ -n "$MTLS_RULE_ID" ]; then
    echo -e "${GREEN}Found mTLS rule: $MTLS_RULE_ID${NC}"
    
    # Get the filter ID
    FILTER_ID=$(jq -r ".result[] | select(.id == \"$MTLS_RULE_ID\") | .filter.id" "$OUTPUT_DIR/firewall_rules.json")
    
    # Get current filter expression
    curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$CF_ZONE_ID/filters/$FILTER_ID" \
      -H "Authorization: Bearer $CF_API_TOKEN" \
      -H "Content-Type: application/json" > "$OUTPUT_DIR/filter_response.json"
    
    CURRENT_EXPRESSION=$(jq -r '.result.expression' "$OUTPUT_DIR/filter_response.json")
    
    echo -e "${BLUE}Current mTLS filter expression: $CURRENT_EXPRESSION${NC}"
    
    # Check if the expression already excludes OPTIONS
    if [[ $CURRENT_EXPRESSION == *"http.request.method ne \"OPTIONS\""* ]]; then
      echo -e "${GREEN}mTLS rule already excludes OPTIONS requests${NC}"
    else
      echo -e "${YELLOW}mTLS rule does not exclude OPTIONS requests. Updating...${NC}"
      
      # Add OPTIONS exclusion
      NEW_EXPRESSION="(http.request.method ne \"OPTIONS\") and ($CURRENT_EXPRESSION)"
      
      curl -s -X PUT "https://api.cloudflare.com/client/v4/zones/$CF_ZONE_ID/filters/$FILTER_ID" \
        -H "Authorization: Bearer $CF_API_TOKEN" \
        -H "Content-Type: application/json" \
        --data "{
          \"expression\": \"$NEW_EXPRESSION\",
          \"paused\": false
        }" > "$OUTPUT_DIR/update_filter_response.json"
      
      if grep -q '"success":true' "$OUTPUT_DIR/update_filter_response.json"; then
        echo -e "${GREEN}mTLS filter updated to exclude OPTIONS requests${NC}"
      else
        echo -e "${RED}Failed to update mTLS filter${NC}"
        cat "$OUTPUT_DIR/update_filter_response.json"
      fi
    fi
    
    # Change the mTLS rule action to "skip" if it's "block"
    MTLS_ACTION=$(jq -r ".result[] | select(.id == \"$MTLS_RULE_ID\") | .action" "$OUTPUT_DIR/firewall_rules.json")
    
    if [ "$MTLS_ACTION" == "block" ]; then
      echo -e "${YELLOW}mTLS rule action is 'block'. Updating to 'skip'...${NC}"
      
      curl -s -X PUT "https://api.cloudflare.com/client/v4/zones/$CF_ZONE_ID/firewall/rules/$MTLS_RULE_ID" \
        -H "Authorization: Bearer $CF_API_TOKEN" \
        -H "Content-Type: application/json" \
        --data "{
          \"action\": \"skip\",
          \"description\": \"Enforce mTLS authentication (except OPTIONS)\",
          \"paused\": false,
          \"filter\": {
            \"id\": \"$FILTER_ID\"
          }
        }" > "$OUTPUT_DIR/update_rule_response.json"
      
      if grep -q '"success":true' "$OUTPUT_DIR/update_rule_response.json"; then
        echo -e "${GREEN}mTLS rule action updated to 'skip'${NC}"
      else
        echo -e "${RED}Failed to update mTLS rule action${NC}"
        cat "$OUTPUT_DIR/update_rule_response.json"
      fi
    else
      echo -e "${GREEN}mTLS rule action is already '$MTLS_ACTION'${NC}"
    fi
  else
    echo -e "${YELLOW}No mTLS rule found${NC}"
  fi
  
  # Create a dedicated OPTIONS rule if it doesn't exist
  OPTIONS_RULE_ID=$(jq -r '.result[] | select(.description | contains("OPTIONS") and .description | contains("Handle")) | .id' "$OUTPUT_DIR/firewall_rules.json")
  
  if [ -n "$OPTIONS_RULE_ID" ]; then
    echo -e "${GREEN}Dedicated OPTIONS handling rule found: $OPTIONS_RULE_ID${NC}"
  else
    echo -e "${BLUE}Creating dedicated OPTIONS handling rule...${NC}"
    
    # Create filter for OPTIONS
    curl -s -X POST "https://api.cloudflare.com/client/v4/zones/$CF_ZONE_ID/filters" \
      -H "Authorization: Bearer $CF_API_TOKEN" \
      -H "Content-Type: application/json" \
      --data "[{
        \"expression\": \"(http.request.method eq \\\"OPTIONS\\\")\",
        \"paused\": false
      }]" > "$OUTPUT_DIR/create_options_filter_response.json"
    
    if grep -q '"success":true' "$OUTPUT_DIR/create_options_filter_response.json"; then
      OPTIONS_FILTER_ID=$(jq -r '.[0].result.id' "$OUTPUT_DIR/create_options_filter_response.json")
      
      # Create rule
      curl -s -X POST "https://api.cloudflare.com/client/v4/zones/$CF_ZONE_ID/firewall/rules" \
        -H "Authorization: Bearer $CF_API_TOKEN" \
        -H "Content-Type: application/json" \
        --data "[{
          \"filter\": {
            \"id\": \"$OPTIONS_FILTER_ID\"
          },
          \"action\": \"allow\",
          \"description\": \"Handle OPTIONS requests at the edge\",
          \"paused\": false,
          \"priority\": 1
        }]" > "$OUTPUT_DIR/create_options_rule_response.json"
      
      if grep -q '"success":true' "$OUTPUT_DIR/create_options_rule_response.json"; then
        echo -e "${GREEN}Dedicated OPTIONS handling rule created${NC}"
      else
        echo -e "${RED}Failed to create OPTIONS rule${NC}"
        cat "$OUTPUT_DIR/create_options_rule_response.json"
      fi
    else
      echo -e "${RED}Failed to create OPTIONS filter${NC}"
      cat "$OUTPUT_DIR/create_options_filter_response.json"
    fi
  fi
}

# Function to check request transform rules
create_request_transform_ruleset() {
  echo -e "${BLUE}Checking for HTTP request transform ruleset...${NC}"
  
  # Check if a request transform ruleset exists
  REQUEST_RULESET_ID=$(jq -r '.result[] | select(.phase == "http_request_transform") | .id' "$OUTPUT_DIR/transform_rulesets.json")
  
  if [ -n "$REQUEST_RULESET_ID" ]; then
    echo -e "${GREEN}Found HTTP request transform ruleset: $REQUEST_RULESET_ID${NC}"
  else
    echo -e "${BLUE}Creating HTTP request transform ruleset for OPTIONS...${NC}"
    
    curl -s -X POST "https://api.cloudflare.com/client/v4/zones/$CF_ZONE_ID/rulesets" \
      -H "Authorization: Bearer $CF_API_TOKEN" \
      -H "Content-Type: application/json" \
      --data '{
        "name": "OPTIONS Request Handling",
        "kind": "zone",
        "phase": "http_request_transform",
        "description": "Handle OPTIONS requests at the edge",
        "rules": [
          {
            "action": "rewrite",
            "action_parameters": {
              "headers": {
                "Access-Control-Allow-Origin": {
                  "operation": "set",
                  "expression": "http.request.headers.origin"
                },
                "Access-Control-Allow-Methods": {
                  "operation": "set",
                  "value": "GET, POST, PUT, DELETE, OPTIONS, PATCH"
                },
                "Access-Control-Allow-Headers": {
                  "operation": "set",
                  "value": "Content-Type, Authorization, Origin, X-Debug-Client, CF-Access-Client-Id, CF-Access-Client-Secret, divinci-organization"
                },
                "Access-Control-Allow-Credentials": {
                  "operation": "set",
                  "value": "true"
                },
                "Access-Control-Max-Age": {
                  "operation": "set",
                  "value": "86400"
                }
              }
            },
            "expression": "(http.request.method eq \"OPTIONS\")",
            "description": "Add CORS headers to OPTIONS requests",
            "enabled": true
          }
        ]
      }' > "$OUTPUT_DIR/create_request_transform_ruleset_response.json"
    
    if grep -q '"success":true' "$OUTPUT_DIR/create_request_transform_ruleset_response.json"; then
      echo -e "${GREEN}HTTP request transform ruleset created successfully!${NC}"
    else
      echo -e "${RED}Failed to create HTTP request transform ruleset!${NC}"
      cat "$OUTPUT_DIR/create_request_transform_ruleset_response.json"
    fi
  fi
}

# Function to check and update response rules
check_and_update_response_rules() {
  echo -e "${BLUE}Checking if 'bypass_response_rules' ruleset exists...${NC}"
  
  # Get rulesets specifically for http_config_rules
  curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$CF_ZONE_ID/rulesets" \
    -H "Authorization: Bearer $CF_API_TOKEN" \
    -H "Content-Type: application/json" > "$OUTPUT_DIR/all_rulesets.json"
  
  CONFIG_RULESET_ID=$(jq -r '.result[] | select(.phase == "http_config_rules") | .id' "$OUTPUT_DIR/all_rulesets.json")
  
  if [ -n "$CONFIG_RULESET_ID" ]; then
    echo -e "${GREEN}Found HTTP config ruleset: $CONFIG_RULESET_ID${NC}"
    
    # Get details of the config ruleset
    curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$CF_ZONE_ID/rulesets/$CONFIG_RULESET_ID" \
      -H "Authorization: Bearer $CF_API_TOKEN" \
      -H "Content-Type: application/json" > "$OUTPUT_DIR/config_ruleset_details.json"
    
    # Check if there's an OPTIONS rule
    OPTIONS_CONFIG_RULE=$(jq -r '.result.rules[] | select(.description | contains("OPTIONS") and .action == "skip") | .id' "$OUTPUT_DIR/config_ruleset_details.json")
    
    if [ -n "$OPTIONS_CONFIG_RULE" ]; then
      echo -e "${GREEN}Found OPTIONS rule in config ruleset: $OPTIONS_CONFIG_RULE${NC}"
    else
      echo -e "${BLUE}Adding OPTIONS rule to config ruleset...${NC}"
      
      # Get existing rules
      CONFIG_RULES=$(jq -c '.result.rules' "$OUTPUT_DIR/config_ruleset_details.json")
      
      # Add new rule for OPTIONS
      NEW_RULES=$(echo "[$CONFIG_RULES, {
        \"action\": \"skip\",
        \"action_parameters\": {},
        \"expression\": \"(http.request.method eq \\\"OPTIONS\\\")\",
        \"description\": \"Skip processing for OPTIONS requests\",
        \"enabled\": true
      }]" | jq -s 'add')
      
      # Update ruleset
      curl -s -X PUT "https://api.cloudflare.com/client/v4/zones/$CF_ZONE_ID/rulesets/$CONFIG_RULESET_ID" \
        -H "Authorization: Bearer $CF_API_TOKEN" \
        -H "Content-Type: application/json" \
        --data "{
          \"description\": \"HTTP Config Rules\",
          \"rules\": $NEW_RULES
        }" > "$OUTPUT_DIR/update_config_ruleset_response.json"
      
      if grep -q '"success":true' "$OUTPUT_DIR/update_config_ruleset_response.json"; then
        echo -e "${GREEN}Added OPTIONS rule to config ruleset${NC}"
      else
        echo -e "${RED}Failed to update config ruleset${NC}"
        cat "$OUTPUT_DIR/update_config_ruleset_response.json"
      fi
    fi
  else
    echo -e "${YELLOW}No HTTP config ruleset found${NC}"
  fi
}

# Create specific options bypass rule
create_options_bypass_rule() {
  echo -e "${BLUE}Creating dedicated OPTIONS bypass rule for mTLS...${NC}"
  
  # Get current firewall rules first
  echo -e "${BLUE}Fetching current firewall rules...${NC}"
  curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$CF_ZONE_ID/firewall/rules" \
    -H "Authorization: Bearer $CF_API_TOKEN" \
    -H "Content-Type: application/json" > "$OUTPUT_DIR/firewall_rules.json"
  
  # Check if the API call was successful
  if grep -q '"success":true' "$OUTPUT_DIR/firewall_rules.json"; then
    echo -e "${GREEN}Successfully fetched firewall rules${NC}"
  else
    echo -e "${RED}Failed to fetch firewall rules. API response:${NC}"
    cat "$OUTPUT_DIR/firewall_rules.json"
    echo -e "${YELLOW}Checking API credentials...${NC}"
    echo "Zone ID: $CF_ZONE_ID"
    echo "API Token: ${CF_API_TOKEN:0:5}..." # Only show first 5 chars for security
    return 1
  fi
  
  # Check if a similar rule already exists (check for "Allow All OPTIONs" or "Bypass mTLS for OPTIONS")
  OPTIONS_BYPASS_RULE_ID=$(jq -r '.result // [] | .[] | select(.description | contains("OPTIONS") and (.action == "skip" or .action == "allow")) | .id' "$OUTPUT_DIR/firewall_rules.json")
  
  if [ -n "$OPTIONS_BYPASS_RULE_ID" ]; then
    # Get the description of the rule
    OPTIONS_BYPASS_RULE_DESC=$(jq -r --arg ID "$OPTIONS_BYPASS_RULE_ID" '.result[] | select(.id == $ID) | .description' "$OUTPUT_DIR/firewall_rules.json")
    echo -e "${GREEN}OPTIONS bypass rule already exists: $OPTIONS_BYPASS_RULE_DESC (ID: $OPTIONS_BYPASS_RULE_ID)${NC}"
    
    # Check the action of the rule
    OPTIONS_BYPASS_RULE_ACTION=$(jq -r --arg ID "$OPTIONS_BYPASS_RULE_ID" '.result[] | select(.id == $ID) | .action' "$OUTPUT_DIR/firewall_rules.json")
    if [ "$OPTIONS_BYPASS_RULE_ACTION" != "skip" ]; then
      echo -e "${YELLOW}Existing OPTIONS rule has action '$OPTIONS_BYPASS_RULE_ACTION' instead of 'skip'. Updating...${NC}"
      
      # Get filter ID
      FILTER_ID=$(jq -r --arg ID "$OPTIONS_BYPASS_RULE_ID" '.result[] | select(.id == $ID) | .filter.id' "$OUTPUT_DIR/firewall_rules.json")
      
      # Update rule action to 'skip'
      curl -s -X PUT "https://api.cloudflare.com/client/v4/zones/$CF_ZONE_ID/firewall/rules/$OPTIONS_BYPASS_RULE_ID" \
        -H "Authorization: Bearer $CF_API_TOKEN" \
        -H "Content-Type: application/json" \
        --data "{
          \"action\": \"skip\",
          \"description\": \"Bypass mTLS for OPTIONS requests\",
          \"paused\": false,
          \"filter\": {
            \"id\": \"$FILTER_ID\"
          }
        }" > "$OUTPUT_DIR/update_options_bypass_rule_response.json"
      
      if grep -q '"success":true' "$OUTPUT_DIR/update_options_bypass_rule_response.json"; then
        echo -e "${GREEN}OPTIONS bypass rule updated to use 'skip' action${NC}"
      else
        echo -e "${RED}Failed to update OPTIONS bypass rule action${NC}"
        cat "$OUTPUT_DIR/update_options_bypass_rule_response.json"
      fi
    fi
    
    # Check for priority
    OPTIONS_BYPASS_RULE_PRIORITY=$(jq -r --arg ID "$OPTIONS_BYPASS_RULE_ID" '.result[] | select(.id == $ID) | .priority' "$OUTPUT_DIR/firewall_rules.json")
    if [ "$OPTIONS_BYPASS_RULE_PRIORITY" != "1" ] && [ "$OPTIONS_BYPASS_RULE_PRIORITY" != "null" ]; then
      echo -e "${YELLOW}Existing OPTIONS rule has priority '$OPTIONS_BYPASS_RULE_PRIORITY' instead of '1'. Consider updating for highest priority.${NC}"
    fi
    
    return 0
  else
    # Create filter for OPTIONS bypass
    curl -s -X POST "https://api.cloudflare.com/client/v4/zones/$CF_ZONE_ID/filters" \
      -H "Authorization: Bearer $CF_API_TOKEN" \
      -H "Content-Type: application/json" \
      --data "[{
        \"expression\": \"(http.request.method eq \\\"OPTIONS\\\")\",
        \"paused\": false
      }]" > "$OUTPUT_DIR/create_options_bypass_filter_response.json"
    
    if grep -q '"success":true' "$OUTPUT_DIR/create_options_bypass_filter_response.json"; then
      OPTIONS_BYPASS_FILTER_ID=$(jq -r '.[0].result.id' "$OUTPUT_DIR/create_options_bypass_filter_response.json")
      
      # Create rule with highest priority
      curl -s -X POST "https://api.cloudflare.com/client/v4/zones/$CF_ZONE_ID/firewall/rules" \
        -H "Authorization: Bearer $CF_API_TOKEN" \
        -H "Content-Type: application/json" \
        --data "[{
          \"filter\": {
            \"id\": \"$OPTIONS_BYPASS_FILTER_ID\"
          },
          \"action\": \"skip\",
          \"description\": \"Bypass mTLS for OPTIONS requests\",
          \"paused\": false,
          \"priority\": 1
        }]" > "$OUTPUT_DIR/create_options_bypass_rule_response.json"
      
      if grep -q '"success":true' "$OUTPUT_DIR/create_options_bypass_rule_response.json"; then
        echo -e "${GREEN}OPTIONS bypass rule created with highest priority${NC}"
      else
        echo -e "${RED}Failed to create OPTIONS bypass rule${NC}"
        cat "$OUTPUT_DIR/create_options_bypass_rule_response.json"
      fi
    else
      echo -e "${RED}Failed to create OPTIONS bypass filter${NC}"
      cat "$OUTPUT_DIR/create_options_bypass_filter_response.json"
    fi
  fi
}

# Function to verify credentials
verify_credentials() {
  echo -e "${BLUE}Verifying Cloudflare API credentials...${NC}"
  
  # Try a simple API call to verify credentials
  curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$CF_ZONE_ID" \
    -H "Authorization: Bearer $CF_API_TOKEN" \
    -H "Content-Type: application/json" > "$OUTPUT_DIR/zone_info.json"
  
  # Check if the API call was successful
  if grep -q '"success":true' "$OUTPUT_DIR/zone_info.json"; then
    ZONE_NAME=$(jq -r '.result.name' "$OUTPUT_DIR/zone_info.json")
    echo -e "${GREEN}Successfully authenticated with Cloudflare API${NC}"
    echo -e "${GREEN}Zone ID $CF_ZONE_ID is valid for domain: $ZONE_NAME${NC}"
    return 0
  else
    echo -e "${RED}Failed to authenticate with Cloudflare API. Response:${NC}"
    cat "$OUTPUT_DIR/zone_info.json"
    echo ""
    echo -e "${YELLOW}Please check your Cloudflare credentials:${NC}"
    echo -e "1. Zone ID: $CF_ZONE_ID"
    echo -e "2. API Token: ${CF_API_TOKEN:0:5}..."
    echo -e "${YELLOW}Make sure you've sourced the correct environment file with valid credentials${NC}"
    return 1
  fi
}

# Main function
main() {
  echo -e "${BLUE}=== Fixing Cloudflare Rules for OPTIONS Handling ===${NC}"
  
  # First verify credentials are working
  verify_credentials || { echo -e "${RED}Cannot proceed without valid Cloudflare credentials.${NC}"; exit 1; }
  
  # Create high-priority OPTIONS bypass rule
  create_options_bypass_rule || { echo -e "${RED}Failed to create OPTIONS bypass rule.${NC}"; exit 1; }
  
  # Update transform rules
  update_transform_rules
  
  # Update firewall rules
  update_firewall_rules
  
  # Create request transform ruleset
  create_request_transform_ruleset
  
  # Check and update response rules
  check_and_update_response_rules
  
  echo -e "${GREEN}=== Finished updating Cloudflare rules ===${NC}"
  echo -e "${BLUE}Run the test script to check if the OPTIONS requests are now working:${NC}"
  echo "./test-cors-options-updated.sh"
}

# Run the main function
main