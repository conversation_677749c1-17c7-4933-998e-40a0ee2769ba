import { chromium, FullConfig } from '@playwright/test';
import { getCloudTestConfig, getServiceEndpoints } from './config/cloud-config';

/**
 * Global setup for cloud E2E testing
 * This runs once before all tests and ensures the environment is ready
 */
async function globalSetup(config: FullConfig) {
  console.log('🌍 Starting global setup for cloud E2E testing...');
  
  const cloudConfig = getCloudTestConfig();
  const endpoints = getServiceEndpoints(cloudConfig);
  
  console.log(`🎯 Target environment: ${cloudConfig.environment}`);
  console.log(`🔗 Base URL: ${cloudConfig.baseURL}`);
  console.log(`🔗 API URL: ${cloudConfig.apiURL}`);
  
  // Launch a browser for setup tasks
  const browser = await chromium.launch();
  const context = await browser.newContext({
    extraHTTPHeaders: cloudConfig.headers,
    ignoreHTTPSErrors: cloudConfig.environment !== 'production',
  });
  
  try {
    // 1. Verify API is accessible
    console.log('🔍 Verifying API accessibility...');
    const page = await context.newPage();
    
    try {
      const response = await page.request.get(endpoints.api.health, {
        timeout: 30000, // 30 second timeout for initial check
      });
      
      if (response.status() !== 200) {
        throw new Error(`API health check failed with status ${response.status()}`);
      }
      
      console.log('✅ API is accessible and healthy');
    } catch (error) {
      console.error('❌ API health check failed:', error);
      throw new Error(`API is not accessible at ${endpoints.api.health}. Please ensure the environment is deployed and running.`);
    }
    
    // 2. Verify web application is accessible
    console.log('🔍 Verifying web application accessibility...');
    
    try {
      await page.goto(cloudConfig.baseURL, {
        timeout: 30000,
        waitUntil: 'networkidle',
      });
      
      const title = await page.title();
      if (!title || title.length === 0) {
        throw new Error('Web application loaded but has no title');
      }
      
      console.log(`✅ Web application is accessible (title: "${title}")`);
    } catch (error) {
      console.error('❌ Web application accessibility check failed:', error);
      throw new Error(`Web application is not accessible at ${cloudConfig.baseURL}. Please ensure the environment is deployed and running.`);
    }
    
    // 3. Verify Cloudflare Access configuration (if enabled)
    if (cloudConfig.features.cloudflareAccess) {
      console.log('🔍 Verifying Cloudflare Access configuration...');
      
      const cfHeaders = {
        'CF-Access-Client-Id': cloudConfig.headers['CF-Access-Client-Id'],
        'CF-Access-Client-Secret': cloudConfig.headers['CF-Access-Client-Secret'],
      };
      
      if (!cfHeaders['CF-Access-Client-Id'] || !cfHeaders['CF-Access-Client-Secret']) {
        console.warn('⚠️ Cloudflare Access credentials not found - some tests may fail');
      } else {
        try {
          const response = await page.request.get(endpoints.api.health, {
            headers: cfHeaders,
            timeout: 15000,
          });
          
          if (response.status() === 200) {
            console.log('✅ Cloudflare Access is properly configured');
          } else {
            console.warn(`⚠️ Cloudflare Access check returned status ${response.status()}`);
          }
        } catch (error) {
          console.warn('⚠️ Cloudflare Access verification failed:', error);
        }
      }
    }
    
    // 4. Environment-specific setup
    await performEnvironmentSpecificSetup(cloudConfig, page);
    
    console.log('🎉 Global setup completed successfully');
    
  } finally {
    await context.close();
    await browser.close();
  }
}

/**
 * Perform environment-specific setup tasks
 */
async function performEnvironmentSpecificSetup(cloudConfig: ReturnType<typeof getCloudTestConfig>, page: any) {
  console.log(`🔧 Performing ${cloudConfig.environment}-specific setup...`);
  
  switch (cloudConfig.environment) {
    case 'develop':
      // Development environment setup
      console.log('🛠️ Development environment - enabling debug features');
      break;
      
    case 'staging':
      // Staging environment setup
      console.log('🎭 Staging environment - preparing for comprehensive testing');
      
      // Maybe clear any test data from previous runs
      // await clearTestData(page);
      break;
      
    case 'production':
      // Production environment setup
      console.log('🏭 Production environment - read-only testing mode');
      
      // Ensure we're in read-only mode for production
      process.env.READ_ONLY_MODE = 'true';
      break;
  }
  
  console.log(`✅ ${cloudConfig.environment} environment setup completed`);
}

/**
 * Clear test data (for staging environment)
 */
async function clearTestData(page: any) {
  console.log('🧹 Clearing test data from previous runs...');
  
  // This would implement cleanup logic specific to your application
  // For example, deleting test users, test files, etc.
  
  console.log('✅ Test data cleared');
}

export default globalSetup;
