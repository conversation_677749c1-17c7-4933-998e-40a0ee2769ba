# GitHub Codespaces Setup Guide

This guide explains how to set up the development environment in GitHub Codespaces with automatic CORS configuration.

## 🚀 Quick Start

### 1. First-Time Setup

When you create a new Codespace, the setup will happen automatically:

1. **SSH Key Generation**: A new SSH key is generated for your Codespace
2. **Add SSH Key to GitHub**: You'll see instructions to add the key to your GitHub account
3. **Submodule Initialization**: Private submodules (including `private-keys`) will be pulled
4. **CORS Configuration**: CORS settings will be automatically updated for your Codespace URLs
5. **Service Startup**: All services will be ready to start

### 2. Adding SSH Key to GitHub

You have several options to add the SSH key:

#### Option A: GitHub Web Interface (Recommended)

1. Copy the SSH public key shown in the terminal
2. Go to https://github.com/settings/ssh/new
3. Paste the key and give it a title like "Codespace [your-codespace-name]"

#### Option B: GitHub CLI (if authenticated)

```bash
gh ssh-key add ~/.ssh/id_ed25519.pub --title "Codespace $(hostname) $(date +%Y-%m-%d)"
```

#### Option C: GitHub API

```bash
curl -H "Authorization: token <YOUR_PERSONAL_ACCESS_TOKEN>" \
     -H "Accept: application/vnd.github+json" \
     https://api.github.com/user/keys \
     -d '{"title":"Codespace $(hostname) $(date +%Y-%m-%d)","key":"<YOUR_PUBLIC_KEY>"}'
```

### 3. Starting Services

After setup is complete, start all services:

```bash
./start-codespace.sh
```

## 🌐 CORS Configuration

### Automatic Configuration

The setup automatically configures CORS for your current Codespace URLs:

- `https://[codespace-name]-8080.app.github.dev` (Web Client)
- `https://[codespace-name]-8081.app.github.dev` (API Live)
- `https://[codespace-name]-8083.app.github.dev` (API Webhook)
- `https://[codespace-name]-9080.app.github.dev` (Public API)

### Manual CORS Updates

If you need to update CORS configuration manually:

```bash
./update-codespace-cors.sh
```

## 📁 Project Structure

After setup, your workspace will have:

```
/workspaces/server/
├── private-keys/          # Private configuration (from submodule)
│   └── local-fast/
│       └── cors.env       # CORS configuration
├── workspace/             # Main application code
│   ├── clients/web/       # Frontend application
│   └── servers/           # Backend services
├── start-codespace.sh     # Start all services
├── restart-services.sh    # Restart services with new config
└── update-codespace-cors.sh # Update CORS for current Codespace
```

## 🔧 Available Scripts

| Script                       | Purpose                                          |
| ---------------------------- | ------------------------------------------------ |
| `./start-codespace.sh`       | Start all services with automatic CORS update    |
| `./restart-services.sh`      | Restart all services (created after first setup) |
| `./update-codespace-cors.sh` | Update CORS configuration only                   |

## 🌍 Service URLs

Once services are running, access them at:

- **Web Client**: `https://[codespace-name]-8080.app.github.dev`
- **Public API**: `https://[codespace-name]-9080.app.github.dev`
- **API Live**: `https://[codespace-name]-8081.app.github.dev`
- **API Webhook**: `https://[codespace-name]-8083.app.github.dev`

Replace `[codespace-name]` with your actual Codespace name (e.g., `sturdy-space-broccoli-g4xpjgv6376q`).

## 🐛 Troubleshooting

### SSH Key Issues

If submodule initialization fails:

1. Verify your SSH key was added to GitHub: https://github.com/settings/keys
2. Test SSH connection: `ssh -T **************`
3. Manually initialize submodules: `git submodule sync && git submodule update --init --recursive --remote`

### CORS Issues

If you see CORS errors in the browser:

1. Check your current Codespace name: `echo $CODESPACE_NAME`
2. Update CORS configuration: `./update-codespace-cors.sh`
3. Restart services: `./restart-services.sh`

### Service Health Issues

Check service logs:

```bash
# View logs
tail -f /workspaces/server/public-api.log
tail -f /workspaces/server/public-api-live.log
tail -f /workspaces/server/public-api-webhook.log
tail -f /workspaces/server/web-client.log

# Check service status
curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/
curl -s -o /dev/null -w "%{http_code}" http://localhost:9080/
```

### Port Conflicts

If services fail to start due to port conflicts:

```bash
# Check what's using the ports
netstat -tlnp | grep -E ":(8080|8081|8083|9080)"

# Kill conflicting processes
pkill -f "pnpm start"
pkill -f "node.*dist"
```

## 🔄 Starting Fresh

To completely reset your Codespace environment:

1. Delete the Codespace and create a new one, OR
2. Reset the environment:

   ```bash
   # Stop all services
   pkill -f "pnpm start" || true
   pkill -f "node.*dist" || true

   # Clean and reinitialize submodules
   git submodule deinit --all -f
   git submodule update --init --recursive --remote

   # Update CORS and restart
   ./update-codespace-cors.sh
   ./start-codespace.sh
   ```

## 💡 Tips

1. **Bookmark your service URLs** - they remain consistent for the lifetime of your Codespace
2. **Use VS Code tasks** - The workspace includes pre-configured tasks for starting individual services
3. **Monitor logs** - Each service logs to its own file in the workspace root
4. **Check health endpoints** - Services respond with HTTP status codes for health checking

## 🆘 Getting Help

If you encounter issues:

1. Check the troubleshooting section above
2. Review service logs for error details
3. Ensure all environment variables are properly set
4. Verify that private-keys submodule is accessible and up to date
