<<<<<<< HEAD

=======
>>>>>>> WA-170_MCP
export const DIVINCI_DOMAINS: Array<string> = [];
export const CORS_PORTS = (
  !process.env.CORS_PORTS ? undefined :
  process.env.CORS_PORTS.split(",").map(port=>parseInt(port.trim()))
);

if(process.env.CORS_SUBDOMAINS) {
  // Clean and parse subdomains, removing any quotes or extra whitespace
  const rawSubdomains = process.env.CORS_SUBDOMAINS;
  const cleanedSubdomains = rawSubdomains.replace(/['"]/g, ""); // Remove any quotes
  const subdomains = cleanedSubdomains.split(",").map(s=>s.trim());

  // Get current environment
  const currentEnv = process.env.ENVIRONMENT || "";
<<<<<<< HEAD
  console.log("🔎 CURRENT ENVIRONMENT:", currentEnv);
=======
  const nodeEnv = process.env.NODE_ENV || "";
  const forceR2 = process.env.FORCE_R2_STORAGE;
  console.log("🔎 CURRENT ENVIRONMENT:", currentEnv, "| NODE_ENV:", nodeEnv, "| FORCE_R2_STORAGE:", forceR2);
>>>>>>> WA-170_MCP

  // For staging and production environments, prioritize environment-specific domains
  if(currentEnv === "staging" || currentEnv === "production") {
    // Add environment-specific domains if specified
    if(process.env.CORS_SUBDOMAIN_ENVS) {
      const rawEnvs = process.env.CORS_SUBDOMAIN_ENVS;
      const cleanedEnvs = rawEnvs.replace(/['"]/g, ""); // Remove any quotes
      const subdomainEnvs = cleanedEnvs.split(",").map(e=>e.trim());

      // Create all combinations of subdomain.env.divinci.app
      const envDomains: string[] = [];
      subdomainEnvs.forEach((subdomainEnv)=>{
        subdomains.forEach((subdomain)=>{
          const domain = `${subdomain}.${subdomainEnv}.divinci.app`;
          envDomains.push(domain);
          DIVINCI_DOMAINS.push(domain);
        });
      });

      // console.log("🔎 CORS SUBDOMAIN ENVS (raw):", rawEnvs);
      // console.log("🔎 CORS SUBDOMAIN ENVS (cleaned):", subdomainEnvs);
      // console.log("🔎 CORS ENV DOMAINS:", envDomains);
    }

    // Add base domains as fallback (without environment)
    const baseDomains = subdomains.map(subdomain=>`${subdomain}.divinci.app`);
    DIVINCI_DOMAINS.push(...baseDomains);

    // console.log("🔎 CORS SUBDOMAINS (raw):", rawSubdomains);
    // console.log("🔎 CORS SUBDOMAINS (cleaned):", subdomains);
    // console.log("🔎 CORS BASE DOMAINS:", baseDomains);
  } else {
    // For local and development environments, add base domains first
    const baseDomains = subdomains.map(subdomain=>`${subdomain}.divinci.app`);
    DIVINCI_DOMAINS.push(...baseDomains);

    // console.log("🔎 CORS SUBDOMAINS (raw):", rawSubdomains);
    // console.log("🔎 CORS SUBDOMAINS (cleaned):", subdomains);
    // console.log("🔎 CORS BASE DOMAINS:", baseDomains);

    // Add environment-specific domains if specified
    if(process.env.CORS_SUBDOMAIN_ENVS) {
      const rawEnvs = process.env.CORS_SUBDOMAIN_ENVS;
      const cleanedEnvs = rawEnvs.replace(/['"]/g, ""); // Remove any quotes
      const subdomainEnvs = cleanedEnvs.split(",").map(e=>e.trim());

      // Create all combinations of subdomain.env.divinci.app
      const envDomains: string[] = [];
      subdomainEnvs.forEach((subdomainEnv)=>{
        subdomains.forEach((subdomain)=>{
          const domain = `${subdomain}.${subdomainEnv}.divinci.app`;
          envDomains.push(domain);
          DIVINCI_DOMAINS.push(domain);
        });
      });

      // console.log("🔎 CORS SUBDOMAIN ENVS (raw):", rawEnvs);
      // console.log("🔎 CORS SUBDOMAIN ENVS (cleaned):", subdomainEnvs);
      // console.log("🔎 CORS ENV DOMAINS:", envDomains);
    }
  }
}

if(process.env.CORS_IP_ADDRESSES) {
  // Clean and parse IP addresses, removing any quotes or extra whitespace
  const rawIpAddresses = process.env.CORS_IP_ADDRESSES;
  const cleanedIpAddresses = rawIpAddresses.replace(/['"]/g, ""); // Remove any quotes
  const ipAddresses = cleanedIpAddresses.split(",").map(ip=>ip.trim());

  // console.log("🔎 CORS IP ADDRESSES (raw):", rawIpAddresses);
  // console.log("🔎 CORS IP ADDRESSES (cleaned):", ipAddresses);

  // Add IP addresses to allowed domains
  DIVINCI_DOMAINS.push(...ipAddresses);
}

if(process.env.CORS_FULL_ORIGINS) {
  // Clean and parse full origins, removing any quotes or extra whitespace
  const rawOrigins = process.env.CORS_FULL_ORIGINS;
  const cleanedOrigins = rawOrigins.replace(/['"]/g, ""); // Remove any quotes
  const origins = cleanedOrigins.split(",").map(origin=>origin.trim());

  console.log("🔎 CORS FULL ORIGINS (raw):", rawOrigins);
  console.log("🔎 CORS FULL ORIGINS (cleaned):", origins);

  // Add full origins to allowed domains
  DIVINCI_DOMAINS.push(...origins);
}
